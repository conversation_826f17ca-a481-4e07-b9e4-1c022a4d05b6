{#
/**
 * @file
 * Default theme implementation of a color swatch formatter.
 *
 * Available variables:
 * - color: The color background.
 * - width: The width of the color swatch.
 * - height: The height of the color swatch.
 * - shape: The shape of the color swatch.
 *
 * @see template_preprocess()
 * @see template_preprocess_color_field_formatter_swatch()
 *
 * @ingroup themeable
 */
#}

{% if shape == 'triangle' %}
<div {{ attributes }} style="border-top: {{ width }} solid {{ color }}; border-right: {{ height }} solid transparent;"></div>
{% else %}
<div  {{ attributes }} style="background-color: {{ color }}; width: {{ width }}; height: {{ height }};"></div>
{% endif %}
