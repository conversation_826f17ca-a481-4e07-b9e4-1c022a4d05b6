{#
/**
 * @file
 * Default theme implementation for a webform section element and its children.
 *
 * Available variables:
 * - attributes: HTML attributes for the <section> element.
 * - errors: (optional) Any errors for this <section> element, may not be set.
 * - required: <PERSON><PERSON><PERSON> indicating whether the <section> element is required.
 * - title: The title/header of the section header.
 * - title_attributes: HTML attributes to apply to the title/header element.
 * - title_tag: The title/header HTML tag.
 * - description: The description element containing the following properties:
 *   - content: The description content of the <fieldset>.
 *   - attributes: HTML attributes to apply to the description container.
 * - description_display: Description display setting. It can have these values:
 *   - before: The description is output before the element.
 *   - after: The description is output after the element. This is the default
 *     value.
 *   - invisible: The description is output after the element, hidden visually
 *     but available to screen readers.
 * - children: The rendered child elements of the <fieldset>.
 * - prefix: The content to add before the .section-wrapper children.
 * - suffix: The content to add after the .section-wrapper children.
 *
 * Copied from: fieldset.html.twig
 *
 * @see template_preprocess_webform_section()
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
    'js-form-item',
    'form-item',
    'js-form-wrapper',
    'form-wrapper',
    'webform-section',
  ]
%}
<section{{ attributes.addClass(classes) }}>
  {%
    set title_classes = [
      'webform-section-title',
      required ? 'js-form-required',
      required ? 'form-required',
    ]
  %}
  {% if title %}
    <{{ title_tag }}{{ title_attributes.addClass(title_classes) }}>{{ title }}</{{ title_tag }}>
  {% endif %}
  <div class="webform-section-wrapper">
    {% if errors %}
      <div>
        {{ errors }}
      </div>
    {% endif %}
    {% if description_display in ['before', 'invisible'] and description.content %}
      <div{{ description.attributes.addClass('description') }}>{{ description.content }}</div>
    {% endif %}
    {% if prefix %}
      <span class="field-prefix">{{ prefix }}</span>
    {% endif %}
    {{ children }}
    {% if suffix %}
      <span class="field-suffix">{{ suffix }}</span>
    {% endif %}
    {% if description_display == 'after' and description.content %}
      <div{{ description.attributes.addClass('description') }}>{{ description.content }}</div>
    {% endif %}
  </div>
</section>
