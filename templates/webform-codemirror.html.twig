{#
/**
 * @file
 * Theme implementation for 'CodeMirror' code.
 *
 * Available variables
 * - code: The code.
 * - type: The type of code.
 * - mode: The CodeMirror mode used to format the code.
 *
 * @see http://codemirror.net/doc/manual.html#option_mode
 * @see \Drupal\webform\Element\WebformCodeMirror()
 * @see template_preprocess_webform_codemirror()
 *
 * @ingroup themeable
 */
#}
{% if type == 'html' %}
  {{ attach_library('webform/webform.element.codemirror.html') }}
{% elseif type == 'yaml' %}
  {{ attach_library('webform/webform.element.codemirror.yaml') }}
{% else %}
  {{ attach_library('webform/webform.element.codemirror.text') }}
{% endif %}
<pre class="js-webform-codemirror-runmode webform-codemirror-runmode" data-webform-codemirror-mode="{{ mode }}">{{ code }}</pre>
