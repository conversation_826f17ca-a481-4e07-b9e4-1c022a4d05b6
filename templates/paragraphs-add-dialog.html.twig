{#
/**
 * @file
 * Default theme implementation of modal add paragraph dialog template.
 *
 * Following classes have custom use:
 *   - paragraphs-add-dialog - is used to wrap the dialog.
 *   - paragraphs-add-dialog-row - is used to wrap the paragraph type rows.
 *
 *
 * @ingroup themeable
 */
#}
{{ add }}
<div class="paragraphs-add-dialog js-hide">
    <ul class="paragraphs-add-dialog-list">
    {% for button in buttons %}
      <li class="paragraphs-add-dialog-row">
        {{ button }}
      </li>
    {% endfor %}
  </ul>
</div>
