language: php
dist: trusty
sudo: false

php:
  - 7.0
  - 7.1
  - 7.2
  - 7.3

env:
  global:
    - SIMPLETEST_DB=sqlite://tmp/site.sqlite
    - SIMPLETEST_BASE_URL="http://127.0.0.1:8080"
  matrix:
    - RELEASE=stable COMPOSER_CHANNEL=stable
    - RELEASE=dev COMPOSER_CHANNEL=stable
    - RELEASE=stable COMPOSER_CHANNEL=snapshot

before_install:
  - echo 'sendmail_path = /bin/true' >> ~/.phpenv/versions/$(phpenv version-name)/etc/conf.d/travis.ini
  - phpenv config-rm xdebug.ini
  - composer --verbose self-update --$COMPOSER_CHANNEL
  - composer --version

install:
  - composer --verbose validate
  - composer --verbose install

script:
  - if [[ $RELEASE = dev ]]; then composer --verbose remove --no-update drupal/console; fi;
  - if [[ $RELEASE = dev ]]; then composer --verbose require --no-update drupal/core:8.8.x-dev webflo/drupal-core-require-dev:8.8.x-dev; fi;
  - if [[ $RELEASE = dev ]]; then composer --verbose update; fi;
  - cd $TRAVIS_BUILD_DIR/web
  - ./../vendor/bin/drush site-install --verbose --yes --db-url=sqlite://tmp/site.sqlite
  - ./../vendor/bin/drush runserver $SIMPLETEST_BASE_URL &
  - until curl -s $SIMPLETEST_BASE_URL; do true; done > /dev/null
  # Skip core/tests/Drupal/Tests/ComposerIntegrationTest.php because web/ has no composer.json
  # Ignore PageCache group temporarily, @see https://www.drupal.org/node/2770673
  # Ignore Setup group temporarily, @see https://www.drupal.org/node/2962157
  - ./../vendor/bin/phpunit -c core --testsuite unit --exclude-group Composer,DependencyInjection,PageCache,Setup
  - ./../vendor/bin/drush
  - if [[ $RELEASE = stable ]]; then ./../vendor/bin/drupal; fi;
