<?php

/**
 * @file
 * A light-weight, customizable image gallery plugin for <PERSON><PERSON><PERSON> based on jQuery.
 */

use Drupal\Core\Url;
use Dr<PERSON>al\flexslider\Entity\Flexslider;


/**
 * Implements hook_library_info_alter().
 */
function flexslider_library_info_alter(&$libraries, $extension) {
  if ($extension == 'flexslider' and isset($libraries['flexslider'])) {

    // Remove the library and/or module css if privileged user disabled it.
    if (!\Drupal::config('flexslider.settings')->get('flexslider_css')) {
      if (isset($libraries['flexslider']['css'])) {
        unset($libraries['flexslider']['css']);
      }
    }
    if (!\Drupal::config('flexslider.settings')->get('flexslider_module_css')) {
      if (isset($libraries['integration']['css'])) {
        unset($libraries['integration']['css']);
      }
    }

    $standard = '/libraries/flexslider';
    // @todo Remove this conditional once 8.9 is the minimum supported core
    // version.
    if (\Drupal::hasService('library.libraries_directory_file_finder')) {
      /** @var \Drupal\Core\Asset\LibrariesDirectoryFileFinder $library_file_finder */
      $library_file_finder = \Drupal::service('library.libraries_directory_file_finder');
      $library_path = '/' . $library_file_finder->find('flexslider');
    }
    else {
      $library_path = $standard;
    }

    // Enable debug mode.
    $debug = \Drupal::config('flexslider.settings')->get('flexslider_debug');
    if ($debug) {
      // Add the unminified version.
      $libraries['flexslider']['js'] = [
        $library_path . '/jquery.flexslider.js' => [],
      ];
    }

  }
}

/**
 * Implements hook_help().
 */
function flexslider_help($route_name, $route_match) {

  switch ($route_name) {
    case 'help.page.flexslider':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The FlexSlider module integrates the FlexSlider library into Drupal. For more information about the library, see the <a href=":docs" target="_new">FlexSlider documentation</a>', [':docs' => Url::fromUri('https://www.woothemes.com/flexslider/')->getUri()]) . '</p>';
      $output .= '<h3>' . t('Terminology') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Option set') . '</dt>';
      $output .= '<dd>' . t('An option set defines all the settings for displaying the slider. Things like slide direction, speed, starting slide, etc... You can define as many option sets as you like and on top of that they are all exportable! Which means you can carry configuration of your Flex Slider instances from one site to the next or create features.') . '</dd>';
      $output .= '</dl>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Defining option sets') . '</dt>';
      $output .= '<dd>' . t('No matter how you want to use FlexSlider (with fields or views) you need to define "option sets" to tell FlexSlider how you want it to display. Go to the <a href="/:flexslider_admin">flexslider administration page</a>. From there you can edit the default option set and define new ones. These will be listed as options in the various forms where you setup FlexSlider to display. NOTE: under advanced options, you can set a namespace prefix for the optionset. This will allow you to build custom CSS for each optionset.  Start by copying the flexslider_img.css from the assets subfolder to your theme. Build new custom CSS for each prefix in your optionsets.', [':flexslider_admin' => Url::fromRoute('entity.flexslider.collection')->getInternalPath()]) . '</dd>';
      $output .= '<dt>' . t('Configuring FlexSlider field') . '</dt>';
      $output .= '<dd>' . t('The flexslider_fields module provides a way for you to turn a multi-file image field into a FlexSlider. Simply go to the "Manage Display" page of the Content type (or other Entity) containing your field and set the Format of the field to FlexSlider. There you can select your preferred option set and image style. The Responsive FlexSlider formatter is also available when core Responsive Image module is enabled.') . '</dd>';
      $output .= '<dt>' . t('Configuring FlexSlider view style') . '</dt>';
      $output .= '<dd>' . t('Flex Slider Views (flexslider_views module) allows you to build views which display their results in Flex Slider. Similar to how you can output fields as an "HTML List" or "Table", you can now select "Flex Slider" as an option. Create or edit a view and ensure it can load a content type which contain image fields. Set your display fields to include an image field. In the field settings, DO NOT SET THE FORMATTER TO FLEXSLIDER. This will attempt to put Flex Sliders inside other Flex Sliders and will just get messy. Ensure you don\'t include any wrapper markup, labels or container markup for the field value itself. Save your field. Next, go to "Format" in the main Views windows. Click and select "Flex Slider", then select your option set. Save your view and you should see your results displayed in Flex Slider.') . '</dd>';
      $output .= '<dt>' . t('Attaching the library') . '</dt>';
      $output .= '<dd>' . t('The flexslider_add() function may be used in multiple ways to manually attach the library to any render array, such as a page or block.') . '</dd>';
      $output .= '</dl>';
      return $output;

    case 'entity.flexslider.collection':
      $output = '';
      $output .= t('An <em>option set</em> defines exactly how a flexslider image gallery looks and behaves on your site.');
      $output .= '<br/>';
      $output .= t('For a full documentation of all options, refer to the official <a href=":docs" target="_new">FlexSlider documentation</a>.', [':docs' => Url::fromUri('https://www.woothemes.com/flexslider/')->getUri()]);
      return '<p>' . $output . '</p>';
  }
}

/**
 * Implements hook_theme().
 */
function flexslider_theme() {
  return [
    // Flexslider container.
    'flexslider' => [
      'variables' => ['flexslider' => []],
      'file' => 'templates/flexslider.theme.inc',
    ],

    // Slide container.
    'flexslider_list' => [
      'variables' => ['items' => [], 'settings' => []],
      'file' => 'templates/flexslider.theme.inc',
    ],

    // Slide item.
    'flexslider_list_item' => [
      'variables' => [
        'item' => [],
        'settings' => [],
        'caption' => '',
      ],
      'file' => 'templates/flexslider.theme.inc',
    ],
  ];
}

/**
 * Gets an array of option sets suitable for using as select list options.
 *
 * @return array
 *   Array of optionset labels keyed by id
 */
function flexslider_optionset_list() {
  $optionsets = Flexslider::loadMultiple();
  $options = [];
  foreach ($optionsets as $name => $optionset) {
    $options[$name] = $optionset->label();
  }

  if (empty($options)) {
    $options[''] = t('No defined option sets');
  }
  return $options;
}

/**
 * Returns array of required JavaScripts and settings for a flexslider instance.
 *
 * @param string $id
 *   optional ID Attribute for FlexSlider container.
 * @param object|string $optionset
 *   optional Option set to load or the machine name of an existing optionset.
 *
 * @return array
 *   Array of asset attachments.
 */
function flexslider_add($id = NULL, $optionset = NULL) {

  $attached = [];

  // Check optionset value.
  if (is_string($optionset)) {
    $name = $optionset;
    $optionset = Flexslider::load($name);
    if (empty($optionset)) {
      \Drupal::logger('flexslider')->warning('Invalid optionset name supplied to flexslider_add: @name', ['@name' => $name]);
      return $attached;
    }
  }

  // Static array to remember which scripts are already attached.
  // @todo not currently in use
  $cache = &drupal_static(__FUNCTION__, []);

  $attached['library'][] = 'flexslider/integration';

  // If the ID or optionset aren't set, it is assumed the settings will be set
  // manually via the calling module/theme.
  if (!empty($optionset) && !in_array($optionset->id(), $cache)) {
    $cache[] = $optionset->id();
    $attached['drupalSettings']['flexslider']['optionsets'] = [
      $optionset->id() => $optionset->getOptions(TRUE),
    ];
  }

  if (!empty($id)) {
    // JavaScript settings.
    $attached['drupalSettings']['flexslider']['instances'] = [
      $id => $optionset ? $optionset->id() : '-1',
    ];
    // Create an empty optionset setting if none given.
    if (empty($optionset)) {
      $attached['drupalSettings']['flexslider']['optionsets'] = [
        '-1' => [],
      ];
    }
  }

  return $attached;
}
