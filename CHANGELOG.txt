Smart Trim 2.0
------------------
Issue #3301743 by <PERSON><PERSON>, <PERSON>revil: Support tokens in more text and replace in...
Issue #3289742 by d<PERSON><PERSON>, Project Update Bot: Automated Drupal...
Issue #3194975: Settings form '#states' values only work on fields named 'body'
Fixed typo and added test for multiple nested tags.
Issue #2829817 by ultimike, markie, ruloweb: Apply dependency injection (DI) to TruncateHTML class
Updating my short url.
Update README.md with better config instructions
Removed the remaining minus and plus characters from copy/paste in demo
removed some "+"s
switching to the markdown
Update to readme.txt.
Issue #3246542 by <PERSON>, yash.rode: Support D7 migrations
Updated Unit test for 2.x branch.
Issue #2974633 by <PERSON> - rerollr
Issue #3172729 by <PERSON><PERSON><PERSON><PERSON>: Update source url in composer.json
Remove 8 and add 10
Adding patch #4 from issue
Issue #3194955 by markie, justcaldwell: Improve more link accessibility with aria-label attribute

Smart Trim 8.x-1.3
------------------
Issue #3131842 by markie, <PERSON>, <PERSON><PERSON><PERSON><PERSON>: Read more still not translated
Issue #3131394 by loland<PERSON>: License "GPL-2.0+" is a deprecated SPDX license identifier
Issue #3092581 by jenlampton, DamienMcKenna: Drupal 9 Release plan for Smart Trim'

Smart Trim 8.x-1.2
------------------
Issue by ckaotik: Trimmed summary  markup.
Issue #2995557 by jkaeser, <EMAIL>: Extra space characters after HTML stripping
Issue #3042672 by Phil Wolstenholme: Drupal 9 Deprecated Code Report
Issue #2169583 by danbohea, MegaChriz, thewilkybarkid: Trim certain punctuation from the end of truncated text output before appending suffix
Issue #2994386 by Daniel Korte: Use OOP translate function instead of procedural one in formatter
Issue #2997706 by deepanker_bhalla, dhirendra.mishra, msankhala, markie: Coding standard
Issue #2875378 by caldenjacobs, timwood: Strip figcaption when Strip HTML is selected (8.x-1.x)
Issue #3031786 by mitrpaka: Use mb_* functions instead of Unicode::* methods
PHPCS fixes
Issue #2941492 by Dan Dalpiaz, sardara: Additional options checkbox do not appear to save
Issue #2983974 by chipway: Apply new {project}:{module} format for dependencies in info.yml

Smart Trim 8.x-1.1
------------------
Issue #2851703 by sardara: Missing schema.yml
Issue #2842404 by pfrilling: Allow a zero length trim
Issue #2842783 by richard.c.allen2386: Smart Trim should support plain text and plain text long data types (or string and string_long)
Issue #2661632 by superbiche: Trim to word boundary when using character count
Issue #2782689 by yvesvanlaer, heddn, markie: How to translate a custom read more link?
Issue #2733381 by Stefdewa, Shreya Shetty: UTF-8 encoding needed to show all characters correctly
Issue #2746533: Add a wrapper around text to separate from read more
Adding composer.
Issue #2796379 by shruti1803: Missing hook_help
Issue #2773709 by seaarg: A fatal error occurred: The [entity type] entity cannot have a URI as it does not have an ID
Issue #2758557 by rajeshwari10: Remove @file tag docblock from all the .php files
Issue #2770075 by Nitesh Pawar: Replaced t() with $this->t()
Issue #2736839 by cbeier: Twig exception if Smart Trim is used on a field -> entity with no link
Issue #2644360 by alan-ps: more link class attribute name is wrong

Smart Trim 8.x-1.0
------------------
Issue #2717471 by alexpott: TruncateHTML has weird scope declarations
Minor standards updates.
Issue #2639188 by mikeyk, alexpott: Encoding issue

Smart Trim 8.x-1.0-beta1
------------------------
Fixing more link options. also minor code standards
Issue #2582839 by mbaynton: Drupal 8.0 RC1 compatibility
Issue #2363123 by markie: Trim excluding HTML (without stripping)
Issue #2355687 by krisahil | kbasarab: Fixed Format setting not appearing.
updating to PSR-4 standard for D8
a useful read me would be useful
Issue #2162985 by pferlito: Adding Drupal 8 branch for testing
Initial Commit
