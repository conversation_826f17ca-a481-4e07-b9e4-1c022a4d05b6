services:
  config_log_database_subscriber:
    class: <PERSON><PERSON>al\config_log\EventSubscriber\ConfigLogDatabaseSubscriber
    arguments: ['@database', '@config.factory', '@current_user', '@datetime.time']
    tags:
      - { name: event_subscriber }
  config_log_psr_subscriber:
    class: Drupal\config_log\EventSubscriber\ConfigLogPsrSubscriber
    arguments: ['@logger.channel.default', '@config.factory', '@datetime.time']
    tags:
      - { name: event_subscriber }
  config_log_mail_subscriber:
    class: Drupal\config_log\EventSubscriber\ConfigLogMailSubscriber
    arguments: ['@plugin.manager.mail', '@config.factory', '@language_manager', '@current_user', '@datetime.time', '@date.formatter']
    tags:
      - { name: event_subscriber }
