<?php

/**
 * @file
 * API functions for installing Drupal.
 */

use Drupal\Component\Utility\UrlHelper;
use Drupal\Core\Batch\BatchBuilder;
use Drupal\Core\Cache\NullBackend;
use Drupal\Core\Config\ConfigImporter;
use Drupal\Core\Config\ConfigImporterException;
use Drupal\Core\Config\Importer\ConfigImporterBatch;
use Drupal\Core\Config\FileStorage;
use Drupal\Core\Config\StorageComparer;
use Drupal\Core\DrupalKernel;
use Drupal\Core\Database\Database;
use Drupal\Core\Database\DatabaseExceptionWrapper;
use Drupal\Core\Extension\Exception\UnknownExtensionException;
use Drupal\Core\File\FileSystemInterface;
use Drupal\Core\Form\FormState;
use Drupal\Core\Installer\Exception\AlreadyInstalledException;
use Drupal\Core\Installer\Exception\InstallerException;
use Drupal\Core\Installer\Exception\NoProfilesException;
use Drupal\Core\Installer\Form\SiteSettingsForm;
use Drupal\Core\Installer\InstallerKernel;
use Drupal\Core\Language\Language;
use Drupal\Core\Language\LanguageManager;
use Drupal\Core\Recipe\Recipe;
use Drupal\Core\Recipe\RecipeRunner;
use Drupal\Core\Site\Settings;
use Drupal\Core\StringTranslation\Translator\FileTranslation;
use Drupal\Core\StackMiddleware\ReverseProxyMiddleware;
use Drupal\Core\Extension\ExtensionDiscovery;
use Drupal\Core\DependencyInjection\ContainerBuilder;
use Drupal\Core\Url;
use Drupal\language\ConfigurableLanguageManagerInterface;
use Drupal\language\Entity\ConfigurableLanguage;
use Drupal\Core\Routing\RouteObjectInterface;
use Psr\Http\Client\ClientExceptionInterface;
use Symfony\Component\DependencyInjection\Reference;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Route;
use Drupal\user\Entity\User;

/**
 * Do not run the task during the current installation request.
 *
 * This can be used to skip running an installation task when certain
 * conditions are met, even though the task may still show on the list of
 * installation tasks presented to the user. For example, the Drupal installer
 * uses this flag to skip over the database configuration form when valid
 * database connection information is already available from settings.php. It
 * also uses this flag to skip language import tasks when the installation is
 * being performed in English.
 */
const INSTALL_TASK_SKIP = 1;

/**
 * Run the task on each installation request that reaches it.
 *
 * This is primarily used by the Drupal installer for bootstrap-related tasks.
 */
const INSTALL_TASK_RUN_IF_REACHED = 2;

/**
 * Run the task on each installation request until the database is set up.
 *
 * This is the default method for running tasks and should be used for most
 * tasks that occur after the database is set up; these tasks will then run
 * once and be marked complete once they are successfully finished. For
 * example, the Drupal installer uses this flag for the batch installation of
 * modules on the new site, and also for the configuration form that collects
 * basic site information and sets up the site maintenance account.
 */
const INSTALL_TASK_RUN_IF_NOT_COMPLETED = 3;

/**
 * Installs Drupal either interactively or via an array of passed-in settings.
 *
 * The Drupal installation happens in a series of steps, which may be spread
 * out over multiple page requests. Each request begins by trying to determine
 * the last completed installation step (also known as a "task"), if one is
 * available from a previous request. Control is then passed to the task
 * handler, which processes the remaining tasks that need to be run until (a)
 * an error is thrown, (b) a new page needs to be displayed, or (c) the
 * installation finishes (whichever happens first).
 *
 * @param $class_loader
 *   The class loader. Normally Composer's ClassLoader, as included by the
 *   front controller, but may also be decorated.
 * @param $settings
 *   An optional array of installation settings. Leave this empty for a normal,
 *   interactive, browser-based installation intended to occur over multiple
 *   page requests. Alternatively, if an array of settings is passed in, the
 *   installer will attempt to use it to perform the installation in a single
 *   page request (optimized for the command line) and not send any output
 *   intended for the web browser. See install_state_defaults() for a list of
 *   elements that are allowed to appear in this array.
 * @param callable|null $callback
 *   (optional) A callback to allow command line processes to update a progress
 *   bar. The callback is passed the $install_state variable.
 *
 * @see install_state_defaults()
 */
function install_drupal($class_loader, $settings = [], ?callable $callback = NULL) {
  global $install_state;
  // Initialize the installation state with the settings that were passed in,
  // as well as a boolean indicating whether or not this is an interactive
  // installation.
  $interactive = empty($settings);
  $install_state = $settings + ['interactive' => $interactive] + install_state_defaults();

  try {
    // Begin the page request. This adds information about the current state of
    // the Drupal installation to the passed-in array.
    install_begin_request($class_loader, $install_state);
    // Based on the installation state, run the remaining tasks for this page
    // request, and collect any output.
    $output = install_run_tasks($install_state, $callback);
  }
  catch (InstallerException $e) {
    // In the non-interactive installer, exceptions are always thrown directly.
    if (!$install_state['interactive']) {
      throw $e;
    }
    $output = [
      '#title' => $e->getTitle(),
      '#markup' => $e->getMessage(),
    ];
  }

  // After execution, all tasks might be complete, in which case
  // $install_state['installation_finished'] is TRUE. In case the last task
  // has been processed, remove the global $install_state, so other code can
  // reliably check whether it is running during the installer.
  // @see \Drupal\Core\Installer\InstallerKernel::installationAttempted()
  $state = $install_state;
  if (!empty($install_state['installation_finished'])) {
    unset($GLOBALS['install_state']);
    // If installation is finished ensure any further container rebuilds do not
    // use the installer's service provider.
    unset($GLOBALS['conf']['container_service_providers']['InstallerServiceProvider']);
  }

  // All available tasks for this page request are now complete. Interactive
  // installations can send output to the browser or redirect the user to the
  // next page.
  if ($state['interactive']) {
    \Drupal::request()->getSession()->save();
    if ($state['parameters_changed']) {
      // Redirect to the correct page if the URL parameters have changed.
      install_goto(install_redirect_url($state));
    }
    elseif (isset($output)) {
      // Display a page only if some output is available. Otherwise it is
      // possible that we are printing a JSON page and theme output should
      // not be shown.
      install_display_output($output, $state);
    }
    elseif ($state['installation_finished']) {
      // Redirect to the newly installed site.
      $finish_url = '';
      if (isset($install_state['profile_info']['distribution']['install']['finish_url'])) {
        $finish_url = $install_state['profile_info']['distribution']['install']['finish_url'];
      }
      install_goto($finish_url);
    }
  }
}

/**
 * Returns an array of default settings for the global installation state.
 *
 * The installation state is initialized with these settings at the beginning
 * of each page request. They may evolve during the page request, but they are
 * initialized again once the next request begins.
 *
 * Non-interactive Drupal installations can override some of these default
 * settings by passing in an array to the installation script, most notably
 * 'parameters' (which contains one-time parameters such as 'profile' and
 * 'langcode' that are normally passed in via the URL) and 'forms' (which can
 * be used to programmatically submit forms during the installation; the keys
 * of each element indicate the name of the installation task that the form
 * submission is for, and the values are used as the $form_state->getValues()
 * array that is passed on to the form submission via
 * \Drupal::formBuilder()->submitForm()).
 *
 * @see \Drupal\Core\Form\FormBuilderInterface::submitForm()
 */
function install_state_defaults() {
  $defaults = [
    // The current task being processed.
    'active_task' => NULL,
    // The last task that was completed during the previous installation
    // request.
    'completed_task' => NULL,
    // Partial configuration cached during an installation from existing config.
    'config' => NULL,
    // The path to the configuration to install when installing from config.
    'config_install_path' => NULL,
    // TRUE when there are valid config directories.
    'config_verified' => FALSE,
    // TRUE when there is a valid database connection.
    'database_verified' => FALSE,
    // TRUE when a valid settings.php exists (containing both database
    // connection information and config directory names).
    'settings_verified' => FALSE,
    // TRUE when the base system has been installed and is ready to operate.
    'base_system_verified' => FALSE,
    // Whether a translation file for the selected language will be downloaded
    // from the translation server.
    'download_translation' => FALSE,
    // An array of forms to be programmatically submitted during the
    // installation. The keys of each element indicate the name of the
    // installation task that the form submission is for, and the values are
    // used as the $form_state->getValues() array that is passed on to the form
    // submission via \Drupal::formBuilder()->submitForm().
    'forms' => [],
    // This becomes TRUE only at the end of the installation process, after
    // all available tasks have been completed and Drupal is fully installed.
    // It is used by the installer to store correct information in the database
    // about the completed installation, as well as to inform theme functions
    // that all tasks are finished (so that the task list can be displayed
    // correctly).
    'installation_finished' => FALSE,
    // Whether or not this installation is interactive. By default this will
    // be set to FALSE if settings are passed in to install_drupal().
    'interactive' => TRUE,
    // An array of parameters for the installation, pre-populated by the URL
    // or by the settings passed in to install_drupal(). This is primarily
    // used to store 'profile' (the name of the chosen installation profile)
    // and 'langcode' (the code of the chosen installation language), since
    // these settings need to persist from page request to page request before
    // the database is available for storage.
    'parameters' => [],
    // Whether or not the parameters have changed during the current page
    // request. For interactive installations, this will trigger a page
    // redirect.
    'parameters_changed' => FALSE,
    // An array of information about the chosen installation profile. This will
    // be filled in based on the profile's .info.yml file.
    'profile_info' => [],
    // An array of available installation profiles.
    'profiles' => [],
    // The name of the theme to use during installation.
    'theme' => 'claro',
    // The server URL where the interface translation files can be downloaded.
    // Tokens in the pattern will be replaced by appropriate values for the
    // required translation file.
    'server_pattern' => 'https://ftp.drupal.org/files/translations/%core/%project/%project-%version.%language.po',
    // Installation tasks can set this to TRUE to force the page request to
    // end (even if there is no themeable output), in the case of an interactive
    // installation. This is needed only rarely; for example, it would be used
    // by an installation task that prints JSON output rather than returning a
    // themed page. The most common example of this is during batch processing,
    // but the Drupal installer automatically takes care of setting this
    // parameter properly in that case, so that individual installation tasks
    // which implement the batch API do not need to set it themselves.
    'stop_page_request' => FALSE,
    // Installation tasks can set this to TRUE to indicate that the task should
    // be run again, even if it normally wouldn't be. This can be used, for
    // example, if a single task needs to be spread out over multiple page
    // requests, or if it needs to perform some validation before allowing
    // itself to be marked complete. The most common examples of this are batch
    // processing and form submissions, but the Drupal installer automatically
    // takes care of setting this parameter properly in those cases, so that
    // individual installation tasks which implement the batch API or form API
    // do not need to set it themselves.
    'task_not_complete' => FALSE,
    // A list of installation tasks which have already been performed during
    // the current page request.
    'tasks_performed' => [],
    // An array of translation files URIs available for the installation. Keyed
    // by the translation language code.
    'translations' => [],
  ];
  return $defaults;
}

/**
 * Begins an installation request, modifying the installation state as needed.
 *
 * This function performs commands that must run at the beginning of every page
 * request. It throws an exception if the installation should not proceed.
 *
 * @param $class_loader
 *   The class loader. Normally Composer's ClassLoader, as included by the
 *   front controller, but may also be decorated.
 * @param $install_state
 *   An array of information about the current installation state. This is
 *   modified with information gleaned from the beginning of the page request.
 *
 * @see install_drupal()
 */
function install_begin_request($class_loader, &$install_state) {
  $request = Request::createFromGlobals();

  // Add any installation parameters passed in via the URL.
  if ($install_state['interactive']) {
    $install_state['parameters'] += $request->query->all();
  }

  // Validate certain core settings that are used throughout the installation.
  if (array_key_exists('profile', $install_state['parameters'])) {
    $install_state['parameters']['profile'] = preg_replace('/[^a-zA-Z_0-9]/', '', $install_state['parameters']['profile']);
    // If the site is not using an install profile then profile will be set to
    // an empty string in the query parameters. Convert this to a FALSE value so
    // that \Drupal\Core\Installer\InstallerKernel::getInstallProfile() returns
    // the same value as \Drupal\Core\DrupalKernel::getInstallProfile() when the
    // site does not have an install profile.
    if ($install_state['parameters']['profile'] === '') {
      $install_state['parameters']['profile'] = FALSE;
    }
  }
  if (!empty($install_state['parameters']['langcode'])) {
    $install_state['parameters']['langcode'] = preg_replace('/[^a-zA-Z_0-9\-]/', '', $install_state['parameters']['langcode']);
  }

  // If the hash salt leaks, it becomes possible to forge a valid testing user
  // agent, install a new copy of Drupal, and take over the original site.
  // The user agent header is used to pass a database prefix in the request when
  // running tests. However, for security reasons, it is imperative that no
  // installation be permitted using such a prefix.
  $user_agent = $request->cookies->get('SIMPLETEST_USER_AGENT') ?: $request->server->get('HTTP_USER_AGENT');
  if ($install_state['interactive'] && str_contains($user_agent, 'simpletest') && !drupal_valid_test_ua()) {
    header($request->server->get('SERVER_PROTOCOL') . ' 403 Forbidden');
    exit;
  }
  if ($install_state['interactive'] && drupal_valid_test_ua()) {
    // Set the default timezone. While this doesn't cause any tests to fail, PHP
    // complains if 'date.timezone' is not set in php.ini. The Australia/Sydney
    // timezone is chosen so all tests are run using an edge case scenario
    // (UTC+10  and DST). This choice is made to prevent timezone related
    // regressions and reduce the fragility of the testing system in general.
    date_default_timezone_set('Australia/Sydney');
  }

  $site_path = empty($install_state['site_path']) ? DrupalKernel::findSitePath($request, FALSE) : $install_state['site_path'];
  Settings::initialize(dirname(__DIR__, 2), $site_path, $class_loader);

  // Ensure that procedural dependencies are loaded as early as possible,
  // since the error/exception handlers depend on them.
  require_once __DIR__ . '/../modules/system/system.install';
  require_once __DIR__ . '/common.inc';
  require_once __DIR__ . '/install.inc';
  require_once __DIR__ . '/form.inc';
  require_once __DIR__ . '/batch.inc';

  // Load module basics (needed for hook invokes).
  include_once __DIR__ . '/module.inc';

  // Create a minimal mocked container to support calls to t() in the pre-kernel
  // base system verification code paths below. The strings are not actually
  // used or output for these calls.
  // @todo Separate API level checks from UI-facing error messages.
  $container = new ContainerBuilder();
  $container->setParameter('language.default_values', Language::$defaultValues);
  $container
    ->register('language.default', 'Drupal\Core\Language\LanguageDefault')
    ->addArgument('%language.default_values%');
  $container
    ->register('string_translation', 'Drupal\Core\StringTranslation\TranslationManager')
    ->addArgument(new Reference('language.default'));

  // Register the database driver extension list provider.
  $container
    ->register('extension.list.database_driver', 'Drupal\Core\Extension\DatabaseDriverList')
    ->addArgument(dirname(__DIR__, 2))
    ->addArgument('database_driver')
    ->addArgument(new NullBackend('database_driver'));

  // Register the class loader so contrib and custom database drivers can be
  // autoloaded.
  // @see \Drupal\Core\Extension\DatabaseDriverList
  $container->set('class_loader', $class_loader);

  \Drupal::setContainer($container);

  // Determine whether base system services are ready to operate.
  try {
    $sync_directory = Settings::get('config_sync_directory', FALSE);
    $install_state['config_verified'] = file_exists($sync_directory);
  }
  catch (Exception $e) {
    $install_state['config_verified'] = FALSE;
  }
  $install_state['database_verified'] = install_verify_database_settings($site_path);
  // A valid settings.php has database settings and a hash_salt value. Other
  // settings will be checked by system_requirements().
  $install_state['settings_verified'] = $install_state['config_verified'] && $install_state['database_verified'] && (bool) Settings::get('hash_salt', FALSE);

  if ($install_state['settings_verified']) {
    try {
      $system_schema = system_schema();
      $table = array_key_last($system_schema);
      $install_state['base_system_verified'] = Database::getConnection()->schema()->tableExists($table);
    }
    catch (DatabaseExceptionWrapper $e) {
      // The last defined table of the base system_schema() does not exist yet.
      // $install_state['base_system_verified'] defaults to FALSE, so the code
      // following below will use the minimal installer service container.
      // As soon as the base system is verified here, the installer operates in
      // a full and regular Drupal environment, without any kind of exceptions.
    }
  }

  // Replace services with in-memory and null implementations. This kernel is
  // replaced with a regular one in drupal_install_system().
  if (!$install_state['base_system_verified']) {
    $environment = 'install';
    $GLOBALS['conf']['container_service_providers']['InstallerServiceProvider'] = 'Drupal\Core\Installer\InstallerServiceProvider';
  }
  else {
    $environment = 'prod';
    $GLOBALS['conf']['container_service_providers']['InstallerServiceProvider'] = 'Drupal\Core\Installer\NormalInstallerServiceProvider';
  }
  $GLOBALS['conf']['container_service_providers']['InstallerConfigOverride'] = 'Drupal\Core\Installer\ConfigOverride';

  // Note, InstallerKernel::createFromRequest() is not used because Settings is
  // already initialized.
  $kernel = new InstallerKernel($environment, $class_loader, FALSE);
  $kernel::bootEnvironment();
  $kernel->setSitePath($site_path);
  $kernel->boot();
  // Get the new version of the container from the kernel. This is now a
  // complete container with all services.
  $container = $kernel->getContainer();
  // If Drupal is being installed behind a proxy, configure the request.
  ReverseProxyMiddleware::setSettingsOnRequest($request, Settings::getInstance());

  // Register the file translation service.
  if (isset($GLOBALS['config']['locale.settings']['translation']['path'])) {
    $directory = $GLOBALS['config']['locale.settings']['translation']['path'];
  }
  else {
    $directory = $site_path . '/files/translations';
  }
  /** @var \Drupal\Core\File\FileSystemInterface $file_system */
  $file_system = $container->get('file_system');
  $container->set('string_translator.file_translation', new FileTranslation($directory, $file_system));
  $container->get('string_translation')
    ->addTranslator($container->get('string_translator.file_translation'));

  // Add list of all available profiles to the installation state.
  $listing = new ExtensionDiscovery($container->getParameter('app.root'));
  $listing->setProfileDirectories([]);
  $install_state['profiles'] += $listing->scan('profile');

  /** @var \Drupal\Core\Extension\ModuleExtensionList $module_list */
  $module_list = \Drupal::service('extension.list.module');
  /** @var \Drupal\Core\Extension\ProfileExtensionList $profile_list */
  $profile_list = \Drupal::service('extension.list.profile');

  // Prime \Drupal\Core\Extension\ExtensionList::getPathname()'s static cache.
  foreach ($install_state['profiles'] as $name => $profile) {
    // Profile path is set in both module and profile lists. See
    // \Drupal\Core\Config\ConfigInstaller::getProfileStorages for example.
    $profile_list->setPathname($name, $profile->getPathname());
    $module_list->setPathname($name, $profile->getPathname());
  }

  $profile = _install_select_profile($install_state);
  if ($profile !== NULL) {
    $install_state['parameters']['profile'] = $profile;
    install_load_profile($install_state);
    if (isset($install_state['profile_info']['distribution']['install']['theme'])) {
      $install_state['theme'] = $install_state['profile_info']['distribution']['install']['theme'];
    }
  }

  // Before having installed the system module and being able to do a module
  // rebuild, prime the module list static cache with the system module's
  // location.
  // @todo Remove as part of https://www.drupal.org/node/2186491
  $module_list->setPathname('system', 'core/modules/system/system.info.yml');

  // Use the language from profile configuration if available.
  if (!empty($install_state['config_install_path']) && $install_state['config']['system.site']) {
    $install_state['parameters']['langcode'] = $install_state['config']['system.site']['default_langcode'];
  }
  elseif (isset($install_state['profile_info']['distribution']['langcode'])) {
    // Otherwise, Use the language from the profile configuration, if available,
    // to override the language previously set in the parameters.
    $install_state['parameters']['langcode'] = $install_state['profile_info']['distribution']['langcode'];
  }

  // Set the default language to the selected language, if any.
  if (isset($install_state['parameters']['langcode'])) {
    $default_language = new Language(['id' => $install_state['parameters']['langcode']]);
    $container->get('language.default')->set($default_language);
    \Drupal::translation()->setDefaultLangcode($install_state['parameters']['langcode']);
  }

  // Override the module list with a minimal set of modules.
  $module_handler = \Drupal::moduleHandler();
  if (!$module_handler->moduleExists('system')) {
    $module_handler->addModule('system', 'core/modules/system');
  }
  if ($profile && !$module_handler->moduleExists($profile)) {
    $module_handler->addProfile($profile, $install_state['profiles'][$profile]->getPath());
  }

  // Load all modules and perform request related initialization.
  $kernel->preHandle($request);

  // Initialize a route on this legacy request similar to
  // \Drupal\Core\DrupalKernel::prepareLegacyRequest() since normal routing
  // will not happen.
  $request->attributes->set(RouteObjectInterface::ROUTE_OBJECT, new Route('<none>'));
  $request->attributes->set(RouteObjectInterface::ROUTE_NAME, '<none>');

  // Prepare for themed output. We need to run this at the beginning of the
  // page request to avoid a different theme accidentally getting set. (We also
  // need to run it even in the case of command-line installations, to prevent
  // any code in the installer that happens to initialize the theme system from
  // accessing the database before it is set up yet.)
  drupal_maintenance_theme();

  if (!$install_state['database_verified']) {
    // Do not install over an existing installation. The call to
    // install_verify_database_ready() will throw an AlreadyInstalledException
    // if this is the case.
    install_verify_database_ready();
  }
  // Verify the last completed task in the database, if there is one.
  $task = install_verify_completed_task();

  // Ensure that the active configuration is empty before installation starts.
  if ($install_state['config_verified'] && empty($task)) {
    if (count($kernel->getConfigStorage()->listAll())) {
      $task = NULL;
      throw new AlreadyInstalledException($container->get('string_translation'));
    }
  }

  // Modify the installation state as appropriate.
  $install_state['completed_task'] = $task;
}

/**
 * Runs all tasks for the current installation request.
 *
 * In the case of an interactive installation, all tasks will be attempted
 * until one is reached that has output which needs to be displayed to the
 * user, or until a page redirect is required. Otherwise, tasks will be
 * attempted until the installation is finished.
 *
 * @param $install_state
 *   An array of information about the current installation state. This is
 *   passed along to each task, so it can be modified if necessary.
 * @param callable|null $callback
 *   (optional) A callback to allow command line processes to update a progress
 *   bar. The callback is passed the $install_state variable.
 *
 * @return array|null
 *   HTML output from the last completed task.
 */
function install_run_tasks(&$install_state, ?callable $callback = NULL) {
  do {
    // Obtain a list of tasks to perform. The list of tasks itself can be
    // dynamic (e.g., some might be defined by the installation profile,
    // which is not necessarily known until the earlier tasks have run),
    // so we regenerate the remaining tasks based on the installation state,
    // each time through the loop.
    $tasks_to_perform = install_tasks_to_perform($install_state);
    $task_name = array_key_first($tasks_to_perform);
    $task = array_shift($tasks_to_perform);
    $install_state['active_task'] = $task_name;
    $original_parameters = $install_state['parameters'];
    $output = install_run_task($task, $install_state);
    // Ensure the maintenance theme is initialized. If the install task has
    // rebuilt the container the active theme will not be set. This can occur if
    // the task has installed a module.
    drupal_maintenance_theme();

    $install_state['parameters_changed'] = ($install_state['parameters'] != $original_parameters);
    // Store this task as having been performed during the current request,
    // and save it to the database as completed, if we need to and if the
    // database is in a state that allows us to do so. Also mark the
    // installation as 'done' when we have run out of tasks.
    if (!$install_state['task_not_complete']) {
      $install_state['tasks_performed'][] = $task_name;
      $install_state['installation_finished'] = empty($tasks_to_perform);
      if ($task['run'] == INSTALL_TASK_RUN_IF_NOT_COMPLETED || $install_state['installation_finished']) {
        \Drupal::state()->set('install_task', $install_state['installation_finished'] ? 'done' : $task_name);
      }
    }
    if ($callback) {
      $callback($install_state);
    }
    // Stop when there are no tasks left. In the case of an interactive
    // installation, also stop if we have some output to send to the browser,
    // the URL parameters have changed, or an end to the page request was
    // specifically called for.
    $finished = empty($tasks_to_perform) || ($install_state['interactive'] && (isset($output) || $install_state['parameters_changed'] || $install_state['stop_page_request']));
  } while (!$finished);
  return $output;
}

/**
 * Runs an individual installation task.
 *
 * @param $task
 *   An array of information about the task to be run as returned by
 *   hook_install_tasks().
 * @param $install_state
 *   An array of information about the current installation state. This is
 *   passed in by reference so that it can be modified by the task.
 *
 * @return array|null
 *   The output of the task function, if there is any.
 */
function install_run_task($task, &$install_state) {
  $function = $task['function'];

  if ($task['type'] == 'form') {
    return install_get_form($function, $install_state);
  }
  elseif ($task['type'] == 'batch') {
    // Start a new batch based on the task function, if one is not running
    // already.
    $current_batch = \Drupal::state()->get('install_current_batch');
    if (!$install_state['interactive'] || !$current_batch) {
      $batches = $function($install_state);
      if (empty($batches)) {
        // If the task did some processing and decided no batch was necessary,
        // there is nothing more to do here.
        return;
      }
      // Create a one item list of batches if only one batch was provided.
      if (isset($batches['operations'])) {
        $batches = [$batches];
      }
      foreach ($batches as $batch_definition) {
        batch_set($batch_definition);
        // For interactive batches, we need to store the fact that this batch
        // task is currently running. Otherwise, we need to make sure the batch
        // will complete in one page request.
        if ($install_state['interactive']) {
          \Drupal::state()->set('install_current_batch', $function);
        }
        else {
          $batch =& batch_get();
          $batch['progressive'] = FALSE;
        }
      }
      // Process the batch. For progressive batches, this will redirect.
      // Otherwise, the batch will complete.
      // Disable the default script for the URL and clone the object, as
      // batch_process() will add additional options to the batch URL.
      $url = Url::fromUri('base:install.php', ['query' => $install_state['parameters'], 'script' => '']);
      $response = batch_process($url, clone $url);
      if ($response instanceof Response) {
        \Drupal::request()->getSession()->save();
        $response->send();
        exit;
      }
    }
    // If we are in the middle of processing this batch, keep sending back
    // any output from the batch process, until the task is complete.
    elseif ($current_batch == $function) {
      $output = _batch_page(\Drupal::request());
      // Because Batch API now returns a JSON response for intermediary steps,
      // but the installer doesn't handle Response objects yet, just send the
      // output here and emulate the old model.
      // @todo Replace this when we refactor the installer to use a request-
      //   response workflow.
      if ($output instanceof Response) {
        \Drupal::request()->getSession()->save();
        $output->send();
        exit;
      }
      // The task is complete when we try to access the batch page and receive
      // FALSE in return, since this means we are at a URL where we are no
      // longer requesting a batch ID.
      if ($output === FALSE) {
        // Return nothing so the next task will run in the same request.
        \Drupal::state()->delete('install_current_batch');
        return;
      }
      else {
        // We need to force the page request to end if the task is not
        // complete, since the batch API sometimes prints JSON output
        // rather than returning a themed page.
        $install_state['task_not_complete'] = $install_state['stop_page_request'] = TRUE;
        return $output;
      }
    }
  }

  else {
    // For normal tasks, just return the function result, whatever it is.
    return $function($install_state);
  }
}

/**
 * Returns a list of tasks to perform during the current installation request.
 *
 * Note that the list of tasks can change based on the installation state as
 * the page request evolves (for example, if an installation profile hasn't
 * been selected yet, we don't yet know which profile tasks need to be run).
 *
 * @param $install_state
 *   An array of information about the current installation state.
 *
 * @return array
 *   A list of tasks to be performed, with associated metadata.
 */
function install_tasks_to_perform($install_state) {
  // Start with a list of all currently available tasks.
  $tasks = install_tasks($install_state);
  foreach ($tasks as $name => $task) {
    // Remove any tasks that were already performed or that never should run.
    // Also, if we started this page request with an indication of the last
    // task that was completed, skip that task and all those that come before
    // it, unless they are marked as always needing to run.
    if ($task['run'] == INSTALL_TASK_SKIP || in_array($name, $install_state['tasks_performed']) || (!empty($install_state['completed_task']) && empty($completed_task_found) && $task['run'] != INSTALL_TASK_RUN_IF_REACHED)) {
      unset($tasks[$name]);
    }
    if (!empty($install_state['completed_task']) && $name == $install_state['completed_task']) {
      $completed_task_found = TRUE;
    }
  }
  return $tasks;
}

/**
 * Returns a list of all tasks the installer currently knows about.
 *
 * This function will return tasks regardless of whether or not they are
 * intended to run on the current page request. However, the list can change
 * based on the installation state (for example, if an installation profile
 * hasn't been selected yet, we don't yet know which profile tasks will be
 * available).
 *
 * You can override this using hook_install_tasks() or
 * hook_install_tasks_alter().
 *
 * @param $install_state
 *   An array of information about the current installation state.
 *
 * @return array
 *   A list of tasks, with associated metadata as returned by
 *   hook_install_tasks().
 */
function install_tasks($install_state) {
  // Determine whether a translation file must be imported during the
  // 'install_import_translations' task. Import when a non-English language is
  // available and selected. Also we will need translations even if the
  // installer language is English but there are other languages on the system.
  $locale_module_installed = \Drupal::moduleHandler()->moduleExists('locale');
  $needs_translations = $locale_module_installed && ((count($install_state['translations']) > 1 && !empty($install_state['parameters']['langcode']) && $install_state['parameters']['langcode'] != 'en') || \Drupal::languageManager()->isMultilingual());
  // Determine whether a translation file must be downloaded during the
  // 'install_download_translation' task. Download when a non-English language
  // is selected, but no translation is yet in the translations directory.
  $needs_download = isset($install_state['parameters']['langcode']) && !isset($install_state['translations'][$install_state['parameters']['langcode']]) && $install_state['parameters']['langcode'] != 'en';

  // Start with the core installation tasks that run before handing control
  // to the installation profile.
  $tasks = [
    'install_select_language' => [
      'display_name' => t('Choose language'),
      'run' => INSTALL_TASK_RUN_IF_REACHED,
    ],
    'install_download_translation' => [
      'run' => $needs_download ? INSTALL_TASK_RUN_IF_REACHED : INSTALL_TASK_SKIP,
    ],
    'install_select_profile' => [
      'display_name' => t('Choose profile'),
      'display' => empty($install_state['profile_info']['distribution']['name']) && count($install_state['profiles']) != 1,
      'run' => INSTALL_TASK_RUN_IF_REACHED,
    ],
    'install_load_profile' => [
      'run' => INSTALL_TASK_RUN_IF_REACHED,
    ],
    'install_verify_requirements' => [
      'display_name' => t('Verify requirements'),
    ],
    'install_settings_form' => [
      'display_name' => t('Set up database'),
      'type' => 'form',
      // Even though the form only allows the user to enter database settings,
      // we still need to display it if settings.php is invalid in any way,
      // since the form submit handler is where settings.php is rewritten.
      'run' => $install_state['settings_verified'] ? INSTALL_TASK_SKIP : INSTALL_TASK_RUN_IF_NOT_COMPLETED,
      'function' => 'Drupal\Core\Installer\Form\SiteSettingsForm',
    ],
    'install_verify_database_ready' => [
      'run' => INSTALL_TASK_RUN_IF_NOT_COMPLETED,
    ],
    'install_base_system' => [
      'run' => $install_state['base_system_verified'] ? INSTALL_TASK_SKIP : INSTALL_TASK_RUN_IF_NOT_COMPLETED,
    ],
    // All tasks below are executed in a regular, full Drupal environment.
    'install_bootstrap_full' => [
      'run' => INSTALL_TASK_RUN_IF_REACHED,
    ],
    'install_profile_modules' => [
      'display_name' => t('Install site'),
      'type' => 'batch',
    ],
    'install_profile_themes' => [],
    'install_install_profile' => [],
    'install_import_translations' => [
      'display_name' => t('Set up translations'),
      'display' => $needs_translations,
      'type' => 'batch',
      'run' => $needs_translations ? INSTALL_TASK_RUN_IF_NOT_COMPLETED : INSTALL_TASK_SKIP,
    ],
    'install_configure_form' => [
      'display_name' => t('Configure site'),
      'type' => 'form',
      'function' => 'Drupal\Core\Installer\Form\SiteConfigureForm',
    ],
  ];

  if (!empty($install_state['config_install_path'])) {
    // The chosen profile indicates that rather than installing a new site, an
    // instance of the same site should be installed from the given
    // configuration.
    // That means we need to remove the steps installing the extensions and
    // replace them with a configuration synchronization step.
    unset($tasks['install_download_translation']);
    $key = array_search('install_profile_modules', array_keys($tasks), TRUE);
    unset($tasks['install_profile_modules']);
    unset($tasks['install_profile_themes']);
    unset($tasks['install_install_profile']);
    $config_tasks = [
      'install_config_import_batch' => [
        'display_name' => t('Install configuration'),
        'type' => 'batch',
      ],
      'install_config_download_translations' => [],
      'install_config_revert_install_changes' => [],
    ];
    $tasks = array_slice($tasks, 0, $key, TRUE) +
      $config_tasks +
      array_slice($tasks, $key, NULL, TRUE);
  }

  if (!empty($install_state['parameters']['recipe'])) {
    // The install state indicates that we are installing from a recipe.
    $key = array_search('install_profile_modules', array_keys($tasks), TRUE);
    unset($tasks['install_profile_modules']);
    unset($tasks['install_profile_themes']);
    unset($tasks['install_install_profile']);
    $recipe_tasks = [
      'install_recipe_required_modules' => [
        'display_name' => t('Install required modules'),
        'type' => 'batch',
      ],
      'install_recipe_batch' => [
        'display_name' => t('Install recipe'),
        'type' => 'batch',
      ],
    ];
    $tasks = array_slice($tasks, 0, $key, TRUE) +
      $recipe_tasks +
      array_slice($tasks, $key, NULL, TRUE);
  }

  // Now add any tasks defined by the installation profile.
  if (!empty($install_state['parameters']['profile'])) {
    // Load the profile install file, because it is not always loaded when
    // hook_install_tasks() is invoked (e.g. batch processing).
    $profile = $install_state['parameters']['profile'];
    if ($profile !== FALSE) {
      $profile_install_file = $install_state['profiles'][$profile]->getPath() . '/' . $profile . '.install';
      if (file_exists($profile_install_file)) {
        include_once \Drupal::root() . '/' . $profile_install_file;
      }
      $function = $install_state['parameters']['profile'] . '_install_tasks';
      if (function_exists($function)) {
        $result = $function($install_state);
        if (is_array($result)) {
          $tasks += $result;
        }
      }
    }
  }

  // Finish by adding the remaining core tasks.
  $tasks += [
    'install_finish_translations' => [
      'display_name' => t('Finish translations'),
      'display' => $needs_translations,
      'type' => 'batch',
      'run' => $needs_translations ? INSTALL_TASK_RUN_IF_NOT_COMPLETED : INSTALL_TASK_SKIP,
    ],
    'install_finished' => [],
  ];

  // Allow the installation profile to modify the full list of tasks.
  if (!empty($install_state['parameters']['profile'])) {
    $profile = $install_state['parameters']['profile'];
    if ($install_state['profiles'][$profile]->load()) {
      $function = $install_state['parameters']['profile'] . '_install_tasks_alter';
      if (function_exists($function)) {
        $function($tasks, $install_state);
      }
    }
  }

  // Fill in default parameters for each task before returning the list.
  foreach ($tasks as $task_name => &$task) {
    $task += [
      'display_name' => NULL,
      'display' => !empty($task['display_name']),
      'type' => 'normal',
      'run' => INSTALL_TASK_RUN_IF_NOT_COMPLETED,
      'function' => $task_name,
    ];
  }
  return $tasks;
}

/**
 * Returns a list of tasks that should be displayed to the end user.
 *
 * The output of this function is a list suitable for sending to
 * maintenance-task-list.html.twig.
 *
 * @param $install_state
 *   An array of information about the current installation state.
 *
 * @return array
 *   A list of tasks, with keys equal to the machine-readable task name and
 *   values equal to the name that should be displayed.
 *
 * @see maintenance-task-list.html.twig
 */
function install_tasks_to_display($install_state) {
  $displayed_tasks = [];
  foreach (install_tasks($install_state) as $name => $task) {
    if ($task['display']) {
      $displayed_tasks[$name] = $task['display_name'];
    }
  }
  return $displayed_tasks;
}

/**
 * Builds and processes a form for the installer environment.
 *
 * Ensures that FormBuilder does not redirect after submitting a form, since the
 * installer uses a custom step/flow logic via install_run_tasks().
 *
 * @param string|array $form_id
 *   The form ID to build and process.
 * @param array $install_state
 *   The current state of the installation.
 *
 * @return array|null
 *   A render array containing the form to render, or NULL in case the form was
 *   successfully submitted.
 *
 * @throws \Drupal\Core\Installer\Exception\InstallerException
 */
function install_get_form($form_id, array &$install_state) {
  // Ensure the form will not redirect, since install_run_tasks() uses a custom
  // redirection logic.
  $form_state = (new FormState())
    ->addBuildInfo('args', [&$install_state])
    ->disableRedirect();
  $form_builder = \Drupal::formBuilder();
  if ($install_state['interactive']) {
    $form = $form_builder->buildForm($form_id, $form_state);
    // If the form submission was not successful, the form needs to be rendered,
    // which means the task is not complete yet.
    if (!$form_state->isExecuted()) {
      $install_state['task_not_complete'] = TRUE;
      return $form;
    }
  }
  else {
    // For non-interactive installs, submit the form programmatically with the
    // values taken from the installation state.
    $install_form_id = $form_builder->getFormId($form_id, $form_state);
    if (!empty($install_state['forms'][$install_form_id])) {
      $values = $install_state['forms'][$install_form_id];
      if ($install_form_id === 'install_settings_form' && isset($values['driver']) && !str_contains($values['driver'], "\\")) {
        @trigger_error("Passing a database driver name '{$values['driver']}' to " . __FUNCTION__ . '() is deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Pass a database driver namespace instead. See https://www.drupal.org/node/3258175', E_USER_DEPRECATED);
        $driverExtension = Database::getDriverList()->getFromDriverName($values['driver']);
        $tmp = [];
        $tmp['driver'] = $driverExtension->getName();
        $tmp[$driverExtension->getName()] = $values[$values['driver']];
        $values = $tmp;
      }
      $form_state->setValues($values);
    }
    $form_builder->submitForm($form_id, $form_state);

    // Throw an exception in case of any form validation error.
    if ($errors = $form_state->getErrors()) {
      throw new InstallerException(implode("\n", $errors));
    }
  }
}

/**
 * Returns the URL that should be redirected to during an installation request.
 *
 * The output of this function is suitable for sending to install_goto().
 *
 * @param $install_state
 *   An array of information about the current installation state.
 *
 * @return string
 *   The URL to redirect to.
 *
 * @see install_full_redirect_url()
 */
function install_redirect_url($install_state) {
  return 'core/install.php?' . UrlHelper::buildQuery($install_state['parameters']);
}

/**
 * Returns the complete URL redirected to during an installation request.
 *
 * @param $install_state
 *   An array of information about the current installation state.
 *
 * @return string
 *   The complete URL to redirect to.
 *
 * @see install_redirect_url()
 */
function install_full_redirect_url($install_state) {
  global $base_url;
  return $base_url . '/' . install_redirect_url($install_state);
}

/**
 * Displays themed installer output and ends the page request.
 *
 * Installation tasks should use #title to set the desired page
 * title, but otherwise this function takes care of theming the overall page
 * output during every step of the installation.
 *
 * @param $output
 *   The content to display on the main part of the page.
 * @param $install_state
 *   An array of information about the current installation state.
 */
function install_display_output($output, $install_state) {
  // Ensure the maintenance theme is initialized.
  // The regular initialization call in install_begin_request() may not be
  // reached in case of an early installer error.
  drupal_maintenance_theme();

  // Prevent install.php from being indexed when installed in a sub folder.
  // robots.txt rules are not read if the site is within domain.com/subfolder
  // resulting in /subfolder/install.php being found through search engines.
  // When settings.php is writable this can be used via an external database
  // leading a malicious user to gain php access to the server.
  $noindex_meta_tag = [
    '#tag' => 'meta',
    '#attributes' => [
      'name' => 'robots',
      'content' => 'noindex, nofollow',
    ],
  ];
  $output['#attached']['html_head'][] = [$noindex_meta_tag, 'install_meta_robots'];

  // Only show the task list if there is an active task; otherwise, the page
  // request has ended before tasks have even been started, so there is nothing
  // meaningful to show.
  $regions = [];
  if (isset($install_state['active_task'])) {
    // Let the theming function know when every step of the installation has
    // been completed.
    $active_task = $install_state['installation_finished'] ? NULL : $install_state['active_task'];
    $task_list = [
      '#theme' => 'maintenance_task_list',
      '#items' => install_tasks_to_display($install_state),
      '#active' => $active_task,
    ];
    $regions['sidebar_first'] = $task_list;
  }

  $bare_html_page_renderer = \Drupal::service('bare_html_page_renderer');
  $response = $bare_html_page_renderer->renderBarePage($output, $output['#title'], 'install_page', $regions);
  $request_time = \Drupal::time()->getRequestTime();
  $default_headers = [
    'Expires' => 'Sun, 19 Nov 1978 05:00:00 GMT',
    'Last-Modified' => gmdate(DATE_RFC1123, $request_time),
    'Cache-Control' => 'no-cache, must-revalidate',
    'ETag' => '"' . $request_time . '"',
  ];
  $response->headers->add($default_headers);
  $response->send();
  exit;
}

/**
 * Verifies the requirements for installing Drupal.
 *
 * @param $install_state
 *   An array of information about the current installation state.
 *
 * @return array
 *   A themed status report, or an exception if there are requirement errors.
 */
function install_verify_requirements(&$install_state) {
  // Check the installation requirements for Drupal and this profile.
  $requirements = install_check_requirements($install_state);

  // Verify existence of all required modules.
  $requirements += drupal_verify_profile($install_state);

  return install_display_requirements($install_state, $requirements);
}

/**
 * Installation task; install the base functionality Drupal needs to bootstrap.
 *
 * @param $install_state
 *   An array of information about the current installation state.
 */
function install_base_system(&$install_state) {
  // Install system.module.
  drupal_install_system($install_state);

  // Call HtaccessWriter::ensure() to ensure that all of Drupal's standard
  // directories (e.g., the public files directory and config directory) have
  // appropriate .htaccess files. These directories will have already been
  // created by this point in the installer, since Drupal creates them during
  // the install_verify_requirements() task. Note that we cannot call
  // file_ensure_access() any earlier than this, since it relies on
  // system.module in order to work.
  \Drupal::service('file.htaccess_writer')->ensure();

  $install_state['base_system_verified'] = TRUE;
}

/**
 * Verifies and returns the last installation task that was completed.
 *
 * @return null|string
 *   The last completed task, if there is one. An exception is thrown if Drupal
 *   is already installed.
 */
function install_verify_completed_task() {
  try {
    $task = \Drupal::state()->get('install_task');
  }
  // Do not trigger an error if the database query fails, since the database
  // might not be set up yet.
  catch (\Exception $e) {
  }
  if (isset($task)) {
    if ($task == 'done') {
      throw new AlreadyInstalledException(\Drupal::service('string_translation'));
    }
    return $task;
  }
}

/**
 * Verifies that settings.php specifies a valid database connection.
 *
 * @param string $site_path
 *   The site path.
 *
 * @return bool
 *   TRUE if there are no database errors.
 */
function install_verify_database_settings($site_path) {
  if ($database = Database::getConnectionInfo()) {
    $database = $database['default'];
    $settings_file = './' . $site_path . '/settings.php';
    $errors = install_database_errors($database, $settings_file);
    if (empty($errors)) {
      return TRUE;
    }
  }
  return FALSE;
}

/**
 * Verify that the database is ready (no existing Drupal installation).
 *
 * @throws \Drupal\Core\Installer\Exception\AlreadyInstalledException
 *   Thrown when the database already has a table that would be created by
 *   installing the System module.
 */
function install_verify_database_ready() {
  $system_schema = system_schema();
  $table = array_key_last($system_schema);

  $existing_install = FALSE;
  if (Database::getConnectionInfo()) {
    try {
      $existing_install = Database::getConnection()->schema()->tableExists($table);
    }
    // Do not trigger an error if the database query fails, since the database
    // might not be set up yet.
    catch (\Exception $e) {
    }
  }
  if ($existing_install) {
    throw new AlreadyInstalledException(\Drupal::service('string_translation'));
  }
}

/**
 * Checks a database connection and returns any errors.
 */
function install_database_errors($database, $settings_file) {
  $errors = [];

  try {
    $driverExtension = Database::getDriverList()->get($database['namespace']);
    // Run driver specific validation
    $errors = $driverExtension->getInstallTasks()->validateDatabaseSettings($database);
    if (!empty($errors)) {
      // No point to try further.
      return $errors;
    }
    // Run tasks associated with the database type. Any errors are caught in the
    // calling function.
    Database::addConnectionInfo('default', 'default', $database);
    $errors = $driverExtension->getInstallTasks()->runTasks();
  }
  catch (UnknownExtensionException $e) {
    $errors['driver'] = t("In your %settings_file file you have configured @drupal to use a %driver server, however your PHP installation currently does not support this database type.", ['%settings_file' => $settings_file, '@drupal' => drupal_install_profile_distribution_name(), '%driver' => $database['driver']]);
  }
  return $errors;
}

/**
 * Selects which profile to install.
 *
 * @param $install_state
 *   An array of information about the current installation state. The chosen
 *   profile will be added here, if it was not already selected previously, as
 *   will a list of all available profiles.
 *
 * @return array|null
 *   For interactive installations, a form allowing the profile to be selected,
 *   if the user has a choice that needs to be made. Otherwise, an exception is
 *   thrown if a profile cannot be chosen automatically.
 */
function install_select_profile(&$install_state) {
  if (!array_key_exists('profile', $install_state['parameters'])) {
    // If there are no profiles at all, installation cannot proceed.
    if (empty($install_state['profiles'])) {
      throw new NoProfilesException(\Drupal::service('string_translation'));
    }
    // Try to automatically select a profile.
    if ($profile = _install_select_profile($install_state)) {
      $install_state['parameters']['profile'] = $profile;
    }
    else {
      // The non-interactive installer requires a profile parameter.
      if (!$install_state['interactive']) {
        throw new InstallerException('Missing profile parameter.');
      }
      // Otherwise, display a form to select a profile.
      return install_get_form('Drupal\Core\Installer\Form\SelectProfileForm', $install_state);
    }
  }
}

/**
 * Determines the installation profile to use in the installer.
 *
 * Depending on the context from which it's being called, this method
 * may be used to:
 * - Automatically select a profile under certain conditions.
 * - Indicate which profile has already been selected.
 * - Indicate that a profile still needs to be selected.
 *
 * A profile will be selected automatically if one of the following conditions
 * is met. They are checked in the given order:
 * - Only one profile is available.
 * - A specific profile name is requested in installation parameters:
 *   - For interactive installations via request query parameters.
 *   - For non-interactive installations via install_drupal() settings.
 * - One of the available profiles is a distribution. If multiple profiles are
 *   distributions, then the first discovered profile will be selected.
 * - Only one visible profile is available.
 *
 * @param array $install_state
 *   The current installer state, containing a 'profiles' key, which is an
 *   associative array of profiles with the machine-readable names as keys.
 *
 * @return string|null|false
 *   The machine-readable name of the selected profile or NULL if no profile was
 *   selected or FALSE if the site has no profile.
 *
 *  @see install_select_profile()
 */
function _install_select_profile(&$install_state) {
  // If there is only one profile available it will always be the one selected.
  if (count($install_state['profiles']) == 1) {
    return key($install_state['profiles']);
  }
  // If a valid profile has already been selected, return the selection.
  if (array_key_exists('profile', $install_state['parameters'])) {
    $profile = $install_state['parameters']['profile'];
    if ($profile === FALSE || isset($install_state['profiles'][$profile])) {
      return $profile;
    }
  }
  // If any of the profiles are distribution profiles, return the first one.
  foreach ($install_state['profiles'] as $profile) {
    $profile_info = install_profile_info($profile->getName());
    if (!empty($profile_info['distribution'])) {
      return $profile->getName();
    }
  }
  // Get all visible (not hidden) profiles.
  $visible_profiles = array_filter($install_state['profiles'], function ($profile) {
    $profile_info = install_profile_info($profile->getName());
    return !isset($profile_info['hidden']) || !$profile_info['hidden'];
  });
  // If there is only one visible profile, return it.
  if (count($visible_profiles) == 1) {
    return (key($visible_profiles));
  }
}

/**
 * Finds all .po files that are useful to the installer.
 *
 * @return array
 *   An associative array of file URIs keyed by language code. URIs as
 *   returned by FileSystemInterface::scanDirectory().
 *
 * @see \Drupal\Core\File\FileSystemInterface::scanDirectory()
 */
function install_find_translations() {
  $translations = [];
  $files = \Drupal::service('string_translator.file_translation')->findTranslationFiles();
  // English does not need a translation file.
  array_unshift($files, (object) ['name' => 'en']);
  foreach ($files as $uri => $file) {
    // Strip off the file name component before the language code.
    $langcode = preg_replace('!^(.+\.)?([^\.]+)$!', '\2', $file->name);
    // Language codes cannot exceed 12 characters to fit into the {language}
    // table.
    if (strlen($langcode) <= 12) {
      $translations[$langcode] = $uri;
    }
  }
  return $translations;
}

/**
 * Selects which language to use during installation.
 *
 * @param $install_state
 *   An array of information about the current installation state. The chosen
 *   langcode will be added here, if it was not already selected previously, as
 *   will a list of all available languages.
 *
 * @return array|null
 *   For interactive installations, a form or other page output allowing the
 *   language to be selected or providing information about language selection,
 *   if a language has not been chosen. Otherwise, an exception is thrown if a
 *   language cannot be chosen automatically.
 */
function install_select_language(&$install_state) {
  // Find all available translation files.
  $files = install_find_translations();
  $install_state['translations'] += $files;

  // If a valid language code is set, continue with the next installation step.
  // When translations from the localization server are used, any language code
  // is accepted because the standard language list is kept in sync with the
  // languages available at http://localize.drupal.org.
  // When files from the translation directory are used, we only accept
  // languages for which a file is available.
  if (!empty($install_state['parameters']['langcode'])) {
    $standard_languages = LanguageManager::getStandardLanguageList();
    $langcode = $install_state['parameters']['langcode'];
    if ($langcode == 'en' || isset($files[$langcode]) || isset($standard_languages[$langcode])) {
      $install_state['parameters']['langcode'] = $langcode;
      return;
    }
  }

  if (empty($install_state['parameters']['langcode'])) {
    // If we are performing an interactive installation, we display a form to
    // select a right language. If no translation files were found in the
    // translations directory, the form shows a list of standard languages. If
    // translation files were found the form shows a select list of the
    // corresponding languages to choose from.
    if ($install_state['interactive']) {
      return install_get_form('Drupal\Core\Installer\Form\SelectLanguageForm', $install_state);
    }
    // If we are performing a non-interactive installation. If only one language
    // (English) is available, assume the user is correct. Otherwise throw an
    // error.
    else {
      if (count($files) == 1) {
        $install_state['parameters']['langcode'] = current(array_keys($files));
        return;
      }
      else {
        throw new InstallerException('You must select a language to continue the installation.');
      }
    }
  }
}

/**
 * Download a translation file for the selected language.
 *
 * @param array $install_state
 *   An array of information about the current installation state.
 *
 * @return string
 *   A themed status report, or an exception if there are requirement errors.
 *   Upon successful download the page is reloaded and no output is returned.
 */
function install_download_translation(&$install_state) {
  // Check whether all conditions are met to download. Download the translation
  // if possible.
  $requirements = install_check_translations($install_state['parameters']['langcode'], $install_state['server_pattern']);
  // Render requirements if any warnings or errors returned.
  if ($output = install_display_requirements($install_state, $requirements)) {
    return $output;
  }

  // The download was successful, reload the page in the new language.
  $install_state['translations'][$install_state['parameters']['langcode']] = TRUE;
  if ($install_state['interactive']) {
    install_goto(install_redirect_url($install_state));
  }
}

/**
 * Attempts to get a file using a HTTP request and to store it locally.
 *
 * @param string $uri
 *   The URI of the file to grab.
 * @param string $destination
 *   Stream wrapper URI specifying where the file should be placed. If a
 *   directory path is provided, the file is saved into that directory under its
 *   original name. If the path contains a filename as well, that one will be
 *   used instead.
 *
 * @return bool
 *   TRUE on success, FALSE on failure.
 */
function install_retrieve_file($uri, $destination) {
  $parsed_url = parse_url($uri);
  /** @var \Drupal\Core\File\FileSystemInterface $file_system */
  $file_system = \Drupal::service('file_system');
  if (is_dir($file_system->realpath($destination))) {
    // Prevent URIs with triple slashes when gluing parts together.
    $path = str_replace('///', '//', "$destination/") . $file_system->basename($parsed_url['path']);
  }
  else {
    $path = $destination;
  }

  try {
    $response = \Drupal::httpClient()->get($uri, ['headers' => ['Accept' => 'text/plain']]);
    $data = (string) $response->getBody();
    if (empty($data)) {
      return FALSE;
    }
  }
  catch (ClientExceptionInterface) {
    return FALSE;
  }

  return file_put_contents($path, $data) !== FALSE;
}

/**
 * Checks if the localization server can be contacted.
 *
 * @param string $uri
 *   The URI to contact.
 *
 * @return string
 *   TRUE if the URI was contacted successfully, FALSE if not.
 */
function install_check_localization_server($uri) {
  try {
    \Drupal::httpClient()->head($uri);
    return TRUE;
  }
  catch (ClientExceptionInterface) {
    return FALSE;
  }
}

/**
 * Extracts version information from a drupal core version string.
 *
 * @param string $version
 *   Version info string (e.g., 8.0.0, 8.1.0, 8.0.0-dev, 8.0.0-unstable1,
 *   8.0.0-alpha2, 8.0.0-beta3, 8.6.x, and 8.0.0-rc4).
 *
 * @return array
 *   Associative array of version info:
 *   - major: Major version (e.g., "8").
 *   - minor: Minor version (e.g., "0").
 *   - patch: Patch version (e.g., "0").
 *   - extra: Extra version info (e.g., "alpha2").
 *   - extra_text: The text part of "extra" (e.g., "alpha").
 *   - extra_number: The number part of "extra" (e.g., "2").
 */
function _install_get_version_info($version) {
  preg_match('/
    (
      (?P<major>[0-9]+)    # Major release number.
      \.          # .
      (?P<minor>[0-9]+)    # Minor release number.
      \.          # .
      (?P<patch>[0-9]+|x)  # Patch release number.
    )             #
    (             #
      -           # - separator for "extra" version information.
      (?P<extra>   #
        (?P<extra_text>[a-z]+)  # Release extra text (e.g., "alpha").
        (?P<extra_number>[0-9]*)  # Release extra number (no separator between text and number).
      )           #
      |           # OR no "extra" information.
    )
    /sx', $version, $matches);

  return $matches;
}

/**
 * Loads information about the chosen profile during installation.
 *
 * @param $install_state
 *   An array of information about the current installation state. The loaded
 *   profile information will be added here.
 */
function install_load_profile(&$install_state) {
  $profile = $install_state['parameters']['profile'];
  if ($profile !== FALSE) {
    $install_state['profiles'][$profile]->load();
    $install_state['profile_info'] = install_profile_info($profile, $install_state['parameters']['langcode'] ?? 'en');
  }

  $sync_directory = Settings::get('config_sync_directory');
  if (!empty($install_state['parameters']['existing_config']) && !empty($sync_directory)) {
    $install_state['config_install_path'] = $sync_directory;
  }
  // If the profile has a config/sync directory copy the information to the
  // install_state global.
  elseif (!empty($install_state['profile_info']['config_install_path'])) {
    $install_state['config_install_path'] = $install_state['profile_info']['config_install_path'];
  }

  if (!empty($install_state['config_install_path'])) {
    $sync = new FileStorage($install_state['config_install_path']);
    $install_state['config']['system.site'] = $sync->read('system.site');
  }
}

/**
 * Performs a full bootstrap of Drupal during installation.
 */
function install_bootstrap_full() {
  // Store the session on the request object and start it.
  /** @var \Symfony\Component\HttpFoundation\Session\SessionInterface $session */
  $session = \Drupal::service('session');
  \Drupal::request()->setSession($session);
  $session->start();
}

/**
 * Installs required modules via a batch process.
 *
 * @param $install_state
 *   An array of information about the current installation state.
 *
 * @return array
 *   The batch definition.
 */
function install_profile_modules(&$install_state) {
  // We need to manually trigger the installation of core-provided entity types,
  // as those will not be handled by the module installer.
  install_core_entity_type_definitions();

  $modules = $install_state['profile_info']['install'];
  $files = \Drupal::service('extension.list.module')->getList();

  // Always install required modules first. Respect the dependencies between
  // the modules.
  $required = [];
  $non_required = [];

  // Add modules that other modules depend on.
  foreach ($modules as $module) {
    if ($files[$module]->requires) {
      $modules = array_merge($modules, array_keys($files[$module]->requires));
    }
  }
  // The System module has already been installed by install_base_system().
  $modules = array_diff(array_unique($modules), ['system']);
  foreach ($modules as $module) {
    if (!empty($files[$module]->info['required'])) {
      $required[$module] = $files[$module]->sort;
    }
    else {
      $non_required[$module] = $files[$module]->sort;
    }
  }
  arsort($required);
  arsort($non_required);

  $batch_builder = new BatchBuilder();
  foreach ($required + $non_required as $module => $weight) {
    $batch_builder->addOperation(
      '_install_module_batch',
      [$module, $files[$module]->info['name']],
    );
  }
  $batch_builder
    ->setTitle(t('Installing @drupal', ['@drupal' => drupal_install_profile_distribution_name()]))
    ->setErrorMessage(t('The installation has encountered an error.'));
  return $batch_builder->toArray();
}

/**
 * Installs entity type definitions provided by core.
 */
function install_core_entity_type_definitions() {
  $update_manager = \Drupal::entityDefinitionUpdateManager();
  foreach (\Drupal::entityTypeManager()->getDefinitions() as $entity_type) {
    if ($entity_type->getProvider() == 'core') {
      $update_manager->installEntityType($entity_type);
    }
  }
}

/**
 * Installs themes.
 *
 * This does not use a batch, since installing themes is faster than modules and
 * because an installation profile typically installs 1-3 themes only (default
 * theme, base theme, admin theme).
 *
 * @param $install_state
 *   An array of information about the current installation state.
 */
function install_profile_themes(&$install_state) {
  // Install the themes specified by the installation profile.
  $themes = $install_state['profile_info']['themes'];
  \Drupal::service('theme_installer')->install($themes);

  // Ensure that the install profile's theme is used.
  // @see _drupal_maintenance_theme()
  \Drupal::theme()->resetActiveTheme();
}

/**
 * Installs the install profile.
 *
 * @param $install_state
 *   An array of information about the current installation state.
 */
function install_install_profile(&$install_state) {
  // Install all available optional config. During installation the module order
  // is determined by dependencies. If there are no dependencies between modules
  // then the order in which they are installed is dependent on random factors
  // like PHP version. Optional configuration therefore might or might not be
  // created depending on this order. Ensuring that we have installed all of the
  // optional configuration whose dependencies can be met at this point removes
  // any disparities that this creates.
  \Drupal::service('config.installer')->installOptionalConfig();

  \Drupal::service('module_installer')->install([$install_state['parameters']['profile']], FALSE);

  // Ensure that the install profile's theme is used.
  // @see _drupal_maintenance_theme()
  \Drupal::theme()->resetActiveTheme();
}

/**
 * Prepares the system for import and downloads additional translations.
 *
 * @param $install_state
 *   An array of information about the current installation state.
 *
 * @return array
 *   The batch definition, if there are language files to download.
 */
function install_download_additional_translations_operations(&$install_state) {
  \Drupal::moduleHandler()->loadInclude('locale', 'bulk.inc');

  $langcode = $install_state['parameters']['langcode'];
  if (!($language = ConfigurableLanguage::load($langcode))) {
    // Create the language if not already shipped with a profile.
    $language = ConfigurableLanguage::createFromLangcode($langcode);
  }
  $language->save();

  // If a non-English language was selected, change the default language and
  // remove English.
  if ($langcode != 'en') {
    \Drupal::configFactory()->getEditable('system.site')
      ->set('langcode', $langcode)
      ->set('default_langcode', $langcode)
      ->save();
    \Drupal::service('language.default')->set($language);
    if (empty($install_state['profile_info']['keep_english'])) {
      if ($lang = ConfigurableLanguage::load('en')) {
        $lang->delete();
      }
    }
  }

  // If there is more than one language or the single one is not English, we
  // should download/import translations.
  $languages = \Drupal::languageManager()->getLanguages();
  $operations = [];
  foreach ($languages as $langcode => $language) {
    // The installer language was already downloaded. Available translations are
    // stored in $install_state. Check downloads for the other languages if any.
    // Ignore any download errors here, since we are in the middle of an install
    // process and there is no way back. We will not import what we cannot
    // download.
    if (!isset($install_state['translations'][$langcode])) {
      $operations[] = ['install_check_translations', [$langcode, $install_state['server_pattern']]];
    }
  }
  return $operations;
}

/**
 * Imports languages via a batch process during installation.
 *
 * @param $install_state
 *   An array of information about the current installation state.
 *
 * @return array|null
 *   The batch definition, if there are language files to import.
 */
function install_import_translations(&$install_state) {
  $module_handler = \Drupal::moduleHandler();
  $module_handler->loadInclude('locale', 'inc', 'locale.translation');

  // If there is more than one language or the single one is not English, we
  // should import translations.
  $batch_builder = (new BatchBuilder())
    ->setFile(\Drupal::service('extension.path.resolver')->getPath('module', 'locale') . '/locale.batch.inc');
  foreach (install_download_additional_translations_operations($install_state) as $operation) {
    $batch_builder->addOperation($operation[0], $operation[1]);
  }
  $languages = \Drupal::languageManager()->getLanguages();
  if (count($languages) > 1 || !isset($languages['en'])) {
    $batch_builder->addOperation(
      '_install_prepare_import',
      [array_keys($languages), $install_state['server_pattern']],
    );

    // Set up a batch to import translations for drupal core. Translation import
    // for contrib modules happens in install_import_translations_remaining.
    foreach ($languages as $language) {
      if (locale_translation_use_remote_source()) {
        $batch_builder->addOperation(
          'locale_translation_batch_fetch_download',
          ['drupal', $language->getId()],
        );
      }
      $batch_builder->addOperation(
        'locale_translation_batch_fetch_import',
        ['drupal', $language->getId(), []],
      );
    }

    $module_handler->loadInclude('locale', 'inc', 'locale.fetch');
    $batch_builder
      ->setTitle(t('Updating translations.'))
      ->setProgressMessage('')
      ->setErrorMessage(t('Error importing translation files'))
      ->setFinishCallback('locale_translation_batch_fetch_finished');
    return $batch_builder->toArray();
  }
}

/**
 * Tells the translation import process that Drupal core is installed.
 *
 * @param array $langcodes
 *   Language codes used for the translations.
 * @param string $server_pattern
 *   Server access pattern (to replace language code, version number, etc. in).
 */
function _install_prepare_import($langcodes, $server_pattern) {
  $module_handler = \Drupal::moduleHandler();
  $module_handler->loadInclude('locale', 'inc', 'locale.bulk');
  $matches = [];

  foreach ($langcodes as $langcode) {
    // Get the translation files located in the translations directory.
    $files = locale_translate_get_interface_translation_files(['drupal'], [$langcode]);
    // Pick the first file which matches the language, if any.
    $file = reset($files);
    if (is_object($file)) {
      $filename = $file->filename;
      preg_match('/drupal-([0-9a-z\.-]+)\.' . $langcode . '\.po/', $filename, $matches);
      // Get the version information. Custom translation files may not have a
      // version number.
      if (isset($matches[1]) && $version = $matches[1]) {
        $info = _install_get_version_info($version);
        // Picking the first file does not necessarily result in the right file. So
        // we check if at least the major version number is available.
        if ($info['major']) {
          $core = $info['major'] . '.x';
          $data = [
            'name' => 'drupal',
            'project_type' => 'module',
            'core' => $core,
            'version' => $version,
            'server_pattern' => $server_pattern,
            'status' => 1,
          ];
          \Drupal::service('locale.project')->set($data['name'], $data);
          $module_handler->loadInclude('locale', 'inc', 'locale.compare');
          // Reset project information static cache so that it uses the data
          // set above.
          locale_translation_clear_cache_projects();
          locale_translation_check_projects_local(['drupal'], [$langcode]);
        }
      }
    }
  }
}

/**
 * Finishes importing files at end of installation.
 *
 * If other projects besides Drupal core have been installed, their translation
 * will be imported here.
 *
 * @param $install_state
 *   An array of information about the current installation state.
 *
 * @return array
 *   An array of batch definitions.
 */
function install_finish_translations(&$install_state) {
  \Drupal::moduleHandler()->loadInclude('locale', 'fetch.inc');
  \Drupal::moduleHandler()->loadInclude('locale', 'compare.inc');
  \Drupal::moduleHandler()->loadInclude('locale', 'bulk.inc');

  // Build a fresh list of installed projects. When more projects than core are
  // installed, their translations will be downloaded (if required) and imported
  // using a batch.
  $projects = locale_translation_build_projects();
  $languages = \Drupal::languageManager()->getLanguages();
  $batches = [];
  if (count($projects) > 1) {
    $options = _locale_translation_default_update_options();
    if ($batch = locale_translation_batch_update_build([], array_keys($languages), $options)) {
      $batches[] = $batch;
    }
  }

  // If installing from configuration, detect custom translations in the
  // configuration files.
  if (!empty($install_state['config_install_path']) && \Drupal::service('module_handler')->moduleExists('locale')) {
    $batches[] = _install_config_locale_overrides();
  }

  // Creates configuration translations.
  $batches[] = locale_config_batch_update_components([], array_keys($languages), [], TRUE);
  return $batches;
}

/**
 * Performs final installation steps and displays a 'finished' page.
 *
 * @param $install_state
 *   An array of information about the current installation state.
 */
function install_finished(&$install_state) {
  $profile = $install_state['parameters']['profile'];

  // Installation profiles are always loaded last.
  module_set_weight($profile, 1000);

  // Build the router once after installing all modules.
  // This would normally happen upon KernelEvents::TERMINATE, but since the
  // installer does not use an HttpKernel, that event is never triggered.
  \Drupal::service('router.builder')->rebuild();

  // Run cron to populate update status tables (if available) so that users
  // will be warned if they've installed an out of date Drupal version.
  // Will also trigger indexing of profile-supplied content or feeds.
  \Drupal::service('cron')->run();

  if ($install_state['interactive']) {
    // Load current user and perform final login tasks.
    // This has to be done after drupal_flush_all_caches()
    // to avoid session regeneration.
    $account = User::load(1);
    user_login_finalize($account);
  }

  $success_message = t('Congratulations, you installed @drupal!', [
    '@drupal' => drupal_install_profile_distribution_name(),
  ]);
  \Drupal::messenger()->addStatus($success_message);

  // Record when this install ran.
  \Drupal::state()->set('install_time', \Drupal::time()->getRequestTime());
}

/**
 * Implements callback_batch_operation().
 *
 * Performs batch installation of modules.
 */
function _install_module_batch($module, $module_name, &$context) {
  \Drupal::service('module_installer')->install([$module], FALSE);
  $context['results'][] = $module;
  $context['message'] = t('Installed %module module.', ['%module' => $module_name]);
}

/**
 * Checks installation requirements and reports any errors.
 *
 * @param string $langcode
 *   Language code to check for download.
 * @param string $server_pattern
 *   Server access pattern (to replace language code, version number, etc. in).
 *
 * @return array
 *   Requirements compliance array. If the translation cannot be downloaded this
 *   will contain a requirements error with detailed information.
 */
function install_check_translations($langcode, $server_pattern) {
  $requirements = [];

  $readable = FALSE;
  $writable = FALSE;
  // @todo Make this configurable.
  $site_path = \Drupal::getContainer()->getParameter('site.path');
  $files_directory = $site_path . '/files';
  $translations_directory = $site_path . '/files/translations';
  $translations_directory_exists = FALSE;
  $online = FALSE;

  // First attempt to create or make writable the files directory.
  /** @var \Drupal\Core\File\FileSystemInterface $file_system */
  $file_system = \Drupal::service('file_system');
  $file_system->prepareDirectory($files_directory, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);

  // Then, attempt to create or make writable the translations directory.
  $file_system->prepareDirectory($translations_directory, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);

  // Get values so the requirements errors can be specific.
  if (drupal_verify_install_file($translations_directory, FILE_EXIST, 'dir')) {
    $readable = is_readable($translations_directory);
    $writable = is_writable($translations_directory);
    $translations_directory_exists = TRUE;
  }

  $version = \Drupal::VERSION;
  // For dev releases, remove the '-dev' part and trust the translation server
  // to fall back to the latest stable release for that branch.
  // @see locale_translation_build_projects()
  if (preg_match("/^(\d+\.\d+\.).*-dev$/", $version, $matches)) {
    // Example match: 8.0.0-dev => 8.0.x (Drupal core)
    $version = $matches[1] . 'x';
  }

  // Build URL for the translation file and the translation server.
  $variables = [
    '%project' => 'drupal',
    '%version' => $version,
    '%core' => 'all',
    '%language' => $langcode,
  ];
  $translation_url = strtr($server_pattern, $variables);

  $elements = parse_url($translation_url);
  $server_url = $elements['scheme'] . '://' . $elements['host'];

  // Build the language name for display.
  $languages = LanguageManager::getStandardLanguageList();
  $language = isset($languages[$langcode]) ? $languages[$langcode][0] : $langcode;

  // Check if any of the desired translation files are available or if the
  // translation server can be reached. In other words, check if we are online
  // and have an internet connection.
  if ($translation_available = install_check_localization_server($translation_url)) {
    $online = TRUE;
  }
  if (!$translation_available) {
    if (install_check_localization_server($server_url)) {
      $online = TRUE;
    }
  }

  // If the translations directory does not exist, throw an error.
  if (!$translations_directory_exists) {
    $requirements['translations directory exists'] = [
      'title'       => t('Translations directory'),
      'value'       => t('The translations directory does not exist.'),
      'severity'    => REQUIREMENT_ERROR,
      'description' => t('The installer requires that you create a translations directory as part of the installation process. Create the directory %translations_directory . More details about installing Drupal are available in <a href=":install_txt">INSTALL.txt</a>.', ['%translations_directory' => $translations_directory, ':install_txt' => base_path() . 'core/INSTALL.txt']),
    ];
  }
  else {
    $requirements['translations directory exists'] = [
      'title'       => t('Translations directory'),
      'value'       => t('The directory %translations_directory exists.', ['%translations_directory' => $translations_directory]),
    ];
    // If the translations directory is not readable, throw an error.
    if (!$readable) {
      $requirements['translations directory readable'] = [
        'title'       => t('Translations directory'),
        'value'       => t('The translations directory is not readable.'),
        'severity'    => REQUIREMENT_ERROR,
        'description' => t('The installer requires read permissions to %translations_directory at all times. The <a href=":handbook_url">webhosting issues</a> documentation section offers help on this and other topics.', ['%translations_directory' => $translations_directory, ':handbook_url' => 'https://www.drupal.org/server-permissions']),
      ];
    }
    // If translations directory is not writable, throw an error.
    if (!$writable) {
      $requirements['translations directory writable'] = [
        'title'       => t('Translations directory'),
        'value'       => t('The translations directory is not writable.'),
        'severity'    => REQUIREMENT_ERROR,
        'description' => t('The installer requires write permissions to %translations_directory during the installation process. The <a href=":handbook_url">webhosting issues</a> documentation section offers help on this and other topics.', ['%translations_directory' => $translations_directory, ':handbook_url' => 'https://www.drupal.org/server-permissions']),
      ];
    }
    else {
      $requirements['translations directory writable'] = [
        'title'       => t('Translations directory'),
        'value'       => t('The translations directory is writable.'),
      ];
    }
  }

  // If the translations server can not be contacted, throw an error.
  if (!$online) {
    $requirements['online'] = [
      'title'       => t('Internet'),
      'value'       => t('The translation server is offline.'),
      'severity'    => REQUIREMENT_ERROR,
      'description' => t('The installer requires to contact the translation server to download a translation file. Check your internet connection and verify that your website can reach the translation server at <a href=":server_url">@server_url</a>.', [':server_url' => $server_url, '@server_url' => $server_url]),
    ];
  }
  else {
    $requirements['online'] = [
      'title'       => t('Internet'),
      'value'       => t('The translation server is online.'),
    ];
    // If translation file is not found at the translation server, throw an
    // error.
    if (!$translation_available) {
      $requirements['translation available'] = [
        'title'       => t('Translation'),
        'value'       => t('The %language translation is not available.', ['%language' => $language]),
        'severity'    => REQUIREMENT_ERROR,
        'description' => t('The %language translation file is not available at the translation server. <a href=":url">Choose a different language</a> or select English and translate your website later.', ['%language' => $language, ':url' => $_SERVER['SCRIPT_NAME']]),
      ];
    }
    else {
      $requirements['translation available'] = [
        'title'       => t('Translation'),
        'value'       => t('The %language translation is available.', ['%language' => $language]),
      ];
    }
  }

  if ($translations_directory_exists && $readable && $writable && $translation_available) {
    $translation_downloaded = install_retrieve_file($translation_url, $translations_directory);

    if (!$translation_downloaded) {
      $requirements['translation downloaded'] = [
        'title'       => t('Translation'),
        'value'       => t('The %language translation could not be downloaded.', ['%language' => $language]),
        'severity'    => REQUIREMENT_ERROR,
        'description' => t('The %language translation file could not be downloaded. <a href=":url">Choose a different language</a> or select English and translate your website later.', ['%language' => $language, ':url' => $_SERVER['SCRIPT_NAME']]),
      ];
    }
  }

  return $requirements;
}

/**
 * Checks installation requirements and reports any errors.
 */
function install_check_requirements($install_state) {
  $requirements = [];
  $profile = $install_state['parameters']['profile'];

  if ($profile !== FALSE) {
    // Check the profile requirements.
    $requirements = drupal_check_profile($profile);
  }

  if ($install_state['settings_verified']) {
    return $requirements;
  }

  // If Drupal is not set up already, we need to try to create the default
  // settings and services files.
  $default_files = [];
  $default_files['settings.php'] = [
    'file' => 'settings.php',
    'file_default' => 'default.settings.php',
    'title_default' => t('Default settings file'),
    'description_default' => t('The default settings file does not exist.'),
    'title' => t('Settings file'),
  ];
  $file_system = \Drupal::service('file_system');

  foreach ($default_files as $default_file_info) {
    $readable = FALSE;
    $writable = FALSE;
    $site_path = './' . \Drupal::getContainer()->getParameter('site.path');
    $file = $site_path . "/{$default_file_info['file']}";
    $default_file = "./sites/default/{$default_file_info['file_default']}";
    $exists = FALSE;
    // Verify that the directory exists.
    if (drupal_verify_install_file($site_path, FILE_EXIST, 'dir')) {
      if (drupal_verify_install_file($file, FILE_EXIST)) {
        // If it does, make sure it is writable.
        $readable = drupal_verify_install_file($file, FILE_READABLE);
        $writable = drupal_verify_install_file($file, FILE_WRITABLE);
        $exists = TRUE;
      }
    }

    // If the default $default_file does not exist, or is not readable,
    // report an error.
    if (!drupal_verify_install_file($default_file, FILE_EXIST | FILE_READABLE)) {
      $requirements["default $file file exists"] = [
        'title' => $default_file_info['title_default'],
        'value' => $default_file_info['description_default'],
        'severity' => REQUIREMENT_ERROR,
        'description' => t('The @drupal installer requires that the %default-file file must not be deleted or modified from the original download.', [
          '@drupal' => drupal_install_profile_distribution_name(),
          '%default-file' => $default_file,
        ]),
      ];
    }
    // Otherwise, if $file does not exist yet, we can try to copy
    // $default_file to create it.
    elseif (!$exists) {
      $copied = drupal_verify_install_file($site_path, FILE_EXIST | FILE_WRITABLE, 'dir') && @copy($default_file, $file);
      if ($copied) {
        // If the new $file file has the same owner as $default_file this means
        // $default_file is owned by the webserver user. This is an inherent
        // security weakness because it allows a malicious webserver process to
        // append arbitrary PHP code and then execute it. However, it is also a
        // common configuration on shared hosting, and there is nothing Drupal
        // can do to prevent it. In this situation, having $file also owned by
        // the webserver does not introduce any additional security risk, so we
        // keep the file in place. Additionally, this situation also occurs when
        // the test runner is being run be different user than the webserver.
        if (fileowner($default_file) === fileowner($file) || DRUPAL_TEST_IN_CHILD_SITE) {
          $readable = drupal_verify_install_file($file, FILE_READABLE);
          $writable = drupal_verify_install_file($file, FILE_WRITABLE);
          $exists = TRUE;
        }
        // If $file and $default_file have different owners, this probably means
        // the server is set up "securely" (with the webserver running as its
        // own user, distinct from the user who owns all the Drupal PHP files),
        // although with either a group or world writable sites directory.
        // Keeping $file owned by the webserver would therefore introduce a
        // security risk. It would also cause a usability problem, since site
        // owners who do not have root access to the file system would be unable
        // to edit their settings file later on. We therefore must delete the
        // file we just created and force the administrator to log on to the
        // server and create it manually.
        else {
          $deleted = @$file_system->unlink($file);
          // We expect deleting the file to be successful (since we just
          // created it ourselves above), but if it fails somehow, we set a
          // variable so we can display a one-time error message to the
          // administrator at the bottom of the requirements list. We also try
          // to make the file writable, to eliminate any conflicting error
          // messages in the requirements list.
          $exists = !$deleted;
          if ($exists) {
            $settings_file_ownership_error = TRUE;
            $readable = drupal_verify_install_file($file, FILE_READABLE);
            $writable = drupal_verify_install_file($file, FILE_WRITABLE);
          }
        }
      }
    }

    // If the $file does not exist, throw an error.
    if (!$exists) {
      $requirements["$file file exists"] = [
        'title' => $default_file_info['title'],
        'value' => t('The %file does not exist.', ['%file' => $default_file_info['title']]),
        'severity' => REQUIREMENT_ERROR,
        'description' => t('The @drupal installer requires that you create a %file as part of the installation process. Copy the %default_file file to %file. More details about installing Drupal are available in <a href=":install_txt">INSTALL.txt</a>.', [
          '@drupal' => drupal_install_profile_distribution_name(),
          '%file' => $file,
          '%default_file' => $default_file,
          ':install_txt' => base_path() . 'core/INSTALL.txt',
        ]),
      ];
    }
    else {
      $requirements["$file file exists"] = [
        'title' => $default_file_info['title'],
        'value' => t('The %file exists.', ['%file' => $file]),
      ];
      // If the $file is not readable, throw an error.
      if (!$readable) {
        $requirements["$file file readable"] = [
          'title' => $default_file_info['title'],
          'value' => t('The %file is not readable.', ['%file' => $default_file_info['title']]),
          'severity' => REQUIREMENT_ERROR,
          'description' => t('@drupal requires read permissions to %file at all times. The <a href=":handbook_url">webhosting issues</a> documentation section offers help on this and other topics.', [
            '@drupal' => drupal_install_profile_distribution_name(),
            '%file' => $file,
            ':handbook_url' => 'https://www.drupal.org/server-permissions',
          ]),
        ];
      }
      // If the $file is not writable, throw an error.
      if (!$writable) {
        $requirements["$file file writable"] = [
          'title' => $default_file_info['title'],
          'value' => t('The %file is not writable.', ['%file' => $default_file_info['title']]),
          'severity' => REQUIREMENT_ERROR,
          'description' => t('The @drupal installer requires write permissions to %file during the installation process. The <a href=":handbook_url">webhosting issues</a> documentation section offers help on this and other topics.', [
            '@drupal' => drupal_install_profile_distribution_name(),
            '%file' => $file,
            ':handbook_url' => 'https://www.drupal.org/server-permissions',
          ]),
        ];
      }
      else {
        $requirements["$file file"] = [
          'title' => $default_file_info['title'],
          'value' => t('The @file is writable.', ['@file' => $default_file_info['title']]),
        ];
      }
      if (!empty($settings_file_ownership_error)) {
        $requirements["$file file ownership"] = [
          'title' => $default_file_info['title'],
          'value' => t('The @file is owned by the web server.', ['@file' => $default_file_info['title']]),
          'severity' => REQUIREMENT_ERROR,
          'description' => t('The @drupal installer failed to create a %file file with proper file ownership. Log on to your web server, remove the existing %file file, and create a new one by copying the %default_file file to %file. More details about installing Drupal are available in <a href=":install_txt">INSTALL.txt</a>. The <a href=":handbook_url">webhosting issues</a> documentation section offers help on this and other topics.', [
            '@drupal' => drupal_install_profile_distribution_name(),
            '%file' => $file,
            '%default_file' => $default_file,
            ':install_txt' => base_path() . 'core/INSTALL.txt',
            ':handbook_url' => 'https://www.drupal.org/server-permissions',
          ]),
        ];
      }
    }

    // Check the database settings if they have been configured in settings.php
    // before running the Drupal installer.
    if ($database = Database::getConnectionInfo()) {
      $request = Request::createFromGlobals();
      $site_path = empty($install_state['site_path']) ? DrupalKernel::findSitePath($request, FALSE) : $install_state['site_path'];
      $database = $database['default'];
      $settings_file = './' . $site_path . '/settings.php';

      $errors = install_database_errors($database, $settings_file);
      if (count($errors)) {
        $error_message = SiteSettingsForm::getDatabaseErrorsTemplate($errors);
        $requirements['database_install_errors'] = [
          'title' => t('Database settings'),
          'description' => $error_message,
          'severity' => REQUIREMENT_ERROR,
        ];
      }
    }
  }
  return $requirements;
}

/**
 * Displays installation requirements.
 *
 * @param array $install_state
 *   An array of information about the current installation state.
 * @param array $requirements
 *   An array of requirements, in the same format as is returned by
 *   hook_requirements().
 *
 * @return array|null
 *   A themed status report, or an exception if there are requirement errors.
 *   If there are only requirement warnings, a themed status report is shown
 *   initially, but the user is allowed to bypass it by providing 'continue=1'
 *   in the URL. Otherwise, no output is returned, so that the next task can be
 *   run in the same page request.
 *
 * @throws \Drupal\Core\Installer\Exception\InstallerException
 */
function install_display_requirements($install_state, $requirements) {
  // Check the severity of the requirements reported.
  $severity = drupal_requirements_severity($requirements);

  // If there are errors, always display them. If there are only warnings, skip
  // them if the user has provided a URL parameter acknowledging the warnings
  // and indicating a desire to continue anyway. See drupal_requirements_url().
  if ($severity == REQUIREMENT_ERROR || ($severity == REQUIREMENT_WARNING && empty($install_state['parameters']['continue']))) {
    if ($install_state['interactive']) {
      $build['report']['#type'] = 'status_report';
      $build['report']['#requirements'] = $requirements;
      if ($severity == REQUIREMENT_WARNING) {
        $build['#title'] = t('Requirements review');
        $build['#suffix'] = t('Check the messages and <a href=":retry">retry</a>, or you may choose to <a href=":cont">continue anyway</a>.', [':retry' => drupal_requirements_url(REQUIREMENT_ERROR), ':cont' => drupal_requirements_url($severity)]);
      }
      else {
        $build['#title'] = t('Requirements problem');
        $build['#suffix'] = t('Check the messages and <a href=":url">try again</a>.', [':url' => drupal_requirements_url($severity)]);
      }
      return $build;
    }
    else {
      // Throw an exception showing any unmet requirements.
      $failures = [];
      foreach ($requirements as $requirement) {
        // Skip warnings altogether for non-interactive installations; these
        // proceed in a single request so there is no good opportunity (and no
        // good method) to warn the user anyway.
        if (isset($requirement['severity']) && $requirement['severity'] == REQUIREMENT_ERROR) {
          $render_array = [
            '#type' => 'inline_template',
            '#template' => '{{ title }}:{{ value }}<br /><br />{{ description }}',
            '#context' => [
              'title' => $requirement['title'],
              'value' => $requirement['value'],
              'description' => $requirement['description'],
            ],
          ];
          $failures[] = \Drupal::service('renderer')->renderInIsolation($render_array);
        }
      }
      if (!empty($failures)) {
        throw new InstallerException(implode("\n\n", $failures));
      }
    }
  }
}

/**
 * Creates a batch for the config importer to process.
 *
 * @see install_tasks()
 */
function install_config_import_batch() {
  // We need to manually trigger the installation of core-provided entity types,
  // as those will not be handled by the module installer.
  // @see install_profile_modules()
  install_core_entity_type_definitions();

  // Get the sync storage.
  $sync = \Drupal::service('config.storage.sync');
  // Match up the site UUIDs, the install_base_system install task will have
  // installed the system module and created a new UUID.
  $system_site = $sync->read('system.site');
  // When installing from configuration it is possible that system.site
  // configuration is not present. If this occurs a ConfigImporterException will
  // by thrown when $config_importer->initialize() is called below and the error
  // will be reported to the user.
  if ($system_site !== FALSE) {
    \Drupal::configFactory()->getEditable('system.site')->set('uuid', $system_site['uuid'])->save();
  }

  // Create the storage comparer and the config importer.
  $storage_comparer = new StorageComparer($sync, \Drupal::service('config.storage'));
  $storage_comparer->createChangelist();
  $config_importer = new ConfigImporter(
    $storage_comparer,
    \Drupal::service('event_dispatcher'),
    \Drupal::service('config.manager'),
    \Drupal::service('lock.persistent'),
    \Drupal::service('config.typed'),
    \Drupal::service('module_handler'),
    \Drupal::service('module_installer'),
    \Drupal::service('theme_handler'),
    \Drupal::service('string_translation'),
    \Drupal::service('extension.list.module'),
    \Drupal::service('extension.list.theme')
  );

  try {
    $sync_steps = $config_importer->initialize();

    $batch_builder = new BatchBuilder();
    $batch_builder
      ->setFinishCallback([ConfigImporterBatch::class, 'finish'])
      ->setTitle(t('Importing configuration'))
      ->setInitMessage(t('Starting configuration import.'))
      ->setErrorMessage(t('Configuration import has encountered an error.'));

    foreach ($sync_steps as $sync_step) {
      $batch_builder->addOperation([ConfigImporterBatch::class, 'process'], [$config_importer, $sync_step]);
    }

    return $batch_builder->toArray();
  }
  catch (ConfigImporterException $e) {
    global $install_state;
    // There are validation errors.
    $messenger = \Drupal::messenger();
    $messenger->addError(t('The configuration synchronization failed validation.'));
    foreach ($config_importer->getErrors() as $message) {
      $messenger->addError($message);
    }
    install_display_output(['#title' => t('Configuration validation')], $install_state);
  }
}

/**
 * Replaces install_download_translation() during configuration installs.
 *
 * @param array $install_state
 *   An array of information about the current installation state.
 *
 * @return string
 *   A themed status report, or an exception if there are requirement errors.
 *   Upon successful download the page is reloaded and no output is returned.
 *
 * @see install_download_translation()
 */
function install_config_download_translations(&$install_state) {
  $needs_download = isset($install_state['parameters']['langcode']) && !isset($install_state['translations'][$install_state['parameters']['langcode']]) && $install_state['parameters']['langcode'] !== 'en';
  if ($needs_download) {
    return install_download_translation($install_state);
  }
}

/**
 * Reverts configuration if hook_install() implementations have made changes.
 *
 * This step ensures that the final configuration matches the configuration
 * provided to the installer.
 */
function install_config_revert_install_changes() {
  global $install_state;

  $storage_comparer = new StorageComparer(\Drupal::service('config.storage.sync'), \Drupal::service('config.storage'));
  $storage_comparer->createChangelist();
  if ($storage_comparer->hasChanges()) {
    $config_importer = new ConfigImporter(
      $storage_comparer,
      \Drupal::service('event_dispatcher'),
      \Drupal::service('config.manager'),
      \Drupal::service('lock.persistent'),
      \Drupal::service('config.typed'),
      \Drupal::service('module_handler'),
      \Drupal::service('module_installer'),
      \Drupal::service('theme_handler'),
      \Drupal::service('string_translation'),
      \Drupal::service('extension.list.module'),
      \Drupal::service('extension.list.theme')
    );
    try {
      $config_importer->import();
    }
    catch (ConfigImporterException $e) {
      $messenger = \Drupal::messenger();
      // There are validation errors.
      $messenger->addError(t('The configuration synchronization failed validation.'));
      foreach ($config_importer->getErrors() as $message) {
        $messenger->addError($message);
      }
      install_display_output(['#title' => t('Configuration validation')], $install_state);
    }

    // At this point the configuration should match completely.
    if (\Drupal::moduleHandler()->moduleExists('language')) {
      // If the English language exists at this point we need to ensure
      // install_download_additional_translations_operations() does not delete
      // it.
      if (ConfigurableLanguage::load('en')) {
        $install_state['profile_info']['keep_english'] = TRUE;
      }
    }
  }
}

/**
 * Creates a batch to process config translations after installing from config.
 *
 * This ensures that the logic from LocaleConfigSubscriber::onConfigSave() is
 * run on sites after installing from configuration so updating translations
 * from PO files does not result in overwriting customizations.
 *
 * @return array
 *   The batch definition.
 *
 * @see \Drupal\locale\LocaleConfigSubscriber::onConfigSave()
 */
function _install_config_locale_overrides() {
  // @todo https://www.drupal.org/project/drupal/issues/3252244 Somehow the
  //   config cache gets filled up with junk after installing from
  //   configuration.
  \Drupal::service('cache.config')->deleteAll();

  // Get the services we need.
  $language_manager = \Drupal::languageManager();
  /** @var \Drupal\locale\LocaleConfigManager $locale_config_manager */
  $locale_config_manager = \Drupal::service('locale.config_manager');

  $langcodes = array_keys($language_manager->getLanguages());
  if (count($langcodes) > 1 && !$language_manager instanceof ConfigurableLanguageManagerInterface) {
    throw new \LogicException('There are multiple languages and the language manager is not an instance of ConfigurableLanguageManagerInterface');
  }

  $batch_builder = (new BatchBuilder())
    ->setFile('core/includes/install.core.inc')
    ->setTitle(t('Updating configuration translations'))
    ->setInitMessage(t('Starting configuration update'))
    ->setErrorMessage(t('Error updating configuration translations'));
  $i = 0;
  $batch_names = [];
  foreach ($locale_config_manager->getComponentNames() as $name) {
    $batch_names[] = $name;
    $i++;
    // During installation the caching of configuration objects is disabled so
    // it is very expensive to initialize the \Drupal::config() object on each
    // request. We batch a small number of configuration object upgrades
    // together to improve the overall performance of the process.
    if ($i % 20 == 0) {
      $batch_builder->addOperation('_install_config_locale_overrides_process_batch', [$batch_names, $langcodes]);
      $batch_names = [];
    }
  }
  if (!empty($batch_names)) {
    $batch_builder->addOperation('_install_config_locale_overrides_process_batch', [$batch_names, $langcodes]);
  }
  return $batch_builder->toArray();
}

/**
 * Batch operation callback for install_config_locale_overrides().
 *
 * @param array $names
 *   The configuration to process.
 * @param array $langcodes
 *   The langcodes available on the site.
 * @param $context
 *   The batch context.
 */
function _install_config_locale_overrides_process_batch(array $names, array $langcodes, &$context) {
  // Get the services we need.
  $language_manager = \Drupal::languageManager();
  /** @var \Drupal\locale\LocaleConfigManager $locale_config_manager */
  $locale_config_manager = \Drupal::service('locale.config_manager');
  /** @var \Drupal\locale\LocaleConfigSubscriber $locale_config_subscriber */
  $locale_config_subscriber = \Drupal::service('locale.config_subscriber');

  foreach ($names as $name) {
    $active_langcode = $locale_config_manager->getActiveConfigLangcode($name);
    foreach ($langcodes as $langcode) {
      if ($langcode === $active_langcode) {
        $config = \Drupal::config($name);
      }
      else {
        $config = $language_manager->getLanguageConfigOverride($langcode, $name);
      }
      $locale_config_subscriber->updateLocaleStorage($config, $langcode);
    }
  }
  $context['finished'] = 1;
}

/**
 * Installs required modules prior to applying a recipe via the installer.
 *
 * @see install_tasks()
 *
 * @internal
 *   All installer code is internal.
 */
function install_recipe_required_modules() {
  // We need to manually trigger the installation of core-provided entity types,
  // as those will not be handled by the module installer.
  // @see install_profile_modules()
  install_core_entity_type_definitions();

  $batch_builder = new BatchBuilder();
  $batch_builder
    ->setFinishCallback([ConfigImporterBatch::class, 'finish'])
    ->setTitle(t('Installing required modules'))
    ->setInitMessage(t('Starting required module installation.'))
    ->setErrorMessage(t('Required module installation has encountered an error.'));

  $files = \Drupal::service('extension.list.module')->getList();

  // Always install required modules first.
  $required = [];

  foreach ($files as $module => $extension) {
    if (!empty($extension->info['required'])) {
      $required[$module] = $extension->sort;
    }
  }
  arsort($required);

  // The system module is already installed. See install_base_system().
  unset($required['system']);

  foreach ($required as $module => $weight) {
    $batch_builder->addOperation(
      '_install_module_batch',
      [$module, $files[$module]->info['name']],
    );
  }
  return $batch_builder->toArray();
}

/**
 * Creates a batch for the recipe system to process.
 *
 * @see install_tasks()
 *
 * @internal
 *   This API is experimental.
 */
function install_recipe_batch(&$install_state) {
  $batch_builder = new BatchBuilder();
  $batch_builder
    ->setTitle(t('Installing recipe'))
    ->setInitMessage(t('Starting recipe installation.'))
    ->setErrorMessage(t('Recipe installation has encountered an error.'));

  $recipe = Recipe::createFromDirectory($install_state['parameters']['recipe']);
  foreach (RecipeRunner::toBatchOperations($recipe) as $step) {
    $batch_builder->addOperation(...$step);
  }

  return $batch_builder->toArray();
}
