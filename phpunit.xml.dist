<?xml version="1.0" encoding="UTF-8"?>

<!-- TODO set checkForUnintentionallyCoveredCode="true" once https://www.drupal.org/node/2626832 is resolved. -->
<!-- PHPUnit expects functional tests to be run with either a privileged user
 or your current system user. See core/tests/README.md and
 https://www.drupal.org/node/2116263 for details.
-->
<phpunit bootstrap="web/core/tests/bootstrap.php" colors="true"
         beStrictAboutTestsThatDoNotTestAnything="true"
         beStrictAboutOutputDuringTests="true"
         beStrictAboutChangesToGlobalState="true"
         printerClass="\Drupal\Tests\Listeners\HtmlOutputPrinter">
  <php>
    <!-- Set error reporting to E_ALL. -->
    <ini name="error_reporting" value="32767"/>
    <!-- Do not limit the amount of memory tests take to run. -->
    <ini name="memory_limit" value="-1"/>
    <!-- Example SIMPLETEST_BASE_URL value: http://localhost -->
<!--    <env name="SIMPLETEST_BASE_URL" value="http://drupal:8888"/>-->
    <!-- Example SIMPLETEST_DB value: mysql://username:password@localhost/databasename#table_prefix -->
<!--    <env name="SIMPLETEST_DB" value="mysql://drupal:drupal@mariadb/drupal"/>-->
    <env name="SIMPLETEST_DB" value="mysql://db:db@db/db"/>
    <!-- Example BROWSERTEST_OUTPUT_DIRECTORY value: /path/to/webroot/sites/simpletest/browser_output -->
    <env name="BROWSERTEST_OUTPUT_DIRECTORY" value="/tmp"/>
    <!-- To have browsertest output use an alternative base URL. For example if
     SIMPLETEST_BASE_URL is an internal DDEV URL, you can set this to the
     external DDev URL so you can follow the links directly.
    -->
    <env name="BROWSERTEST_OUTPUT_BASE_URL" value=""/>
    <env name="SYMFONY_DEPRECATIONS_HELPER" value="disabled"/>
    <!-- Example for changing the driver class for mink tests MINK_DRIVER_CLASS value: 'Drupal\FunctionalJavascriptTests\DrupalSelenium2Driver' -->
    <env name="MINK_DRIVER_CLASS" value=''/>
    <!-- Example for changing the driver args to mink tests MINK_DRIVER_ARGS value: '["http://127.0.0.1:8510"]' -->
    <env name="MINK_DRIVER_ARGS" value=''/>
    <!-- Example for changing the driver args to phantomjs tests MINK_DRIVER_ARGS_PHANTOMJS value: '["http://127.0.0.1:8510"]' -->
    <env name="MINK_DRIVER_ARGS_PHANTOMJS" value=''/>
    <!-- Example for changing the driver args to webdriver tests MINK_DRIVER_ARGS_WEBDRIVER value: '["chrome", { "chromeOptions": { "w3c": false } }, "http://localhost:4444/wd/hub"]' For using the Firefox browser, replace "chrome" with "firefox" -->
    <!-- Example from drupal.org https://dispatcher.drupalci.org/job/drupal_contrib/177251/console-->
    <env name="MINK_DRIVER_ARGS_WEBDRIVER" value='["chrome", {"browserName":"chrome","chromeOptions":{"args":["--disable-gpu","--headless"]}}, "http://chrome:9515"]'/>
  </php>
  <testsuites>
    <testsuite name="kernel">
      <directory>./tests/src/Kernel/</directory>
    </testsuite>
    <testsuite name="unit">
      <directory>./tests/src/Unit/</directory>
    </testsuite>
    <!--    <testsuite name="functional">-->
    <!--      <directory>./tests/src/Functional/</directory>-->
    <!--    </testsuite>-->
  </testsuites>
  <listeners>
    <listener class="\Drupal\Tests\Listeners\DrupalListener">
    </listener>
    <!-- The Symfony deprecation listener has to come after the Drupal listener -->
    <listener class="Symfony\Bridge\PhpUnit\SymfonyTestsListener">
    </listener>
  </listeners>
</phpunit>
