<?php

namespace Dr<PERSON>al\quickedit\Ajax;

use <PERSON><PERSON><PERSON>\Core\Ajax\BaseCommand;

/**
 * AJAX command to indicate the entity was loaded from PrivateTempStore and
 * saved into the database.
 */
class EntitySavedCommand extends BaseCommand {

  /**
   * Constructs an EntitySaveCommand object.
   *
   * @param string $data
   *   The data to pass on to the client side.
   */
  public function __construct($data) {
    parent::__construct('quickeditEntitySaved', $data);
  }

}
