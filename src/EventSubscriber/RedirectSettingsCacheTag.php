<?php

namespace <PERSON><PERSON>al\redirect\EventSubscriber;

use <PERSON><PERSON><PERSON>\Core\Cache\CacheTagsInvalidatorInterface;
use <PERSON><PERSON>al\Core\Config\ConfigCrudEvent;
use <PERSON><PERSON>al\Core\Config\ConfigEvents;
use <PERSON>ymfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * A subscriber invalidating the 'rendered' cache tag when saving redirect settings.
 */
class RedirectSettingsCacheTag implements EventSubscriberInterface {

  /**
   * The cache tags invalidator.
   *
   * @var \Drupal\Core\Cache\CacheTagsInvalidatorInterface
   */
  protected $cacheTagsInvalidator;

  /**
   * Constructs a RedirectSettingsCacheTag object.
   *
   * @param \Drupal\Core\Cache\CacheTagsInvalidatorInterface $cache_tags_invalidator
   *   The cache tags invalidator.
   */
  public function __construct(CacheTagsInvalidatorInterface $cache_tags_invalidator) {
    $this->cacheTagsInvalidator = $cache_tags_invalidator;
  }

  /**
   * Invalidate the 'rendered' cache tag whenever the settings are modified.
   *
   * @param \Drupal\Core\Config\ConfigCrudEvent $event
   *   The Event to process.
   */
  public function onSave(ConfigCrudEvent $event) {
    // Changing the Redirect settings means that any cached page might
    // result in a different response, so we need to invalidate them all.
    if ($event->getConfig()->getName() === 'redirect.settings') {
      $this->cacheTagsInvalidator->invalidateTags(['rendered']);
    }
  }

  /**
   * {@inheritdoc}
   */
  public static function getSubscribedEvents(): array {
    $events[ConfigEvents::SAVE][] = ['onSave'];
    return $events;
  }

}
