<?php

namespace Drupal\paragraphs\Plugin\migrate\process;

use <PERSON><PERSON>al\Component\Plugin\ConfigurableInterface;
use <PERSON><PERSON><PERSON>\Component\Utility\NestedArray;
use <PERSON><PERSON>al\Core\Entity\EntityTypeBundleInfoInterface;
use <PERSON><PERSON><PERSON>\Core\Plugin\ContainerFactoryPluginInterface;
use Dr<PERSON>al\migrate\ProcessPluginBase as MigrateProcessPluginBase;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Base class for Paragraphs process plugins.
 */
abstract class ProcessPluginBase extends MigrateProcessPluginBase implements ConfigurableInterface, ContainerFactoryPluginInterface {

  /**
   * The entity bundle info service.
   *
   * @var \Drupal\Core\Entity\EntityTypeBundleInfoInterface
   */
  protected $entityTypeBundleInfo;

  /**
   * {@inheritdoc}
   */
  public function __construct(array $configuration, $plugin_id, $plugin_definition, EntityTypeBundleInfoInterface $entity_type_bundle_info) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->entityTypeBundleInfo = $entity_type_bundle_info;
    $this->setConfiguration($configuration);
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('entity_type.bundle.info')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getConfiguration() {
    return $this->configuration;
  }

  /**
   * {@inheritdoc}
   */
  public function setConfiguration(array $configuration) {
    $this->configuration = NestedArray::mergeDeep($this->defaultConfiguration(), $configuration);
  }

  /**
   * {@inheritdoc}
   */
  public function defaultConfiguration() {
    return [];
  }

}
