<?php

namespace Drupal\paragraphs\Plugin\migrate\process;

use <PERSON><PERSON>al\migrate\ProcessPluginBase;
use <PERSON><PERSON>al\migrate\MigrateExecutableInterface;
use <PERSON>upal\migrate\Row;

/**
 * Configure field instance settings for paragraphs.
 *
 * @MigrateProcessPlugin(
 *   id = "field_collection_field_settings"
 * )
 */
class FieldCollectionFieldSettings extends ProcessPluginBase {

  /**
   * {@inheritdoc}
   */
  public function transform($value, MigrateExecutableInterface $migrate_executable, Row $row, $destination_property) {
    if ($row->getSourceProperty('type') == 'field_collection') {
      $value['target_type'] = 'paragraph';
    }
    return $value;
  }

}
