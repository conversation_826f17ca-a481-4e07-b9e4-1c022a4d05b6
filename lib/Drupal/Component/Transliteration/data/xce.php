<?php

/**
 * @file
 * Generic transliteration data for the PhpTransliteration class.
 */

$base = [
  0x00 => 'chwik', 'chwit', 'chwip', 'chwih', 'chyu', 'chyug', 'chyukk', 'chyugs', 'chyun', 'chyunj', 'chyunh', 'chyud', 'chyul', 'chyulg', 'chyulm', 'chyulb',
  0x10 => 'chyuls', 'chyult', 'chyulp', 'chyulh', 'chyum', 'chyub', 'chyubs', 'chyus', 'chyuss', 'chyung', 'chyuj', 'chyuch', 'chyuk', 'chyut', 'chyup', 'chyuh',
  0x20 => 'cheu', 'cheug', 'cheukk', 'cheugs', 'cheun', 'cheunj', 'cheunh', 'cheud', 'cheul', 'cheulg', 'cheulm', 'cheulb', 'cheuls', 'cheult', 'cheulp', 'cheulh',
  0x30 => 'cheum', 'cheub', 'cheubs', 'cheus', 'cheuss', 'cheung', 'cheuj', 'cheuch', 'cheuk', 'cheut', 'cheup', 'cheuh', 'chui', 'chuig', 'chuikk', 'chuigs',
  0x40 => 'chuin', 'chuinj', 'chuinh', 'chuid', 'chuil', 'chuilg', 'chuilm', 'chuilb', 'chuils', 'chuilt', 'chuilp', 'chuilh', 'chuim', 'chuib', 'chuibs', 'chuis',
  0x50 => 'chuiss', 'chuing', 'chuij', 'chuich', 'chuik', 'chuit', 'chuip', 'chuih', 'chi', 'chig', 'chikk', 'chigs', 'chin', 'chinj', 'chinh', 'chid',
  0x60 => 'chil', 'chilg', 'chilm', 'chilb', 'chils', 'chilt', 'chilp', 'chilh', 'chim', 'chib', 'chibs', 'chis', 'chiss', 'ching', 'chij', 'chich',
  0x70 => 'chik', 'chit', 'chip', 'chih', 'ka', 'kag', 'kakk', 'kags', 'kan', 'kanj', 'kanh', 'kad', 'kal', 'kalg', 'kalm', 'kalb',
  0x80 => 'kals', 'kalt', 'kalp', 'kalh', 'kam', 'kab', 'kabs', 'kas', 'kass', 'kang', 'kaj', 'kach', 'kak', 'kat', 'kap', 'kah',
  0x90 => 'kae', 'kaeg', 'kaekk', 'kaegs', 'kaen', 'kaenj', 'kaenh', 'kaed', 'kael', 'kaelg', 'kaelm', 'kaelb', 'kaels', 'kaelt', 'kaelp', 'kaelh',
  0xA0 => 'kaem', 'kaeb', 'kaebs', 'kaes', 'kaess', 'kaeng', 'kaej', 'kaech', 'kaek', 'kaet', 'kaep', 'kaeh', 'kya', 'kyag', 'kyakk', 'kyags',
  0xB0 => 'kyan', 'kyanj', 'kyanh', 'kyad', 'kyal', 'kyalg', 'kyalm', 'kyalb', 'kyals', 'kyalt', 'kyalp', 'kyalh', 'kyam', 'kyab', 'kyabs', 'kyas',
  0xC0 => 'kyass', 'kyang', 'kyaj', 'kyach', 'kyak', 'kyat', 'kyap', 'kyah', 'kyae', 'kyaeg', 'kyaekk', 'kyaegs', 'kyaen', 'kyaenj', 'kyaenh', 'kyaed',
  0xD0 => 'kyael', 'kyaelg', 'kyaelm', 'kyaelb', 'kyaels', 'kyaelt', 'kyaelp', 'kyaelh', 'kyaem', 'kyaeb', 'kyaebs', 'kyaes', 'kyaess', 'kyaeng', 'kyaej', 'kyaech',
  0xE0 => 'kyaek', 'kyaet', 'kyaep', 'kyaeh', 'keo', 'keog', 'keokk', 'keogs', 'keon', 'keonj', 'keonh', 'keod', 'keol', 'keolg', 'keolm', 'keolb',
  0xF0 => 'keols', 'keolt', 'keolp', 'keolh', 'keom', 'keob', 'keobs', 'keos', 'keoss', 'keong', 'keoj', 'keoch', 'keok', 'keot', 'keop', 'keoh',
];
