<?php

/**
 * @file
 * Generic transliteration data for the PhpTransliteration class.
 */

$base = [
  0x00 => ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', ' ', '', '', '', '',
  0x10 => '-', '-', '-', '-', '-', '-', '||', '_', '\'', '\'', ',', '\'', '"', '"', ',,', '"',
  0x20 => '+', '++', '*', '*>', '.', '..', '...', '.', '
', '

', '', '', '', '', '', ' ',
  0x30 => '%0', '%00', '\'', '"', '\'\'\'', '`', '``', '```', '^', '<', '>', '*', '!!', '!?', '-', '_',
  0x40 => '-', '^', '***', '--', '/', '[', ']', '??', '?!', '!?', '7', 'PP', '(]', '[)', '*', NULL,
  0x50 => NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, ' ',
  0x60 => NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', '', '', '', '', '',
  0x70 => '0', '', '', '', '4', '5', '6', '7', '8', '9', '+', '-', '=', '(', ')', 'n',
  0x80 => '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '+', '-', '=', '(', ')', NULL,
  0x90 => NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
  0xA0 => 'CE', 'CL', 'Cr', 'Fr.', 'L.', 'mil', 'N', 'Pts', 'Rs', 'W', 'NS', 'D', 'EU', 'K', 'T', 'Dr',
  0xB0 => NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Rs', NULL, NULL, NULL, NULL, NULL, NULL,
  0xC0 => NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
  0xD0 => '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
  0xE0 => '', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
  0xF0 => NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
];
