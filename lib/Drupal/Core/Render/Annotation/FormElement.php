<?php

namespace Drupal\Core\Render\Annotation;

/**
 * Defines a form element plugin annotation object.
 *
 * See \Drupal\Core\Render\Element\FormElementInterface for more information
 * about form element plugins.
 *
 * Plugin Namespace: Element
 *
 * For a working example, see \Drupal\Core\Render\Element\Textfield.
 *
 * @see \Drupal\Core\Render\ElementInfoManager
 * @see \Drupal\Core\Render\Element\FormElementInterface
 * @see \Drupal\Core\Render\Element\FormElementBase
 * @see \Drupal\Core\Render\Annotation\RenderElement
 * @see plugin_api
 *
 * @ingroup theme_render
 *
 * @Annotation
 */
class FormElement extends RenderElement {

}
