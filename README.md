# Twig Tweak

The Twig Tweak module provides a Twig extension with some useful functions
and filters that can improve development experience.

Some functions and filters are built in the Twig Tweak module, while others are
simple wrappers to integrate Drupal functions and services into Twig (for
example: `format_size`, `drupal_view_result`).

## Requirements

This module requires no modules outside of Drupal core.

## Installation

- Install as you would normally install a contributed Drupal module. For further
information, see [Installing Drupal Modules](https://www.drupal.org/docs/extending-drupal/installing-drupal-modules).

## Links

- Project page: https://www.drupal.org/project/twig_tweak
- Twig home page: https://twig.symfony.com/
- Drupal Twig documentation: https://www.drupal.org/docs/theming-drupal/twig-in-drupal
