# Summary

The Coffee module helps you navigate the Drupal admin interface faster, inspired by <PERSON> and <PERSON> (macOS). By default, the management menu is included in the suggestions. Go to the configuration page to add addition menus to the search index.

For a full description of the module, visit the [project page](https://www.drupal.org/project/coffee).

To submit bug reports, feature suggestions, or track changes, visit the [issue queue](https://www.drupal.org/project/issues/coffee).

## Requirements

This module requires the following module:

- [Menu](https://www.drupal.org/project/menu)

## Installation

Install as you would normally install a contributed Drupal module. For further information, see the guide on [Installing Drupal Modules](https://www.drupal.org/docs/extending-drupal/installing-drupal-modules).

## Configuration

- Configure user permissions at `admin/people/permissions`:

  - **Access Coffee**:  
    Users with the "access coffee" permission can use the Coffee module.

- Configure which menus are included in the Coffee results at:  
  `admin/config/user-interface/coffee`

## Usage

Toggle Coffee using the keyboard shortcut:  
- `Alt + D`  
- `Alt + Shift + D` (in Opera)  
- `Alt + Ctrl + D` (in Windows Internet Explorer)

Type the first few characters of the task you want to perform. <PERSON> will attempt to find the correct result with as few characters as possible. For example, to navigate to the Appearance admin page, type `ap` and hit Enter.

To open the first result in a new window, press `Command + Enter` (on macOS) or `Ctrl + Enter` (on Windows).

If your search query returns multiple results, use the up/down arrow keys to select the desired option.

This works for all Drupal admin pages.  

If the Devel module is installed, Coffee will also include items generated by Devel. For example, typing `clear` will return the result `devel/cache/clear`.

## Coffee Commands

Coffee provides default commands that you can use:

- **add**: Quickly add a node of a specific content type.

## Coffee Hooks

You can define your own commands in your module using `hook_coffee_commands()`. See `coffee.api.php` for further documentation.

## Maintainers

- Michael Mol - [michaelmol](https://www.drupal.org/u/michaelmol)  
- Oliver Köhler - [Nebel54](https://www.drupal.org/u/nebel54)  
- Alli Price - [heylookalive](https://www.drupal.org/u/heylookalive)  
- Marco - [willzyx](https://www.drupal.org/u/willzyx)