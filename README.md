# Configuration log

This module can easily log any configuration change in Drupal 10 or 11
giving you a detailed record of all changes made to your site's settings.

For a full description of the module, visit the
[project page](https://www.drupal.org/project/config_log).

Submit bug reports and feature suggestions, or track changes in the
[issue queue](https://www.drupal.org/project/issues/config_log).


## Table of contents

- Requirements
- Installation
- Configuration
- Maintainers


## Requirements

This module requires no modules outside of Drupal core.


## Installation

Install as you would normally install a contributed Drupal module. For further
information, see
[Installing Drupal Modules](https://www.drupal.org/docs/extending-drupal/installing-drupal-modules).


## Configuration

1. Navigate to Administration > Extend and enable the module.
2. Navigate to Administration > Configuration > Development > Configuration Log for settings.
3. Select log destination. If none are selected, no destinations will be allowed.
4. Enter the configuration entity names to ignore, one per line.
5. Submit.


## Maintainers

- <PERSON><PERSON><PERSON> - [nagy.balint](https://www.drupal.org/u/nagybalint)
- <PERSON><PERSON> - [moshe weitzman](https://www.drupal.org/u/moshe-weitzman)
- <PERSON> <PERSON>olanin - [pwolanin](https://www.drupal.org/u/pwolanin)
- Stéphane Corlosquet - [scor](https://www.drupal.org/u/scor)
- Kris Vanderwater - [EclipseGc](https://www.drupal.org/u/eclipsegc)

**Supporting organizations:**

- [Agence Inovae](https://www.drupal.org/agence-inovae)
- [Webbtik.io](https://www.drupal.org/webbtikio)
