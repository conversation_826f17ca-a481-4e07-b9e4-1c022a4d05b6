services:
  entity_clone.settings.manager:
    class: <PERSON><PERSON><PERSON>\entity_clone\EntityCloneSettingsManager
    arguments: ['@entity_type.manager', '@entity_type.bundle.info', '@config.factory']
  entity_clone.route_subscriber:
    class: <PERSON><PERSON>al\entity_clone\Routing\RouteSubscriber
    arguments: ['@entity_type.manager']
    tags:
      - { name: event_subscriber }
  entity_clone.entity_type_subscriber:
    class: <PERSON><PERSON>al\entity_clone\EventSubscriber\EntityTypeSubscriber
    arguments: [ '@config.factory' ]
    tags:
      - { name: event_subscriber }
  entity_clone.service_provider:
    class: Drupal\entity_clone\Services\EntityCloneServiceProvider
    arguments: [ ]
  entity_clone.event_subscriber:
    class: Drupal\entity_clone\EventSubscriber\EntityCloneSubscriber
    arguments: ['@messenger']
    tags:
      - { name: event_subscriber }
  entity_clone.clonable_field:
    class: Drupal\entity_clone\EntityCloneClonableField
    arguments: ['@entity_type.manager']
