<?php

/**
 * @file
 * Views hooks.
 */

use <PERSON><PERSON><PERSON>\date_popup\DatePopup;
use <PERSON><PERSON>al\date_popup\DatePopupSearchApi;
use Drupal\date_popup\DatetimePopup;

/**
 * Implements hook_views_plugins_filter_alter().
 */
function date_popup_views_plugins_filter_alter(&$info) {
  $info['date']['class'] = DatePopup::class;
  if (isset($info['datetime'])) {
    $info['datetime']['class'] = DatetimePopup::class;
  }
  if (isset($info['search_api_date'])) {
    $info['search_api_date']['class'] = DatePopupSearchApi::class;
  }
}
