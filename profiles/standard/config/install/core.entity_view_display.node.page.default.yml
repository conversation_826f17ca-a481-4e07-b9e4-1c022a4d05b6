langcode: en
status: true
dependencies:
  config:
    - field.field.node.page.body
    - node.type.page
  module:
    - text
    - user
id: node.page.default
targetEntityType: node
bundle: page
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
  links:
    weight: 101
    region: content
hidden: {  }
