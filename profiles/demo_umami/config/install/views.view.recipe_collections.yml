langcode: en
status: true
dependencies:
  config:
    - taxonomy.vocabulary.tags
  module:
    - taxonomy
    - user
id: recipe_collections
label: 'Recipe Collections'
module: views
description: ''
tag: ''
base_table: taxonomy_term_field_data
base_field: tid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: 'Recipe collections'
      fields:
        name:
          id: name
          table: taxonomy_term_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: taxonomy_term
          entity_field: name
          plugin_id: term_name
          label: ''
          exclude: false
          alter:
            alter_text: false
            make_link: false
            absolute: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            trim: false
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          convert_spaces: false
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 16
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        name:
          id: name
          table: taxonomy_term_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: taxonomy_term
          entity_field: name
          plugin_id: standard
          order: ASC
          expose:
            label: ''
            field_identifier: name
          exposed: false
      arguments: {  }
      filters:
        vid:
          id: vid
          table: taxonomy_term_field_data
          field: vid
          entity_type: taxonomy_term
          entity_field: vid
          plugin_id: bundle
          value:
            tags: tags
          group: 1
          expose:
            operator_limit_selection: false
            operator_list: {  }
        default_langcode:
          id: default_langcode
          table: taxonomy_term_field_data
          field: default_langcode
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: taxonomy_term
          entity_field: default_langcode
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: grid_responsive
        options:
          grouping: {  }
          columns: 4
          cell_min_width: 100
          grid_gutter: 14
          alignment: vertical
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - user.permissions
      tags: {  }
  block:
    id: block
    display_title: Block
    display_plugin: block
    position: 1
    display_options:
      rendering_language: '***LANGUAGE_language_content***'
      display_extenders: {  }
      block_hide_empty: true
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - user.permissions
      tags: {  }
