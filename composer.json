{"name": "drupal/flexslider", "description": "FlexSlider 2 integration for Drupal", "type": "drupal-module", "license": "GPL-2.0-or-later", "authors": [{"name": "FlexSlider Module Contributors", "homepage": "https://www.drupal.org/node/1316346/committers", "role": "Contributors"}], "support": {"issues": "https://drupal.org/project/issues/flexslider", "source": "https://git.drupalcode.org/project/flexslider"}, "require": {}, "suggest": {"woothemes/flexslider": "~2.0"}, "extra": {"drupal-scaffold": {"gitignore": false, "allowed-packages": ["dropfort/dropfort_module_build"], "locations": {"web-root": "drupal-ignore/"}}}, "require-dev": {"dropfort/dropfort_module_build": "^2.0@beta"}, "config": {"allow-plugins": {"drupal/core-composer-scaffold": true, "dealerdirect/phpcodesniffer-composer-installer": true}}, "version": "3.0.0-alpha1"}