{"name": "drupal-composer/drupal-project", "description": "Project template for Drupal 8 projects with composer", "type": "project", "license": "GPL-2.0-or-later", "authors": [{"name": " ", "role": " "}], "repositories": [{"type": "package", "package": {"name": "fullcalendar/fullcalendar", "version": "3.10.0", "type": "drupal-library", "dist": {"url": "https://github.com/fullcalendar/fullcalendar/releases/download/v3.10.0/fullcalendar-3.10.0.zip", "type": "zip"}, "require": {"composer/installers": "^1.2.0"}}}, {"type": "composer", "url": "https://packages.drupal.org/8", "exclude": ["drupal/videojs"]}, {"type": "composer", "url": "https://asset-packagist.org"}, {"type": "git", "url": "https://git.drupalcode.org/issue/videojs-3363660.git"}, {"type": "package", "package": {"name": "garand/sticky", "version": "1.0.3", "type": "drupal-library", "dist": {"url": "https://github.com/garand/sticky/archive/master.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "northernco/ckeditor5-anchor-drupal", "version": "0.5.0", "type": "drupal-library", "dist": {"url": "https://registry.npmjs.org/@northernco/ckeditor5-anchor-drupal/-/ckeditor5-anchor-drupal-0.5.0.tgz", "type": "tar"}}}, {"type": "package", "package": {"name": "ckeditor/fakeobjects", "version": "4.16.1", "type": "drupal-library", "dist": {"url": "https://download.ckeditor.com/fakeobjects/releases/fakeobjects_4.16.1.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "ckeditor/link", "version": "4.16.1", "type": "drupal-library", "dist": {"url": "https://download.ckeditor.com/link/releases/link_4.16.1.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "filamentgroup/loadcss", "version": "2.0.1", "type": "drupal-library", "dist": {"url": "https://github.com/filamentgroup/loadCSS/archive/refs/heads/master.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "dinbror/blazy", "version": "1.8.2", "type": "drupal-library", "extra": {"installer-name": "blazy"}, "source": {"type": "git", "url": "https://github.com/dinbror/blazy", "reference": "1.8.2"}}}], "require": {"php": ">=5.6", "behat/mink": "^1.8", "behat/mink-selenium2-driver": "^1.0", "bower-asset/chosen": "^1.8", "bower-asset/colorbox": "^1.6", "bower-asset/flexslider": "^2.7", "bower-asset/fullcalendar": "^3.10.4", "bower-asset/slick-carousel": "^1.6.0", "ckeditor/fakeobjects": "^4.16", "ckeditor/link": "^4.16", "composer/installers": "^1.9", "cweagans/composer-patches": "^1.7", "dinbror/blazy": "^1.8", "dompdf/dompdf": "^2.0.3", "drupal/admin_toolbar": "^3.2", "drupal/adminimal_admin_toolbar": "1.x-dev@dev", "drupal/adminimal_theme": "^1.5", "drupal/advagg": "^6.0-alpha1", "drupal/anchor_link": "^3", "drupal/backup_migrate": "5.0.3", "drupal/bg_image_formatter": "^1.8", "drupal/blazy": "^2.0", "drupal/block_class": "^1.1", "drupal/bootstrap_barrio": "^5.5", "drupal/bootstrap_ce": "^2.0", "drupal/bulk_user_registration": "^2.0", "drupal/captcha": "^1.10", "drupal/ckeditor_accordion": "^2.0", "drupal/collapse_text": "^2.0", "drupal/color": "^1.0", "drupal/color_field": "^3.0", "drupal/colorbox": "^2.0", "drupal/colorbox_inline": "^2.0", "drupal/colorbox_load": "^1.0-rc2", "drupal/composer_deploy": "^1.6", "drupal/config_update": "^2.0-alpha3", "drupal/content_lock": "^2.0", "drupal/contentimport": "9.x-dev@dev", "drupal/cookies": "^1.0", "drupal/core": "^10", "drupal/core-composer-scaffold": "^9", "drupal/core-project-message": "^9", "drupal/core-recommended": "^10", "drupal/critical_css": "^1.17", "drupal/crop": "^2.1", "drupal/csv_serialization": "^3.0 || ^4.0", "drupal/ctools": "^3.1", "drupal/date_popup": "^1.1", "drupal/devel": "^5.1", "drupal/devel_mail_logger": "^2.0", "drupal/diff": "^1.0-rc2", "drupal/ds": "^3.2", "drupal/eck": "^2.0", "drupal/editor_advanced_link": "^2.1", "drupal/entity_clone": "^2.0-beta4", "drupal/entity_reference_revisions": "^1.6", "drupal/fakeobjects": "^1.2", "drupal/features": "^3.8", "drupal/field_defaults": "^2.0-alpha2", "drupal/field_group": "^3.0", "drupal/file_replace": "^1.2", "drupal/flexslider": "^3.0-alpha1", "drupal/focal_point": "^2.0", "drupal/google_analytics": "^4.0", "drupal/google_tag": "^2.0", "drupal/imce": "^3.0", "drupal/imce_rename_plugin": "^2.0", "drupal/inline_entity_form": "^1.0-rc1", "drupal/jquery_ui_accordion": "^2.0", "drupal/jquery_ui_draggable": "^2.0", "drupal/jquery_ui_spinner": "^2.0", "drupal/jquery_ui_tooltip": "^2.0", "drupal/libraries": "^4.0", "drupal/mailsystem": "^4.1", "drupal/masquerade": "^2.0-beta2", "drupal/menu_link_attributes": "^1.0", "drupal/metatag": "^1.19", "drupal/ng_lightbox": "^2.0@beta", "drupal/paragraphs": "^1.6", "drupal/pathauto": "^1.3", "drupal/quick_node_clone": "^1.11", "drupal/quickedit": "^1.0", "drupal/rabbit_hole": "^1.0@beta", "drupal/rdf": "^2.1", "drupal/recaptcha": "^3.0", "drupal/redirect": "^1.3", "drupal/schema_metatag": "^2.1", "drupal/scn": "^2.0", "drupal/search404": "^2.0.0", "drupal/simple_popup_blocks": "^3.1", "drupal/simple_sitemap": "^4.0", "drupal/slick": "^2.10", "drupal/slick_entityreference": "^2.0", "drupal/smart_trim": "2.0", "drupal/social_media": "^2.0", "drupal/sticky": "^2.0", "drupal/superfish": "^1.3", "drupal/symfony_mailer_lite": "^1.0", "drupal/tinypng": "^1.0-alpha3", "drupal/token": "^1.5", "drupal/twig_tweak": "^3.2", "drupal/vapn": "^3.0-alpha2", "drupal/video_embed_facebook": "^1.7", "drupal/video_embed_field": "^2.0", "drupal/video_embed_html5": "^2.0", "drupal/videojs": "dev-3363660-drupal-10-compatibility", "drupal/view_unpublished": "^1.0", "drupal/views_accordion": "^2.0", "drupal/views_data_export": "^1.0", "drupal/views_entity_form_field": "^1.0-beta10", "drupal/views_templates": "^1.0-alpha1", "drupal/webform": "^6.2-beta6", "drush/drush": "^12.5", "filamentgroup/loadcss": "^2.0", "freshmail/rest-api": "^2.0", "fullcalendar/fullcalendar": "3.10.0", "garand/sticky": "^1.0", "getresponse/sdk-php": "^3.0", "northernco/ckeditor5-anchor-drupal": "^0.5.0", "oomphinc/composer-installers-extender": "^2.0", "pay-now/paynow-php-sdk": "^2.2", "phpunit/phpunit": "^9.6.13", "symfony/http-client": "^5.4", "symfony/mime": "^6.4", "symfony/polyfill-php73": "^1.23", "symfony/yaml": "^6.4", "vlucas/phpdotenv": "^2.4", "webflo/drupal-finder": "^1.0.0", "webmozart/path-util": "^2.3"}, "conflict": {"drupal/drupal": "*"}, "require-dev": {"drupal/core-dev": "^10"}, "minimum-stability": "dev", "prefer-stable": true, "config": {"preferred-install": "source", "sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "composer/installers": true, "cweagans/composer-patches": true, "drupal/console-extend-plugin": true, "drupal/core-composer-scaffold": true, "drupal/core-project-message": true, "oomphinc/composer-installers-extender": true, "php-http/discovery": true, "phpstan/extension-installer": true, "wikimedia/composer-merge-plugin": true, "tbachert/spi": true}}, "autoload": {"classmap": ["scripts/composer/ScriptHandler.php"], "files": ["load.environment.php"]}, "scripts": {"pre-install-cmd": "DrupalProject\\composer\\ScriptHandler::checkComposerVersion", "pre-update-cmd": "DrupalProject\\composer\\ScriptHandler::checkComposerVersion", "post-install-cmd": "DrupalProject\\composer\\ScriptHandler::createRequiredFiles", "post-update-cmd": "DrupalProject\\composer\\ScriptHandler::createRequiredFiles"}, "extra": {"composer-exit-on-patch-failure": false, "patchLevel": {"drupal/core": "-p2"}, "installer-types": ["npm-asset", "bower-asset", "drupal-library"], "installer-paths": {"web/core": ["type:drupal-core"], "web/libraries/slick": ["bower-asset/slick-carousel"], "web/libraries/{$name}": ["type:drupal-library", "type:npm-asset", "type:bower-asset"], "web/modules/contrib/{$name}": ["type:drupal-module"], "web/profiles/contrib/{$name}": ["type:drupal-profile"], "web/themes/contrib/{$name}": ["type:drupal-theme"], "drush/Commands/{$name}": ["type:drupal-drush"]}, "drupal-scaffold": {"locations": {"web-root": "web/"}}, "patches": {"drupal/advagg": {"fix error in ckeditor": "https://www.drupal.org/files/issues/2022-09-19/3310388-2.patch"}, "drupal/video_embed_facebook": {"patch for fb urls format": "https://www.drupal.org/files/issues/2024-02-01/3364389-facebook-url.patch"}, "drupal/tinypng": {"drupal 10.3 bug": "https://www.drupal.org/files/issues/2024-06-26/route_scheme-3457384-4.patch"}}}}