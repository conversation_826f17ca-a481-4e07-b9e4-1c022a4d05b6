langcode: en
status: true
dependencies: {  }
id: flexslider_default_thumbnail_slider
label: 'Thumbnail Slider'
options:
  animation: slide
  animationSpeed: 600
  direction: horizontal
  slideshow: false
  easing: swing
  smoothHeight: true
  reverse: false
  slideshowSpeed: 7000
  animationLoop: false
  randomize: false
  startAt: 0
  itemWidth: 210
  itemMargin: 5
  minItems: 0
  maxItems: 0
  move: 0
  directionNav: true
  controlNav: '0'
  thumbCaptions: false
  thumbCaptionsBoth: false
  keyboard: true
  multipleKeyboard: false
  mousewheel: false
  touch: true
  prevText: Previous
  nextText: Next
  namespace: flex-
  selector: '.slides > li'
  sync: ''
  asNavFor: '#flexslider-1'
  initDelay: 0
  useCSS: true
  video: false
  pausePlay: false
  pauseText: Pause
  playText: Play
  pauseOnAction: true
  pauseOnHover: false
  controlsContainer: .flex-control-nav-container
  manualControls: ''
