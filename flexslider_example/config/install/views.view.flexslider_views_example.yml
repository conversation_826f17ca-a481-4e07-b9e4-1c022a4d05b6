langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_flexslider_example_image
    - node.type.flexslider_example
    - system.menu.flexslider-example
  module:
    - flexslider_views
    - image
    - node
    - user
id: flexslider_views_example
label: 'FlexSlider Views Example'
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
core: 8.x
display:
  default:
    display_plugin: default
    id: default
    display_title: Master
    position: 0
    display_options:
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: none
        options: {  }
      query:
        type: views_query
        options:
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_comment: ''
          query_tags: {  }
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        type: some
        options:
          items_per_page: 4
          offset: 0
      style:
        type: flexslider
        options:
          grouping: {  }
          flexslider: null
          optionset: flexslider_default_slider_thumbnail_controlnav
          captionfield: ''
          id: ''
      row:
        type: fields
        options:
          default_field_elements: false
          inline: {  }
          separator: ''
          hide_empty: true
      fields:
        field_flexslider_example_image:
          id: field_flexslider_example_image
          table: node__field_flexslider_example_image
          field: field_flexslider_example_image
          relationship: none
          group_type: group
          admin_label: ''
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: image
          settings:
            image_style: flexslider_full
            image_link: ''
          group_column: ''
          group_columns: {  }
          group_rows: false
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          plugin_id: field
      filters:
        status:
          value: true
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            flexslider_example: flexslider_example
          entity_type: node
          entity_field: type
          plugin_id: bundle
      sorts:
        created:
          id: created
          table: node_field_data
          field: created
          order: DESC
          entity_type: node
          entity_field: created
          plugin_id: date
          relationship: none
          group_type: group
          admin_label: ''
          exposed: false
          expose:
            label: ''
          granularity: second
      title: 'Basic Slider'
      header: {  }
      footer: {  }
      empty: {  }
      relationships: {  }
      arguments: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_flexslider_example_image'
  embed_1:
    display_plugin: embed
    id: embed_1
    display_title: 'Thumbnail Controls'
    position: 1
    display_options:
      display_extenders: {  }
      defaults:
        fields: true
        title: false
        style: false
        row: false
        pager: false
      display_description: 'Controls another FlexSlider instance'
      title: ''
      style:
        type: flexslider
        options:
          grouping: {  }
          flexslider: null
          optionset: flexslider_default_thumbnail_slider
          captionfield: ''
          id: ''
      row:
        type: fields
        options:
          default_field_elements: false
          inline: {  }
          separator: ''
          hide_empty: true
      pager:
        type: some
        options:
          items_per_page: 10
          offset: 0
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_flexslider_example_image'
  page_1:
    display_plugin: page
    id: page_1
    display_title: 'Basic Slider'
    position: 1
    display_options:
      display_extenders: {  }
      path: flexslider-views-example
      menu:
        type: normal
        title: 'Basic Slider (Views)'
        menu_name: flexslider-example
      fields:
        field_flexslider_example_image:
          id: field_flexslider_example_image
          table: node__field_flexslider_example_image
          field: field_flexslider_example_image
          relationship: none
          group_type: group
          admin_label: ''
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: image
          settings:
            image_style: flexslider_full
            image_link: ''
          group_column: ''
          group_columns: {  }
          group_rows: false
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          plugin_id: field
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: false
            ellipsis: false
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: p
          element_class: flex-caption
          element_label_type: '0'
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: '0'
          element_wrapper_class: ''
          element_default_classes: false
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          entity_type: node
          entity_field: title
          plugin_id: field
      defaults:
        fields: false
        style: false
        row: false
      display_description: ''
      style:
        type: flexslider
        options:
          grouping: {  }
          flexslider: null
          optionset: default
          captionfield: title
          id: ''
      row:
        type: fields
        options:
          default_field_elements: false
          inline: {  }
          separator: ''
          hide_empty: true
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_flexslider_example_image'
  page_2:
    display_plugin: page
    id: page_2
    display_title: 'Slide with thumbnail controls'
    position: 1
    display_options:
      display_extenders: {  }
      path: flexslider-views-example-with-thumbnail-controls
      menu:
        type: normal
        title: 'slider w/thumbnail controlnav pattern (Views)'
        description: ''
        expanded: false
        parent: ''
        weight: 0
        context: '0'
        menu_name: flexslider-example
      fields:
        field_flexslider_example_image:
          id: field_flexslider_example_image
          table: node__field_flexslider_example_image
          field: field_flexslider_example_image
          relationship: none
          group_type: group
          admin_label: ''
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: image
          settings:
            image_style: flexslider_full
            image_link: ''
          group_column: ''
          group_columns: {  }
          group_rows: false
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          plugin_id: field
      defaults:
        fields: false
        title: false
        style: false
        row: false
        footer: true
      display_description: ''
      title: 'slider w/thumbnail controlnav pattern'
      style:
        type: flexslider
        options:
          grouping: {  }
          flexslider: null
          optionset: flexslider_default_slider_thumbnail_controlnav
          captionfield: ''
          id: ''
      row:
        type: fields
        options:
          default_field_elements: false
          inline: {  }
          separator: ''
          hide_empty: true
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_flexslider_example_image'
  page_3:
    display_plugin: page
    id: page_3
    display_title: 'Slide with thumbnail slider'
    position: 1
    display_options:
      display_extenders: {  }
      path: flexslider-views-example-with-thumbnail-slider
      menu:
        type: normal
        title: 'slider w/thumbnail slider (Views)'
        description: ''
        expanded: false
        parent: ''
        weight: 0
        context: '0'
        menu_name: flexslider-example
      defaults:
        fields: true
        title: false
        style: false
        row: false
        pager: false
        footer: false
      display_description: 'Slider controlled by another instance of FlexSlider'
      title: 'Slider w/thumbnail slider'
      style:
        type: flexslider
        options:
          grouping: {  }
          flexslider: null
          optionset: flexslider_default_slider_w_thumbnail_slider
          captionfield: ''
          id: ''
      row:
        type: fields
        options:
          default_field_elements: false
          inline: {  }
          separator: ''
          hide_empty: false
      footer:
        view:
          id: view
          table: views
          field: view
          relationship: none
          group_type: group
          admin_label: ''
          empty: false
          view_to_insert: 'flexslider_views_example:embed_1'
          inherit_arguments: false
          plugin_id: view
      pager:
        type: some
        options:
          items_per_page: 10
          offset: 0
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_flexslider_example_image'
  page_4:
    display_plugin: page
    id: page_4
    display_title: 'Basic Carousel'
    position: 1
    display_options:
      display_extenders: {  }
      defaults:
        fields: true
        title: false
        style: false
        row: false
        pager: false
      display_description: ''
      title: 'Basic Carousel'
      style:
        type: flexslider
        options:
          grouping: {  }
          flexslider: null
          optionset: flexslider_default_basic_carousel
          captionfield: ''
          id: ''
      row:
        type: fields
        options:
          default_field_elements: false
          inline: {  }
          separator: ''
          hide_empty: false
      path: flexslider-views-example-basic-carousel
      menu:
        type: normal
        title: 'basic carousel (Views)'
        description: ''
        expanded: false
        parent: ''
        weight: 0
        context: '0'
        menu_name: flexslider-example
      pager:
        type: some
        options:
          items_per_page: 10
          offset: 0
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_flexslider_example_image'
  page_5:
    display_plugin: page
    id: page_5
    display_title: 'Carousel with min and max ranges'
    position: 1
    display_options:
      display_extenders: {  }
      defaults:
        fields: true
        title: false
        style: false
        row: false
        pager: false
      display_description: ''
      title: 'carousel with min and max ranges'
      style:
        type: flexslider
        options:
          grouping: {  }
          flexslider: null
          optionset: flexslider_carousel_with_min_and_max_ranges
          captionfield: ''
          id: ''
      row:
        type: fields
        options:
          default_field_elements: false
          inline: {  }
          separator: ''
          hide_empty: false
      path: flexslider-views-example-carousel-min-max
      menu:
        type: normal
        title: 'carousel with min and max ranges (Views)'
        description: ''
        expanded: false
        parent: ''
        weight: 0
        context: '0'
        menu_name: flexslider-example
      pager:
        type: some
        options:
          items_per_page: 10
          offset: 0
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_flexslider_example_image'
  page_6:
    display_plugin: page
    id: page_6
    display_title: 'Basic Slider with Thumbnail Caption'
    position: 1
    display_options:
      display_extenders: {  }
      path: flexslider-views-example-thumbnail-captions
      menu:
        type: normal
        title: 'Thumbnail Captions (Views)'
        description: 'Basic slider with thumbnail captions'
        expanded: false
        parent: ''
        weight: 0
        context: '0'
        menu_name: flexslider-example
      fields:
        field_flexslider_example_image:
          id: field_flexslider_example_image
          table: node__field_flexslider_example_image
          field: field_flexslider_example_image
          relationship: none
          group_type: group
          admin_label: ''
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: image
          settings:
            image_style: flexslider_full
            image_link: ''
          group_column: ''
          group_columns: {  }
          group_rows: false
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          plugin_id: field
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: false
            ellipsis: false
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: '0'
          element_class: ''
          element_label_type: '0'
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: '0'
          element_wrapper_class: ''
          element_default_classes: false
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          entity_type: node
          entity_field: title
          plugin_id: field
      defaults:
        fields: false
        style: false
        row: false
        title: false
      display_description: 'To use thumbnail captions, use the "caption field" option in the style settings.'
      style:
        type: flexslider
        options:
          grouping: {  }
          flexslider: null
          optionset: flexslider_default_thumbnail_captions
          captionfield: title
          id: ''
      row:
        type: fields
        options:
          default_field_elements: false
          inline: {  }
          separator: ''
          hide_empty: true
      title: 'Basic Slider with Thumbnail captions'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_flexslider_example_image'
