langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_flexslider_example_slidesh
    - node.type.flexslider_example
  module:
    - image
id: node.flexslider_example.field_flexslider_example_slidesh
field_name: field_flexslider_example_slidesh
entity_type: node
bundle: flexslider_example
label: 'FlexSlider Example Slideshow'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  file_directory: flexslider_example
  file_extensions: 'png gif jpg jpeg'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: false
  alt_field_required: false
  title_field: true
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
  handler: 'default:file'
  handler_settings: {  }
field_type: image
