!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{"Code block":"Code block","Entering %0 code snippet":"Entering %0 code snippet","Entering code snippet":"Entering code snippet","Insert code block":"Insert code block","Leaving %0 code snippet":"Leaving %0 code snippet","Leaving code snippet":"Leaving code snippet","Plain text":"Plain text"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={535:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var o=n(758),i=n.n(o),r=n(935),c=n.n(r)()(i());c.push([e.id,".ck-content pre{background:hsla(0,0%,78%,.3);border:1px solid #c4c4c4;border-radius:2px;color:#353535;direction:ltr;font-style:normal;min-width:200px;padding:1em;tab-size:4;text-align:left;white-space:pre-wrap}.ck-content pre code{background:unset;border-radius:0;padding:0}.ck.ck-editor__editable pre{position:relative}.ck.ck-editor__editable pre[data-language]:after{content:attr(data-language);position:absolute}:root{--ck-color-code-block-label-background:#757575}.ck.ck-editor__editable pre[data-language]:after{background:var(--ck-color-code-block-label-background);color:#fff;font-family:var(--ck-font-face);font-size:10px;line-height:16px;padding:var(--ck-spacing-tiny) var(--ck-spacing-medium);right:10px;top:-1px;white-space:nowrap}.ck.ck-code-block-dropdown .ck-dropdown__panel{max-height:250px;overflow-x:hidden;overflow-y:auto}",""]);const s=c},935:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n="",o=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),o&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),o&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n})).join("")},t.i=function(e,n,o,i,r){"string"==typeof e&&(e=[[null,e,void 0]]);var c={};if(o)for(var s=0;s<this.length;s++){var a=this[s][0];null!=a&&(c[a]=!0)}for(var l=0;l<e.length;l++){var u=[].concat(e[l]);o&&c[u[0]]||(void 0!==r&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=r),n&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=n):u[2]=n),i&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=i):u[4]="".concat(i)),t.push(u))}},t}},758:e=>{"use strict";e.exports=function(e){return e[1]}},591:e=>{"use strict";var t=[];function n(e){for(var n=-1,o=0;o<t.length;o++)if(t[o].identifier===e){n=o;break}return n}function o(e,o){for(var r={},c=[],s=0;s<e.length;s++){var a=e[s],l=o.base?a[0]+o.base:a[0],u=r[l]||0,d="".concat(l," ").concat(u);r[l]=u+1;var g=n(d),p={css:a[1],media:a[2],sourceMap:a[3],supports:a[4],layer:a[5]};if(-1!==g)t[g].references++,t[g].updater(p);else{var f=i(p,o);o.byIndex=s,t.splice(s,0,{identifier:d,updater:f,references:1})}c.push(d)}return c}function i(e,t){var n=t.domAPI(t);n.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,i){var r=o(e=e||[],i=i||{});return function(e){e=e||[];for(var c=0;c<r.length;c++){var s=n(r[c]);t[s].references--}for(var a=o(e,i),l=0;l<r.length;l++){var u=n(r[l]);0===t[u].references&&(t[u].updater(),t.splice(u,1))}r=a}}},128:e=>{"use strict";var t={};e.exports=function(e,n){var o=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!o)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");o.appendChild(n)}},51:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},21:e=>{"use strict";e.exports=function(e,t){Object.keys(t).forEach((function(n){e.setAttribute(n,t[n])}))}},639:e=>{"use strict";var t,n=(t=[],function(e,n){return t[e]=n,t.filter(Boolean).join("\n")});function o(e,t,o,i){var r;if(o)r="";else{r="",i.supports&&(r+="@supports (".concat(i.supports,") {")),i.media&&(r+="@media ".concat(i.media," {"));var c=void 0!==i.layer;c&&(r+="@layer".concat(i.layer.length>0?" ".concat(i.layer):""," {")),r+=i.css,c&&(r+="}"),i.media&&(r+="}"),i.supports&&(r+="}")}if(e.styleSheet)e.styleSheet.cssText=n(t,r);else{var s=document.createTextNode(r),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(s,a[t]):e.appendChild(s)}}var i={singleton:null,singletonCounter:0};e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=i.singletonCounter++,n=i.singleton||(i.singleton=e.insertStyleElement(e));return{update:function(e){o(n,t,!1,e)},remove:function(e){o(n,t,!0,e)}}}},331:(e,t,n)=>{e.exports=n(237)("./src/clipboard.js")},782:(e,t,n)=>{e.exports=n(237)("./src/core.js")},783:(e,t,n)=>{e.exports=n(237)("./src/engine.js")},507:(e,t,n)=>{e.exports=n(237)("./src/enter.js")},311:(e,t,n)=>{e.exports=n(237)("./src/ui.js")},584:(e,t,n)=>{e.exports=n(237)("./src/utils.js")},237:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function n(o){var i=t[o];if(void 0!==i)return i.exports;var r=t[o]={id:o,exports:{}};return e[o](r,r.exports,n),r.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};(()=>{"use strict";n.r(o),n.d(o,{CodeBlock:()=>V,CodeBlockEditing:()=>x,CodeBlockUI:()=>j});var e=n(782),t=n(507),i=n(783),r=n(331),c=n(584);function s(e){const t=e.t,n=e.config.get("codeBlock.languages");for(const e of n)"Plain text"===e.label&&(e.label=t("Plain text")),void 0===e.class&&(e.class=`language-${e.language}`);return n}function a(e,t,n){const o={};for(const i of e)if("class"===t){o[i[t].split(" ").shift()]=i[n]}else o[i[t]]=i[n];return o}function l(e){return e.data.match(/^(\s*)/)[0]}function u(e){const t=e.document.selection,n=[];if(t.isCollapsed)return[t.anchor];const o=t.getFirstRange().getWalker({ignoreElementEnd:!0,direction:"backward"});for(const{item:t}of o){let o=t.is("$textProxy")?t.textNode:t;const i=o.parent;if(!i.is("element","codeBlock")||o.is("element","softBreak"))continue;for(;o.previousSibling&&!o.previousSibling.is("element","softBreak");)o=o.previousSibling;const r=o.is("$text")?o.startOffset+l(o).length:o.startOffset,c=e.createPositionAt(i,r);n.every((e=>!e.isEqual(c)))&&n.push(c)}return n}function d(e){const t=(0,c.first)(e.getSelectedBlocks());return!!t&&t.is("element","codeBlock")}function g(e,t){return!t.is("rootElement")&&!e.isLimit(t)&&e.checkChild(t.parent,"codeBlock")}function p(e,t,n,o){const i=a(t,"language","label"),r=n.getAttribute("language");if(r in i){const t=i[r];return e("enter"===o?"Entering %0 code snippet":"Leaving %0 code snippet",t)}return e("enter"===o?"Entering code snippet":"Leaving code snippet")}function f(e,t){for(e.textNode&&(e=t.createPositionBefore(e.textNode));e.nodeBefore&&!e.nodeBefore.is("element","softBreak");)e=t.createPositionBefore(e.nodeBefore);const n=e.nodeAfter;return n&&n.is("$text")?n:null}class m extends e.Command{constructor(e){super(e),this._lastLanguage=null}refresh(){this.value=this._getValue(),this.isEnabled=this._checkEnabled()}execute(e={}){const t=this.editor,n=t.model,o=n.document.selection,i=s(t)[0],r=Array.from(o.getSelectedBlocks()),c=null==e.forceValue?!this.value:e.forceValue,a=function(e,t,n){if(e.language)return e.language;if(e.usePreviousLanguageChoice&&t)return t;return n}(e,this._lastLanguage,i.language);n.change((e=>{c?this._applyCodeBlock(e,r,a):this._removeCodeBlock(e,r)}))}_getValue(){const e=this.editor.model.document.selection,t=(0,c.first)(e.getSelectedBlocks());return!!!(!t||!t.is("element","codeBlock"))&&t.getAttribute("language")}_checkEnabled(){if(this.value)return!0;const e=this.editor.model.document.selection,t=this.editor.model.schema,n=(0,c.first)(e.getSelectedBlocks());return!!n&&g(t,n)}_applyCodeBlock(e,t,n){this._lastLanguage=n;const o=this.editor.model.schema,i=t.filter((e=>g(o,e)));for(const t of i)e.rename(t,"codeBlock"),e.setAttribute("language",n,t),o.removeDisallowedAttributes([t],e),Array.from(t.getChildren()).filter((e=>!o.checkChild(t,e))).forEach((t=>e.remove(t)));i.reverse().forEach(((t,n)=>{const o=i[n+1];t.previousSibling===o&&(e.appendElement("softBreak",o),e.merge(e.createPositionBefore(t)))}))}_removeCodeBlock(e,t){const n=t.filter((e=>e.is("element","codeBlock")));for(const t of n){const n=e.createRangeOn(t);for(const t of Array.from(n.getItems()).reverse())if(t.is("element","softBreak")&&t.parent.is("element","codeBlock")){const{position:n}=e.split(e.createPositionBefore(t)),o=n.nodeAfter;e.rename(o,"paragraph"),e.removeAttribute("language",o),e.remove(t)}e.rename(t,"paragraph"),e.removeAttribute("language",t)}}}class h extends e.Command{constructor(e){super(e),this._indentSequence=e.config.get("codeBlock.indentSequence")}refresh(){this.isEnabled=this._checkEnabled()}execute(){const e=this.editor.model;e.change((t=>{const n=u(e);for(const o of n){const n=t.createText(this._indentSequence);e.insertContent(n,o)}}))}_checkEnabled(){return!!this._indentSequence&&d(this.editor.model.document.selection)}}class b extends e.Command{constructor(e){super(e),this._indentSequence=e.config.get("codeBlock.indentSequence")}refresh(){this.isEnabled=this._checkEnabled()}execute(){const e=this.editor.model;e.change((()=>{const t=u(e);for(const n of t){const t=v(e,n,this._indentSequence);t&&e.deleteContent(e.createSelection(t))}}))}_checkEnabled(){if(!this._indentSequence)return!1;const e=this.editor.model;return!!d(e.document.selection)&&u(e).some((t=>v(e,t,this._indentSequence)))}}function v(e,t,n){const o=f(t,e);if(!o)return null;const i=l(o),r=i.lastIndexOf(n);if(r+n.length!==i.length)return null;if(-1===r)return null;const{parent:c,startOffset:s}=o;return e.createRange(e.createPositionAt(c,s+r),e.createPositionAt(c,s+r+n.length))}function k(e,t,n=!1){const o=a(t,"language","class"),i=a(t,"language","label");return(t,r,c)=>{const{writer:s,mapper:a,consumable:l}=c;if(!l.consume(r.item,"insert"))return;const u=r.item.getAttribute("language"),d=a.toViewPosition(e.createPositionBefore(r.item)),g={};n&&(g["data-language"]=i[u],g.spellcheck="false");const p=o[u]?{class:o[u]}:void 0,f=s.createContainerElement("code",p),m=s.createContainerElement("pre",g,f);s.insert(d,m),a.bindElements(r.item,f)}}const B="paragraph";class x extends e.Plugin{static get pluginName(){return"CodeBlockEditing"}static get isOfficialPlugin(){return!0}static get requires(){return[t.ShiftEnter]}constructor(e){super(e),e.config.define("codeBlock",{languages:[{language:"plaintext",label:"Plain text"},{language:"c",label:"C"},{language:"cs",label:"C#"},{language:"cpp",label:"C++"},{language:"css",label:"CSS"},{language:"diff",label:"Diff"},{language:"html",label:"HTML"},{language:"java",label:"Java"},{language:"javascript",label:"JavaScript"},{language:"php",label:"PHP"},{language:"python",label:"Python"},{language:"ruby",label:"Ruby"},{language:"typescript",label:"TypeScript"},{language:"xml",label:"XML"}],indentSequence:"\t"})}init(){const e=this.editor,t=e.model.schema,n=e.model,o=e.editing.view,c=s(e);e.commands.add("codeBlock",new m(e)),e.commands.add("indentCodeBlock",new h(e)),e.commands.add("outdentCodeBlock",new b(e)),this.listenTo(o.document,"tab",((t,n)=>{const o=n.shiftKey?"outdentCodeBlock":"indentCodeBlock";e.commands.get(o).isEnabled&&(e.execute(o),n.stopPropagation(),n.preventDefault(),t.stop())}),{context:"pre"}),t.register("codeBlock",{allowWhere:"$block",allowChildren:"$text",disallowChildren:"$inlineObject",allowAttributes:["language"],allowAttributesOf:"$listItem",isBlock:!0}),t.addAttributeCheck(((e,n)=>{const o=e.getItem(e.length-2);if(t.getAttributeProperties(n).isFormatting&&o&&"codeBlock"==o.name)return!1})),e.editing.downcastDispatcher.on("insert:codeBlock",k(n,c,!0)),e.data.downcastDispatcher.on("insert:codeBlock",k(n,c)),e.data.downcastDispatcher.on("insert:softBreak",function(e){return(t,n,o)=>{if("codeBlock"!==n.item.parent.name)return;const{writer:i,mapper:r,consumable:c}=o;if(!c.consume(n.item,"insert"))return;const s=r.toViewPosition(e.createPositionBefore(n.item));i.insert(s,i.createText("\n"))}}(n),{priority:"high"}),e.data.upcastDispatcher.on("element:code",function(e,t){const n=a(t,"class","language"),o=t[0].language;return(e,t,i)=>{const r=t.viewItem,c=r.parent;if(!c||!c.is("element","pre"))return;if(t.modelCursor.findAncestor("codeBlock"))return;const{consumable:s,writer:a}=i;if(!s.test(r,{name:!0}))return;const l=a.createElement("codeBlock"),u=[...r.getClassNames()];u.length||u.push("");for(const e of u){const t=n[e];if(t){a.setAttribute("language",t,l);break}}l.hasAttribute("language")||a.setAttribute("language",o,l),i.convertChildren(r,l),i.safeInsert(l,t.modelCursor)&&(s.consume(r,{name:!0}),i.updateConversionResult(l,t))}}(0,c)),e.data.upcastDispatcher.on("text",((e,t,{consumable:n,writer:o})=>{let i=t.modelCursor;if(!n.test(t.viewItem))return;if(!i.findAncestor("codeBlock"))return;n.consume(t.viewItem);const r=t.viewItem.data.split("\n").map((e=>o.createText(e))),c=r[r.length-1];for(const e of r)if(o.insert(e,i),i=i.getShiftedBy(e.offsetSize),e!==c){const e=o.createElement("softBreak");o.insert(e,i),i=o.createPositionAfter(e)}t.modelRange=o.createRange(t.modelCursor,i),t.modelCursor=i})),e.data.upcastDispatcher.on("element:pre",((e,t,{consumable:n})=>{const o=t.viewItem;if(o.findAncestor("pre"))return;const i=Array.from(o.getChildren()),r=i.find((e=>e.is("element","code")));if(r)for(const e of i)e!==r&&e.is("$text")&&n.consume(e,{name:!0})}),{priority:"high"}),this.listenTo(e.editing.view.document,"clipboardInput",((t,o)=>{let r=n.createRange(n.document.selection.anchor);if(o.targetRanges&&(r=e.editing.mapper.toModelRange(o.targetRanges[0])),!r.start.parent.is("element","codeBlock"))return;const c=o.dataTransfer.getData("text/plain"),s=new i.UpcastWriter(e.editing.view.document);o.content=function(e,t){const n=e.createDocumentFragment(),o=t.split("\n"),i=o.reduce(((t,n,i)=>(t.push(n),i<o.length-1&&t.push(e.createElement("br")),t)),[]);return e.appendChild(i,n),n}(s,c)})),e.plugins.has("ClipboardPipeline")&&e.plugins.get(r.ClipboardPipeline).on("contentInsertion",((n,o)=>{const i=e.model,r=i.document.selection;r.anchor.parent.is("element","codeBlock")&&i.change((e=>{const n=e.createRangeIn(o.content);for(const o of[...n.getItems()])o.is("node")&&!t.checkChild(r.anchor,o)&&e.remove(o)}))})),this.listenTo(n,"getSelectedContent",((e,[o])=>{const i=o.anchor;!o.isCollapsed&&i.parent.is("element","codeBlock")&&i.hasSameParentAs(o.focus)&&n.change((n=>{const r=e.return;if(i.parent.is("element")&&(r.childCount>1||o.containsEntireContent(i.parent))){const t=n.createElement("codeBlock",i.parent.getAttributes());n.append(r,t);const o=n.createDocumentFragment();return n.append(t,o),void(e.return=o)}const c=r.getChild(0);t.checkAttribute(c,"code")&&n.setAttribute("code",!0,c)}))}))}afterInit(){const e=this.editor,t=e.commands,n=t.get("indent"),o=t.get("outdent");n&&n.registerChildCommand(t.get("indentCodeBlock"),{priority:"highest"}),o&&o.registerChildCommand(t.get("outdentCodeBlock")),this.listenTo(e.editing.view.document,"enter",((t,n)=>{e.model.document.selection.getLastPosition().parent.is("element","codeBlock")&&(function(e,t){const n=e.model,o=n.document,i=e.editing.view,r=o.selection.getLastPosition(),c=r.nodeAfter;if(t||!o.selection.isCollapsed||!r.isAtStart)return!1;if(!C(c))return!1;return e.model.change((t=>{e.execute("enter");const n=o.selection.anchor.parent.previousSibling;t.rename(n,B),t.setSelection(n,"in"),e.model.schema.removeDisallowedAttributes([n],t),t.remove(c)})),i.scrollToTheSelection(),!0}(e,n.isSoft)||function(e,t){const n=e.model,o=n.document,i=e.editing.view,r=o.selection.getLastPosition(),c=r.nodeBefore;let s;if(t||!o.selection.isCollapsed||!r.isAtEnd||!c||!c.previousSibling)return!1;if(C(c)&&C(c.previousSibling))s=n.createRange(n.createPositionBefore(c.previousSibling),n.createPositionAfter(c));else if(w(c)&&C(c.previousSibling)&&C(c.previousSibling.previousSibling))s=n.createRange(n.createPositionBefore(c.previousSibling.previousSibling),n.createPositionAfter(c));else{if(!(w(c)&&C(c.previousSibling)&&w(c.previousSibling.previousSibling)&&c.previousSibling.previousSibling&&C(c.previousSibling.previousSibling.previousSibling)))return!1;s=n.createRange(n.createPositionBefore(c.previousSibling.previousSibling.previousSibling),n.createPositionAfter(c))}return e.model.change((t=>{t.remove(s),e.execute("enter");const n=o.selection.anchor.parent;t.rename(n,B),e.model.schema.removeDisallowedAttributes([n],t)})),i.scrollToTheSelection(),!0}(e,n.isSoft)||function(e){const t=e.model,n=t.document;let o;const i=f(n.selection.getLastPosition(),t);i&&i.is("$text")&&(o=l(i));e.model.change((t=>{e.execute("shiftEnter"),o&&t.insertText(o,n.selection.anchor)}))}(e),n.preventDefault(),t.stop())}),{context:"pre"}),this._initAriaAnnouncements()}_initAriaAnnouncements(){const{model:e,ui:t,t:n}=this.editor,o=s(this.editor);let i=null;e.document.selection.on("change:range",(()=>{const r=e.document.selection.focus.parent;t&&i!==r&&r.is("element")&&(i&&i.is("element","codeBlock")&&t.ariaLiveAnnouncer.announce(p(n,o,i,"leave")),r.is("element","codeBlock")&&t.ariaLiveAnnouncer.announce(p(n,o,r,"enter")),i=r)}))}}function w(e){return e&&e.is("$text")&&!e.data.match(/\S/)}function C(e){return e&&e.is("element","softBreak")}var S=n(311),y=n(591),A=n.n(y),E=n(639),P=n.n(E),_=n(128),L=n.n(_),I=n(21),T=n.n(I),O=n(51),D=n.n(O),M=n(535),R={attributes:{"data-cke":!0}};R.setAttributes=T(),R.insert=L().bind(null,"head"),R.domAPI=P(),R.insertStyleElement=D();A()(M.A,R);M.A&&M.A.locals&&M.A.locals;class j extends e.Plugin{static get pluginName(){return"CodeBlockUI"}static get isOfficialPlugin(){return!0}init(){const t=this.editor,n=t.t,o=t.ui.componentFactory,i=s(t),r=this._getLanguageListItemDefinitions(i),c=t.commands.get("codeBlock");o.add("codeBlock",(o=>{const i=(0,S.createDropdown)(o,S.SplitButtonView),s=i.buttonView,a=n("Insert code block");return s.set({label:a,tooltip:!0,icon:e.icons.codeBlock,isToggleable:!0}),s.bind("isOn").to(c,"value",(e=>!!e)),s.on("execute",(()=>{t.execute("codeBlock",{usePreviousLanguageChoice:!0}),t.editing.view.focus()})),i.on("execute",(e=>{t.execute("codeBlock",{language:e.source._codeBlockLanguage,forceValue:!0}),t.editing.view.focus()})),i.class="ck-code-block-dropdown",i.bind("isEnabled").to(c),(0,S.addListToDropdown)(i,r,{role:"menu",ariaLabel:a}),i})),o.add("menuBar:codeBlock",(o=>{const i=new S.MenuBarMenuView(o);i.buttonView.set({role:"menuitem",label:n("Code block"),icon:e.icons.codeBlock}),i.bind("isEnabled").to(c);const s=new S.MenuBarMenuListView(o);s.set({ariaLabel:n("Insert code block")});for(const e of r){const n=new S.MenuBarMenuListItemView(o,i),r=new S.MenuBarMenuListItemButtonView(o);r.bind(...Object.keys(e.model)).to(e.model),r.set({isToggleable:!0,role:"menuitemcheckbox"}),r.delegate("execute").to(i),r.on("execute",(()=>{t.execute("codeBlock",{language:e.model._codeBlockLanguage,forceValue:c.value!=e.model._codeBlockLanguage}),t.editing.view.focus()})),n.children.add(r),s.items.add(n)}return i.panelView.children.add(s),i}))}_getLanguageListItemDefinitions(e){const t=this.editor.commands.get("codeBlock"),n=new c.Collection;for(const o of e){const e={type:"button",model:new S.ViewModel({_codeBlockLanguage:o.language,label:o.label,role:"menuitemradio",withText:!0})};e.model.bind("isOn").to(t,"value",(t=>t===e.model._codeBlockLanguage)),n.add(e)}return n}}class V extends e.Plugin{static get requires(){return[x,j]}static get pluginName(){return"CodeBlock"}static get isOfficialPlugin(){return!0}}})(),(window.CKEditor5=window.CKEditor5||{}).codeBlock=o})();