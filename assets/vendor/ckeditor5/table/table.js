!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{"Align cell text to the bottom":"Align cell text to the bottom","Align cell text to the center":"Align cell text to the center","Align cell text to the left":"Align cell text to the left","Align cell text to the middle":"Align cell text to the middle","Align cell text to the right":"Align cell text to the right","Align cell text to the top":"Align cell text to the top","Align table to the left":"Align table to the left","Align table to the right":"Align table to the right",Alignment:"Alignment",Background:"Background",Border:"Border","Cell properties":"Cell properties","Center table":"Center table",Color:"Color",Column:"Column",Dashed:"Dashed","Delete column":"Delete column","Delete row":"Delete row",Dimensions:"Dimensions",Dotted:"Dotted",Double:"Double","Enter table caption":"Enter table caption",Groove:"Groove","Header column":"Header column","Header row":"Header row",Height:"Height","Horizontal text alignment toolbar":"Horizontal text alignment toolbar","Insert a new table row (when in the last cell of a table)":"Insert a new table row (when in the last cell of a table)","Insert column left":"Insert column left","Insert column right":"Insert column right","Insert row above":"Insert row above","Insert row below":"Insert row below","Insert table":"Insert table",Inset:"Inset","Justify cell text":"Justify cell text","Keystrokes that can be used in a table cell":"Keystrokes that can be used in a table cell","Merge cell down":"Merge cell down","Merge cell left":"Merge cell left","Merge cell right":"Merge cell right","Merge cell up":"Merge cell up","Merge cells":"Merge cells","Move the selection to the next cell":"Move the selection to the next cell","Move the selection to the previous cell":"Move the selection to the previous cell","Navigate through the table":"Navigate through the table",None:"None",Outset:"Outset",Padding:"Padding",Ridge:"Ridge",Row:"Row","Select column":"Select column","Select row":"Select row",Solid:"Solid","Split cell horizontally":"Split cell horizontally","Split cell vertically":"Split cell vertically",Style:"Style",Table:"Table","Table alignment toolbar":"Table alignment toolbar","Table cell text alignment":"Table cell text alignment","Table properties":"Table properties","Table toolbar":"Table toolbar",'The color is invalid. Try "#FF0000" or "rgb(255,0,0)" or "red".':'The color is invalid. Try "#FF0000" or "rgb(255,0,0)" or "red".','The value is invalid. Try "10px" or "2em" or simply "2".':'The value is invalid. Try "10px" or "2em" or simply "2".',"Vertical text alignment toolbar":"Vertical text alignment toolbar",Width:"Width"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={770:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});var n=o(758),i=o.n(n),r=o(935),l=o.n(r)()(i());l.push([e.id,".ck.ck-input-color{display:flex;flex-direction:row-reverse;width:100%}.ck.ck-input-color>input.ck.ck-input-text{flex-grow:1;min-width:auto}.ck.ck-input-color>div.ck.ck-dropdown{min-width:auto}.ck.ck-input-color>div.ck.ck-dropdown>.ck-input-color__button .ck-dropdown__arrow{display:none}.ck.ck-input-color .ck.ck-input-color__button{display:flex}.ck.ck-input-color .ck.ck-input-color__button .ck.ck-input-color__button__preview{overflow:hidden;position:relative}.ck.ck-input-color .ck.ck-input-color__button .ck.ck-input-color__button__preview>.ck.ck-input-color__button__preview__no-color-indicator{display:block;position:absolute}[dir=ltr] .ck.ck-input-color>.ck.ck-input-text{border-bottom-right-radius:0;border-top-right-radius:0}[dir=rtl] .ck.ck-input-color>.ck.ck-input-text{border-bottom-left-radius:0;border-top-left-radius:0}.ck.ck-input-color>.ck.ck-input-text:focus{z-index:0}.ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button{padding:0}[dir=ltr] .ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button{border-bottom-left-radius:0;border-top-left-radius:0}[dir=ltr] .ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button:not(:focus){border-left:1px solid transparent}[dir=rtl] .ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button{border-bottom-right-radius:0;border-top-right-radius:0}[dir=rtl] .ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button:not(:focus){border-right:1px solid transparent}.ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button.ck-disabled{background:var(--ck-color-input-disabled-background)}.ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button>.ck.ck-input-color__button__preview{border:1px solid var(--ck-color-input-border);border-radius:0;height:20px;width:20px}.ck-rounded-corners .ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button>.ck.ck-input-color__button__preview,.ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button>.ck.ck-input-color__button__preview.ck-rounded-corners{border-radius:var(--ck-border-radius)}.ck.ck-input-color>.ck.ck-dropdown>.ck.ck-button.ck-input-color__button>.ck.ck-input-color__button__preview>.ck.ck-input-color__button__preview__no-color-indicator{background:red;border-radius:2px;height:150%;left:50%;top:-30%;transform:rotate(45deg);transform-origin:50%;width:8%}.ck.ck-input-color .ck.ck-input-color__remove-color{border-bottom-left-radius:0;border-bottom-right-radius:0;padding:calc(var(--ck-spacing-standard)/2) var(--ck-spacing-standard);width:100%}.ck.ck-input-color .ck.ck-input-color__remove-color:not(:focus){border-bottom:1px solid var(--ck-color-input-border)}[dir=ltr] .ck.ck-input-color .ck.ck-input-color__remove-color{border-top-right-radius:0}[dir=rtl] .ck.ck-input-color .ck.ck-input-color__remove-color{border-top-left-radius:0}.ck.ck-input-color .ck.ck-input-color__remove-color .ck.ck-icon{margin-right:var(--ck-spacing-standard)}[dir=rtl] .ck.ck-input-color .ck.ck-input-color__remove-color .ck.ck-icon{margin-left:var(--ck-spacing-standard);margin-right:0}",""]);const s=l},67:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});var n=o(758),i=o.n(n),r=o(935),l=o.n(r)()(i());l.push([e.id,".ck.ck-form{padding:0 0 var(--ck-spacing-large)}.ck.ck-form:focus{outline:none}.ck.ck-form .ck.ck-input-text{min-width:100%;width:0}.ck.ck-form .ck.ck-dropdown{min-width:100%}.ck.ck-form .ck.ck-dropdown .ck-dropdown__button:not(:focus){border:1px solid var(--ck-color-base-border)}.ck.ck-form .ck.ck-dropdown .ck-dropdown__button .ck-button__label{width:100%}",""]);const s=l},839:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});var n=o(758),i=o.n(n),r=o(935),l=o.n(r)()(i());l.push([e.id,".ck.ck-form__row{display:flex;flex-direction:row;flex-wrap:nowrap;justify-content:space-between}.ck.ck-form__row>:not(.ck-label){flex-grow:1}.ck.ck-form__row.ck-table-form__action-row .ck-button-cancel,.ck.ck-form__row.ck-table-form__action-row .ck-button-save{justify-content:center}.ck.ck-form__row{padding:var(--ck-spacing-standard) var(--ck-spacing-large) 0}[dir=ltr] .ck.ck-form__row>:not(.ck-label)+*{margin-left:var(--ck-spacing-large)}[dir=rtl] .ck.ck-form__row>:not(.ck-label)+*{margin-right:var(--ck-spacing-large)}.ck.ck-form__row>.ck-label{min-width:100%;width:100%}.ck.ck-form__row.ck-table-form__action-row{margin-top:var(--ck-spacing-large)}.ck.ck-form__row.ck-table-form__action-row .ck-button .ck-button__label{color:var(--ck-color-text)}",""]);const s=l},712:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});var n=o(758),i=o.n(n),r=o(935),l=o.n(r)()(i());l.push([e.id,".ck .ck-insert-table-dropdown__grid{display:flex;flex-direction:row;flex-wrap:wrap}:root{--ck-insert-table-dropdown-padding:10px;--ck-insert-table-dropdown-box-height:11px;--ck-insert-table-dropdown-box-width:12px;--ck-insert-table-dropdown-box-margin:1px}.ck .ck-insert-table-dropdown__grid{padding:var(--ck-insert-table-dropdown-padding) var(--ck-insert-table-dropdown-padding) 0;width:calc(var(--ck-insert-table-dropdown-box-width)*10 + var(--ck-insert-table-dropdown-box-margin)*20 + var(--ck-insert-table-dropdown-padding)*2)}.ck .ck-insert-table-dropdown__label,.ck[dir=rtl] .ck-insert-table-dropdown__label{text-align:center}.ck .ck-insert-table-dropdown-grid-box{border:1px solid var(--ck-color-base-border);border-radius:1px;margin:var(--ck-insert-table-dropdown-box-margin);min-height:var(--ck-insert-table-dropdown-box-height);min-width:var(--ck-insert-table-dropdown-box-width);outline:none;transition:none}@media (prefers-reduced-motion:reduce){.ck .ck-insert-table-dropdown-grid-box{transition:none}}.ck .ck-insert-table-dropdown-grid-box:focus{box-shadow:none}.ck .ck-insert-table-dropdown-grid-box.ck-on{background:var(--ck-color-focus-outer-shadow);border-color:var(--ck-color-focus-border)}",""]);const s=l},25:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});var n=o(758),i=o.n(n),r=o(935),l=o.n(r)()(i());l.push([e.id,".ck-content .table{display:table;margin:.9em auto}.ck-content .table table{border:1px double #b3b3b3;border-collapse:collapse;border-spacing:0;height:100%;width:100%}.ck-content .table table td,.ck-content .table table th{border:1px solid #bfbfbf;min-width:2em;padding:.4em}.ck-content .table table th{background:rgba(0,0,0,.05);font-weight:700}@media print{.ck-content .table table{height:auto}}.ck-content[dir=rtl] .table th{text-align:right}.ck-content[dir=ltr] .table th{text-align:left}.ck-editor__editable .ck-table-bogus-paragraph{display:inline-block;width:100%}",""]);const s=l},175:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});var n=o(758),i=o.n(n),r=o(935),l=o.n(r)()(i());l.push([e.id,":root{--ck-color-selector-caption-background:#f7f7f7;--ck-color-selector-caption-text:#333;--ck-color-selector-caption-highlighted-background:#fd0}.ck-content .table>figcaption{background-color:var(--ck-color-selector-caption-background);caption-side:top;color:var(--ck-color-selector-caption-text);display:table-caption;font-size:.75em;outline-offset:-1px;padding:.6em;text-align:center;word-break:break-word}@media (forced-colors:active){.ck-content .table>figcaption{background-color:unset;color:unset}}@media (forced-colors:none){.ck.ck-editor__editable .table>figcaption.table__caption_highlighted{animation:ck-table-caption-highlight .6s ease-out}}.ck.ck-editor__editable .table>figcaption.ck-placeholder:before{overflow:hidden;padding-left:inherit;padding-right:inherit;text-overflow:ellipsis;white-space:nowrap}@keyframes ck-table-caption-highlight{0%{background-color:var(--ck-color-selector-caption-highlighted-background)}to{background-color:var(--ck-color-selector-caption-background)}}",""]);const s=l},266:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});var n=o(758),i=o.n(n),r=o(935),l=o.n(r)()(i());l.push([e.id,".ck.ck-table-cell-properties-form .ck-form__row.ck-table-cell-properties-form__alignment-row{flex-wrap:wrap}.ck.ck-table-cell-properties-form .ck-form__row.ck-table-cell-properties-form__alignment-row .ck.ck-toolbar:first-of-type{flex-grow:0.57}.ck.ck-table-cell-properties-form .ck-form__row.ck-table-cell-properties-form__alignment-row .ck.ck-toolbar:last-of-type{flex-grow:0.43}.ck.ck-table-cell-properties-form .ck-form__row.ck-table-cell-properties-form__alignment-row .ck.ck-toolbar .ck-button{flex-grow:1}.ck.ck-table-cell-properties-form{width:320px}.ck.ck-table-cell-properties-form .ck-form__row.ck-table-cell-properties-form__padding-row{align-self:flex-end;padding:0;width:25%}.ck.ck-table-cell-properties-form .ck-form__row.ck-table-cell-properties-form__alignment-row .ck.ck-toolbar{background:none;margin-top:var(--ck-spacing-standard)}",""]);const s=l},363:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});var n=o(758),i=o.n(n),r=o(935),l=o.n(r)()(i());l.push([e.id,":root{--ck-color-selector-column-resizer-hover:var(--ck-color-base-active);--ck-table-column-resizer-width:7px;--ck-table-column-resizer-position-offset:calc(var(--ck-table-column-resizer-width)*-0.5 - 0.5px)}.ck-content .table .ck-table-resized{table-layout:fixed}.ck-content .table table{overflow:hidden}.ck-content .table td,.ck-content .table th{overflow-wrap:break-word;position:relative}.ck.ck-editor__editable .table .ck-table-column-resizer{bottom:0;cursor:col-resize;position:absolute;right:var(--ck-table-column-resizer-position-offset);top:0;user-select:none;width:var(--ck-table-column-resizer-width);z-index:var(--ck-z-default)}.ck.ck-editor__editable .table[draggable] .ck-table-column-resizer,.ck.ck-editor__editable.ck-column-resize_disabled .table .ck-table-column-resizer{display:none}.ck.ck-editor__editable .table .ck-table-column-resizer:hover,.ck.ck-editor__editable .table .ck-table-column-resizer__active{background-color:var(--ck-color-selector-column-resizer-hover);bottom:-999999px;opacity:.25;top:-999999px}.ck.ck-editor__editable[dir=rtl] .table .ck-table-column-resizer{left:var(--ck-table-column-resizer-position-offset);right:unset}",""]);const s=l},817:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});var n=o(758),i=o.n(n),r=o(935),l=o.n(r)()(i());l.push([e.id,":root{--ck-color-selector-focused-cell-background:rgba(158,201,250,.3)}.ck-widget.table td.ck-editor__nested-editable.ck-editor__nested-editable_focused,.ck-widget.table td.ck-editor__nested-editable:focus,.ck-widget.table th.ck-editor__nested-editable.ck-editor__nested-editable_focused,.ck-widget.table th.ck-editor__nested-editable:focus{background:var(--ck-color-selector-focused-cell-background);outline:1px solid var(--ck-color-focus-border);outline-offset:-1px}",""]);const s=l},911:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});var n=o(758),i=o.n(n),r=o(935),l=o.n(r)()(i());l.push([e.id,'.ck.ck-table-form .ck-form__row.ck-table-form__background-row,.ck.ck-table-form .ck-form__row.ck-table-form__border-row{flex-wrap:wrap}.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row{align-items:center;flex-wrap:wrap}.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row .ck-labeled-field-view{align-items:center;display:flex;flex-direction:column-reverse}.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row .ck-labeled-field-view .ck.ck-dropdown,.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row .ck-table-form__dimension-operator{flex-grow:0}.ck.ck-table-form .ck.ck-labeled-field-view{position:relative}.ck.ck-table-form .ck.ck-labeled-field-view .ck.ck-labeled-field-view__status{bottom:calc(var(--ck-table-properties-error-arrow-size)*-1);left:50%;position:absolute;transform:translate(-50%,100%);z-index:1}.ck.ck-table-form .ck.ck-labeled-field-view .ck.ck-labeled-field-view__status:after{content:"";left:50%;position:absolute;top:calc(var(--ck-table-properties-error-arrow-size)*-1);transform:translateX(-50%)}:root{--ck-table-properties-error-arrow-size:6px;--ck-table-properties-min-error-width:150px}.ck.ck-table-form .ck-form__row.ck-table-form__border-row .ck-labeled-field-view>.ck-label{font-size:var(--ck-font-size-tiny);text-align:center}.ck.ck-table-form .ck-form__row.ck-table-form__border-row .ck-table-form__border-style,.ck.ck-table-form .ck-form__row.ck-table-form__border-row .ck-table-form__border-width{max-width:80px;min-width:80px;width:80px}.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row{padding:0}.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row .ck-table-form__dimensions-row__height,.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row .ck-table-form__dimensions-row__width{margin:0}.ck.ck-table-form .ck-form__row.ck-table-form__dimensions-row .ck-table-form__dimension-operator{align-self:flex-end;display:inline-block;height:var(--ck-ui-component-min-height);line-height:var(--ck-ui-component-min-height);margin:0 var(--ck-spacing-small)}.ck.ck-table-form .ck.ck-labeled-field-view{padding-top:var(--ck-spacing-standard)}.ck.ck-table-form .ck.ck-labeled-field-view .ck.ck-labeled-field-view__status{animation:ck-table-form-labeled-view-status-appear .15s ease both;background:var(--ck-color-base-error);border-radius:0;color:var(--ck-color-base-background);min-width:var(--ck-table-properties-min-error-width);padding:var(--ck-spacing-small) var(--ck-spacing-medium);text-align:center}.ck-rounded-corners .ck.ck-table-form .ck.ck-labeled-field-view .ck.ck-labeled-field-view__status,.ck.ck-table-form .ck.ck-labeled-field-view .ck.ck-labeled-field-view__status.ck-rounded-corners{border-radius:var(--ck-border-radius)}.ck.ck-table-form .ck.ck-labeled-field-view .ck.ck-labeled-field-view__status:after{border-color:transparent transparent var(--ck-color-base-error) transparent;border-style:solid;border-width:0 var(--ck-table-properties-error-arrow-size) var(--ck-table-properties-error-arrow-size) var(--ck-table-properties-error-arrow-size)}@media (prefers-reduced-motion:reduce){.ck.ck-table-form .ck.ck-labeled-field-view .ck.ck-labeled-field-view__status{animation:none}}.ck.ck-table-form .ck.ck-labeled-field-view .ck-input.ck-error:not(:focus)+.ck.ck-labeled-field-view__status{display:none}@keyframes ck-table-form-labeled-view-status-appear{0%{opacity:0}to{opacity:1}}',""]);const s=l},218:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});var n=o(758),i=o.n(n),r=o(935),l=o.n(r)()(i());l.push([e.id,".ck.ck-table-properties-form .ck-form__row.ck-table-properties-form__alignment-row{align-content:baseline;flex-basis:0;flex-wrap:wrap}.ck.ck-table-properties-form .ck-form__row.ck-table-properties-form__alignment-row .ck.ck-toolbar .ck-toolbar__items{flex-wrap:nowrap}.ck.ck-table-properties-form{width:320px}.ck.ck-table-properties-form .ck-form__row.ck-table-properties-form__alignment-row{align-self:flex-end;padding:0}.ck.ck-table-properties-form .ck-form__row.ck-table-properties-form__alignment-row .ck.ck-toolbar{background:none;margin-top:var(--ck-spacing-standard)}.ck.ck-table-properties-form .ck-form__row.ck-table-properties-form__alignment-row .ck.ck-toolbar .ck-toolbar__items>*{width:40px}",""]);const s=l},719:(e,t,o)=>{"use strict";o.d(t,{A:()=>s});var n=o(758),i=o.n(n),r=o(935),l=o.n(r)()(i());l.push([e.id,':root{--ck-table-selected-cell-background:rgba(158,207,250,.3)}.ck.ck-editor__editable .table table td.ck-editor__editable_selected,.ck.ck-editor__editable .table table th.ck-editor__editable_selected{box-shadow:unset;caret-color:transparent;outline:unset;position:relative}.ck.ck-editor__editable .table table td.ck-editor__editable_selected:after,.ck.ck-editor__editable .table table th.ck-editor__editable_selected:after{background-color:var(--ck-table-selected-cell-background);bottom:0;content:"";left:0;pointer-events:none;position:absolute;right:0;top:0}.ck.ck-editor__editable .table table td.ck-editor__editable_selected ::selection,.ck.ck-editor__editable .table table td.ck-editor__editable_selected:focus,.ck.ck-editor__editable .table table th.ck-editor__editable_selected ::selection,.ck.ck-editor__editable .table table th.ck-editor__editable_selected:focus{background-color:transparent}.ck.ck-editor__editable .table table td.ck-editor__editable_selected .ck-widget,.ck.ck-editor__editable .table table th.ck-editor__editable_selected .ck-widget{outline:unset}.ck.ck-editor__editable .table table td.ck-editor__editable_selected .ck-widget>.ck-widget__selection-handle,.ck.ck-editor__editable .table table th.ck-editor__editable_selected .ck-widget>.ck-widget__selection-handle{display:none}',""]);const s=l},935:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var o="",n=void 0!==t[5];return t[4]&&(o+="@supports (".concat(t[4],") {")),t[2]&&(o+="@media ".concat(t[2]," {")),n&&(o+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),o+=e(t),n&&(o+="}"),t[2]&&(o+="}"),t[4]&&(o+="}"),o})).join("")},t.i=function(e,o,n,i,r){"string"==typeof e&&(e=[[null,e,void 0]]);var l={};if(n)for(var s=0;s<this.length;s++){var a=this[s][0];null!=a&&(l[a]=!0)}for(var c=0;c<e.length;c++){var d=[].concat(e[c]);n&&l[d[0]]||(void 0!==r&&(void 0===d[5]||(d[1]="@layer".concat(d[5].length>0?" ".concat(d[5]):""," {").concat(d[1],"}")),d[5]=r),o&&(d[2]?(d[1]="@media ".concat(d[2]," {").concat(d[1],"}"),d[2]=o):d[2]=o),i&&(d[4]?(d[1]="@supports (".concat(d[4],") {").concat(d[1],"}"),d[4]=i):d[4]="".concat(i)),t.push(d))}},t}},758:e=>{"use strict";e.exports=function(e){return e[1]}},591:e=>{"use strict";var t=[];function o(e){for(var o=-1,n=0;n<t.length;n++)if(t[n].identifier===e){o=n;break}return o}function n(e,n){for(var r={},l=[],s=0;s<e.length;s++){var a=e[s],c=n.base?a[0]+n.base:a[0],d=r[c]||0,u="".concat(c," ").concat(d);r[c]=d+1;var h=o(u),b={css:a[1],media:a[2],sourceMap:a[3],supports:a[4],layer:a[5]};if(-1!==h)t[h].references++,t[h].updater(b);else{var m=i(b,n);n.byIndex=s,t.splice(s,0,{identifier:u,updater:m,references:1})}l.push(u)}return l}function i(e,t){var o=t.domAPI(t);o.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;o.update(e=t)}else o.remove()}}e.exports=function(e,i){var r=n(e=e||[],i=i||{});return function(e){e=e||[];for(var l=0;l<r.length;l++){var s=o(r[l]);t[s].references--}for(var a=n(e,i),c=0;c<r.length;c++){var d=o(r[c]);0===t[d].references&&(t[d].updater(),t.splice(d,1))}r=a}}},128:e=>{"use strict";var t={};e.exports=function(e,o){var n=function(e){if(void 0===t[e]){var o=document.querySelector(e);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch(e){o=null}t[e]=o}return t[e]}(e);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(o)}},51:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},21:e=>{"use strict";e.exports=function(e,t){Object.keys(t).forEach((function(o){e.setAttribute(o,t[o])}))}},639:e=>{"use strict";var t,o=(t=[],function(e,o){return t[e]=o,t.filter(Boolean).join("\n")});function n(e,t,n,i){var r;if(n)r="";else{r="",i.supports&&(r+="@supports (".concat(i.supports,") {")),i.media&&(r+="@media ".concat(i.media," {"));var l=void 0!==i.layer;l&&(r+="@layer".concat(i.layer.length>0?" ".concat(i.layer):""," {")),r+=i.css,l&&(r+="}"),i.media&&(r+="}"),i.supports&&(r+="}")}if(e.styleSheet)e.styleSheet.cssText=o(t,r);else{var s=document.createTextNode(r),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(s,a[t]):e.appendChild(s)}}var i={singleton:null,singletonCounter:0};e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=i.singletonCounter++,o=i.singleton||(i.singleton=e.insertStyleElement(e));return{update:function(e){n(o,t,!1,e)},remove:function(e){n(o,t,!0,e)}}}},331:(e,t,o)=>{e.exports=o(237)("./src/clipboard.js")},782:(e,t,o)=>{e.exports=o(237)("./src/core.js")},783:(e,t,o)=>{e.exports=o(237)("./src/engine.js")},311:(e,t,o)=>{e.exports=o(237)("./src/ui.js")},584:(e,t,o)=>{e.exports=o(237)("./src/utils.js")},901:(e,t,o)=>{e.exports=o(237)("./src/widget.js")},237:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function o(n){var i=t[n];if(void 0!==i)return i.exports;var r=t[n]={id:n,exports:{}};return e[n](r,r.exports,o),r.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";o.r(n),o.d(n,{PlainTableOutput:()=>rt,Table:()=>it,TableCaption:()=>fn,TableCaptionEditing:()=>bn,TableCaptionUI:()=>mn,TableCellProperties:()=>Mo,TableCellPropertiesEditing:()=>Do,TableCellPropertiesUI:()=>xo,TableCellWidthEditing:()=>Ro,TableClipboard:()=>Ge,TableColumnResize:()=>qr,TableColumnResizeEditing:()=>$r,TableEditing:()=>Oe,TableKeyboard:()=>Ye,TableMouse:()=>et,TableProperties:()=>sn,TablePropertiesEditing:()=>Qo,TablePropertiesUI:()=>ln,TableSelection:()=>$e,TableToolbar:()=>ut,TableUI:()=>Me,TableUtils:()=>oe});var e=o(782),t=o(901);function i(e,t){const{modelAttribute:o,styleName:n,viewElement:i,defaultValue:r,reduceBoxSides:l=!1,shouldUpcast:s=()=>!0}=t;e.for("upcast").attributeToAttribute({view:{name:i,styles:{[n]:/[\s\S]+/}},model:{key:o,value:e=>{if(!s(e))return;const t=e.getNormalizedStyle(n),o=l?a(t):t;return r!==o?o:void 0}}})}function r(e,t,o,n){e.for("upcast").add((e=>e.on("element:"+t,((e,t,i)=>{if(!t.modelRange)return;const r=["border-top-width","border-top-color","border-top-style","border-bottom-width","border-bottom-color","border-bottom-style","border-right-width","border-right-color","border-right-style","border-left-width","border-left-color","border-left-style"].filter((e=>t.viewItem.hasStyle(e)));if(!r.length)return;const l={styles:r};if(!i.consumable.test(t.viewItem,l))return;const s=[...t.modelRange.getItems({shallow:!0})].pop();i.consumable.consume(t.viewItem,l);const c={style:t.viewItem.getNormalizedStyle("border-style"),color:t.viewItem.getNormalizedStyle("border-color"),width:t.viewItem.getNormalizedStyle("border-width")},d={style:a(c.style),color:a(c.color),width:a(c.width)};d.style!==n.style&&i.writer.setAttribute(o.style,d.style,s),d.color!==n.color&&i.writer.setAttribute(o.color,d.color,s),d.width!==n.width&&i.writer.setAttribute(o.width,d.width,s)}))))}function l(e,t){const{modelElement:o,modelAttribute:n,styleName:i}=t;e.for("downcast").attributeToAttribute({model:{name:o,key:n},view:e=>({key:"style",value:{[i]:e}})})}function s(e,t){const{modelAttribute:o,styleName:n}=t;e.for("downcast").add((e=>e.on(`attribute:${o}:table`,((e,t,o)=>{const{item:i,attributeNewValue:r}=t,{mapper:l,writer:s}=o;if(!o.consumable.consume(t.item,e.name))return;const a=[...l.toViewElement(i).getChildren()].find((e=>e.is("element","table")));r?s.setStyle(n,r,a):s.removeStyle(n,a)}))))}function a(e){if(!e)return;const t=["top","right","bottom","left"];if(!t.every((t=>e[t])))return e;const o=e.top;return t.every((t=>e[t]===o))?o:e}function c(e,t,o,n,i=1){null!=t&&null!=i&&t>i?n.setAttribute(e,t,o):n.removeAttribute(e,o)}function d(e,t,o={}){const n=e.createElement("tableCell",o);return e.insertElement("paragraph",n),e.insert(n,t),n}function u(e,t){const o=t.parent.parent,n=parseInt(o.getAttribute("headingColumns")||"0"),{column:i}=e.getCellLocation(t);return!!n&&i<n}function h(e,t,o){const{modelAttribute:n}=o;e.extend("tableCell",{allowAttributes:[n]}),i(t,{viewElement:/^(td|th)$/,...o}),l(t,{modelElement:"tableCell",...o})}function b(e){const t=e.getSelectedElement();return t&&t.is("element","table")?t:e.getFirstPosition().findAncestor("table")}var m=o(584);function g(){return e=>{e.on("element:table",((e,t,o)=>{const n=t.viewItem;if(!o.consumable.test(n,{name:!0}))return;const{rows:i,headingRows:r,headingColumns:l}=function(e){let t,o=0;const n=[],i=[];let r;for(const l of Array.from(e.getChildren())){if("tbody"!==l.name&&"thead"!==l.name&&"tfoot"!==l.name)continue;"thead"!==l.name||r||(r=l);const e=Array.from(l.getChildren()).filter((e=>e.is("element","tr")));for(const s of e)if(r&&l===r||"tbody"===l.name&&Array.from(s.getChildren()).length&&Array.from(s.getChildren()).every((e=>e.is("element","th"))))o++,n.push(s);else{i.push(s);const e=f(s);(!t||e<t)&&(t=e)}}return{headingRows:o,headingColumns:t||0,rows:[...n,...i]}}(n),s={};l&&(s.headingColumns=l),r&&(s.headingRows=r);const a=o.writer.createElement("table",s);if(o.safeInsert(a,t.modelCursor)){if(o.consumable.consume(n,{name:!0}),i.forEach((e=>o.convertItem(e,o.writer.createPositionAt(a,"end")))),o.convertChildren(n,o.writer.createPositionAt(a,"end")),a.isEmpty){const e=o.writer.createElement("tableRow");o.writer.insert(e,o.writer.createPositionAt(a,"end")),d(o.writer,o.writer.createPositionAt(e,"end"))}o.updateConversionResult(a,t)}}))}}function p(e){return t=>{t.on(`element:${e}`,((e,t,{writer:o})=>{if(!t.modelRange)return;const n=t.modelRange.start.nodeAfter,i=o.createPositionAt(n,0);if(t.viewItem.isEmpty)return void o.insertElement("paragraph",i);const r=Array.from(n.getChildren());if(r.every((e=>e.is("element","$marker")))){const e=o.createElement("paragraph");o.insert(e,o.createPositionAt(n,0));for(const t of r)o.move(o.createRangeOn(t),o.createPositionAt(e,"end"))}}),{priority:"low"})}}function f(e){let t=0,o=0;const n=Array.from(e.getChildren()).filter((e=>"th"===e.name||"td"===e.name));for(;o<n.length&&"th"===n[o].name;){const e=n[o];t+=parseInt(e.getAttribute("colspan")||"1"),o++}return t}class w{constructor(e,t={}){this._jumpedToStartRow=!1,this._table=e,this._startRow=void 0!==t.row?t.row:t.startRow||0,this._endRow=void 0!==t.row?t.row:t.endRow,this._startColumn=void 0!==t.column?t.column:t.startColumn||0,this._endColumn=void 0!==t.column?t.column:t.endColumn,this._includeAllSlots=!!t.includeAllSlots,this._skipRows=new Set,this._row=0,this._rowIndex=0,this._column=0,this._cellIndex=0,this._spannedCells=new Map,this._nextCellAtColumn=-1}[Symbol.iterator](){return this}next(){this._canJumpToStartRow()&&this._jumpToNonSpannedRowClosestToStartRow();const e=this._table.getChild(this._rowIndex);if(!e||this._isOverEndRow())return{done:!0,value:void 0};if(!e.is("element","tableRow"))return this._rowIndex++,this.next();if(this._isOverEndColumn())return this._advanceToNextRow();let t=null;const o=this._getSpanned();if(o)this._includeAllSlots&&!this._shouldSkipSlot()&&(t=this._formatOutValue(o.cell,o.row,o.column));else{const o=e.getChild(this._cellIndex);if(!o)return this._advanceToNextRow();const n=parseInt(o.getAttribute("colspan")||"1"),i=parseInt(o.getAttribute("rowspan")||"1");(n>1||i>1)&&this._recordSpans(o,i,n),this._shouldSkipSlot()||(t=this._formatOutValue(o)),this._nextCellAtColumn=this._column+n}return this._column++,this._column==this._nextCellAtColumn&&this._cellIndex++,t||this.next()}skipRow(e){this._skipRows.add(e)}_advanceToNextRow(){return this._row++,this._rowIndex++,this._column=0,this._cellIndex=0,this._nextCellAtColumn=-1,this.next()}_isOverEndRow(){return void 0!==this._endRow&&this._row>this._endRow}_isOverEndColumn(){return void 0!==this._endColumn&&this._column>this._endColumn}_formatOutValue(e,t=this._row,o=this._column){return{done:!1,value:new _(this,e,t,o)}}_shouldSkipSlot(){const e=this._skipRows.has(this._row),t=this._row<this._startRow,o=this._column<this._startColumn,n=void 0!==this._endColumn&&this._column>this._endColumn;return e||t||o||n}_getSpanned(){const e=this._spannedCells.get(this._row);return e&&e.get(this._column)||null}_recordSpans(e,t,o){const n={cell:e,row:this._row,column:this._column};for(let e=this._row;e<this._row+t;e++)for(let t=this._column;t<this._column+o;t++)e==this._row&&t==this._column||this._markSpannedCell(e,t,n)}_markSpannedCell(e,t,o){this._spannedCells.has(e)||this._spannedCells.set(e,new Map);this._spannedCells.get(e).set(t,o)}_canJumpToStartRow(){return!!this._startRow&&this._startRow>0&&!this._jumpedToStartRow}_jumpToNonSpannedRowClosestToStartRow(){const e=this._getRowLength(0);for(let t=this._startRow;!this._jumpedToStartRow;t--)e===this._getRowLength(t)&&(this._row=t,this._rowIndex=t,this._jumpedToStartRow=!0)}_getRowLength(e){return[...this._table.getChild(e).getChildren()].reduce(((e,t)=>e+parseInt(t.getAttribute("colspan")||"1")),0)}}class _{constructor(e,t,o,n){this.cell=t,this.row=e._row,this.column=e._column,this.cellAnchorRow=o,this.cellAnchorColumn=n,this._cellIndex=e._cellIndex,this._rowIndex=e._rowIndex,this._table=e._table}get isAnchor(){return this.row===this.cellAnchorRow&&this.column===this.cellAnchorColumn}get cellWidth(){return parseInt(this.cell.getAttribute("colspan")||"1")}get cellHeight(){return parseInt(this.cell.getAttribute("rowspan")||"1")}get rowIndex(){return this._rowIndex}getPositionBefore(){return this._table.root.document.model.createPositionAt(this._table.getChild(this.row),this._cellIndex)}}function k(e,o){return(n,{writer:i})=>{const r=n.getAttribute("headingRows")||0,l=i.createContainerElement("table",null,[]),s=i.createContainerElement("figure",{class:"table"},l);r>0&&i.insert(i.createPositionAt(l,"end"),i.createContainerElement("thead",null,i.createSlot((e=>e.is("element","tableRow")&&e.index<r)))),r<e.getRows(n)&&i.insert(i.createPositionAt(l,"end"),i.createContainerElement("tbody",null,i.createSlot((e=>e.is("element","tableRow")&&e.index>=r))));for(const{positionOffset:e,filter:t}of o.additionalSlots)i.insert(i.createPositionAt(l,e),i.createSlot(t));return i.insert(i.createPositionAt(l,"after"),i.createSlot((e=>!e.is("element","tableRow")&&!o.additionalSlots.some((({filter:t})=>t(e)))))),o.asWidget?function(e,o){return o.setCustomProperty("table",!0,e),(0,t.toWidget)(e,o,{hasSelectionHandle:!0})}(s,i):s}}function v(e={}){return(o,{writer:n})=>{const i=o.parent,r=i.parent,l=r.getChildIndex(i),s=new w(r,{row:l}),a=r.getAttribute("headingRows")||0,c=r.getAttribute("headingColumns")||0;let d=null;for(const i of s)if(i.cell==o){const o=i.row<a||i.column<c?"th":"td";d=e.asWidget?(0,t.toWidgetEditable)(n.createEditableElement(o),n):n.createContainerElement(o);break}return d}}function C(e={}){return(t,{writer:o})=>{if(!t.parent.is("element","tableCell"))return null;if(!y(t))return null;if(e.asWidget)return o.createContainerElement("span",{class:"ck-table-bogus-paragraph"});{const e=o.createContainerElement("p");return o.setCustomProperty("dataPipeline:transparentRendering",!0,e),e}}}function y(e){return 1==e.parent.childCount&&!!e.getAttributeKeys().next().done}class A extends e.Command{refresh(){const e=this.editor.model,t=e.document.selection,o=e.schema;this.isEnabled=function(e,t){const o=e.getFirstPosition().parent,n=o===o.root?o:o.parent;return t.checkChild(n,"table")}(t,o)}execute(e={}){const t=this.editor,o=t.model,n=t.plugins.get("TableUtils"),i=t.config.get("table.defaultHeadings.rows"),r=t.config.get("table.defaultHeadings.columns");void 0===e.headingRows&&i&&(e.headingRows=i),void 0===e.headingColumns&&r&&(e.headingColumns=r),o.change((t=>{const i=n.createTable(t,e);o.insertObject(i,null,null,{findOptimalPosition:"auto"}),t.setSelection(t.createPositionAt(i.getNodeByPath([0,0,0]),0))}))}}class T extends e.Command{constructor(e,t={}){super(e),this.order=t.order||"below"}refresh(){const e=this.editor.model.document.selection,t=!!this.editor.plugins.get("TableUtils").getSelectionAffectedTableCells(e).length;this.isEnabled=t}execute(){const e=this.editor,t=e.model.document.selection,o=e.plugins.get("TableUtils"),n="above"===this.order,i=o.getSelectionAffectedTableCells(t),r=o.getRowIndexes(i),l=n?r.first:r.last,s=i[0].findAncestor("table");o.insertRows(s,{at:n?l:l+1,copyStructureFromAbove:!n})}}class x extends e.Command{constructor(e,t={}){super(e),this.order=t.order||"right"}refresh(){const e=this.editor.model.document.selection,t=!!this.editor.plugins.get("TableUtils").getSelectionAffectedTableCells(e).length;this.isEnabled=t}execute(){const e=this.editor,t=e.model.document.selection,o=e.plugins.get("TableUtils"),n="left"===this.order,i=o.getSelectionAffectedTableCells(t),r=o.getColumnIndexes(i),l=n?r.first:r.last,s=i[0].findAncestor("table");o.insertColumns(s,{columns:1,at:n?l:l+1})}}class S extends e.Command{constructor(e,t={}){super(e),this.direction=t.direction||"horizontally"}refresh(){const e=this.editor.plugins.get("TableUtils").getSelectionAffectedTableCells(this.editor.model.document.selection);this.isEnabled=1===e.length}execute(){const e=this.editor.plugins.get("TableUtils"),t=e.getSelectionAffectedTableCells(this.editor.model.document.selection)[0];"horizontally"===this.direction?e.splitCellHorizontally(t,2):e.splitCellVertically(t,2)}}function V(e,t,o){const{startRow:n,startColumn:i,endRow:r,endColumn:l}=t,s=o.createElement("table"),a=r-n+1;for(let e=0;e<a;e++)o.insertElement("tableRow",s,"end");const u=[...new w(e,{startRow:n,endRow:r,startColumn:i,endColumn:l,includeAllSlots:!0})];for(const{row:e,column:t,cell:a,isAnchor:c,cellAnchorRow:h,cellAnchorColumn:b}of u){const u=e-n,m=s.getChild(u);if(c){const n=o.cloneElement(a);o.append(n,m),z(n,e,t,r,l,o)}else(h<n||b<i)&&d(o,o.createPositionAt(m,"end"))}return function(e,t,o,n,i){const r=parseInt(t.getAttribute("headingRows")||"0");if(r>0){c("headingRows",r-o,e,i,0)}const l=parseInt(t.getAttribute("headingColumns")||"0");if(l>0){c("headingColumns",l-n,e,i,0)}}(s,e,n,i,o),s}function R(e,t,o=0){const n=[],i=new w(e,{startRow:o,endRow:t-1});for(const e of i){const{row:o,cellHeight:i}=e;o<t&&t<=o+i-1&&n.push(e)}return n}function P(e,t,o){const n=e.parent,i=n.parent,r=n.index,l=t-r,s={},a=parseInt(e.getAttribute("rowspan"))-l;a>1&&(s.rowspan=a);const u=parseInt(e.getAttribute("colspan")||"1");u>1&&(s.colspan=u);const h=r+l,b=[...new w(i,{startRow:r,endRow:h,includeAllSlots:!0})];let m,g=null;for(const t of b){const{row:n,column:i,cell:r}=t;r===e&&void 0===m&&(m=i),void 0!==m&&m===i&&n===h&&(g=d(o,t.getPositionBefore(),s))}return c("rowspan",l,e,o),g}function I(e,t){const o=[],n=new w(e);for(const e of n){const{column:n,cellWidth:i}=e;n<t&&t<=n+i-1&&o.push(e)}return o}function E(e,t,o,n){const i=o-t,r={},l=parseInt(e.getAttribute("colspan"))-i;l>1&&(r.colspan=l);const s=parseInt(e.getAttribute("rowspan")||"1");s>1&&(r.rowspan=s);const a=d(n,n.createPositionAfter(e),r);return c("colspan",i,e,n),a}function z(e,t,o,n,i,r){const l=parseInt(e.getAttribute("colspan")||"1"),s=parseInt(e.getAttribute("rowspan")||"1");if(o+l-1>i){c("colspan",i-o+1,e,r,1)}if(t+s-1>n){c("rowspan",n-t+1,e,r,1)}}function B(e,t){const o=t.getColumns(e),n=new Array(o).fill(0);for(const{column:t}of new w(e))n[t]++;const i=n.reduce(((e,t,o)=>t?e:[...e,o]),[]);if(i.length>0){const o=i[i.length-1];return t.removeColumns(e,{at:o}),!0}return!1}function O(e,t){const o=[],n=t.getRows(e);for(let t=0;t<n;t++){e.getChild(t).isEmpty&&o.push(t)}if(o.length>0){const n=o[o.length-1];return t.removeRows(e,{at:n}),!0}return!1}function L(e,t){B(e,t)||O(e,t)}function F(e,t){const o=Array.from(new w(e,{startColumn:t.firstColumn,endColumn:t.lastColumn,row:t.lastRow}));if(o.every((({cellHeight:e})=>1===e)))return t.lastRow;const n=o[0].cellHeight-1;return t.lastRow+n}function W(e,t){const o=Array.from(new w(e,{startRow:t.firstRow,endRow:t.lastRow,column:t.lastColumn}));if(o.every((({cellWidth:e})=>1===e)))return t.lastColumn;const n=o[0].cellWidth-1;return t.lastColumn+n}class N extends e.Command{constructor(e,t){super(e),this.direction=t.direction,this.isHorizontal="right"==this.direction||"left"==this.direction}refresh(){const e=this._getMergeableCell();this.value=e,this.isEnabled=!!e}execute(){const e=this.editor.model,t=e.document,o=this.editor.plugins.get("TableUtils").getTableCellsContainingSelection(t.selection)[0],n=this.value,i=this.direction;e.change((e=>{const t="right"==i||"down"==i,r=t?o:n,l=t?n:o,s=l.parent;!function(e,t,o){D(e)||(D(t)&&o.remove(o.createRangeIn(t)),o.move(o.createRangeIn(e),o.createPositionAt(t,"end")));o.remove(e)}(l,r,e);const a=this.isHorizontal?"colspan":"rowspan",c=parseInt(o.getAttribute(a)||"1"),d=parseInt(n.getAttribute(a)||"1");e.setAttribute(a,c+d,r),e.setSelection(e.createRangeIn(r));const u=this.editor.plugins.get("TableUtils");L(s.findAncestor("table"),u)}))}_getMergeableCell(){const e=this.editor.model.document,t=this.editor.plugins.get("TableUtils"),o=t.getTableCellsContainingSelection(e.selection)[0];if(!o)return;const n=this.isHorizontal?function(e,t,o){const n=e.parent,i=n.parent,r="right"==t?e.nextSibling:e.previousSibling,l=(i.getAttribute("headingColumns")||0)>0;if(!r)return;const s="right"==t?e:r,a="right"==t?r:e,{column:c}=o.getCellLocation(s),{column:d}=o.getCellLocation(a),h=parseInt(s.getAttribute("colspan")||"1"),b=u(o,s),m=u(o,a);if(l&&b!=m)return;return c+h===d?r:void 0}(o,this.direction,t):function(e,t,o){const n=e.parent,i=n.parent,r=i.getChildIndex(n);if("down"==t&&r===o.getRows(i)-1||"up"==t&&0===r)return null;const l=parseInt(e.getAttribute("rowspan")||"1"),s=i.getAttribute("headingRows")||0,a="down"==t&&r+l===s,c="up"==t&&r===s;if(s&&(a||c))return null;const d=parseInt(e.getAttribute("rowspan")||"1"),u="down"==t?r+d:r,h=[...new w(i,{endRow:u})],b=h.find((t=>t.cell===e)),m=b.column,g=h.find((({row:e,cellHeight:o,column:n})=>n===m&&("down"==t?e===u:u===e+o)));return g&&g.cell?g.cell:null}(o,this.direction,t);if(!n)return;const i=this.isHorizontal?"rowspan":"colspan",r=parseInt(o.getAttribute(i)||"1");return parseInt(n.getAttribute(i)||"1")===r?n:void 0}}function D(e){const t=e.getChild(0);return 1==e.childCount&&t.is("element","paragraph")&&t.isEmpty}class M extends e.Command{refresh(){const e=this.editor.plugins.get("TableUtils"),t=e.getSelectionAffectedTableCells(this.editor.model.document.selection),o=t[0];if(o){const n=o.findAncestor("table"),i=e.getRows(n)-1,r=e.getRowIndexes(t),l=0===r.first&&r.last===i;this.isEnabled=!l}else this.isEnabled=!1}execute(){const e=this.editor.model,t=this.editor.plugins.get("TableUtils"),o=t.getSelectionAffectedTableCells(e.document.selection),n=t.getRowIndexes(o),i=o[0],r=i.findAncestor("table"),l=t.getCellLocation(i).column;e.change((e=>{const o=n.last-n.first+1;t.removeRows(r,{at:n.first,rows:o});const i=function(e,t,o,n){const i=e.getChild(Math.min(t,n-1));let r=i.getChild(0),l=0;for(const e of i.getChildren()){if(l>o)return r;r=e,l+=parseInt(e.getAttribute("colspan")||"1")}return r}(r,n.first,l,t.getRows(r));e.setSelection(e.createPositionAt(i,0))}))}}class j extends e.Command{refresh(){const e=this.editor.plugins.get("TableUtils"),t=e.getSelectionAffectedTableCells(this.editor.model.document.selection),o=t[0];if(o){const n=o.findAncestor("table"),i=e.getColumns(n),{first:r,last:l}=e.getColumnIndexes(t);this.isEnabled=l-r<i-1}else this.isEnabled=!1}execute(){const e=this.editor.plugins.get("TableUtils"),[t,o]=function(e,t){const o=t.getSelectionAffectedTableCells(e),n=o[0],i=o.pop(),r=[n,i];return n.isBefore(i)?r:r.reverse()}(this.editor.model.document.selection,e),n=t.parent.parent,i=[...new w(n)],r={first:i.find((e=>e.cell===t)).column,last:i.find((e=>e.cell===o)).column},l=function(e,t,o,n){const i=parseInt(o.getAttribute("colspan")||"1");return i>1?o:t.previousSibling||o.nextSibling?o.nextSibling||t.previousSibling:n.first?e.reverse().find((({column:e})=>e<n.first)).cell:e.reverse().find((({column:e})=>e>n.last)).cell}(i,t,o,r);this.editor.model.change((t=>{const o=r.last-r.first+1;e.removeColumns(n,{at:r.first,columns:o}),t.setSelection(t.createPositionAt(l,0))}))}}class H extends e.Command{refresh(){const e=this.editor.plugins.get("TableUtils"),t=this.editor.model,o=e.getSelectionAffectedTableCells(t.document.selection),n=o.length>0;this.isEnabled=n,this.value=n&&o.every((e=>this._isInHeading(e,e.parent.parent)))}execute(e={}){if(e.forceValue===this.value)return;const t=this.editor.plugins.get("TableUtils"),o=this.editor.model,n=t.getSelectionAffectedTableCells(o.document.selection),i=n[0].findAncestor("table"),{first:r,last:l}=t.getRowIndexes(n),s=this.value?r:l+1,a=i.getAttribute("headingRows")||0;o.change((e=>{if(s){const t=R(i,s,s>a?a:0);for(const{cell:o}of t)P(o,s,e)}c("headingRows",s,i,e,0)}))}_isInHeading(e,t){const o=parseInt(t.getAttribute("headingRows")||"0");return!!o&&e.parent.index<o}}class U extends e.Command{refresh(){const e=this.editor.model,t=this.editor.plugins.get("TableUtils"),o=t.getSelectionAffectedTableCells(e.document.selection),n=o.length>0;this.isEnabled=n,this.value=n&&o.every((e=>u(t,e)))}execute(e={}){if(e.forceValue===this.value)return;const t=this.editor.plugins.get("TableUtils"),o=this.editor.model,n=t.getSelectionAffectedTableCells(o.document.selection),i=n[0].findAncestor("table"),{first:r,last:l}=t.getColumnIndexes(n),s=this.value?r:l+1;o.change((e=>{if(s){const t=I(i,s);for(const{cell:o,column:n}of t)E(o,n,s,e)}c("headingColumns",s,i,e,0)}))}}function $(e,t){return 4e3/K(e,t)}function K(e,t){const o=G(e,"tbody",t)||G(e,"thead",t);return q(t.editing.view.domConverter.mapViewToDom(o))}function G(e,t,o){return[...[...o.editing.mapper.toViewElement(e).getChildren()].find((e=>e.is("element","table"))).getChildren()].find((e=>e.is("element",t)))}function q(e){const t=m.global.window.getComputedStyle(e);return"border-box"===t.boxSizing?parseFloat(t.width)-parseFloat(t.paddingLeft)-parseFloat(t.paddingRight)-parseFloat(t.borderLeftWidth)-parseFloat(t.borderRightWidth):parseFloat(t.width)}function J(e){const t=Math.pow(10,2),o="number"==typeof e?e:parseFloat(e);return Math.round(o*t)/t}function X(e){return e.map((e=>"number"==typeof e?e:parseFloat(e))).filter((e=>!Number.isNaN(e))).reduce(((e,t)=>e+t),0)}function Y(e){let t=function(e){const t=e.filter((e=>"auto"===e)).length;if(0===t)return e.map((e=>J(e)));const o=X(e),n=Math.max((100-o)/t,5);return e.map((e=>"auto"===e?n:e)).map((e=>J(e)))}(e.map((e=>"auto"===e?e:parseFloat(e.replace("%","")))));const o=X(t);return 100!==o&&(t=t.map((e=>J(100*e/o))).map(((e,t,o)=>{if(!(t===o.length-1))return e;return J(e+100-X(o))}))),t.map((e=>e+"%"))}function Q(e){const t=m.global.window.getComputedStyle(e);return"border-box"===t.boxSizing?parseInt(t.width):parseFloat(t.width)+parseFloat(t.paddingLeft)+parseFloat(t.paddingRight)+parseFloat(t.borderWidth)}function Z(e,t,o,n){for(let i=0;i<Math.max(o.length,e.length);i++){const r=e[i],l=o[i];l?r?n.setAttribute("columnWidth",l,r):n.appendElement("tableColumn",{columnWidth:l},t):n.remove(r)}}function ee(e){if(e.is("element","tableColumnGroup"))return e;const t=e.getChildren();return Array.from(t).find((e=>e.is("element","tableColumnGroup")))}function te(e){const t=ee(e);return t?Array.from(t.getChildren()):[]}class oe extends e.Plugin{static get pluginName(){return"TableUtils"}static get isOfficialPlugin(){return!0}init(){this.decorate("insertColumns"),this.decorate("insertRows")}getCellLocation(e){const t=e.parent,o=t.parent,n=o.getChildIndex(t),i=new w(o,{row:n});for(const{cell:t,row:o,column:n}of i)if(t===e)return{row:o,column:n}}createTable(e,t){const o=e.createElement("table"),n=t.rows||2,i=t.columns||2;return ne(e,o,0,n,i),t.headingRows&&c("headingRows",Math.min(t.headingRows,n),o,e,0),t.headingColumns&&c("headingColumns",Math.min(t.headingColumns,i),o,e,0),o}insertRows(e,t={}){const o=this.editor.model,n=t.at||0,i=t.rows||1,r=void 0!==t.copyStructureFromAbove,l=t.copyStructureFromAbove?n-1:n,s=this.getRows(e),a=this.getColumns(e);if(n>s)throw new m.CKEditorError("tableutils-insertrows-insert-out-of-range",this,{options:t});o.change((t=>{const o=e.getAttribute("headingRows")||0;if(o>n&&c("headingRows",o+i,e,t,0),!r&&(0===n||n===s))return void ne(t,e,n,i,a);const u=r?Math.max(n,l):n,h=new w(e,{endRow:u}),b=new Array(a).fill(1);for(const{row:e,column:o,cellHeight:s,cellWidth:a,cell:c}of h){const d=e+s-1,u=e<=l&&l<=d;e<n&&n<=d?(t.setAttribute("rowspan",s+i,c),b[o]=-a):r&&u&&(b[o]=a)}for(let o=0;o<i;o++){const o=t.createElement("tableRow");t.insert(o,e,n);for(let e=0;e<b.length;e++){const n=b[e],i=t.createPositionAt(o,"end");n>0&&d(t,i,n>1?{colspan:n}:void 0),e+=Math.abs(n)-1}}}))}insertColumns(e,t={}){const o=this.editor.model,n=t.at||0,i=t.columns||1;o.change((t=>{const o=e.getAttribute("headingColumns");n<o&&t.setAttribute("headingColumns",o+i,e);const r=this.getColumns(e);if(0===n||r===n){for(const o of e.getChildren())o.is("element","tableRow")&&ie(i,t,t.createPositionAt(o,n?"end":0));return}const l=new w(e,{column:n,includeAllSlots:!0});for(const e of l){const{row:o,cell:r,cellAnchorColumn:s,cellAnchorRow:a,cellWidth:c,cellHeight:d}=e;if(s<n){t.setAttribute("colspan",c+i,r);const e=a+d-1;for(let t=o;t<=e;t++)l.skipRow(t)}else ie(i,t,e.getPositionBefore())}}))}removeRows(e,t){const o=this.editor.model,n=t.rows||1,i=this.getRows(e),r=t.at,l=r+n-1;if(l>i-1)throw new m.CKEditorError("tableutils-removerows-row-index-out-of-range",this,{table:e,options:t});o.change((t=>{const o={first:r,last:l},{cellsToMove:n,cellsToTrim:i}=function(e,{first:t,last:o}){const n=new Map,i=[];for(const{row:r,column:l,cellHeight:s,cell:a}of new w(e,{endRow:o})){const e=r+s-1;if(r>=t&&r<=o&&e>o){const e=s-(o-r+1);n.set(l,{cell:a,rowspan:e})}if(r<t&&e>=t){let n;n=e>=o?o-t+1:e-t+1,i.push({cell:a,rowspan:s-n})}}return{cellsToMove:n,cellsToTrim:i}}(e,o);if(n.size){!function(e,t,o,n){const i=new w(e,{includeAllSlots:!0,row:t}),r=[...i],l=e.getChild(t);let s;for(const{column:e,cell:t,isAnchor:i}of r)if(o.has(e)){const{cell:t,rowspan:i}=o.get(e),r=s?n.createPositionAfter(s):n.createPositionAt(l,0);n.move(n.createRangeOn(t),r),c("rowspan",i,t,n),s=t}else i&&(s=t)}(e,l+1,n,t)}for(let o=l;o>=r;o--)t.remove(e.getChild(o));for(const{rowspan:e,cell:o}of i)c("rowspan",e,o,t);!function(e,{first:t,last:o},n){const i=e.getAttribute("headingRows")||0;if(t<i){c("headingRows",o<i?i-(o-t+1):t,e,n,0)}}(e,o,t),B(e,this)||O(e,this)}))}removeColumns(e,t){const o=this.editor.model,n=t.at,i=t.columns||1,r=t.at+i-1;o.change((t=>{!function(e,t,o){const n=e.getAttribute("headingColumns")||0;if(n&&t.first<n){const i=Math.min(n-1,t.last)-t.first+1;o.setAttribute("headingColumns",n-i,e)}}(e,{first:n,last:r},t);const o=te(e);for(let i=r;i>=n;i--){for(const{cell:o,column:n,cellWidth:r}of[...new w(e)])n<=i&&r>1&&n+r>i?c("colspan",r-1,o,t):n===i&&t.remove(o);if(o[i]){const e=0===i?o[1]:o[i-1],n=parseFloat(o[i].getAttribute("columnWidth")),r=parseFloat(e.getAttribute("columnWidth"));t.remove(o[i]),t.setAttribute("columnWidth",n+r+"%",e)}}O(e,this)||B(e,this)}))}splitCellVertically(e,t=2){const o=this.editor.model,n=e.parent.parent,i=parseInt(e.getAttribute("rowspan")||"1"),r=parseInt(e.getAttribute("colspan")||"1");o.change((o=>{if(r>1){const{newCellsSpan:n,updatedSpan:l}=re(r,t);c("colspan",l,e,o);const s={};n>1&&(s.colspan=n),i>1&&(s.rowspan=i);ie(r>t?t-1:r-1,o,o.createPositionAfter(e),s)}if(r<t){const l=t-r,s=[...new w(n)],{column:a}=s.find((({cell:t})=>t===e)),d=s.filter((({cell:t,cellWidth:o,column:n})=>t!==e&&n===a||n<a&&n+o>a));for(const{cell:e,cellWidth:t}of d)o.setAttribute("colspan",t+l,e);const u={};i>1&&(u.rowspan=i),ie(l,o,o.createPositionAfter(e),u);const h=n.getAttribute("headingColumns")||0;h>a&&c("headingColumns",h+l,n,o)}}))}splitCellHorizontally(e,t=2){const o=this.editor.model,n=e.parent,i=n.parent,r=i.getChildIndex(n),l=parseInt(e.getAttribute("rowspan")||"1"),s=parseInt(e.getAttribute("colspan")||"1");o.change((o=>{if(l>1){const n=[...new w(i,{startRow:r,endRow:r+l-1,includeAllSlots:!0})],{newCellsSpan:a,updatedSpan:d}=re(l,t);c("rowspan",d,e,o);const{column:u}=n.find((({cell:t})=>t===e)),h={};a>1&&(h.rowspan=a),s>1&&(h.colspan=s);let b=0;for(const e of n){const{column:t,row:n}=e,i=t===u;b>=a&&i&&(b=0),n>=r+d&&i&&(b||ie(1,o,e.getPositionBefore(),h),b++)}}if(l<t){const n=t-l,a=[...new w(i,{startRow:0,endRow:r})];for(const{cell:t,cellHeight:i,row:l}of a)if(t!==e&&l+i>r){const e=i+n;o.setAttribute("rowspan",e,t)}const d={};s>1&&(d.colspan=s),ne(o,i,r+1,n,1,d);const u=i.getAttribute("headingRows")||0;u>r&&c("headingRows",u+n,i,o)}}))}getColumns(e){return[...e.getChild(0).getChildren()].filter((e=>e.is("element","tableCell"))).reduce(((e,t)=>e+parseInt(t.getAttribute("colspan")||"1")),0)}getRows(e){return Array.from(e.getChildren()).reduce(((e,t)=>t.is("element","tableRow")?e+1:e),0)}createTableWalker(e,t={}){return new w(e,t)}getSelectedTableCells(e){const t=[];for(const o of this.sortRanges(e.getRanges())){const e=o.getContainedElement();e&&e.is("element","tableCell")&&t.push(e)}return t}getTableCellsContainingSelection(e){const t=[];for(const o of e.getRanges()){const e=o.start.findAncestor("tableCell");e&&t.push(e)}return t}getSelectionAffectedTableCells(e){const t=this.getSelectedTableCells(e);return t.length?t:this.getTableCellsContainingSelection(e)}getRowIndexes(e){const t=e.map((e=>e.parent.index));return this._getFirstLastIndexesObject(t)}getColumnIndexes(e){const t=e[0].findAncestor("table"),o=[...new w(t)].filter((t=>e.includes(t.cell))).map((e=>e.column));return this._getFirstLastIndexesObject(o)}isSelectionRectangular(e){if(e.length<2||!this._areCellInTheSameTableSection(e))return!1;const t=new Set,o=new Set;let n=0;for(const i of e){const{row:e,column:r}=this.getCellLocation(i),l=parseInt(i.getAttribute("rowspan"))||1,s=parseInt(i.getAttribute("colspan"))||1;t.add(e),o.add(r),l>1&&t.add(e+l-1),s>1&&o.add(r+s-1),n+=l*s}const i=function(e,t){const o=Array.from(e.values()),n=Array.from(t.values()),i=Math.max(...o),r=Math.min(...o),l=Math.max(...n),s=Math.min(...n);return(i-r+1)*(l-s+1)}(t,o);return i==n}sortRanges(e){return Array.from(e).sort(le)}_getFirstLastIndexesObject(e){const t=e.sort(((e,t)=>e-t));return{first:t[0],last:t[t.length-1]}}_areCellInTheSameTableSection(e){const t=e[0].findAncestor("table"),o=this.getRowIndexes(e),n=parseInt(t.getAttribute("headingRows"))||0;if(!this._areIndexesInSameSection(o,n))return!1;const i=this.getColumnIndexes(e),r=parseInt(t.getAttribute("headingColumns"))||0;return this._areIndexesInSameSection(i,r)}_areIndexesInSameSection({first:e,last:t},o){return e<o===t<o}}function ne(e,t,o,n,i,r={}){for(let l=0;l<n;l++){const n=e.createElement("tableRow");e.insert(n,t,o),ie(i,e,e.createPositionAt(n,"end"),r)}}function ie(e,t,o,n={}){for(let i=0;i<e;i++)d(t,o,n)}function re(e,t){if(e<t)return{newCellsSpan:1,updatedSpan:1};const o=Math.floor(e/t);return{newCellsSpan:o,updatedSpan:e-o*t+o}}function le(e,t){const o=e.start,n=t.start;return o.isBefore(n)?-1:1}class se extends e.Command{refresh(){const e=this.editor.plugins.get(oe),t=e.getSelectedTableCells(this.editor.model.document.selection);this.isEnabled=e.isSelectionRectangular(t)}execute(){const e=this.editor.model,t=this.editor.plugins.get(oe);e.change((o=>{const n=t.getSelectedTableCells(e.document.selection),i=n.shift(),{mergeWidth:r,mergeHeight:l}=function(e,t,o){let n=0,i=0;for(const e of t){const{row:t,column:r}=o.getCellLocation(e);n=de(e,r,n,"colspan"),i=de(e,t,i,"rowspan")}const{row:r,column:l}=o.getCellLocation(e),s=n-l,a=i-r;return{mergeWidth:s,mergeHeight:a}}(i,n,t);c("colspan",r,i,o),c("rowspan",l,i,o);for(const e of n)ae(e,i,o);L(i.findAncestor("table"),t),o.setSelection(i,"in")}))}}function ae(e,t,o){ce(e)||(ce(t)&&o.remove(o.createRangeIn(t)),o.move(o.createRangeIn(e),o.createPositionAt(t,"end"))),o.remove(e)}function ce(e){const t=e.getChild(0);return 1==e.childCount&&t.is("element","paragraph")&&t.isEmpty}function de(e,t,o,n){const i=parseInt(e.getAttribute(n)||"1");return Math.max(o,t+i)}class ue extends e.Command{constructor(e){super(e),this.affectsData=!1}refresh(){const e=this.editor.plugins.get("TableUtils").getSelectionAffectedTableCells(this.editor.model.document.selection);this.isEnabled=e.length>0}execute(){const e=this.editor.model,t=this.editor.plugins.get("TableUtils"),o=t.getSelectionAffectedTableCells(e.document.selection),n=t.getRowIndexes(o),i=o[0].findAncestor("table"),r=[];for(let t=n.first;t<=n.last;t++)for(const o of i.getChild(t).getChildren())r.push(e.createRangeOn(o));e.change((e=>{e.setSelection(r)}))}}class he extends e.Command{constructor(e){super(e),this.affectsData=!1}refresh(){const e=this.editor.plugins.get("TableUtils").getSelectionAffectedTableCells(this.editor.model.document.selection);this.isEnabled=e.length>0}execute(){const e=this.editor.plugins.get("TableUtils"),t=this.editor.model,o=e.getSelectionAffectedTableCells(t.document.selection),n=o[0],i=o.pop(),r=n.findAncestor("table"),l=e.getCellLocation(n),s=e.getCellLocation(i),a=Math.min(l.column,s.column),c=Math.max(l.column,s.column),d=[];for(const e of new w(r,{startColumn:a,endColumn:c}))d.push(t.createRangeOn(e.cell));t.change((e=>{e.setSelection(d)}))}}function be(e){e.document.registerPostFixer((t=>function(e,t){const o=t.document.differ.getChanges();let n=!1;const i=new Set;for(const t of o){let o=null;"insert"==t.type&&"table"==t.name&&(o=t.position.nodeAfter),"insert"!=t.type&&"remove"!=t.type||"tableRow"!=t.name&&"tableCell"!=t.name||(o=t.position.findAncestor("table")),pe(t)&&(o=t.range.start.findAncestor("table")),o&&!i.has(o)&&(n=me(o,e)||n,n=ge(o,e)||n,i.add(o))}return n}(t,e)))}function me(e,t){let o=!1;const n=function(e){const t=parseInt(e.getAttribute("headingRows")||"0"),o=Array.from(e.getChildren()).reduce(((e,t)=>t.is("element","tableRow")?e+1:e),0),n=[];for(const{row:i,cell:r,cellHeight:l}of new w(e)){if(l<2)continue;const e=i<t?t:o;if(i+l>e){const t=e-i;n.push({cell:r,rowspan:t})}}return n}(e);if(n.length){o=!0;for(const e of n)c("rowspan",e.rowspan,e.cell,t,1)}return o}function ge(e,t){let o=!1;const n=function(e){const t=new Array(e.childCount).fill(0);for(const{rowIndex:o}of new w(e,{includeAllSlots:!0}))t[o]++;return t}(e),i=[];for(const[t,o]of n.entries())!o&&e.getChild(t).is("element","tableRow")&&i.push(t);if(i.length){o=!0;for(const o of i.reverse())t.remove(e.getChild(o)),n.splice(o,1)}const r=n.filter(((t,o)=>e.getChild(o).is("element","tableRow"))),l=r[0];if(!r.every((e=>e===l))){const n=r.reduce(((e,t)=>t>e?t:e),0);for(const[i,l]of r.entries()){const r=n-l;if(r){for(let o=0;o<r;o++)d(t,t.createPositionAt(e.getChild(i),"end"));o=!0}}}return o}function pe(e){if("attribute"!==e.type)return!1;const t=e.attributeKey;return"headingRows"===t||"colspan"===t||"rowspan"===t}function fe(e){e.document.registerPostFixer((t=>function(e,t){const o=t.document.differ.getChanges();let n=!1;for(const t of o)"insert"==t.type&&"table"==t.name&&(n=we(t.position.nodeAfter,e)||n),"insert"==t.type&&"tableRow"==t.name&&(n=_e(t.position.nodeAfter,e)||n),"insert"==t.type&&"tableCell"==t.name&&(n=ke(t.position.nodeAfter,e)||n),"remove"!=t.type&&"insert"!=t.type||!ve(t)||(n=ke(t.position.parent,e)||n);return n}(t,e)))}function we(e,t){let o=!1;for(const n of e.getChildren())n.is("element","tableRow")&&(o=_e(n,t)||o);return o}function _e(e,t){let o=!1;for(const n of e.getChildren())o=ke(n,t)||o;return o}function ke(e,t){if(0==e.childCount)return t.insertElement("paragraph",e),!0;const o=Array.from(e.getChildren()).filter((e=>e.is("$text")));for(const e of o)t.wrap(t.createRangeOn(e),"paragraph");return!!o.length}function ve(e){return!!e.position.parent.is("element","tableCell")&&("insert"==e.type&&"$text"==e.name||"remove"==e.type)}function Ce(e,t){if(!e.is("element","paragraph"))return!1;const o=t.toViewElement(e);return!!o&&y(e)!==o.is("element","span")}var ye=o(591),Ae=o.n(ye),Te=o(639),xe=o.n(Te),Se=o(128),Ve=o.n(Se),Re=o(21),Pe=o.n(Re),Ie=o(51),Ee=o.n(Ie),ze=o(817),Be={attributes:{"data-cke":!0}};Be.setAttributes=Pe(),Be.insert=Ve().bind(null,"head"),Be.domAPI=xe(),Be.insertStyleElement=Ee();Ae()(ze.A,Be);ze.A&&ze.A.locals&&ze.A.locals;class Oe extends e.Plugin{static get pluginName(){return"TableEditing"}static get isOfficialPlugin(){return!0}static get requires(){return[oe]}constructor(e){super(e),this._additionalSlots=[]}init(){const e=this.editor,t=e.model,o=t.schema,n=e.conversion,i=e.plugins.get(oe);o.register("table",{inheritAllFrom:"$blockObject",allowAttributes:["headingRows","headingColumns"]}),o.register("tableRow",{allowIn:"table",isLimit:!0}),o.register("tableCell",{allowContentOf:"$container",allowIn:"tableRow",allowAttributes:["colspan","rowspan"],isLimit:!0,isSelectable:!0}),n.for("upcast").add((e=>{e.on("element:figure",((e,t,o)=>{if(!o.consumable.test(t.viewItem,{name:!0,classes:"table"}))return;const n=function(e){for(const t of e.getChildren())if(t.is("element","table"))return t}(t.viewItem);if(!n||!o.consumable.test(n,{name:!0}))return;o.consumable.consume(t.viewItem,{name:!0,classes:"table"});const i=o.convertItem(n,t.modelCursor),r=(0,m.first)(i.modelRange.getItems());r?(o.convertChildren(t.viewItem,o.writer.createPositionAt(r,"end")),o.updateConversionResult(r,t)):o.consumable.revert(t.viewItem,{name:!0,classes:"table"})}))})),n.for("upcast").add(g()),n.for("editingDowncast").elementToStructure({model:{name:"table",attributes:["headingRows"]},view:k(i,{asWidget:!0,additionalSlots:this._additionalSlots})}),n.for("dataDowncast").elementToStructure({model:{name:"table",attributes:["headingRows"]},view:k(i,{additionalSlots:this._additionalSlots})}),n.for("upcast").elementToElement({model:"tableRow",view:"tr"}),n.for("upcast").add((e=>{e.on("element:tr",((e,t)=>{t.viewItem.isEmpty&&0==t.modelCursor.index&&e.stop()}),{priority:"high"})})),n.for("downcast").elementToElement({model:"tableRow",view:(e,{writer:t})=>e.isEmpty?t.createEmptyElement("tr"):t.createContainerElement("tr")}),n.for("upcast").elementToElement({model:"tableCell",view:"td"}),n.for("upcast").elementToElement({model:"tableCell",view:"th"}),n.for("upcast").add(p("td")),n.for("upcast").add(p("th")),n.for("editingDowncast").elementToElement({model:"tableCell",view:v({asWidget:!0})}),n.for("dataDowncast").elementToElement({model:"tableCell",view:v()}),n.for("editingDowncast").elementToElement({model:"paragraph",view:C({asWidget:!0}),converterPriority:"high"}),n.for("dataDowncast").elementToElement({model:"paragraph",view:C(),converterPriority:"high"}),n.for("downcast").attributeToAttribute({model:"colspan",view:"colspan"}),n.for("upcast").attributeToAttribute({model:{key:"colspan",value:Le("colspan")},view:"colspan"}),n.for("downcast").attributeToAttribute({model:"rowspan",view:"rowspan"}),n.for("upcast").attributeToAttribute({model:{key:"rowspan",value:Le("rowspan")},view:"rowspan"}),e.config.define("table.defaultHeadings.rows",0),e.config.define("table.defaultHeadings.columns",0),e.commands.add("insertTable",new A(e)),e.commands.add("insertTableRowAbove",new T(e,{order:"above"})),e.commands.add("insertTableRowBelow",new T(e,{order:"below"})),e.commands.add("insertTableColumnLeft",new x(e,{order:"left"})),e.commands.add("insertTableColumnRight",new x(e,{order:"right"})),e.commands.add("removeTableRow",new M(e)),e.commands.add("removeTableColumn",new j(e)),e.commands.add("splitTableCellVertically",new S(e,{direction:"vertically"})),e.commands.add("splitTableCellHorizontally",new S(e,{direction:"horizontally"})),e.commands.add("mergeTableCells",new se(e)),e.commands.add("mergeTableCellRight",new N(e,{direction:"right"})),e.commands.add("mergeTableCellLeft",new N(e,{direction:"left"})),e.commands.add("mergeTableCellDown",new N(e,{direction:"down"})),e.commands.add("mergeTableCellUp",new N(e,{direction:"up"})),e.commands.add("setTableColumnHeader",new U(e)),e.commands.add("setTableRowHeader",new H(e)),e.commands.add("selectTableRow",new ue(e)),e.commands.add("selectTableColumn",new he(e)),be(t),fe(t),this.listenTo(t.document,"change:data",(()=>{!function(e,t){const o=e.document.differ;for(const e of o.getChanges()){let o,n=!1;if("attribute"==e.type){const t=e.range.start.nodeAfter;if(!t||!t.is("element","table"))continue;if("headingRows"!=e.attributeKey&&"headingColumns"!=e.attributeKey)continue;o=t,n="headingRows"==e.attributeKey}else"tableRow"!=e.name&&"tableCell"!=e.name||(o=e.position.findAncestor("table"),n="tableRow"==e.name);if(!o)continue;const i=o.getAttribute("headingRows")||0,r=o.getAttribute("headingColumns")||0,l=new w(o);for(const e of l){const o=e.row<i||e.column<r?"th":"td",l=t.mapper.toViewElement(e.cell);l&&l.is("element")&&l.name!=o&&t.reconvertItem(n?e.cell.parent:e.cell)}}}(t,e.editing),function(e,t){const o=e.document.differ,n=new Set;for(const e of o.getChanges()){const t="attribute"==e.type?e.range.start.parent:e.position.parent;t.is("element","tableCell")&&n.add(t)}for(const e of n.values()){const o=Array.from(e.getChildren()).filter((e=>Ce(e,t.mapper)));for(const e of o)t.reconvertItem(e)}}(t,e.editing)}))}registerAdditionalSlot(e){this._additionalSlots.push(e)}}function Le(e){return t=>{const o=parseInt(t.getAttribute(e));return Number.isNaN(o)||o<=0?null:o}}var Fe=o(311),We=o(712),Ne={attributes:{"data-cke":!0}};Ne.setAttributes=Pe(),Ne.insert=Ve().bind(null,"head"),Ne.domAPI=xe(),Ne.insertStyleElement=Ee();Ae()(We.A,Ne);We.A&&We.A.locals&&We.A.locals;class De extends Fe.View{constructor(e){super(e);const t=this.bindTemplate;this.items=this._createGridCollection(),this.keystrokes=new m.KeystrokeHandler,this.focusTracker=new m.FocusTracker,this.set("rows",0),this.set("columns",0),this.bind("label").to(this,"columns",this,"rows",((e,t)=>`${t} × ${e}`)),this.setTemplate({tag:"div",attributes:{class:["ck"]},children:[{tag:"div",attributes:{class:["ck-insert-table-dropdown__grid"]},on:{"mouseover@.ck-insert-table-dropdown-grid-box":t.to("boxover")},children:this.items},{tag:"div",attributes:{class:["ck","ck-insert-table-dropdown__label"],"aria-hidden":!0},children:[{text:t.to("label")}]}],on:{mousedown:t.to((e=>{e.preventDefault()})),click:t.to((()=>{this.fire("execute")}))}}),this.on("boxover",((e,t)=>{const{row:o,column:n}=t.target.dataset;this.items.get(10*(parseInt(o,10)-1)+(parseInt(n,10)-1)).focus()})),this.focusTracker.on("change:focusedElement",((e,t,o)=>{if(!o)return;const{row:n,column:i}=o.dataset;this.set({rows:parseInt(n),columns:parseInt(i)})})),this.on("change:columns",(()=>this._highlightGridBoxes())),this.on("change:rows",(()=>this._highlightGridBoxes()))}render(){super.render(),(0,Fe.addKeyboardHandlingForGrid)({keystrokeHandler:this.keystrokes,focusTracker:this.focusTracker,gridItems:this.items,numberOfColumns:10,uiLanguageDirection:this.locale&&this.locale.uiLanguageDirection});for(const e of this.items)this.focusTracker.add(e.element);this.keystrokes.listenTo(this.element)}reset(){this.set({rows:1,columns:1})}focus(){this.items.get(0).focus()}focusLast(){this.items.get(0).focus()}_highlightGridBoxes(){const e=this.rows,t=this.columns;this.items.map(((o,n)=>{const i=Math.floor(n/10)<e&&n%10<t;o.set("isOn",i)}))}_createGridButton(e,t,o,n){const i=new Fe.ButtonView(e);return i.set({label:n,class:"ck-insert-table-dropdown-grid-box"}),i.extendTemplate({attributes:{"data-row":t,"data-column":o}}),i}_createGridCollection(){const e=[];for(let t=0;t<100;t++){const o=Math.floor(t/10),n=t%10,i=`${o+1} × ${n+1}`;e.push(this._createGridButton(this.locale,o+1,n+1,i))}return this.createCollection(e)}}class Me extends e.Plugin{static get pluginName(){return"TableUI"}static get isOfficialPlugin(){return!0}init(){const t=this.editor,o=this.editor.t,n="ltr"===t.locale.contentLanguageDirection;t.ui.componentFactory.add("insertTable",(n=>{const i=t.commands.get("insertTable"),r=(0,Fe.createDropdown)(n);let l;return r.bind("isEnabled").to(i),r.buttonView.set({icon:e.icons.table,label:o("Insert table"),tooltip:!0}),r.on("change:isOpen",(()=>{l||(l=new De(n),r.panelView.children.add(l),l.delegate("execute").to(r),r.on("execute",(()=>{t.execute("insertTable",{rows:l.rows,columns:l.columns}),t.editing.view.focus()})))})),r})),t.ui.componentFactory.add("menuBar:insertTable",(n=>{const i=t.commands.get("insertTable"),r=new Fe.MenuBarMenuView(n),l=new De(n);return l.delegate("execute").to(r),r.on("change:isOpen",((e,t,o)=>{o||l.reset()})),l.on("execute",(()=>{t.execute("insertTable",{rows:l.rows,columns:l.columns}),t.editing.view.focus()})),r.buttonView.set({label:o("Table"),icon:e.icons.table}),r.panelView.children.add(l),r.bind("isEnabled").to(i),r})),t.ui.componentFactory.add("tableColumn",(e=>{const t=[{type:"switchbutton",model:{commandName:"setTableColumnHeader",label:o("Header column"),bindIsOn:!0}},{type:"separator"},{type:"button",model:{commandName:n?"insertTableColumnLeft":"insertTableColumnRight",label:o("Insert column left")}},{type:"button",model:{commandName:n?"insertTableColumnRight":"insertTableColumnLeft",label:o("Insert column right")}},{type:"button",model:{commandName:"removeTableColumn",label:o("Delete column")}},{type:"button",model:{commandName:"selectTableColumn",label:o("Select column")}}];return this._prepareDropdown(o("Column"),'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M2.5 1h15A1.5 1.5 0 0 1 19 2.5v15a1.5 1.5 0 0 1-1.5 1.5h-15A1.5 1.5 0 0 1 1 17.5v-15A1.5 1.5 0 0 1 2.5 1zM2 2v16h16V2H2z" opacity=".6"/><path d="M18 7v1H2V7h16zm0 5v1H2v-1h16z" opacity=".6"/><path d="M14 1v18a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V1a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1zm-2 1H8v4h4V2zm0 6H8v4h4V8zm0 6H8v4h4v-4z"/></svg>',t,e)})),t.ui.componentFactory.add("tableRow",(e=>{const t=[{type:"switchbutton",model:{commandName:"setTableRowHeader",label:o("Header row"),bindIsOn:!0}},{type:"separator"},{type:"button",model:{commandName:"insertTableRowAbove",label:o("Insert row above")}},{type:"button",model:{commandName:"insertTableRowBelow",label:o("Insert row below")}},{type:"button",model:{commandName:"removeTableRow",label:o("Delete row")}},{type:"button",model:{commandName:"selectTableRow",label:o("Select row")}}];return this._prepareDropdown(o("Row"),'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M2.5 1h15A1.5 1.5 0 0 1 19 2.5v15a1.5 1.5 0 0 1-1.5 1.5h-15A1.5 1.5 0 0 1 1 17.5v-15A1.5 1.5 0 0 1 2.5 1zM2 2v16h16V2H2z" opacity=".6"/><path d="M7 2h1v16H7V2zm5 0h1v16h-1V2z" opacity=".6"/><path d="M1 6h18a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1zm1 2v4h4V8H2zm6 0v4h4V8H8zm6 0v4h4V8h-4z"/></svg>',t,e)})),t.ui.componentFactory.add("mergeTableCells",(e=>{const t=[{type:"button",model:{commandName:"mergeTableCellUp",label:o("Merge cell up")}},{type:"button",model:{commandName:n?"mergeTableCellRight":"mergeTableCellLeft",label:o("Merge cell right")}},{type:"button",model:{commandName:"mergeTableCellDown",label:o("Merge cell down")}},{type:"button",model:{commandName:n?"mergeTableCellLeft":"mergeTableCellRight",label:o("Merge cell left")}},{type:"separator"},{type:"button",model:{commandName:"splitTableCellVertically",label:o("Split cell vertically")}},{type:"button",model:{commandName:"splitTableCellHorizontally",label:o("Split cell horizontally")}}];return this._prepareMergeSplitButtonDropdown(o("Merge cells"),'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M2.5 1h15A1.5 1.5 0 0 1 19 2.5v15a1.5 1.5 0 0 1-1.5 1.5h-15A1.5 1.5 0 0 1 1 17.5v-15A1.5 1.5 0 0 1 2.5 1zM2 2v16h16V2H2z" opacity=".6"/><path d="M7 2h1v16H7V2zm5 0h1v7h-1V2zm6 5v1H2V7h16zM8 12v1H2v-1h6z" opacity=".6"/><path d="M7 7h12a1 1 0 0 1 1 1v11a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1zm1 2v9h10V9H8z"/></svg>',t,e)}))}_prepareDropdown(e,t,o,n){const i=this.editor,r=(0,Fe.createDropdown)(n),l=this._fillDropdownWithListOptions(r,o);return r.buttonView.set({label:e,icon:t,tooltip:!0}),r.bind("isEnabled").toMany(l,"isEnabled",((...e)=>e.some((e=>e)))),this.listenTo(r,"execute",(e=>{i.execute(e.source.commandName),e.source instanceof Fe.SwitchButtonView||i.editing.view.focus()})),r}_prepareMergeSplitButtonDropdown(e,t,o,n){const i=this.editor,r=(0,Fe.createDropdown)(n,Fe.SplitButtonView),l="mergeTableCells",s=i.commands.get(l),a=this._fillDropdownWithListOptions(r,o);return r.buttonView.set({label:e,icon:t,tooltip:!0,isEnabled:!0}),r.bind("isEnabled").toMany([s,...a],"isEnabled",((...e)=>e.some((e=>e)))),this.listenTo(r.buttonView,"execute",(()=>{i.execute(l),i.editing.view.focus()})),this.listenTo(r,"execute",(e=>{i.execute(e.source.commandName),i.editing.view.focus()})),r}_fillDropdownWithListOptions(e,t){const o=this.editor,n=[],i=new m.Collection;for(const e of t)je(e,o,n,i);return(0,Fe.addListToDropdown)(e,i),n}}function je(e,t,o,n){if("button"===e.type||"switchbutton"===e.type){const n=e.model=new Fe.ViewModel(e.model),{commandName:i,bindIsOn:r}=e.model,l=t.commands.get(i);o.push(l),n.set({commandName:i}),n.bind("isEnabled").to(l),r&&n.bind("isOn").to(l,"value"),n.set({withText:!0})}n.add(e)}var He=o(719),Ue={attributes:{"data-cke":!0}};Ue.setAttributes=Pe(),Ue.insert=Ve().bind(null,"head"),Ue.domAPI=xe(),Ue.insertStyleElement=Ee();Ae()(He.A,Ue);He.A&&He.A.locals&&He.A.locals;class $e extends e.Plugin{static get pluginName(){return"TableSelection"}static get isOfficialPlugin(){return!0}static get requires(){return[oe,oe]}init(){const e=this.editor,t=e.model,o=e.editing.view;this.listenTo(t,"deleteContent",((e,t)=>this._handleDeleteContent(e,t)),{priority:"high"}),this.listenTo(o.document,"insertText",((e,t)=>this._handleInsertTextEvent(e,t)),{priority:"high"}),this._defineSelectionConverter(),this._enablePluginDisabling()}getSelectedTableCells(){const e=this.editor.plugins.get(oe),t=this.editor.model.document.selection,o=e.getSelectedTableCells(t);return 0==o.length?null:o}getSelectionAsFragment(){const e=this.editor.plugins.get(oe),t=this.getSelectedTableCells();return t?this.editor.model.change((o=>{const n=o.createDocumentFragment(),{first:i,last:r}=e.getColumnIndexes(t),{first:l,last:s}=e.getRowIndexes(t),a=t[0].findAncestor("table");let c=s,d=r;if(e.isSelectionRectangular(t)){const e={firstColumn:i,lastColumn:r,firstRow:l,lastRow:s};c=F(a,e),d=W(a,e)}const u=V(a,{startRow:l,startColumn:i,endRow:c,endColumn:d},o);return o.insert(u,n,0),n})):null}setCellSelection(e,t){const o=this._getCellsToSelect(e,t);this.editor.model.change((e=>{e.setSelection(o.cells.map((t=>e.createRangeOn(t))),{backward:o.backward})}))}getFocusCell(){const e=[...this.editor.model.document.selection.getRanges()].pop().getContainedElement();return e&&e.is("element","tableCell")?e:null}getAnchorCell(){const e=this.editor.model.document.selection,t=(0,m.first)(e.getRanges()).getContainedElement();return t&&t.is("element","tableCell")?t:null}_defineSelectionConverter(){const e=this.editor,t=new Set;e.conversion.for("editingDowncast").add((e=>e.on("selection",((e,o,n)=>{const i=n.writer;!function(e){for(const o of t)e.removeClass("ck-editor__editable_selected",o);t.clear()}(i);const r=this.getSelectedTableCells();if(!r)return;for(const e of r){const o=n.mapper.toViewElement(e);i.addClass("ck-editor__editable_selected",o),t.add(o)}const l=n.mapper.toViewElement(r[r.length-1]);i.setSelection(l,0)}),{priority:"lowest"})))}_enablePluginDisabling(){const e=this.editor;this.on("change:isEnabled",(()=>{if(!this.isEnabled){const t=this.getSelectedTableCells();if(!t)return;e.model.change((o=>{const n=o.createPositionAt(t[0],0),i=e.model.schema.getNearestSelectionRange(n);o.setSelection(i)}))}}))}_handleDeleteContent(e,t){const o=this.editor.plugins.get(oe),n=t[0],i=t[1],r=this.editor.model,l=!i||"backward"==i.direction,s=o.getSelectedTableCells(n);s.length&&(e.stop(),r.change((e=>{const t=s[l?s.length-1:0];r.change((e=>{for(const t of s)r.deleteContent(e.createSelection(t,"in"))}));const o=r.schema.getNearestSelectionRange(e.createPositionAt(t,0));n.is("documentSelection")?e.setSelection(o):n.setTo(o)})))}_handleInsertTextEvent(e,t){const o=this.editor,n=this.getSelectedTableCells();if(!n)return;const i=o.editing.view,r=o.editing.mapper,l=n.map((e=>i.createRangeOn(r.toViewElement(e))));t.selection=i.createSelection(l)}_getCellsToSelect(e,t){const o=this.editor.plugins.get("TableUtils"),n=o.getCellLocation(e),i=o.getCellLocation(t),r=Math.min(n.row,i.row),l=Math.max(n.row,i.row),s=Math.min(n.column,i.column),a=Math.max(n.column,i.column),c=new Array(l-r+1).fill(null).map((()=>[])),d={startRow:r,endRow:l,startColumn:s,endColumn:a};for(const{row:t,cell:o}of new w(e.findAncestor("table"),d))c[t-r].push(o);const u=i.row<n.row,h=i.column<n.column;return u&&c.reverse(),h&&c.forEach((e=>e.reverse())),{cells:c.flat(),backward:u||h}}}var Ke=o(331);class Ge extends e.Plugin{static get pluginName(){return"TableClipboard"}static get isOfficialPlugin(){return!0}static get requires(){return[Ke.ClipboardMarkersUtils,Ke.ClipboardPipeline,$e,oe]}init(){const e=this.editor,t=e.editing.view.document;this.listenTo(t,"copy",((e,t)=>this._onCopyCut(e,t))),this.listenTo(t,"cut",((e,t)=>this._onCopyCut(e,t))),this.listenTo(e.model,"insertContent",((e,[t,o])=>this._onInsertContent(e,t,o)),{priority:"high"}),this.decorate("_replaceTableSlotCell")}_onCopyCut(e,t){const o=this.editor.editing.view,n=this.editor.plugins.get($e),i=this.editor.plugins.get(Ke.ClipboardMarkersUtils);n.getSelectedTableCells()&&("cut"!=e.name||this.editor.model.canEditAt(this.editor.model.document.selection))&&(t.preventDefault(),e.stop(),this.editor.model.enqueueChange({isUndoable:"cut"===e.name},(()=>{const r=i._copySelectedFragmentWithMarkers(e.name,this.editor.model.document.selection,(()=>n.getSelectionAsFragment()));o.document.fire("clipboardOutput",{dataTransfer:t.dataTransfer,content:this.editor.data.toView(r),method:e.name})})))}_onInsertContent(e,t,o){if(o&&!o.is("documentSelection"))return;const n=this.editor.model,i=this.editor.plugins.get(oe),r=this.editor.plugins.get(Ke.ClipboardMarkersUtils),l=this.getTableIfOnlyTableInContent(t,n);if(!l)return;const s=i.getSelectionAffectedTableCells(n.document.selection);s.length?(e.stop(),t.is("documentFragment")?r._pasteMarkersIntoTransformedElement(t.markers,(e=>this._replaceSelectedCells(l,s,e))):this.editor.model.change((e=>{this._replaceSelectedCells(l,s,e)}))):L(l,i)}_replaceSelectedCells(e,t,o){const n=this.editor.plugins.get(oe),i={width:n.getColumns(e),height:n.getRows(e)},r=function(e,t,o,n){const i=e[0].findAncestor("table"),r=n.getColumnIndexes(e),l=n.getRowIndexes(e),s={firstColumn:r.first,lastColumn:r.last,firstRow:l.first,lastRow:l.last},a=1===e.length;a&&(s.lastRow+=t.height-1,s.lastColumn+=t.width-1,function(e,t,o,n){const i=n.getColumns(e),r=n.getRows(e);o>i&&n.insertColumns(e,{at:i,columns:o-i});t>r&&n.insertRows(e,{at:r,rows:t-r})}(i,s.lastRow+1,s.lastColumn+1,n));a||!n.isSelectionRectangular(e)?function(e,t,o){const{firstRow:n,lastRow:i,firstColumn:r,lastColumn:l}=t,s={first:n,last:i},a={first:r,last:l};Je(e,r,s,o),Je(e,l+1,s,o),qe(e,n,a,o),qe(e,i+1,a,o,n)}(i,s,o):(s.lastRow=F(i,s),s.lastColumn=W(i,s));return s}(t,i,o,n),l=r.lastRow-r.firstRow+1,s=r.lastColumn-r.firstColumn+1;e=V(e,{startRow:0,startColumn:0,endRow:Math.min(l,i.height)-1,endColumn:Math.min(s,i.width)-1},o);const a=t[0].findAncestor("table"),c=this._replaceSelectedCellsWithPasted(e,i,a,r,o);if(this.editor.plugins.get("TableSelection").isEnabled){const e=n.sortRanges(c.map((e=>o.createRangeOn(e))));o.setSelection(e)}else o.setSelection(c[0],0);return a}_replaceSelectedCellsWithPasted(e,t,o,n,i){const{width:r,height:l}=t,s=function(e,t,o){const n=new Array(o).fill(null).map((()=>new Array(t).fill(null)));for(const{column:t,row:o,cell:i}of new w(e))n[o][t]=i;return n}(e,r,l),a=[...new w(o,{startRow:n.firstRow,endRow:n.lastRow,startColumn:n.firstColumn,endColumn:n.lastColumn,includeAllSlots:!0})],c=[];let d;for(const e of a){const{row:t,column:o}=e;o===n.firstColumn&&(d=e.getPositionBefore());const a=t-n.firstRow,u=o-n.firstColumn,h=s[a%l][u%r],b=h?i.cloneElement(h):null,m=this._replaceTableSlotCell(e,b,d,i);m&&(z(m,t,o,n.lastRow,n.lastColumn,i),c.push(m),d=i.createPositionAfter(m))}const u=parseInt(o.getAttribute("headingRows")||"0"),h=parseInt(o.getAttribute("headingColumns")||"0"),b=n.firstRow<u&&u<=n.lastRow,m=n.firstColumn<h&&h<=n.lastColumn;if(b){const e=qe(o,u,{first:n.firstColumn,last:n.lastColumn},i,n.firstRow);c.push(...e)}if(m){const e=Je(o,h,{first:n.firstRow,last:n.lastRow},i);c.push(...e)}return c}_replaceTableSlotCell(e,t,o,n){const{cell:i,isAnchor:r}=e;return r&&n.remove(i),t?(n.insert(t,o),t):null}getTableIfOnlyTableInContent(e,t){if(!e.is("documentFragment")&&!e.is("element"))return null;if(e.is("element","table"))return e;if(1==e.childCount&&e.getChild(0).is("element","table"))return e.getChild(0);const o=t.createRangeIn(e);for(const e of o.getItems())if(e.is("element","table")){const n=t.createRange(o.start,t.createPositionBefore(e));if(t.hasContent(n,{ignoreWhitespaces:!0}))return null;const i=t.createRange(t.createPositionAfter(e),o.end);return t.hasContent(i,{ignoreWhitespaces:!0})?null:e}return null}}function qe(e,t,o,n,i=0){if(t<1)return;return R(e,t,i).filter((({column:e,cellWidth:t})=>Xe(e,t,o))).map((({cell:e})=>P(e,t,n)))}function Je(e,t,o,n){if(t<1)return;return I(e,t).filter((({row:e,cellHeight:t})=>Xe(e,t,o))).map((({cell:e,column:o})=>E(e,o,t,n)))}function Xe(e,t,o){const n=e+t-1,{first:i,last:r}=o;return e>=i&&e<=r||e<i&&n>=i}class Ye extends e.Plugin{static get pluginName(){return"TableKeyboard"}static get isOfficialPlugin(){return!0}static get requires(){return[$e,oe]}init(){const e=this.editor,t=e.editing.view.document,o=e.t;this.listenTo(t,"arrowKey",((...e)=>this._onArrowKey(...e)),{context:"table"}),this.listenTo(t,"tab",((...e)=>this._handleTabOnSelectedTable(...e)),{context:"figure"}),this.listenTo(t,"tab",((...e)=>this._handleTab(...e)),{context:["th","td"]}),e.accessibility.addKeystrokeInfoGroup({id:"table",label:o("Keystrokes that can be used in a table cell"),keystrokes:[{label:o("Move the selection to the next cell"),keystroke:"Tab"},{label:o("Move the selection to the previous cell"),keystroke:"Shift+Tab"},{label:o("Insert a new table row (when in the last cell of a table)"),keystroke:"Tab"},{label:o("Navigate through the table"),keystroke:[["arrowup"],["arrowright"],["arrowdown"],["arrowleft"]]}]})}_handleTabOnSelectedTable(e,t){const o=this.editor,n=o.model.document.selection.getSelectedElement();n&&n.is("element","table")&&(t.preventDefault(),t.stopPropagation(),e.stop(),o.model.change((e=>{e.setSelection(e.createRangeIn(n.getChild(0).getChild(0)))})))}_handleTab(e,t){const o=this.editor,n=this.editor.plugins.get(oe),i=this.editor.plugins.get("TableSelection"),r=o.model.document.selection,l=!t.shiftKey;let s=n.getTableCellsContainingSelection(r)[0];if(s||(s=i.getFocusCell()),!s)return;t.preventDefault(),t.stopPropagation(),e.stop();const a=s.parent,c=a.parent,d=c.getChildIndex(a),u=a.getChildIndex(s),h=0===u;if(!l&&h&&0===d)return void o.model.change((e=>{e.setSelection(e.createRangeOn(c))}));const b=u===a.childCount-1,m=d===n.getRows(c)-1;if(l&&m&&b&&(o.execute("insertTableRowBelow"),d===n.getRows(c)-1))return void o.model.change((e=>{e.setSelection(e.createRangeOn(c))}));let g;if(l&&b){const e=c.getChild(d+1);g=e.getChild(0)}else if(!l&&h){const e=c.getChild(d-1);g=e.getChild(e.childCount-1)}else g=a.getChild(u+(l?1:-1));o.model.change((e=>{e.setSelection(e.createRangeIn(g))}))}_onArrowKey(e,t){const o=this.editor,n=t.keyCode,i=(0,m.getLocalizedArrowKeyCodeDirection)(n,o.locale.contentLanguageDirection);this._handleArrowKeys(i,t.shiftKey)&&(t.preventDefault(),t.stopPropagation(),e.stop())}_handleArrowKeys(e,t){const o=this.editor.plugins.get(oe),n=this.editor.plugins.get("TableSelection"),i=this.editor.model,r=i.document.selection,l=["right","down"].includes(e),s=o.getSelectedTableCells(r);if(s.length){let o;return o=t?n.getFocusCell():l?s[s.length-1]:s[0],this._navigateFromCellInDirection(o,e,t),!0}const a=r.focus.findAncestor("tableCell");if(!a)return!1;if(!r.isCollapsed)if(t){if(r.isBackward==l&&!r.containsEntireContent(a))return!1}else{const e=r.getSelectedElement();if(!e||!i.schema.isObject(e))return!1}return!!this._isSelectionAtCellEdge(r,a,l)&&(this._navigateFromCellInDirection(a,e,t),!0)}_isSelectionAtCellEdge(e,t,o){const n=this.editor.model,i=this.editor.model.schema,r=o?e.getLastPosition():e.getFirstPosition();if(!i.getLimitElement(r).is("element","tableCell")){return n.createPositionAt(t,o?"end":0).isTouching(r)}const l=n.createSelection(r);return n.modifySelection(l,{direction:o?"forward":"backward"}),r.isEqual(l.focus)}_navigateFromCellInDirection(e,t,o=!1){const n=this.editor.model,i=e.findAncestor("table"),r=[...new w(i,{includeAllSlots:!0})],{row:l,column:s}=r[r.length-1],a=r.find((({cell:t})=>t==e));let{row:c,column:d}=a;switch(t){case"left":d--;break;case"up":c--;break;case"right":d+=a.cellWidth;break;case"down":c+=a.cellHeight}if(c<0||c>l||d<0&&c<=0||d>s&&c>=l)return void n.change((e=>{e.setSelection(e.createRangeOn(i))}));d<0?(d=o?0:s,c--):d>s&&(d=o?s:0,c++);const u=r.find((e=>e.row==c&&e.column==d)).cell,h=["right","down"].includes(t),b=this.editor.plugins.get("TableSelection");if(o&&b.isEnabled){const t=b.getAnchorCell()||e;b.setCellSelection(t,u)}else{const e=n.createPositionAt(u,h?0:"end");n.change((t=>{t.setSelection(e)}))}}}var Qe=o(783);class Ze extends Qe.DomEventObserver{constructor(){super(...arguments),this.domEventType=["mousemove","mouseleave"]}onDomEvent(e){this.fire(e.type,e)}}class et extends e.Plugin{static get pluginName(){return"TableMouse"}static get isOfficialPlugin(){return!0}static get requires(){return[$e,oe]}init(){this.editor.editing.view.addObserver(Ze),this._enableShiftClickSelection(),this._enableMouseDragSelection()}_enableShiftClickSelection(){const e=this.editor,t=e.plugins.get(oe);let o=!1;const n=e.plugins.get($e);this.listenTo(e.editing.view.document,"mousedown",((i,r)=>{const l=e.model.document.selection;if(!this.isEnabled||!n.isEnabled)return;if(!r.domEvent.shiftKey)return;const s=n.getAnchorCell()||t.getTableCellsContainingSelection(l)[0];if(!s)return;const a=this._getModelTableCellFromDomEvent(r);a&&tt(s,a)&&(o=!0,n.setCellSelection(s,a),r.preventDefault())})),this.listenTo(e.editing.view.document,"mouseup",(()=>{o=!1})),this.listenTo(e.editing.view.document,"selectionChange",(e=>{o&&e.stop()}),{priority:"highest"})}_enableMouseDragSelection(){const e=this.editor;let t,o,n=!1,i=!1;const r=e.plugins.get($e);this.listenTo(e.editing.view.document,"mousedown",((e,o)=>{this.isEnabled&&r.isEnabled&&(o.domEvent.shiftKey||o.domEvent.ctrlKey||o.domEvent.altKey||(t=this._getModelTableCellFromDomEvent(o)))})),this.listenTo(e.editing.view.document,"mousemove",((e,l)=>{if(!l.domEvent.buttons)return;if(!t)return;const s=this._getModelTableCellFromDomEvent(l);s&&tt(t,s)&&(o=s,n||o==t||(n=!0)),n&&(i=!0,r.setCellSelection(t,o),l.preventDefault())})),this.listenTo(e.editing.view.document,"mouseup",(()=>{n=!1,i=!1,t=null,o=null})),this.listenTo(e.editing.view.document,"selectionChange",(e=>{i&&e.stop()}),{priority:"highest"})}_getModelTableCellFromDomEvent(e){const t=e.target,o=this.editor.editing.view.createPositionAt(t,0);return this.editor.editing.mapper.toModelPosition(o).parent.findAncestor("tableCell",{includeSelf:!0})}}function tt(e,t){return e.parent.parent==t.parent.parent}var ot=o(25),nt={attributes:{"data-cke":!0}};nt.setAttributes=Pe(),nt.insert=Ve().bind(null,"head"),nt.domAPI=xe(),nt.insertStyleElement=Ee();Ae()(ot.A,nt);ot.A&&ot.A.locals&&ot.A.locals;class it extends e.Plugin{static get requires(){return[Oe,Me,$e,et,Ye,Ge,t.Widget]}static get pluginName(){return"Table"}static get isOfficialPlugin(){return!0}}class rt extends e.Plugin{static get pluginName(){return"PlainTableOutput"}static get isOfficialPlugin(){return!0}static get requires(){return[it]}init(){const e=this.editor;e.conversion.for("dataDowncast").elementToStructure({model:"table",view:lt,converterPriority:"high"}),e.plugins.has("TableCaption")&&e.conversion.for("dataDowncast").elementToElement({model:"caption",view:(e,{writer:t})=>{if("table"===e.parent.name)return t.createContainerElement("caption")},converterPriority:"high"}),e.plugins.has("TableProperties")&&function(e){const t={"border-width":"tableBorderWidth","border-color":"tableBorderColor","border-style":"tableBorderStyle","background-color":"tableBackgroundColor"};for(const[o,n]of Object.entries(t))e.conversion.for("dataDowncast").add((e=>e.on(`attribute:${n}:table`,((e,t,n)=>{const{item:i,attributeNewValue:r}=t,{mapper:l,writer:s}=n;if(!n.consumable.consume(i,e.name))return;const a=l.toViewElement(i);r?s.setStyle(o,r,a):s.removeStyle(o,a)}),{priority:"high"})))}(e)}}function lt(e,{writer:t}){const o=e.getAttribute("headingRows")||0,n=t.createSlot((e=>e.is("element","tableRow")&&e.index<o)),i=t.createSlot((e=>e.is("element","tableRow")&&e.index>=o)),r=t.createSlot((e=>!e.is("element","tableRow"))),l=t.createContainerElement("thead",null,n),s=t.createContainerElement("tbody",null,i),a=[];return o&&a.push(l),o<e.childCount&&a.push(s),t.createContainerElement("table",null,[r,...a])}function st(e){const t=at(e);return t||ct(e)}function at(e){const t=e.getSelectedElement();return t&&dt(t)?t:null}function ct(e){const t=e.getFirstPosition();if(!t)return null;let o=t.parent;for(;o;){if(o.is("element")&&dt(o))return o;o=o.parent}return null}function dt(e){return!!e.getCustomProperty("table")&&(0,t.isWidget)(e)}class ut extends e.Plugin{static get requires(){return[t.WidgetToolbarRepository]}static get pluginName(){return"TableToolbar"}static get isOfficialPlugin(){return!0}afterInit(){const e=this.editor,o=e.t,n=e.plugins.get(t.WidgetToolbarRepository),i=e.config.get("table.contentToolbar"),r=e.config.get("table.tableToolbar");i&&n.register("tableContent",{ariaLabel:o("Table toolbar"),items:i,getRelatedElement:ct}),r&&n.register("table",{ariaLabel:o("Table toolbar"),items:r,getRelatedElement:at})}}var ht=o(770),bt={attributes:{"data-cke":!0}};bt.setAttributes=Pe(),bt.insert=Ve().bind(null,"head"),bt.domAPI=xe(),bt.insertStyleElement=Ee();Ae()(ht.A,bt);ht.A&&ht.A.locals&&ht.A.locals;class mt extends Fe.View{constructor(e,t){super(e),this.set("value",""),this.set("isReadOnly",!1),this.set("isFocused",!1),this.set("isEmpty",!0),this.options=t,this.focusTracker=new m.FocusTracker,this._focusables=new Fe.ViewCollection,this.dropdownView=this._createDropdownView(),this.inputView=this._createInputTextView(),this.keystrokes=new m.KeystrokeHandler,this._stillTyping=!1,this.focusCycler=new Fe.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.setTemplate({tag:"div",attributes:{class:["ck","ck-input-color"]},children:[this.dropdownView,this.inputView]}),this.on("change:value",((e,t,o)=>this._setInputValue(o)))}render(){super.render(),[this.inputView,this.dropdownView.buttonView].forEach((e=>{this.focusTracker.add(e.element),this._focusables.add(e)})),this.keystrokes.listenTo(this.element)}focus(e){-1===e?this.focusCycler.focusLast():this.focusCycler.focusFirst()}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}_createDropdownView(){const e=this.locale,t=e.t,o=this.bindTemplate,n=this._createColorSelector(e),i=(0,Fe.createDropdown)(e),r=new Fe.View;return r.setTemplate({tag:"span",attributes:{class:["ck","ck-input-color__button__preview"],style:{backgroundColor:o.to("value")}},children:[{tag:"span",attributes:{class:["ck","ck-input-color__button__preview__no-color-indicator",o.if("value","ck-hidden",(e=>""!=e))]}}]}),i.buttonView.extendTemplate({attributes:{class:"ck-input-color__button"}}),i.buttonView.children.add(r),i.buttonView.label=t("Color picker"),i.buttonView.tooltip=!0,i.panelPosition="rtl"===e.uiLanguageDirection?"se":"sw",i.panelView.children.add(n),i.bind("isEnabled").to(this,"isReadOnly",(e=>!e)),i.on("change:isOpen",((e,t,o)=>{o&&(n.updateSelectedColors(),n.showColorGridsFragment())})),i}_createInputTextView(){const e=this.locale,t=new Fe.InputTextView(e);return t.extendTemplate({on:{blur:t.bindTemplate.to("blur")}}),t.value=this.value,t.bind("isReadOnly","hasError").to(this),this.bind("isFocused","isEmpty").to(t),t.on("input",(()=>{const e=t.element.value,o=this.options.colorDefinitions.find((t=>e===t.label));this._stillTyping=!0,this.value=o&&o.color||e})),t.on("blur",(()=>{this._stillTyping=!1,this._setInputValue(t.element.value)})),t.delegate("input").to(this),t}_createColorSelector(e){const t=e.t,o=this.options.defaultColorValue||"",n=t(o?"Restore default":"Remove color"),i=new Fe.ColorSelectorView(e,{colors:this.options.colorDefinitions,columns:this.options.columns,removeButtonLabel:n,colorPickerLabel:t("Color picker"),colorPickerViewConfig:!1!==this.options.colorPickerConfig&&{...this.options.colorPickerConfig,hideInput:!0}});i.appendUI(),i.on("execute",((e,t)=>{"colorPickerSaveButton"!==t.source?(this.value=t.value||o,this.fire("input"),"colorPicker"!==t.source&&(this.dropdownView.isOpen=!1)):this.dropdownView.isOpen=!1}));let r=this.value;return i.on("colorPicker:cancel",(()=>{this.value=r,this.fire("input"),this.dropdownView.isOpen=!1})),i.colorGridsFragmentView.colorPickerButtonView.on("execute",(()=>{r=this.value})),i.bind("selectedColor").to(this,"value"),i}_setInputValue(e){if(!this._stillTyping){const t=gt(e),o=this.options.colorDefinitions.find((e=>t===gt(e.color)));this.inputView.value=o?o.label:e||""}}}function gt(e){return e.replace(/([(,])\s+/g,"$1").replace(/^\s+|\s+(?=[),\s]|$)/g,"").replace(/,|\s/g," ")}const pt=e=>""===e;function ft(e){return{none:e("None"),solid:e("Solid"),dotted:e("Dotted"),dashed:e("Dashed"),double:e("Double"),groove:e("Groove"),ridge:e("Ridge"),inset:e("Inset"),outset:e("Outset")}}function wt(e){return e('The color is invalid. Try "#FF0000" or "rgb(255,0,0)" or "red".')}function _t(e){return e('The value is invalid. Try "10px" or "2em" or simply "2".')}function kt(e){return e=e.trim().toLowerCase(),pt(e)||(0,Qe.isColor)(e)}function vt(e){return e=e.trim(),pt(e)||St(e)||(0,Qe.isLength)(e)||(0,Qe.isPercentage)(e)}function Ct(e){return e=e.trim(),pt(e)||St(e)||(0,Qe.isLength)(e)}function yt(e,t){const o=new m.Collection,n=ft(e.t);for(const i in n){const r={type:"button",model:new Fe.ViewModel({_borderStyleValue:i,label:n[i],role:"menuitemradio",withText:!0})};"none"===i?r.model.bind("isOn").to(e,"borderStyle",(e=>"none"===t?!e:e===i)):r.model.bind("isOn").to(e,"borderStyle",(e=>e===i)),o.add(r)}return o}function At(e){const{view:t,icons:o,toolbar:n,labels:i,propertyName:r,nameToValue:l,defaultValue:s}=e;for(const e in i){const a=new Fe.ButtonView(t.locale);a.set({label:i[e],icon:o[e],tooltip:i[e]});const c=l?l(e):e;a.bind("isOn").to(t,r,(e=>{let t=e;return""===e&&s&&(t=s),c===t})),a.on("execute",(()=>{t[r]=c})),n.items.add(a)}}const Tt=[{color:"hsl(0, 0%, 0%)",label:"Black"},{color:"hsl(0, 0%, 30%)",label:"Dim grey"},{color:"hsl(0, 0%, 60%)",label:"Grey"},{color:"hsl(0, 0%, 90%)",label:"Light grey"},{color:"hsl(0, 0%, 100%)",label:"White",hasBorder:!0},{color:"hsl(0, 75%, 60%)",label:"Red"},{color:"hsl(30, 75%, 60%)",label:"Orange"},{color:"hsl(60, 75%, 60%)",label:"Yellow"},{color:"hsl(90, 75%, 60%)",label:"Light green"},{color:"hsl(120, 75%, 60%)",label:"Green"},{color:"hsl(150, 75%, 60%)",label:"Aquamarine"},{color:"hsl(180, 75%, 60%)",label:"Turquoise"},{color:"hsl(210, 75%, 60%)",label:"Light blue"},{color:"hsl(240, 75%, 60%)",label:"Blue"},{color:"hsl(270, 75%, 60%)",label:"Purple"}];function xt(e){return(t,o,n)=>{const i=new mt(t.locale,{colorDefinitions:(r=e.colorConfig,r.map((e=>({color:e.model,label:e.label,options:{hasBorder:e.hasBorder}})))),columns:e.columns,defaultColorValue:e.defaultColorValue,colorPickerConfig:e.colorPickerConfig});var r;return i.inputView.set({id:o,ariaDescribedById:n}),i.bind("isReadOnly").to(t,"isEnabled",(e=>!e)),i.bind("hasError").to(t,"errorText",(e=>!!e)),i.on("input",(()=>{t.errorText=null})),t.bind("isEmpty","isFocused").to(i),i}}function St(e){const t=parseFloat(e);return!Number.isNaN(t)&&e===String(t)}var Vt=o(839),Rt={attributes:{"data-cke":!0}};Rt.setAttributes=Pe(),Rt.insert=Ve().bind(null,"head"),Rt.domAPI=xe(),Rt.insertStyleElement=Ee();Ae()(Vt.A,Rt);Vt.A&&Vt.A.locals&&Vt.A.locals;class Pt extends Fe.View{constructor(e,t={}){super(e);const o=this.bindTemplate;this.set("class",t.class||null),this.children=this.createCollection(),t.children&&t.children.forEach((e=>this.children.add(e))),this.set("_role",null),this.set("_ariaLabelledBy",null),t.labelView&&this.set({_role:"group",_ariaLabelledBy:t.labelView.id}),this.setTemplate({tag:"div",attributes:{class:["ck","ck-form__row",o.to("class")],role:o.to("_role"),"aria-labelledby":o.to("_ariaLabelledBy")},children:this.children})}}var It=o(67),Et={attributes:{"data-cke":!0}};Et.setAttributes=Pe(),Et.insert=Ve().bind(null,"head"),Et.domAPI=xe(),Et.insertStyleElement=Ee();Ae()(It.A,Et);It.A&&It.A.locals&&It.A.locals;var zt=o(911),Bt={attributes:{"data-cke":!0}};Bt.setAttributes=Pe(),Bt.insert=Ve().bind(null,"head"),Bt.domAPI=xe(),Bt.insertStyleElement=Ee();Ae()(zt.A,Bt);zt.A&&zt.A.locals&&zt.A.locals;var Ot=o(266),Lt={attributes:{"data-cke":!0}};Lt.setAttributes=Pe(),Lt.insert=Ve().bind(null,"head"),Lt.domAPI=xe(),Lt.insertStyleElement=Ee();Ae()(Ot.A,Lt);Ot.A&&Ot.A.locals&&Ot.A.locals;class Ft extends Fe.View{constructor(e,t){super(e),this.set({borderStyle:"",borderWidth:"",borderColor:"",padding:"",backgroundColor:"",width:"",height:"",horizontalAlignment:"",verticalAlignment:""}),this.options=t;const{borderStyleDropdown:o,borderWidthInput:n,borderColorInput:i,borderRowLabel:r}=this._createBorderFields(),{backgroundRowLabel:l,backgroundInput:s}=this._createBackgroundFields(),{widthInput:a,operatorLabel:c,heightInput:d,dimensionsLabel:u}=this._createDimensionFields(),{horizontalAlignmentToolbar:h,verticalAlignmentToolbar:b,alignmentLabel:g}=this._createAlignmentFields();this.focusTracker=new m.FocusTracker,this.keystrokes=new m.KeystrokeHandler,this.children=this.createCollection(),this.borderStyleDropdown=o,this.borderWidthInput=n,this.borderColorInput=i,this.backgroundInput=s,this.paddingInput=this._createPaddingField(),this.widthInput=a,this.heightInput=d,this.horizontalAlignmentToolbar=h,this.verticalAlignmentToolbar=b;const{saveButtonView:p,cancelButtonView:f}=this._createActionButtons();this.saveButtonView=p,this.cancelButtonView=f,this._focusables=new Fe.ViewCollection,this._focusCycler=new Fe.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.children.add(new Fe.FormHeaderView(e,{label:this.t("Cell properties")})),this.children.add(new Pt(e,{labelView:r,children:[r,o,i,n],class:"ck-table-form__border-row"})),this.children.add(new Pt(e,{labelView:l,children:[l,s],class:"ck-table-form__background-row"})),this.children.add(new Pt(e,{children:[new Pt(e,{labelView:u,children:[u,a,c,d],class:"ck-table-form__dimensions-row"}),new Pt(e,{children:[this.paddingInput],class:"ck-table-cell-properties-form__padding-row"})]})),this.children.add(new Pt(e,{labelView:g,children:[g,h,b],class:"ck-table-cell-properties-form__alignment-row"})),this.children.add(new Pt(e,{children:[this.saveButtonView,this.cancelButtonView],class:"ck-table-form__action-row"})),this.setTemplate({tag:"form",attributes:{class:["ck","ck-form","ck-table-form","ck-table-cell-properties-form"],tabindex:"-1"},children:this.children})}render(){super.render(),(0,Fe.submitHandler)({view:this}),[this.borderColorInput,this.backgroundInput].forEach((e=>{this._focusCycler.chain(e.fieldView.focusCycler)})),[this.borderStyleDropdown,this.borderColorInput,this.borderWidthInput,this.backgroundInput,this.widthInput,this.heightInput,this.paddingInput,this.horizontalAlignmentToolbar,this.verticalAlignmentToolbar,this.saveButtonView,this.cancelButtonView].forEach((e=>{this._focusables.add(e),this.focusTracker.add(e.element)})),this.keystrokes.listenTo(this.element)}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}focus(){this._focusCycler.focusFirst()}_createBorderFields(){const e=this.options.defaultTableCellProperties,t={style:e.borderStyle,width:e.borderWidth,color:e.borderColor},o=xt({colorConfig:this.options.borderColors,columns:5,defaultColorValue:t.color,colorPickerConfig:this.options.colorPickerConfig}),n=this.locale,i=this.t,r=i("Style"),l=new Fe.LabelView(n);l.text=i("Border");const s=ft(i),a=new Fe.LabeledFieldView(n,Fe.createLabeledDropdown);a.set({label:r,class:"ck-table-form__border-style"}),a.fieldView.buttonView.set({ariaLabel:r,ariaLabelledBy:void 0,isOn:!1,withText:!0,tooltip:r}),a.fieldView.buttonView.bind("label").to(this,"borderStyle",(e=>s[e||"none"])),a.fieldView.on("execute",(e=>{this.borderStyle=e.source._borderStyleValue})),a.bind("isEmpty").to(this,"borderStyle",(e=>!e)),(0,Fe.addListToDropdown)(a.fieldView,yt(this,t.style),{role:"menu",ariaLabel:r});const c=new Fe.LabeledFieldView(n,Fe.createLabeledInputText);c.set({label:i("Width"),class:"ck-table-form__border-width"}),c.fieldView.bind("value").to(this,"borderWidth"),c.bind("isEnabled").to(this,"borderStyle",Wt),c.fieldView.on("input",(()=>{this.borderWidth=c.fieldView.element.value}));const d=new Fe.LabeledFieldView(n,o);return d.set({label:i("Color"),class:"ck-table-form__border-color"}),d.fieldView.bind("value").to(this,"borderColor"),d.bind("isEnabled").to(this,"borderStyle",Wt),d.fieldView.on("input",(()=>{this.borderColor=d.fieldView.value})),this.on("change:borderStyle",((e,o,n,i)=>{Wt(n)||(this.borderColor="",this.borderWidth=""),Wt(i)||(this.borderColor=t.color,this.borderWidth=t.width)})),{borderRowLabel:l,borderStyleDropdown:a,borderColorInput:d,borderWidthInput:c}}_createBackgroundFields(){const e=this.locale,t=this.t,o=new Fe.LabelView(e);o.text=t("Background");const n=xt({colorConfig:this.options.backgroundColors,columns:5,defaultColorValue:this.options.defaultTableCellProperties.backgroundColor,colorPickerConfig:this.options.colorPickerConfig}),i=new Fe.LabeledFieldView(e,n);return i.set({label:t("Color"),class:"ck-table-cell-properties-form__background"}),i.fieldView.bind("value").to(this,"backgroundColor"),i.fieldView.on("input",(()=>{this.backgroundColor=i.fieldView.value})),{backgroundRowLabel:o,backgroundInput:i}}_createDimensionFields(){const e=this.locale,t=this.t,o=new Fe.LabelView(e);o.text=t("Dimensions");const n=new Fe.LabeledFieldView(e,Fe.createLabeledInputText);n.set({label:t("Width"),class:"ck-table-form__dimensions-row__width"}),n.fieldView.bind("value").to(this,"width"),n.fieldView.on("input",(()=>{this.width=n.fieldView.element.value}));const i=new Fe.View(e);i.setTemplate({tag:"span",attributes:{class:["ck-table-form__dimension-operator"]},children:[{text:"×"}]});const r=new Fe.LabeledFieldView(e,Fe.createLabeledInputText);return r.set({label:t("Height"),class:"ck-table-form__dimensions-row__height"}),r.fieldView.bind("value").to(this,"height"),r.fieldView.on("input",(()=>{this.height=r.fieldView.element.value})),{dimensionsLabel:o,widthInput:n,operatorLabel:i,heightInput:r}}_createPaddingField(){const e=this.locale,t=this.t,o=new Fe.LabeledFieldView(e,Fe.createLabeledInputText);return o.set({label:t("Padding"),class:"ck-table-cell-properties-form__padding"}),o.fieldView.bind("value").to(this,"padding"),o.fieldView.on("input",(()=>{this.padding=o.fieldView.element.value})),o}_createAlignmentFields(){const t=this.locale,o=this.t,n=new Fe.LabelView(t),i={left:e.icons.alignLeft,center:e.icons.alignCenter,right:e.icons.alignRight,justify:e.icons.alignJustify,top:e.icons.alignTop,middle:e.icons.alignMiddle,bottom:e.icons.alignBottom};n.text=o("Table cell text alignment");const r=new Fe.ToolbarView(t),l="rtl"===t.contentLanguageDirection;r.set({isCompact:!0,ariaLabel:o("Horizontal text alignment toolbar")}),At({view:this,icons:i,toolbar:r,labels:this._horizontalAlignmentLabels,propertyName:"horizontalAlignment",nameToValue:e=>{if(l){if("left"===e)return"right";if("right"===e)return"left"}return e},defaultValue:this.options.defaultTableCellProperties.horizontalAlignment});const s=new Fe.ToolbarView(t);return s.set({isCompact:!0,ariaLabel:o("Vertical text alignment toolbar")}),At({view:this,icons:i,toolbar:s,labels:this._verticalAlignmentLabels,propertyName:"verticalAlignment",defaultValue:this.options.defaultTableCellProperties.verticalAlignment}),{horizontalAlignmentToolbar:r,verticalAlignmentToolbar:s,alignmentLabel:n}}_createActionButtons(){const t=this.locale,o=this.t,n=new Fe.ButtonView(t),i=new Fe.ButtonView(t),r=[this.borderWidthInput,this.borderColorInput,this.backgroundInput,this.paddingInput];return n.set({label:o("Save"),icon:e.icons.check,class:"ck-button-save",type:"submit",withText:!0}),n.bind("isEnabled").toMany(r,"errorText",((...e)=>e.every((e=>!e)))),i.set({label:o("Cancel"),icon:e.icons.cancel,class:"ck-button-cancel",withText:!0}),i.delegate("execute").to(this,"cancel"),{saveButtonView:n,cancelButtonView:i}}get _horizontalAlignmentLabels(){const e=this.locale,t=this.t,o=t("Align cell text to the left"),n=t("Align cell text to the center"),i=t("Align cell text to the right"),r=t("Justify cell text");return"rtl"===e.uiLanguageDirection?{right:i,center:n,left:o,justify:r}:{left:o,center:n,right:i,justify:r}}get _verticalAlignmentLabels(){const e=this.t;return{top:e("Align cell text to the top"),middle:e("Align cell text to the middle"),bottom:e("Align cell text to the bottom")}}}function Wt(e){return"none"!==e}const Nt=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)};const Dt="object"==typeof global&&global&&global.Object===Object&&global;var Mt="object"==typeof self&&self&&self.Object===Object&&self;const jt=Dt||Mt||Function("return this")();const Ht=function(){return jt.Date.now()};var Ut=/\s/;const $t=function(e){for(var t=e.length;t--&&Ut.test(e.charAt(t)););return t};var Kt=/^\s+/;const Gt=function(e){return e?e.slice(0,$t(e)+1).replace(Kt,""):e};const qt=jt.Symbol;var Jt=Object.prototype,Xt=Jt.hasOwnProperty,Yt=Jt.toString,Qt=qt?qt.toStringTag:void 0;const Zt=function(e){var t=Xt.call(e,Qt),o=e[Qt];try{e[Qt]=void 0;var n=!0}catch(e){}var i=Yt.call(e);return n&&(t?e[Qt]=o:delete e[Qt]),i};var eo=Object.prototype.toString;const to=function(e){return eo.call(e)};var oo=qt?qt.toStringTag:void 0;const no=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":oo&&oo in Object(e)?Zt(e):to(e)};const io=function(e){return null!=e&&"object"==typeof e};const ro=function(e){return"symbol"==typeof e||io(e)&&"[object Symbol]"==no(e)};var lo=/^[-+]0x[0-9a-f]+$/i,so=/^0b[01]+$/i,ao=/^0o[0-7]+$/i,co=parseInt;const uo=function(e){if("number"==typeof e)return e;if(ro(e))return NaN;if(Nt(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Nt(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=Gt(e);var o=so.test(e);return o||ao.test(e)?co(e.slice(2),o?2:8):lo.test(e)?NaN:+e};var ho=Math.max,bo=Math.min;const mo=function(e,t,o){var n,i,r,l,s,a,c=0,d=!1,u=!1,h=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function b(t){var o=n,r=i;return n=i=void 0,c=t,l=e.apply(r,o)}function m(e){var o=e-a;return void 0===a||o>=t||o<0||u&&e-c>=r}function g(){var e=Ht();if(m(e))return p(e);s=setTimeout(g,function(e){var o=t-(e-a);return u?bo(o,r-(e-c)):o}(e))}function p(e){return s=void 0,h&&n?b(e):(n=i=void 0,l)}function f(){var e=Ht(),o=m(e);if(n=arguments,i=this,a=e,o){if(void 0===s)return function(e){return c=e,s=setTimeout(g,t),d?b(e):l}(a);if(u)return clearTimeout(s),s=setTimeout(g,t),b(a)}return void 0===s&&(s=setTimeout(g,t)),l}return t=uo(t)||0,Nt(o)&&(d=!!o.leading,r=(u="maxWait"in o)?ho(uo(o.maxWait)||0,t):r,h="trailing"in o?!!o.trailing:h),f.cancel=function(){void 0!==s&&clearTimeout(s),c=0,n=a=i=s=void 0},f.flush=function(){return void 0===s?l:p(Ht())},f},go=(()=>[Fe.BalloonPanelView.defaultPositions.northArrowSouth,Fe.BalloonPanelView.defaultPositions.northArrowSouthWest,Fe.BalloonPanelView.defaultPositions.northArrowSouthEast,Fe.BalloonPanelView.defaultPositions.southArrowNorth,Fe.BalloonPanelView.defaultPositions.southArrowNorthWest,Fe.BalloonPanelView.defaultPositions.southArrowNorthEast,Fe.BalloonPanelView.defaultPositions.viewportStickyNorth])();function po(e,t){const o=e.plugins.get("ContextualBalloon"),n=e.editing.view.document.selection;let i;"cell"===t?ct(n)&&(i=wo(e)):st(n)&&(i=fo(e)),i&&o.updatePosition(i)}function fo(e){const t=b(e.model.document.selection),o=e.editing.mapper.toViewElement(t);return{target:e.editing.view.domConverter.mapViewToDom(o),positions:go}}function wo(e){const t=e.editing.mapper,o=e.editing.view.domConverter,n=e.model.document.selection;if(n.rangeCount>1)return{target:()=>function(e,t){const o=t.editing.mapper,n=t.editing.view.domConverter,i=Array.from(e).map((e=>{const t=_o(e.start),i=o.toViewElement(t);return new m.Rect(n.mapViewToDom(i))}));return m.Rect.getBoundingRect(i)}(n.getRanges(),e),positions:go};const i=_o(n.getFirstPosition()),r=t.toViewElement(i);return{target:o.mapViewToDom(r),positions:go}}function _o(e){return e.nodeAfter&&e.nodeAfter.is("element","tableCell")?e.nodeAfter:e.findAncestor("tableCell")}function ko(e){if(!e||!Nt(e))return e;const{top:t,right:o,bottom:n,left:i}=e;return t==o&&o==n&&n==i?t:void 0}function vo(e,t){const o=parseFloat(e);return Number.isNaN(o)||String(o)!==String(e)?e:`${o}${t}`}function Co(e,t={}){const o={borderStyle:"none",borderWidth:"",borderColor:"",backgroundColor:"",width:"",height:"",...e};return t.includeAlignmentProperty&&!o.alignment&&(o.alignment="center"),t.includePaddingProperty&&!o.padding&&(o.padding=""),t.includeVerticalAlignmentProperty&&!o.verticalAlignment&&(o.verticalAlignment="middle"),t.includeHorizontalAlignmentProperty&&!o.horizontalAlignment&&(o.horizontalAlignment=t.isRightToLeftContent?"right":"left"),o}function yo(e,t){return Co({borderStyle:"double",borderColor:"hsl(0, 0%, 70%)",borderWidth:"1px",...e},t)}function Ao(e,t){return Co({borderStyle:"solid",borderColor:"hsl(0, 0%, 75%)",borderWidth:"1px",...e},t)}const To={borderStyle:"tableCellBorderStyle",borderColor:"tableCellBorderColor",borderWidth:"tableCellBorderWidth",height:"tableCellHeight",width:"tableCellWidth",padding:"tableCellPadding",backgroundColor:"tableCellBackgroundColor",horizontalAlignment:"tableCellHorizontalAlignment",verticalAlignment:"tableCellVerticalAlignment"};class xo extends e.Plugin{static get requires(){return[Fe.ContextualBalloon]}static get pluginName(){return"TableCellPropertiesUI"}static get isOfficialPlugin(){return!0}constructor(e){super(e),e.config.define("table.tableCellProperties",{borderColors:Tt,backgroundColors:Tt})}init(){const e=this.editor,t=e.t;this._defaultTableCellProperties=Ao(e.config.get("table.tableCellProperties.defaultProperties"),{includeVerticalAlignmentProperty:!0,includeHorizontalAlignmentProperty:!0,includePaddingProperty:!0,isRightToLeftContent:"rtl"===e.locale.contentLanguageDirection}),this._balloon=e.plugins.get(Fe.ContextualBalloon),this.view=null,this._isReady=!1,e.ui.componentFactory.add("tableCellProperties",(o=>{const n=new Fe.ButtonView(o);n.set({label:t("Cell properties"),icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="m11.105 18-.17 1H2.5A1.5 1.5 0 0 1 1 17.5v-15A1.5 1.5 0 0 1 2.5 1h15A1.5 1.5 0 0 1 19 2.5v9.975l-.85-.124-.15-.302V8h-5v4h.021l-.172.351-1.916.28-.151.027c-.287.063-.54.182-.755.341L8 13v5h3.105zM2 12h5V8H2v4zm10-4H8v4h4V8zM2 2v5h5V2H2zm0 16h5v-5H2v5zM13 7h5V2h-5v5zM8 2v5h4V2H8z" opacity=".6"/><path d="m15.5 11.5 1.323 2.68 2.957.43-2.14 2.085.505 2.946L15.5 18.25l-2.645 1.39.505-2.945-2.14-2.086 2.957-.43L15.5 11.5zM13 6a1 1 0 0 1 1 1v3.172a2.047 2.047 0 0 0-.293.443l-.858 1.736-1.916.28-.151.027A1.976 1.976 0 0 0 9.315 14H7a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1h6zm-1 2H8v4h4V8z"/></svg>',tooltip:!0}),this.listenTo(n,"execute",(()=>this._showView()));const i=Object.values(To).map((t=>e.commands.get(t)));return n.bind("isEnabled").toMany(i,"isEnabled",((...e)=>e.some((e=>e)))),n}))}destroy(){super.destroy(),this.view&&this.view.destroy()}_createPropertiesView(){const e=this.editor,t=e.config.get("table.tableCellProperties"),o=(0,Fe.normalizeColorOptions)(t.borderColors),n=(0,Fe.getLocalizedColorOptions)(e.locale,o),i=(0,Fe.normalizeColorOptions)(t.backgroundColors),r=(0,Fe.getLocalizedColorOptions)(e.locale,i),l=!1!==t.colorPicker,s=new Ft(e.locale,{borderColors:n,backgroundColors:r,defaultTableCellProperties:this._defaultTableCellProperties,colorPickerConfig:!!l&&(t.colorPicker||{})}),a=e.t;s.render(),this.listenTo(s,"submit",(()=>{this._hideView()})),this.listenTo(s,"cancel",(()=>{this._undoStepBatch.operations.length&&e.execute("undo",this._undoStepBatch),this._hideView()})),s.keystrokes.set("Esc",((e,t)=>{this._hideView(),t()})),(0,Fe.clickOutsideHandler)({emitter:s,activator:()=>this._isViewInBalloon,contextElements:[this._balloon.view.element],callback:()=>this._hideView()});const c=wt(a),d=_t(a);return s.on("change:borderStyle",this._getPropertyChangeCallback("tableCellBorderStyle")),s.on("change:borderColor",this._getValidatedPropertyChangeCallback({viewField:s.borderColorInput,commandName:"tableCellBorderColor",errorText:c,validator:kt})),s.on("change:borderWidth",this._getValidatedPropertyChangeCallback({viewField:s.borderWidthInput,commandName:"tableCellBorderWidth",errorText:d,validator:Ct})),s.on("change:padding",this._getValidatedPropertyChangeCallback({viewField:s.paddingInput,commandName:"tableCellPadding",errorText:d,validator:vt})),s.on("change:width",this._getValidatedPropertyChangeCallback({viewField:s.widthInput,commandName:"tableCellWidth",errorText:d,validator:vt})),s.on("change:height",this._getValidatedPropertyChangeCallback({viewField:s.heightInput,commandName:"tableCellHeight",errorText:d,validator:vt})),s.on("change:backgroundColor",this._getValidatedPropertyChangeCallback({viewField:s.backgroundInput,commandName:"tableCellBackgroundColor",errorText:c,validator:kt})),s.on("change:horizontalAlignment",this._getPropertyChangeCallback("tableCellHorizontalAlignment")),s.on("change:verticalAlignment",this._getPropertyChangeCallback("tableCellVerticalAlignment")),s}_fillViewFormFromCommandValues(){const e=this.editor.commands,t=e.get("tableCellBorderStyle");Object.entries(To).map((([t,o])=>{const n=this._defaultTableCellProperties[t]||"";return[t,e.get(o).value||n]})).forEach((([e,o])=>{("borderColor"!==e&&"borderWidth"!==e||"none"!==t.value)&&this.view.set(e,o)})),this._isReady=!0}_showView(){const e=this.editor;this.view||(this.view=this._createPropertiesView()),this.listenTo(e.ui,"update",(()=>{this._updateView()})),this._fillViewFormFromCommandValues(),this._balloon.add({view:this.view,position:wo(e)}),this._undoStepBatch=e.model.createBatch(),this.view.focus()}_hideView(){const e=this.editor;this.stopListening(e.ui,"update"),this._isReady=!1,this.view.saveButtonView.focus(),this._balloon.remove(this.view),this.editor.editing.view.focus()}_updateView(){const e=this.editor;ct(e.editing.view.document.selection)?this._isViewVisible&&po(e,"cell"):this._hideView()}get _isViewVisible(){return!!this.view&&this._balloon.visibleView===this.view}get _isViewInBalloon(){return!!this.view&&this._balloon.hasView(this.view)}_getPropertyChangeCallback(e){return(t,o,n)=>{this._isReady&&this.editor.execute(e,{value:n,batch:this._undoStepBatch})}}_getValidatedPropertyChangeCallback(e){const{commandName:t,viewField:o,validator:n,errorText:i}=e,r=mo((()=>{o.errorText=i}),500);return(e,i,l)=>{r.cancel(),this._isReady&&(n(l)?(this.editor.execute(t,{value:l,batch:this._undoStepBatch}),o.errorText=null):r())}}}class So extends e.Command{constructor(e,t,o){super(e),this.attributeName=t,this._defaultValue=o}refresh(){const e=this.editor,t=this.editor.plugins.get("TableUtils").getSelectionAffectedTableCells(e.model.document.selection);this.isEnabled=!!t.length,this.value=this._getSingleValue(t)}execute(e={}){const{value:t,batch:o}=e,n=this.editor.model,i=this.editor.plugins.get("TableUtils").getSelectionAffectedTableCells(n.document.selection),r=this._getValueToSet(t);n.enqueueChange(o,(e=>{r?i.forEach((t=>e.setAttribute(this.attributeName,r,t))):i.forEach((t=>e.removeAttribute(this.attributeName,t)))}))}_getAttribute(e){if(!e)return;const t=e.getAttribute(this.attributeName);return t!==this._defaultValue?t:void 0}_getValueToSet(e){if(e!==this._defaultValue)return e}_getSingleValue(e){const t=this._getAttribute(e[0]);return e.every((e=>this._getAttribute(e)===t))?t:void 0}}class Vo extends So{constructor(e,t){super(e,"tableCellWidth",t)}_getValueToSet(e){if((e=vo(e,"px"))!==this._defaultValue)return e}}class Ro extends e.Plugin{static get pluginName(){return"TableCellWidthEditing"}static get isOfficialPlugin(){return!0}static get requires(){return[Oe]}init(){const e=this.editor,t=Ao(e.config.get("table.tableCellProperties.defaultProperties"));h(e.model.schema,e.conversion,{modelAttribute:"tableCellWidth",styleName:"width",defaultValue:t.width}),e.commands.add("tableCellWidth",new Vo(e,t.width))}}class Po extends So{constructor(e,t){super(e,"tableCellPadding",t)}_getAttribute(e){if(!e)return;const t=ko(e.getAttribute(this.attributeName));return t!==this._defaultValue?t:void 0}_getValueToSet(e){const t=vo(e,"px");if(t!==this._defaultValue)return t}}class Io extends So{constructor(e,t){super(e,"tableCellHeight",t)}_getValueToSet(e){const t=vo(e,"px");if(t!==this._defaultValue)return t}}class Eo extends So{constructor(e,t){super(e,"tableCellBackgroundColor",t)}}class zo extends So{constructor(e,t){super(e,"tableCellVerticalAlignment",t)}}class Bo extends So{constructor(e,t){super(e,"tableCellHorizontalAlignment",t)}}class Oo extends So{constructor(e,t){super(e,"tableCellBorderStyle",t)}_getAttribute(e){if(!e)return;const t=ko(e.getAttribute(this.attributeName));return t!==this._defaultValue?t:void 0}}class Lo extends So{constructor(e,t){super(e,"tableCellBorderColor",t)}_getAttribute(e){if(!e)return;const t=ko(e.getAttribute(this.attributeName));return t!==this._defaultValue?t:void 0}}class Fo extends So{constructor(e,t){super(e,"tableCellBorderWidth",t)}_getAttribute(e){if(!e)return;const t=ko(e.getAttribute(this.attributeName));return t!==this._defaultValue?t:void 0}_getValueToSet(e){const t=vo(e,"px");if(t!==this._defaultValue)return t}}const Wo=/^(top|middle|bottom)$/,No=/^(left|center|right|justify)$/;class Do extends e.Plugin{static get pluginName(){return"TableCellPropertiesEditing"}static get isOfficialPlugin(){return!0}static get requires(){return[Oe,Ro]}init(){const e=this.editor,t=e.model.schema,o=e.conversion;e.config.define("table.tableCellProperties.defaultProperties",{});const n=Ao(e.config.get("table.tableCellProperties.defaultProperties"),{includeVerticalAlignmentProperty:!0,includeHorizontalAlignmentProperty:!0,includePaddingProperty:!0,isRightToLeftContent:"rtl"===e.locale.contentLanguageDirection});e.data.addStyleProcessorRules(Qe.addBorderRules),function(e,t,o){const n={width:"tableCellBorderWidth",color:"tableCellBorderColor",style:"tableCellBorderStyle"};e.extend("tableCell",{allowAttributes:Object.values(n)}),r(t,"td",n,o),r(t,"th",n,o),l(t,{modelElement:"tableCell",modelAttribute:n.style,styleName:"border-style"}),l(t,{modelElement:"tableCell",modelAttribute:n.color,styleName:"border-color"}),l(t,{modelElement:"tableCell",modelAttribute:n.width,styleName:"border-width"})}(t,o,{color:n.borderColor,style:n.borderStyle,width:n.borderWidth}),e.commands.add("tableCellBorderStyle",new Oo(e,n.borderStyle)),e.commands.add("tableCellBorderColor",new Lo(e,n.borderColor)),e.commands.add("tableCellBorderWidth",new Fo(e,n.borderWidth)),h(t,o,{modelAttribute:"tableCellHeight",styleName:"height",defaultValue:n.height}),e.commands.add("tableCellHeight",new Io(e,n.height)),e.data.addStyleProcessorRules(Qe.addPaddingRules),h(t,o,{modelAttribute:"tableCellPadding",styleName:"padding",reduceBoxSides:!0,defaultValue:n.padding}),e.commands.add("tableCellPadding",new Po(e,n.padding)),e.data.addStyleProcessorRules(Qe.addBackgroundRules),h(t,o,{modelAttribute:"tableCellBackgroundColor",styleName:"background-color",defaultValue:n.backgroundColor}),e.commands.add("tableCellBackgroundColor",new Eo(e,n.backgroundColor)),function(e,t,o){e.extend("tableCell",{allowAttributes:["tableCellHorizontalAlignment"]}),t.for("downcast").attributeToAttribute({model:{name:"tableCell",key:"tableCellHorizontalAlignment"},view:e=>({key:"style",value:{"text-align":e}})}),t.for("upcast").attributeToAttribute({view:{name:/^(td|th)$/,styles:{"text-align":No}},model:{key:"tableCellHorizontalAlignment",value:e=>{const t=e.getStyle("text-align");return t===o?null:t}}}).attributeToAttribute({view:{name:/^(td|th)$/,attributes:{align:No}},model:{key:"tableCellHorizontalAlignment",value:e=>{const t=e.getAttribute("align");return t===o?null:t}}})}(t,o,n.horizontalAlignment),e.commands.add("tableCellHorizontalAlignment",new Bo(e,n.horizontalAlignment)),function(e,t,o){e.extend("tableCell",{allowAttributes:["tableCellVerticalAlignment"]}),t.for("downcast").attributeToAttribute({model:{name:"tableCell",key:"tableCellVerticalAlignment"},view:e=>({key:"style",value:{"vertical-align":e}})}),t.for("upcast").attributeToAttribute({view:{name:/^(td|th)$/,styles:{"vertical-align":Wo}},model:{key:"tableCellVerticalAlignment",value:e=>{const t=e.getStyle("vertical-align");return t===o?null:t}}}).attributeToAttribute({view:{name:/^(td|th)$/,attributes:{valign:Wo}},model:{key:"tableCellVerticalAlignment",value:e=>{const t=e.getAttribute("valign");return t===o?null:t}}})}(t,o,n.verticalAlignment),e.commands.add("tableCellVerticalAlignment",new zo(e,n.verticalAlignment))}}class Mo extends e.Plugin{static get pluginName(){return"TableCellProperties"}static get isOfficialPlugin(){return!0}static get requires(){return[Do,xo]}}class jo extends e.Command{constructor(e,t,o){super(e),this.attributeName=t,this._defaultValue=o}refresh(){const e=b(this.editor.model.document.selection);this.isEnabled=!!e,this.value=this._getValue(e)}execute(e={}){const t=this.editor.model,o=t.document.selection,{value:n,batch:i}=e,r=b(o),l=this._getValueToSet(n);t.enqueueChange(i,(e=>{l?e.setAttribute(this.attributeName,l,r):e.removeAttribute(this.attributeName,r)}))}_getValue(e){if(!e)return;const t=e.getAttribute(this.attributeName);return t!==this._defaultValue?t:void 0}_getValueToSet(e){if(e!==this._defaultValue)return e}}class Ho extends jo{constructor(e,t){super(e,"tableBackgroundColor",t)}}class Uo extends jo{constructor(e,t){super(e,"tableBorderColor",t)}_getValue(e){if(!e)return;const t=ko(e.getAttribute(this.attributeName));return t!==this._defaultValue?t:void 0}}class $o extends jo{constructor(e,t){super(e,"tableBorderStyle",t)}_getValue(e){if(!e)return;const t=ko(e.getAttribute(this.attributeName));return t!==this._defaultValue?t:void 0}}class Ko extends jo{constructor(e,t){super(e,"tableBorderWidth",t)}_getValue(e){if(!e)return;const t=ko(e.getAttribute(this.attributeName));return t!==this._defaultValue?t:void 0}_getValueToSet(e){const t=vo(e,"px");if(t!==this._defaultValue)return t}}class Go extends jo{constructor(e,t){super(e,"tableWidth",t)}_getValueToSet(e){if((e=vo(e,"px"))!==this._defaultValue)return e}}class qo extends jo{constructor(e,t){super(e,"tableHeight",t)}_getValueToSet(e){if((e=vo(e,"px"))!==this._defaultValue)return e}}class Jo extends jo{constructor(e,t){super(e,"tableAlignment",t)}}const Xo=/^(left|center|right)$/,Yo=/^(left|none|right)$/;class Qo extends e.Plugin{static get pluginName(){return"TablePropertiesEditing"}static get isOfficialPlugin(){return!0}static get requires(){return[Oe]}init(){const e=this.editor,t=e.model.schema,o=e.conversion;e.config.define("table.tableProperties.defaultProperties",{});const n=yo(e.config.get("table.tableProperties.defaultProperties"),{includeAlignmentProperty:!0});e.data.addStyleProcessorRules(Qe.addBorderRules),function(e,t,o){const n={width:"tableBorderWidth",color:"tableBorderColor",style:"tableBorderStyle"};e.extend("table",{allowAttributes:Object.values(n)}),r(t,"table",n,o),s(t,{modelAttribute:n.color,styleName:"border-color"}),s(t,{modelAttribute:n.style,styleName:"border-style"}),s(t,{modelAttribute:n.width,styleName:"border-width"})}(t,o,{color:n.borderColor,style:n.borderStyle,width:n.borderWidth}),e.commands.add("tableBorderColor",new Uo(e,n.borderColor)),e.commands.add("tableBorderStyle",new $o(e,n.borderStyle)),e.commands.add("tableBorderWidth",new Ko(e,n.borderWidth)),function(e,t,o){e.extend("table",{allowAttributes:["tableAlignment"]}),t.for("downcast").attributeToAttribute({model:{name:"table",key:"tableAlignment"},view:e=>({key:"style",value:{float:"center"===e?"none":e}}),converterPriority:"high"}),t.for("upcast").attributeToAttribute({view:{name:/^(table|figure)$/,styles:{float:Yo}},model:{key:"tableAlignment",value:e=>{let t=e.getStyle("float");return"none"===t&&(t="center"),t===o?null:t}}}).attributeToAttribute({view:{attributes:{align:Xo}},model:{name:"table",key:"tableAlignment",value:e=>{const t=e.getAttribute("align");return t===o?null:t}}})}(t,o,n.alignment),e.commands.add("tableAlignment",new Jo(e,n.alignment)),Zo(t,o,{modelAttribute:"tableWidth",styleName:"width",defaultValue:n.width}),e.commands.add("tableWidth",new Go(e,n.width)),Zo(t,o,{modelAttribute:"tableHeight",styleName:"height",defaultValue:n.height}),e.commands.add("tableHeight",new qo(e,n.height)),e.data.addStyleProcessorRules(Qe.addBackgroundRules),function(e,t,o){const{modelAttribute:n}=o;e.extend("table",{allowAttributes:[n]}),i(t,{viewElement:"table",...o}),s(t,o)}(t,o,{modelAttribute:"tableBackgroundColor",styleName:"background-color",defaultValue:n.backgroundColor}),e.commands.add("tableBackgroundColor",new Ho(e,n.backgroundColor))}}function Zo(e,t,o){const{modelAttribute:n}=o;e.extend("table",{allowAttributes:[n]}),i(t,{viewElement:/^(table|figure)$/,shouldUpcast:e=>!("table"==e.name&&"figure"==e.parent.name),...o}),l(t,{modelElement:"table",...o})}var en=o(218),tn={attributes:{"data-cke":!0}};tn.setAttributes=Pe(),tn.insert=Ve().bind(null,"head"),tn.domAPI=xe(),tn.insertStyleElement=Ee();Ae()(en.A,tn);en.A&&en.A.locals&&en.A.locals;class on extends Fe.View{constructor(e,t){super(e),this.set({borderStyle:"",borderWidth:"",borderColor:"",backgroundColor:"",width:"",height:"",alignment:""}),this.options=t;const{borderStyleDropdown:o,borderWidthInput:n,borderColorInput:i,borderRowLabel:r}=this._createBorderFields(),{backgroundRowLabel:l,backgroundInput:s}=this._createBackgroundFields(),{widthInput:a,operatorLabel:c,heightInput:d,dimensionsLabel:u}=this._createDimensionFields(),{alignmentToolbar:h,alignmentLabel:b}=this._createAlignmentFields();this.focusTracker=new m.FocusTracker,this.keystrokes=new m.KeystrokeHandler,this.children=this.createCollection(),this.borderStyleDropdown=o,this.borderWidthInput=n,this.borderColorInput=i,this.backgroundInput=s,this.widthInput=a,this.heightInput=d,this.alignmentToolbar=h;const{saveButtonView:g,cancelButtonView:p}=this._createActionButtons();this.saveButtonView=g,this.cancelButtonView=p,this._focusables=new Fe.ViewCollection,this._focusCycler=new Fe.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.children.add(new Fe.FormHeaderView(e,{label:this.t("Table properties")})),this.children.add(new Pt(e,{labelView:r,children:[r,o,i,n],class:"ck-table-form__border-row"})),this.children.add(new Pt(e,{labelView:l,children:[l,s],class:"ck-table-form__background-row"})),this.children.add(new Pt(e,{children:[new Pt(e,{labelView:u,children:[u,a,c,d],class:"ck-table-form__dimensions-row"}),new Pt(e,{labelView:b,children:[b,h],class:"ck-table-properties-form__alignment-row"})]})),this.children.add(new Pt(e,{children:[this.saveButtonView,this.cancelButtonView],class:"ck-table-form__action-row"})),this.setTemplate({tag:"form",attributes:{class:["ck","ck-form","ck-table-form","ck-table-properties-form"],tabindex:"-1"},children:this.children})}render(){super.render(),(0,Fe.submitHandler)({view:this}),[this.borderColorInput,this.backgroundInput].forEach((e=>{this._focusCycler.chain(e.fieldView.focusCycler)})),[this.borderStyleDropdown,this.borderColorInput,this.borderWidthInput,this.backgroundInput,this.widthInput,this.heightInput,this.alignmentToolbar,this.saveButtonView,this.cancelButtonView].forEach((e=>{this._focusables.add(e),this.focusTracker.add(e.element)})),this.keystrokes.listenTo(this.element)}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}focus(){this._focusCycler.focusFirst()}_createBorderFields(){const e=this.options.defaultTableProperties,t={style:e.borderStyle,width:e.borderWidth,color:e.borderColor},o=xt({colorConfig:this.options.borderColors,columns:5,defaultColorValue:t.color,colorPickerConfig:this.options.colorPickerConfig}),n=this.locale,i=this.t,r=i("Style"),l=new Fe.LabelView(n);l.text=i("Border");const s=ft(i),a=new Fe.LabeledFieldView(n,Fe.createLabeledDropdown);a.set({label:r,class:"ck-table-form__border-style"}),a.fieldView.buttonView.set({ariaLabel:r,ariaLabelledBy:void 0,isOn:!1,withText:!0,tooltip:r}),a.fieldView.buttonView.bind("label").to(this,"borderStyle",(e=>s[e||"none"])),a.fieldView.on("execute",(e=>{this.borderStyle=e.source._borderStyleValue})),a.bind("isEmpty").to(this,"borderStyle",(e=>!e)),(0,Fe.addListToDropdown)(a.fieldView,yt(this,t.style),{role:"menu",ariaLabel:r});const c=new Fe.LabeledFieldView(n,Fe.createLabeledInputText);c.set({label:i("Width"),class:"ck-table-form__border-width"}),c.fieldView.bind("value").to(this,"borderWidth"),c.bind("isEnabled").to(this,"borderStyle",nn),c.fieldView.on("input",(()=>{this.borderWidth=c.fieldView.element.value}));const d=new Fe.LabeledFieldView(n,o);return d.set({label:i("Color"),class:"ck-table-form__border-color"}),d.fieldView.bind("value").to(this,"borderColor"),d.bind("isEnabled").to(this,"borderStyle",nn),d.fieldView.on("input",(()=>{this.borderColor=d.fieldView.value})),this.on("change:borderStyle",((e,o,n,i)=>{nn(n)||(this.borderColor="",this.borderWidth=""),nn(i)||(this.borderColor=t.color,this.borderWidth=t.width)})),{borderRowLabel:l,borderStyleDropdown:a,borderColorInput:d,borderWidthInput:c}}_createBackgroundFields(){const e=this.locale,t=this.t,o=new Fe.LabelView(e);o.text=t("Background");const n=xt({colorConfig:this.options.backgroundColors,columns:5,defaultColorValue:this.options.defaultTableProperties.backgroundColor,colorPickerConfig:this.options.colorPickerConfig}),i=new Fe.LabeledFieldView(e,n);return i.set({label:t("Color"),class:"ck-table-properties-form__background"}),i.fieldView.bind("value").to(this,"backgroundColor"),i.fieldView.on("input",(()=>{this.backgroundColor=i.fieldView.value})),{backgroundRowLabel:o,backgroundInput:i}}_createDimensionFields(){const e=this.locale,t=this.t,o=new Fe.LabelView(e);o.text=t("Dimensions");const n=new Fe.LabeledFieldView(e,Fe.createLabeledInputText);n.set({label:t("Width"),class:"ck-table-form__dimensions-row__width"}),n.fieldView.bind("value").to(this,"width"),n.fieldView.on("input",(()=>{this.width=n.fieldView.element.value}));const i=new Fe.View(e);i.setTemplate({tag:"span",attributes:{class:["ck-table-form__dimension-operator"]},children:[{text:"×"}]});const r=new Fe.LabeledFieldView(e,Fe.createLabeledInputText);return r.set({label:t("Height"),class:"ck-table-form__dimensions-row__height"}),r.fieldView.bind("value").to(this,"height"),r.fieldView.on("input",(()=>{this.height=r.fieldView.element.value})),{dimensionsLabel:o,widthInput:n,operatorLabel:i,heightInput:r}}_createAlignmentFields(){const t=this.locale,o=this.t,n=new Fe.LabelView(t);n.text=o("Alignment");const i=new Fe.ToolbarView(t);return i.set({isCompact:!0,ariaLabel:o("Table alignment toolbar")}),At({view:this,icons:{left:e.icons.objectLeft,center:e.icons.objectCenter,right:e.icons.objectRight},toolbar:i,labels:this._alignmentLabels,propertyName:"alignment",defaultValue:this.options.defaultTableProperties.alignment}),{alignmentLabel:n,alignmentToolbar:i}}_createActionButtons(){const t=this.locale,o=this.t,n=new Fe.ButtonView(t),i=new Fe.ButtonView(t),r=[this.borderWidthInput,this.borderColorInput,this.backgroundInput,this.widthInput,this.heightInput];return n.set({label:o("Save"),icon:e.icons.check,class:"ck-button-save",type:"submit",withText:!0}),n.bind("isEnabled").toMany(r,"errorText",((...e)=>e.every((e=>!e)))),i.set({label:o("Cancel"),icon:e.icons.cancel,class:"ck-button-cancel",withText:!0}),i.delegate("execute").to(this,"cancel"),{saveButtonView:n,cancelButtonView:i}}get _alignmentLabels(){const e=this.locale,t=this.t,o=t("Align table to the left"),n=t("Center table"),i=t("Align table to the right");return"rtl"===e.uiLanguageDirection?{right:i,center:n,left:o}:{left:o,center:n,right:i}}}function nn(e){return"none"!==e}const rn={borderStyle:"tableBorderStyle",borderColor:"tableBorderColor",borderWidth:"tableBorderWidth",backgroundColor:"tableBackgroundColor",width:"tableWidth",height:"tableHeight",alignment:"tableAlignment"};class ln extends e.Plugin{static get requires(){return[Fe.ContextualBalloon]}static get pluginName(){return"TablePropertiesUI"}static get isOfficialPlugin(){return!0}constructor(e){super(e),this.view=null,e.config.define("table.tableProperties",{borderColors:Tt,backgroundColors:Tt})}init(){const e=this.editor,t=e.t;this._defaultTableProperties=yo(e.config.get("table.tableProperties.defaultProperties"),{includeAlignmentProperty:!0}),this._balloon=e.plugins.get(Fe.ContextualBalloon),e.ui.componentFactory.add("tableProperties",(o=>{const n=new Fe.ButtonView(o);n.set({label:t("Table properties"),icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M8 2v5h4V2h1v5h5v1h-5v4h.021l-.172.351-1.916.28-.151.027c-.287.063-.54.182-.755.341L8 13v5H7v-5H2v-1h5V8H2V7h5V2h1zm4 6H8v4h4V8z" opacity=".6"/><path d="m15.5 11.5 1.323 2.68 2.957.43-2.14 2.085.505 2.946L15.5 18.25l-2.645 1.39.505-2.945-2.14-2.086 2.957-.43L15.5 11.5zM17 1a2 2 0 0 1 2 2v9.475l-.85-.124-.857-1.736a2.048 2.048 0 0 0-.292-.44L17 3H3v14h7.808l.402.392L10.935 19H3a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h14z"/></svg>',tooltip:!0}),this.listenTo(n,"execute",(()=>this._showView()));const i=Object.values(rn).map((t=>e.commands.get(t)));return n.bind("isEnabled").toMany(i,"isEnabled",((...e)=>e.some((e=>e)))),n}))}destroy(){super.destroy(),this.view&&this.view.destroy()}_createPropertiesView(){const e=this.editor,t=e.config.get("table.tableProperties"),o=(0,Fe.normalizeColorOptions)(t.borderColors),n=(0,Fe.getLocalizedColorOptions)(e.locale,o),i=(0,Fe.normalizeColorOptions)(t.backgroundColors),r=(0,Fe.getLocalizedColorOptions)(e.locale,i),l=!1!==t.colorPicker,s=new on(e.locale,{borderColors:n,backgroundColors:r,defaultTableProperties:this._defaultTableProperties,colorPickerConfig:!!l&&(t.colorPicker||{})}),a=e.t;s.render(),this.listenTo(s,"submit",(()=>{this._hideView()})),this.listenTo(s,"cancel",(()=>{this._undoStepBatch.operations.length&&e.execute("undo",this._undoStepBatch),this._hideView()})),s.keystrokes.set("Esc",((e,t)=>{this._hideView(),t()})),(0,Fe.clickOutsideHandler)({emitter:s,activator:()=>this._isViewInBalloon,contextElements:[this._balloon.view.element],callback:()=>this._hideView()});const c=wt(a),d=_t(a);return s.on("change:borderStyle",this._getPropertyChangeCallback("tableBorderStyle")),s.on("change:borderColor",this._getValidatedPropertyChangeCallback({viewField:s.borderColorInput,commandName:"tableBorderColor",errorText:c,validator:kt})),s.on("change:borderWidth",this._getValidatedPropertyChangeCallback({viewField:s.borderWidthInput,commandName:"tableBorderWidth",errorText:d,validator:Ct})),s.on("change:backgroundColor",this._getValidatedPropertyChangeCallback({viewField:s.backgroundInput,commandName:"tableBackgroundColor",errorText:c,validator:kt})),s.on("change:width",this._getValidatedPropertyChangeCallback({viewField:s.widthInput,commandName:"tableWidth",errorText:d,validator:vt})),s.on("change:height",this._getValidatedPropertyChangeCallback({viewField:s.heightInput,commandName:"tableHeight",errorText:d,validator:vt})),s.on("change:alignment",this._getPropertyChangeCallback("tableAlignment")),s}_fillViewFormFromCommandValues(){const e=this.editor.commands,t=e.get("tableBorderStyle");Object.entries(rn).map((([t,o])=>{const n=t,i=this._defaultTableProperties[n]||"";return[n,e.get(o).value||i]})).forEach((([e,o])=>{("borderColor"!==e&&"borderWidth"!==e||"none"!==t.value)&&this.view.set(e,o)})),this._isReady=!0}_showView(){const e=this.editor;this.view||(this.view=this._createPropertiesView()),this.listenTo(e.ui,"update",(()=>{this._updateView()})),this._fillViewFormFromCommandValues(),this._balloon.add({view:this.view,position:fo(e)}),this._undoStepBatch=e.model.createBatch(),this.view.focus()}_hideView(){const e=this.editor;this.stopListening(e.ui,"update"),this._isReady=!1,this.view.saveButtonView.focus(),this._balloon.remove(this.view),this.editor.editing.view.focus()}_updateView(){const e=this.editor;st(e.editing.view.document.selection)?this._isViewVisible&&po(e,"table"):this._hideView()}get _isViewVisible(){return!!this.view&&this._balloon.visibleView===this.view}get _isViewInBalloon(){return!!this.view&&this._balloon.hasView(this.view)}_getPropertyChangeCallback(e){return(t,o,n)=>{this._isReady&&this.editor.execute(e,{value:n,batch:this._undoStepBatch})}}_getValidatedPropertyChangeCallback(e){const{commandName:t,viewField:o,validator:n,errorText:i}=e,r=mo((()=>{o.errorText=i}),500);return(e,i,l)=>{r.cancel(),this._isReady&&(n(l)?(this.editor.execute(t,{value:l,batch:this._undoStepBatch}),o.errorText=null):r())}}}class sn extends e.Plugin{static get pluginName(){return"TableProperties"}static get isOfficialPlugin(){return!0}static get requires(){return[Qo,ln]}}function an(e){e.document.registerPostFixer((t=>function(e,t){const o=t.document.differ.getChanges();let n=!1;for(const t of o){if("insert"!=t.type)continue;const o=t.position.parent;if(o.is("element","table")||"table"==t.name){const i="table"==t.name?t.position.nodeAfter:o,r=Array.from(i.getChildren()).filter((e=>e.is("element","caption"))),l=r.shift();if(!l)continue;for(const t of r)e.move(e.createRangeIn(t),l,"end"),e.remove(t);l.nextSibling&&(e.move(e.createRangeOn(l),i,"end"),n=!0),n=!!r.length||n}}return n}(t,e)))}function cn(e){return!!e&&e.is("element","table")}function dn(e){for(const t of e.getChildren())if(t.is("element","caption"))return t;return null}function un(e){const t=e.parent;return"figcaption"==e.name&&t&&t.is("element","figure")&&t.hasClass("table")||"caption"==e.name&&t&&t.is("element","table")?{name:!0}:null}class hn extends e.Command{refresh(){const e=b(this.editor.model.document.selection);this.isEnabled=!!e,this.isEnabled?this.value=!!dn(e):this.value=!1}execute({focusCaptionOnShow:e=!1}={}){this.editor.model.change((t=>{this.value?this._hideTableCaption(t):this._showTableCaption(t,e)}))}_showTableCaption(e,t){const o=this.editor.model,n=b(o.document.selection),i=this.editor.plugins.get("TableCaptionEditing")._getSavedCaption(n)||e.createElement("caption");o.insertContent(i,n,"end"),t&&e.setSelection(i,"in")}_hideTableCaption(e){const t=this.editor.model,o=b(t.document.selection),n=this.editor.plugins.get("TableCaptionEditing"),i=dn(o);n._saveCaption(o,i),t.deleteContent(e.createSelection(i,"on"))}}class bn extends e.Plugin{static get pluginName(){return"TableCaptionEditing"}static get isOfficialPlugin(){return!0}constructor(e){super(e),this._savedCaptionsMap=new WeakMap}init(){const e=this.editor,o=e.model.schema,n=e.editing.view,i=e.t;o.isRegistered("caption")?o.extend("caption",{allowIn:"table"}):o.register("caption",{allowIn:"table",allowContentOf:"$block",isLimit:!0}),e.commands.add("toggleTableCaption",new hn(this.editor)),e.conversion.for("upcast").elementToElement({view:un,model:"caption"}),e.conversion.for("dataDowncast").elementToElement({model:"caption",view:(e,{writer:t})=>cn(e.parent)?t.createContainerElement("figcaption"):null}),e.conversion.for("editingDowncast").elementToElement({model:"caption",view:(e,{writer:o})=>{if(!cn(e.parent))return null;const r=o.createEditableElement("figcaption");return o.setCustomProperty("tableCaption",!0,r),r.placeholder=i("Enter table caption"),(0,Qe.enablePlaceholder)({view:n,element:r,keepOnFocus:!0}),(0,t.toWidgetEditable)(r,o)}}),an(e.model)}_getSavedCaption(e){const t=this._savedCaptionsMap.get(e);return t?Qe.Element.fromJSON(t):null}_saveCaption(e,t){this._savedCaptionsMap.set(e,t.toJSON())}}class mn extends e.Plugin{static get pluginName(){return"TableCaptionUI"}static get isOfficialPlugin(){return!0}init(){const t=this.editor,o=t.editing.view,n=t.t;t.ui.componentFactory.add("toggleTableCaption",(i=>{const r=t.commands.get("toggleTableCaption"),l=new Fe.ButtonView(i);return l.set({icon:e.icons.caption,tooltip:!0,isToggleable:!0}),l.bind("isOn","isEnabled").to(r,"value","isEnabled"),l.bind("label").to(r,"value",(e=>n(e?"Toggle caption off":"Toggle caption on"))),this.listenTo(l,"execute",(()=>{if(t.execute("toggleTableCaption",{focusCaptionOnShow:!0}),r.value){const e=function(e){const t=b(e);return t?dn(t):null}(t.model.document.selection),n=t.editing.mapper.toViewElement(e);if(!n)return;o.scrollToTheSelection(),o.change((e=>{e.addClass("table__caption_highlighted",n)}))}t.editing.view.focus()})),l}))}}var gn=o(175),pn={attributes:{"data-cke":!0}};pn.setAttributes=Pe(),pn.insert=Ve().bind(null,"head"),pn.domAPI=xe(),pn.insertStyleElement=Ee();Ae()(gn.A,pn);gn.A&&gn.A.locals&&gn.A.locals;class fn extends e.Plugin{static get pluginName(){return"TableCaption"}static get isOfficialPlugin(){return!0}static get requires(){return[bn,mn]}}const wn=function(){this.__data__=[],this.size=0};const _n=function(e,t){return e===t||e!=e&&t!=t};const kn=function(e,t){for(var o=e.length;o--;)if(_n(e[o][0],t))return o;return-1};var vn=Array.prototype.splice;const Cn=function(e){var t=this.__data__,o=kn(t,e);return!(o<0)&&(o==t.length-1?t.pop():vn.call(t,o,1),--this.size,!0)};const yn=function(e){var t=this.__data__,o=kn(t,e);return o<0?void 0:t[o][1]};const An=function(e){return kn(this.__data__,e)>-1};const Tn=function(e,t){var o=this.__data__,n=kn(o,e);return n<0?(++this.size,o.push([e,t])):o[n][1]=t,this};function xn(e){var t=-1,o=null==e?0:e.length;for(this.clear();++t<o;){var n=e[t];this.set(n[0],n[1])}}xn.prototype.clear=wn,xn.prototype.delete=Cn,xn.prototype.get=yn,xn.prototype.has=An,xn.prototype.set=Tn;const Sn=xn;const Vn=function(){this.__data__=new Sn,this.size=0};const Rn=function(e){var t=this.__data__,o=t.delete(e);return this.size=t.size,o};const Pn=function(e){return this.__data__.get(e)};const In=function(e){return this.__data__.has(e)};const En=function(e){if(!Nt(e))return!1;var t=no(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t};const zn=jt["__core-js_shared__"];var Bn,On=(Bn=/[^.]+$/.exec(zn&&zn.keys&&zn.keys.IE_PROTO||""))?"Symbol(src)_1."+Bn:"";const Ln=function(e){return!!On&&On in e};var Fn=Function.prototype.toString;const Wn=function(e){if(null!=e){try{return Fn.call(e)}catch(e){}try{return e+""}catch(e){}}return""};var Nn=/^\[object .+?Constructor\]$/,Dn=Function.prototype,Mn=Object.prototype,jn=Dn.toString,Hn=Mn.hasOwnProperty,Un=RegExp("^"+jn.call(Hn).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const $n=function(e){return!(!Nt(e)||Ln(e))&&(En(e)?Un:Nn).test(Wn(e))};const Kn=function(e,t){return null==e?void 0:e[t]};const Gn=function(e,t){var o=Kn(e,t);return $n(o)?o:void 0};const qn=Gn(jt,"Map");const Jn=Gn(Object,"create");const Xn=function(){this.__data__=Jn?Jn(null):{},this.size=0};const Yn=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t};var Qn=Object.prototype.hasOwnProperty;const Zn=function(e){var t=this.__data__;if(Jn){var o=t[e];return"__lodash_hash_undefined__"===o?void 0:o}return Qn.call(t,e)?t[e]:void 0};var ei=Object.prototype.hasOwnProperty;const ti=function(e){var t=this.__data__;return Jn?void 0!==t[e]:ei.call(t,e)};const oi=function(e,t){var o=this.__data__;return this.size+=this.has(e)?0:1,o[e]=Jn&&void 0===t?"__lodash_hash_undefined__":t,this};function ni(e){var t=-1,o=null==e?0:e.length;for(this.clear();++t<o;){var n=e[t];this.set(n[0],n[1])}}ni.prototype.clear=Xn,ni.prototype.delete=Yn,ni.prototype.get=Zn,ni.prototype.has=ti,ni.prototype.set=oi;const ii=ni;const ri=function(){this.size=0,this.__data__={hash:new ii,map:new(qn||Sn),string:new ii}};const li=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};const si=function(e,t){var o=e.__data__;return li(t)?o["string"==typeof t?"string":"hash"]:o.map};const ai=function(e){var t=si(this,e).delete(e);return this.size-=t?1:0,t};const ci=function(e){return si(this,e).get(e)};const di=function(e){return si(this,e).has(e)};const ui=function(e,t){var o=si(this,e),n=o.size;return o.set(e,t),this.size+=o.size==n?0:1,this};function hi(e){var t=-1,o=null==e?0:e.length;for(this.clear();++t<o;){var n=e[t];this.set(n[0],n[1])}}hi.prototype.clear=ri,hi.prototype.delete=ai,hi.prototype.get=ci,hi.prototype.has=di,hi.prototype.set=ui;const bi=hi;const mi=function(e,t){var o=this.__data__;if(o instanceof Sn){var n=o.__data__;if(!qn||n.length<199)return n.push([e,t]),this.size=++o.size,this;o=this.__data__=new bi(n)}return o.set(e,t),this.size=o.size,this};function gi(e){var t=this.__data__=new Sn(e);this.size=t.size}gi.prototype.clear=Vn,gi.prototype.delete=Rn,gi.prototype.get=Pn,gi.prototype.has=In,gi.prototype.set=mi;const pi=gi;const fi=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this};const wi=function(e){return this.__data__.has(e)};function _i(e){var t=-1,o=null==e?0:e.length;for(this.__data__=new bi;++t<o;)this.add(e[t])}_i.prototype.add=_i.prototype.push=fi,_i.prototype.has=wi;const ki=_i;const vi=function(e,t){for(var o=-1,n=null==e?0:e.length;++o<n;)if(t(e[o],o,e))return!0;return!1};const Ci=function(e,t){return e.has(t)};const yi=function(e,t,o,n,i,r){var l=1&o,s=e.length,a=t.length;if(s!=a&&!(l&&a>s))return!1;var c=r.get(e),d=r.get(t);if(c&&d)return c==t&&d==e;var u=-1,h=!0,b=2&o?new ki:void 0;for(r.set(e,t),r.set(t,e);++u<s;){var m=e[u],g=t[u];if(n)var p=l?n(g,m,u,t,e,r):n(m,g,u,e,t,r);if(void 0!==p){if(p)continue;h=!1;break}if(b){if(!vi(t,(function(e,t){if(!Ci(b,t)&&(m===e||i(m,e,o,n,r)))return b.push(t)}))){h=!1;break}}else if(m!==g&&!i(m,g,o,n,r)){h=!1;break}}return r.delete(e),r.delete(t),h};const Ai=jt.Uint8Array;const Ti=function(e){var t=-1,o=Array(e.size);return e.forEach((function(e,n){o[++t]=[n,e]})),o};const xi=function(e){var t=-1,o=Array(e.size);return e.forEach((function(e){o[++t]=e})),o};var Si=qt?qt.prototype:void 0,Vi=Si?Si.valueOf:void 0;const Ri=function(e,t,o,n,i,r,l){switch(o){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!r(new Ai(e),new Ai(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return _n(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var s=Ti;case"[object Set]":var a=1&n;if(s||(s=xi),e.size!=t.size&&!a)return!1;var c=l.get(e);if(c)return c==t;n|=2,l.set(e,t);var d=yi(s(e),s(t),n,i,r,l);return l.delete(e),d;case"[object Symbol]":if(Vi)return Vi.call(e)==Vi.call(t)}return!1};const Pi=function(e,t){for(var o=-1,n=t.length,i=e.length;++o<n;)e[i+o]=t[o];return e};const Ii=Array.isArray;const Ei=function(e,t,o){var n=t(e);return Ii(e)?n:Pi(n,o(e))};const zi=function(e,t){for(var o=-1,n=null==e?0:e.length,i=0,r=[];++o<n;){var l=e[o];t(l,o,e)&&(r[i++]=l)}return r};const Bi=function(){return[]};var Oi=Object.prototype.propertyIsEnumerable,Li=Object.getOwnPropertySymbols;const Fi=Li?function(e){return null==e?[]:(e=Object(e),zi(Li(e),(function(t){return Oi.call(e,t)})))}:Bi;const Wi=function(e,t){for(var o=-1,n=Array(e);++o<e;)n[o]=t(o);return n};const Ni=function(e){return io(e)&&"[object Arguments]"==no(e)};var Di=Object.prototype,Mi=Di.hasOwnProperty,ji=Di.propertyIsEnumerable;const Hi=Ni(function(){return arguments}())?Ni:function(e){return io(e)&&Mi.call(e,"callee")&&!ji.call(e,"callee")};const Ui=function(){return!1};var $i="object"==typeof exports&&exports&&!exports.nodeType&&exports,Ki=$i&&"object"==typeof module&&module&&!module.nodeType&&module,Gi=Ki&&Ki.exports===$i?jt.Buffer:void 0;const qi=(Gi?Gi.isBuffer:void 0)||Ui;var Ji=/^(?:0|[1-9]\d*)$/;const Xi=function(e,t){var o=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==o||"symbol"!=o&&Ji.test(e))&&e>-1&&e%1==0&&e<t};const Yi=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991};var Qi={};Qi["[object Float32Array]"]=Qi["[object Float64Array]"]=Qi["[object Int8Array]"]=Qi["[object Int16Array]"]=Qi["[object Int32Array]"]=Qi["[object Uint8Array]"]=Qi["[object Uint8ClampedArray]"]=Qi["[object Uint16Array]"]=Qi["[object Uint32Array]"]=!0,Qi["[object Arguments]"]=Qi["[object Array]"]=Qi["[object ArrayBuffer]"]=Qi["[object Boolean]"]=Qi["[object DataView]"]=Qi["[object Date]"]=Qi["[object Error]"]=Qi["[object Function]"]=Qi["[object Map]"]=Qi["[object Number]"]=Qi["[object Object]"]=Qi["[object RegExp]"]=Qi["[object Set]"]=Qi["[object String]"]=Qi["[object WeakMap]"]=!1;const Zi=function(e){return io(e)&&Yi(e.length)&&!!Qi[no(e)]};const er=function(e){return function(t){return e(t)}};var tr="object"==typeof exports&&exports&&!exports.nodeType&&exports,or=tr&&"object"==typeof module&&module&&!module.nodeType&&module,nr=or&&or.exports===tr&&Dt.process,ir=function(){try{var e=or&&or.require&&or.require("util").types;return e||nr&&nr.binding&&nr.binding("util")}catch(e){}}();var rr=ir&&ir.isTypedArray;const lr=rr?er(rr):Zi;var sr=Object.prototype.hasOwnProperty;const ar=function(e,t){var o=Ii(e),n=!o&&Hi(e),i=!o&&!n&&qi(e),r=!o&&!n&&!i&&lr(e),l=o||n||i||r,s=l?Wi(e.length,String):[],a=s.length;for(var c in e)!t&&!sr.call(e,c)||l&&("length"==c||i&&("offset"==c||"parent"==c)||r&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Xi(c,a))||s.push(c);return s};var cr=Object.prototype;const dr=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||cr)};const ur=function(e,t){return function(o){return e(t(o))}}(Object.keys,Object);var hr=Object.prototype.hasOwnProperty;const br=function(e){if(!dr(e))return ur(e);var t=[];for(var o in Object(e))hr.call(e,o)&&"constructor"!=o&&t.push(o);return t};const mr=function(e){return null!=e&&Yi(e.length)&&!En(e)};const gr=function(e){return mr(e)?ar(e):br(e)};const pr=function(e){return Ei(e,gr,Fi)};var fr=Object.prototype.hasOwnProperty;const wr=function(e,t,o,n,i,r){var l=1&o,s=pr(e),a=s.length;if(a!=pr(t).length&&!l)return!1;for(var c=a;c--;){var d=s[c];if(!(l?d in t:fr.call(t,d)))return!1}var u=r.get(e),h=r.get(t);if(u&&h)return u==t&&h==e;var b=!0;r.set(e,t),r.set(t,e);for(var m=l;++c<a;){var g=e[d=s[c]],p=t[d];if(n)var f=l?n(p,g,d,t,e,r):n(g,p,d,e,t,r);if(!(void 0===f?g===p||i(g,p,o,n,r):f)){b=!1;break}m||(m="constructor"==d)}if(b&&!m){var w=e.constructor,_=t.constructor;w==_||!("constructor"in e)||!("constructor"in t)||"function"==typeof w&&w instanceof w&&"function"==typeof _&&_ instanceof _||(b=!1)}return r.delete(e),r.delete(t),b};const _r=Gn(jt,"DataView");const kr=Gn(jt,"Promise");const vr=Gn(jt,"Set");const Cr=Gn(jt,"WeakMap");var yr="[object Map]",Ar="[object Promise]",Tr="[object Set]",xr="[object WeakMap]",Sr="[object DataView]",Vr=Wn(_r),Rr=Wn(qn),Pr=Wn(kr),Ir=Wn(vr),Er=Wn(Cr),zr=no;(_r&&zr(new _r(new ArrayBuffer(1)))!=Sr||qn&&zr(new qn)!=yr||kr&&zr(kr.resolve())!=Ar||vr&&zr(new vr)!=Tr||Cr&&zr(new Cr)!=xr)&&(zr=function(e){var t=no(e),o="[object Object]"==t?e.constructor:void 0,n=o?Wn(o):"";if(n)switch(n){case Vr:return Sr;case Rr:return yr;case Pr:return Ar;case Ir:return Tr;case Er:return xr}return t});const Br=zr;var Or="[object Arguments]",Lr="[object Array]",Fr="[object Object]",Wr=Object.prototype.hasOwnProperty;const Nr=function(e,t,o,n,i,r){var l=Ii(e),s=Ii(t),a=l?Lr:Br(e),c=s?Lr:Br(t),d=(a=a==Or?Fr:a)==Fr,u=(c=c==Or?Fr:c)==Fr,h=a==c;if(h&&qi(e)){if(!qi(t))return!1;l=!0,d=!1}if(h&&!d)return r||(r=new pi),l||lr(e)?yi(e,t,o,n,i,r):Ri(e,t,a,o,n,i,r);if(!(1&o)){var b=d&&Wr.call(e,"__wrapped__"),m=u&&Wr.call(t,"__wrapped__");if(b||m){var g=b?e.value():e,p=m?t.value():t;return r||(r=new pi),i(g,p,o,n,r)}}return!!h&&(r||(r=new pi),wr(e,t,o,n,i,r))};const Dr=function e(t,o,n,i,r){return t===o||(null==t||null==o||!io(t)&&!io(o)?t!=t&&o!=o:Nr(t,o,n,i,e,r))};const Mr=function(e,t){return Dr(e,t)};const jr=function(e,t,o){var n=!0,i=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return Nt(o)&&(n="leading"in o?!!o.leading:n,i="trailing"in o?!!o.trailing:i),mo(e,t,{leading:n,maxWait:t,trailing:i})};class Hr extends e.Command{refresh(){this.isEnabled=!0}execute(e={}){const{model:t,plugins:o}=this.editor;let{table:n=t.document.selection.getSelectedElement(),columnWidths:i,tableWidth:r}=e;i&&(i=Array.isArray(i)?i:i.split(",")),t.change((e=>{r?e.setAttribute("tableWidth",r,n):e.removeAttribute("tableWidth",n);const t=o.get("TableColumnResizeEditing").getColumnGroupElement(n);if(!i&&!t)return;if(!i)return e.remove(t);const l=Y(i);if(t)Array.from(t.getChildren()).forEach(((t,o)=>e.setAttribute("columnWidth",l[o],t)));else{const t=e.createElement("tableColumnGroup");l.forEach((o=>e.appendElement("tableColumn",{columnWidth:o},t))),e.append(t,n)}}))}}function Ur(e){return t=>t.on("element:colgroup",((t,o,n)=>{const i=o.modelCursor.findAncestor("table"),r=ee(i);if(!r)return;const l=te(r),s=e.getColumns(i);let a=(c=r,d=n.writer,te(c).reduce(((e,t)=>{const o=t.getAttribute("columnWidth"),n=t.getAttribute("colSpan");if(!n)return e.push(o),e;for(let t=0;t<n;t++)e.push(o);return d.removeAttribute("colSpan",t),e}),[]));var c,d;a=Array.from({length:s},((e,t)=>a[t]||"auto")),(a.length!=l.length||a.includes("auto"))&&Z(l,r,Y(a),n.writer)}),{priority:"low"})}class $r extends e.Plugin{static get requires(){return[Oe,oe]}static get pluginName(){return"TableColumnResizeEditing"}static get isOfficialPlugin(){return!0}constructor(e){super(e),this._isResizingActive=!1,this.set("_isResizingAllowed",!0),this._resizingData=null,this._domEmitter=new((0,m.DomEmitterMixin)()),this._tableUtilsPlugin=e.plugins.get("TableUtils"),this.on("change:_isResizingAllowed",((t,o,n)=>{const i=n?"removeClass":"addClass";e.editing.view.change((t=>{for(const o of e.editing.view.document.roots)t[i]("ck-column-resize_disabled",e.editing.view.document.getRoot(o.rootName))}))}))}init(){this._extendSchema(),this._registerPostFixer(),this._registerConverters(),this._registerResizingListeners(),this._registerResizerInserter();const e=this.editor,t=e.plugins.get("TableColumnResize");e.plugins.get("TableEditing").registerAdditionalSlot({filter:e=>e.is("element","tableColumnGroup"),positionOffset:0});const o=new Hr(e);e.commands.add("resizeTableWidth",o),e.commands.add("resizeColumnWidths",o),this.bind("_isResizingAllowed").to(e,"isReadOnly",t,"isEnabled",o,"isEnabled",((e,t,o)=>!e&&t&&o))}destroy(){this._domEmitter.stopListening(),super.destroy()}getColumnGroupElement(e){return ee(e)}getTableColumnElements(e){return te(e)}getTableColumnsWidths(e){return function(e){return te(e).map((e=>e.getAttribute("columnWidth")))}(e)}_extendSchema(){this.editor.model.schema.extend("table",{allowAttributes:["tableWidth"]}),this.editor.model.schema.register("tableColumnGroup",{allowIn:"table",isLimit:!0}),this.editor.model.schema.register("tableColumn",{allowIn:"tableColumnGroup",allowAttributes:["columnWidth","colSpan"],isLimit:!0})}_registerPostFixer(){const e=this.editor.model;function t(e,t,o){const n=o._tableUtilsPlugin.getColumns(t);if(0===n-e.length)return e;const i=e.map((e=>Number(e.replace("%","")))),r=function(e,t){const o=new Set;for(const n of e.getChanges())if("insert"==n.type&&n.position.nodeAfter&&"tableCell"==n.position.nodeAfter.name&&n.position.nodeAfter.getAncestors().includes(t))o.add(n.position.nodeAfter);else if("remove"==n.type){const e=n.position.nodeBefore||n.position.nodeAfter;"tableCell"==e.name&&e.getAncestors().includes(t)&&o.add(e)}return o}(o.editor.model.document.differ,t);for(const e of r){const r=n-i.length;if(0===r)continue;const s=r>0,a=o._tableUtilsPlugin.getCellLocation(e).column;if(s){const e=$(t,o.editor),n=(l=e,Array(r).fill(l));i.splice(a,0,...n)}else{const e=i.splice(a,Math.abs(r));i[a]+=X(e)}}var l;return i.map((e=>e+"%"))}e.document.registerPostFixer((o=>{let n=!1;for(const i of function(e){const t=new Set;for(const o of e.document.differ.getChanges()){let n=null;switch(o.type){case"insert":n=["table","tableRow","tableCell"].includes(o.name)?o.position:null;break;case"remove":n=["tableRow","tableCell"].includes(o.name)?o.position:null;break;case"attribute":o.range.start.nodeAfter&&(n=["table","tableRow","tableCell"].includes(o.range.start.nodeAfter.name)?o.range.start:null)}if(!n)continue;const i=n.nodeAfter&&n.nodeAfter.is("element","table")?n.nodeAfter:n.findAncestor("table");for(const o of e.createRangeOn(i).getItems())o.is("element","table")&&ee(o)&&t.add(o)}return t}(e)){const e=this.getColumnGroupElement(i),r=this.getTableColumnElements(e),l=this.getTableColumnsWidths(e);let s=Y(l);s=t(s,i,this),Mr(l,s)||(Z(r,e,s,o),n=!0)}return n}))}_registerConverters(){const e=this.editor.conversion;e.for("upcast").attributeToAttribute({view:{name:"figure",key:"style",value:{width:/[\s\S]+/}},model:{name:"table",key:"tableWidth",value:e=>e.getStyle("width")}}),e.for("downcast").attributeToAttribute({model:{name:"table",key:"tableWidth"},view:e=>({name:"figure",key:"style",value:{width:e}})}),e.elementToElement({model:"tableColumnGroup",view:"colgroup"}),e.elementToElement({model:"tableColumn",view:"col"}),e.for("downcast").add((e=>e.on("insert:table",((e,t,o)=>{const n=o.writer,i=t.item,r=o.mapper.toViewElement(i),l=r.is("element","table")?r:Array.from(r.getChildren()).find((e=>e.is("element","table")));ee(i)?n.addClass("ck-table-resized",l):n.removeClass("ck-table-resized",l)}),{priority:"low"}))),e.for("upcast").add(Ur(this._tableUtilsPlugin)),e.for("upcast").attributeToAttribute({view:{name:"col",styles:{width:/.*/}},model:{key:"columnWidth",value:e=>{const t=e.getStyle("width");return t&&(t.endsWith("%")||t.endsWith("pt"))?t:"auto"}}}),e.for("upcast").attributeToAttribute({view:{name:"col",key:"span"},model:"colSpan"}),e.for("downcast").attributeToAttribute({model:{name:"tableColumn",key:"columnWidth"},view:e=>({key:"style",value:{width:e}})})}_registerResizingListeners(){const e=this.editor.editing.view;e.addObserver(Ze),e.document.on("mousedown",this._onMouseDownHandler.bind(this),{priority:"high"}),this._domEmitter.listenTo(m.global.window.document,"mousemove",jr(this._onMouseMoveHandler.bind(this),50)),this._domEmitter.listenTo(m.global.window.document,"mouseup",this._onMouseUpHandler.bind(this))}_onMouseDownHandler(e,t){const o=t.target;if(!o.hasClass("ck-table-column-resizer"))return;if(!this._isResizingAllowed)return;const n=this.editor,i=n.editing.mapper.toModelElement(o.findAncestor("figure"));if(!n.model.canEditAt(i))return;t.preventDefault(),e.stop();const r=function(e,t,o){const n=Array(t.getColumns(e)),i=new w(e);for(const e of i){const t=o.editing.mapper.toViewElement(e.cell),i=Q(o.editing.view.domConverter.mapViewToDom(t));(!n[e.column]||i<n[e.column])&&(n[e.column]=J(i))}return n}(i,this._tableUtilsPlugin,n),l=o.findAncestor("table"),s=n.editing.view;Array.from(l.getChildren()).find((e=>e.is("element","colgroup")))||s.change((e=>{!function(e,t,o){const n=e.createContainerElement("colgroup");for(let o=0;o<t.length;o++){const i=e.createEmptyElement("col"),r=`${J(t[o]/X(t)*100)}%`;e.setStyle("width",r,i),e.insert(e.createPositionAt(n,"end"),i)}e.insert(e.createPositionAt(o,0),n)}(e,r,l)})),this._isResizingActive=!0,this._resizingData=this._getResizingData(t,r),s.change((e=>function(e,t,o){const n=o.widths.viewFigureWidth/o.widths.viewFigureParentWidth;e.addClass("ck-table-resized",t),e.addClass("ck-table-column-resizer__active",o.elements.viewResizer),e.setStyle("width",`${J(100*n)}%`,t.findAncestor("figure"))}(e,l,this._resizingData)))}_onMouseMoveHandler(e,t){if(!this._isResizingActive)return;if(!this._isResizingAllowed)return void this._onMouseUpHandler();const{columnPosition:o,flags:{isRightEdge:n,isTableCentered:i,isLtrContent:r},elements:{viewFigure:l,viewLeftColumn:s,viewRightColumn:a},widths:{viewFigureParentWidth:c,tableWidth:d,leftColumnWidth:u,rightColumnWidth:h}}=this._resizingData,b=40-u,m=n?c-d:h-40,g=(r?1:-1)*(n&&i?2:1),p=(f=(t.clientX-o)*g,w=Math.min(b,0),_=Math.max(m,0),J(f<=w?w:f>=_?_:f));var f,w,_;0!==p&&this.editor.editing.view.change((e=>{const t=J(100*(u+p)/d);if(e.setStyle("width",`${t}%`,s),n){const t=J(100*(d+p)/c);e.setStyle("width",`${t}%`,l)}else{const t=J(100*(h-p)/d);e.setStyle("width",`${t}%`,a)}}))}_onMouseUpHandler(){if(!this._isResizingActive)return;const{viewResizer:e,modelTable:t,viewFigure:o,viewColgroup:n}=this._resizingData.elements,i=this.editor,r=i.editing.view,l=this.getColumnGroupElement(t),s=Array.from(n.getChildren()).filter((e=>e.is("view:element"))),a=l?this.getTableColumnsWidths(l):null,c=s.map((e=>e.getStyle("width"))),d=!Mr(a,c),u=t.getAttribute("tableWidth"),h=o.getStyle("width"),b=u!==h;(d||b)&&(this._isResizingAllowed?i.execute("resizeTableWidth",{table:t,tableWidth:`${J(h)}%`,columnWidths:c}):r.change((e=>{if(a)for(const t of s)e.setStyle("width",a.shift(),t);else e.remove(n);b&&(u?e.setStyle("width",u,o):e.removeStyle("width",o)),a||u||e.removeClass("ck-table-resized",[...o.getChildren()].find((e=>"table"===e.name)))}))),r.change((t=>{t.removeClass("ck-table-column-resizer__active",e)})),this._isResizingActive=!1,this._resizingData=null}_getResizingData(e,t){const o=this.editor,n=e.domEvent.clientX,i=e.target,r=i.findAncestor("td")||i.findAncestor("th"),l=o.editing.mapper.toModelElement(r),s=l.findAncestor("table"),a=function(e,t){const o=t.getCellLocation(e).column;return{leftEdge:o,rightEdge:o+(e.getAttribute("colspan")||1)-1}}(l,this._tableUtilsPlugin).rightEdge,c=a===this._tableUtilsPlugin.getColumns(s)-1,d=!s.hasAttribute("tableAlignment"),u="rtl"!==o.locale.contentLanguageDirection,h=r.findAncestor("table"),b=h.findAncestor("figure"),m=[...h.getChildren()].find((e=>e.is("element","colgroup"))),g=m.getChild(a),p=c?void 0:m.getChild(a+1);return{columnPosition:n,flags:{isRightEdge:c,isTableCentered:d,isLtrContent:u},elements:{viewResizer:i,modelTable:s,viewFigure:b,viewColgroup:m,viewLeftColumn:g,viewRightColumn:p},widths:{viewFigureParentWidth:q(o.editing.view.domConverter.mapViewToDom(b.parent)),viewFigureWidth:q(o.editing.view.domConverter.mapViewToDom(b)),tableWidth:K(s,o),leftColumnWidth:t[a],rightColumnWidth:c?void 0:t[a+1]}}}_registerResizerInserter(){this.editor.conversion.for("editingDowncast").add((e=>{e.on("insert:tableCell",((e,t,o)=>{const n=t.item,i=o.mapper.toViewElement(n),r=o.writer;r.insert(r.createPositionAt(i,"end"),r.createUIElement("div",{class:"ck-table-column-resizer"}))}),{priority:"lowest"})}))}}var Kr=o(363),Gr={attributes:{"data-cke":!0}};Gr.setAttributes=Pe(),Gr.insert=Ve().bind(null,"head"),Gr.domAPI=xe(),Gr.insertStyleElement=Ee();Ae()(Kr.A,Gr);Kr.A&&Kr.A.locals&&Kr.A.locals;class qr extends e.Plugin{static get requires(){return[$r,Ro]}static get pluginName(){return"TableColumnResize"}static get isOfficialPlugin(){return!0}}})(),(window.CKEditor5=window.CKEditor5||{}).table=n})();