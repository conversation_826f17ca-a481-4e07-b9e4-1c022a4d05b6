!function(e){const o=e.pt=e.pt||{};o.dictionary=Object.assign(o.dictionary||{},{"(may require <kbd>Fn</kbd>)":"(pode exigir <kbd>Fn</kbd>)","%0 of %1":"%0 de %1",Accept:"Aceitar",Accessibility:"Acessibilidade","Accessibility help":"Ajuda de acessibilidade",Aquamarine:"Verde-azulado","Below, you can find a list of keyboard shortcuts that can be used in the editor.":"Abaixo, encontra-se uma lista de atalhos de teclado que podem ser utilizados no editor.",Black:"Preto",Blue:"Azul",Cancel:"Cancelar","Cannot upload file:":"Não foi possível carregar o ficheiro:",Clear:"Limpar","Click to edit block":"Clique para editar o bloco",Close:"Fechar","Close contextual balloons, dropdowns, and dialogs":"<PERSON><PERSON><PERSON> balões contextuais, menus suspensos e caixas de diálogo","Color picker":"Seletor de cor","Content editing keystrokes":"Batimentos de teclas para editar o conteúdo","Copy selected content":"Copiar o conteúdo selecionado","Dim grey":"Cinzento-escuro","Drag to move":"Arraste para mover","Dropdown menu":"Menu suspenso","Dropdown toolbar":"Barra de ferramentas do menu pendente","Edit block":"Editar bloco","Editor block content toolbar":"Barra de ferramentas de edição do conteúdo de blocos","Editor contextual toolbar":"Barra de ferramentas contextual de edição","Editor dialog":"Diálogo do editor","Editor menu bar":"Barra de menu do editor","Editor toolbar":"Barra de ferramentas do editor","Execute the currently focused button. Executing buttons that interact with the editor content moves the focus back to the content.":"Executar o botão atualmente em foco. A execução de botões que interagem com o conteúdo do editor coloca novamente o foco sobre o conteúdo.",File:"Ficheiro",Green:"Verde",Grey:"Cinzento","Help Contents. To close this dialog press ESC.":"Conteúdos de ajuda. Para fechar esta caixa de diálogo, prima ESC.",HEX:"HEX","Insert a hard break (a new paragraph)":"Inserir uma quebra brusca (um novo parágrafo)","Insert a new paragraph directly after a widget":"Inserir um novo parágrafo diretamente após um widget","Insert a new paragraph directly before a widget":"Inserir um novo parágrafo diretamente antes de um widget","Insert a soft break (a <code>&lt;br&gt;</code> element)":"Inserir uma quebra suave (um elemento <code>&lt;br&gt;</code>)","Insert image with file manager":"Inserir imagem com o gestor de ficheiros","Insert paragraph after block":"Inserir parágrafo após o bloco","Insert paragraph before block":"Inserir parágrafo antes do bloco","Insert with file manager":"Inserir com o gestor de ficheiros","Keystrokes that can be used when a widget is selected (for example: image, table, etc.)":"Batimentos de teclas que podem ser utilizados quando um widget é selecionado (por exemplo: imagem, tabela, etc.)","Light blue":"Azul-claro","Light green":"Verde-claro","Light grey":"Cinzento-claro",MENU_BAR_MENU_EDIT:"Editar",MENU_BAR_MENU_FILE:"Ficheiro",MENU_BAR_MENU_FONT:"Tipo de letra",MENU_BAR_MENU_FORMAT:"Formatação",MENU_BAR_MENU_HELP:"Ajuda",MENU_BAR_MENU_INSERT:"Inserir",MENU_BAR_MENU_TEXT:"Texto",MENU_BAR_MENU_TOOLS:"Ferramentas",MENU_BAR_MENU_VIEW:"Visualizar","Move focus between form fields (inputs, buttons, etc.)":"Mover o foco entre os campos do formulário (entradas, botões, etc.)","Move focus from an editable area back to the parent widget":"Deslocar o foco de uma área editável de volta para o widget principal","Move focus in and out of an active dialog window":"Mover o foco para dentro e para fora de uma janela de diálogo ativa","Move focus to the menu bar, navigate between menu bars":"Mover o foco para a barra de menu, navegar entre as barras de menu","Move focus to the toolbar, navigate between toolbars":"Mover o foco para a barra de ferramentas, navegar entre barras de ferramentas","Move the caret to allow typing directly after a widget":"Mover o ponto de inserção para permitir escrever diretamente após um widget","Move the caret to allow typing directly before a widget":"Mover o ponto de inserção para permitir escrever diretamente antes de um widget","Navigate through the toolbar or menu bar":"Navegar pela barra de ferramentas ou pela barra de menu",Next:"Seguinte","No results found":"Nenhum resultado encontrado","No searchable items":"Nenhum item pesquisável","Open the accessibility help dialog":"Abrir a caixa de diálogo de ajuda de acessibilidade",Orange:"Laranja",Paragraph:"Parágrafo","Paste content":"Colar o conteúdo","Paste content as plain text":"Colar o conteúdo como texto sem formatação",'Please enter a valid color (e.g. "ff0000").':'Introduza uma cor válida (por ex. "ff0000").',"Press %0 for help.":"Para obter ajuda, pressione %0.","Press Enter to type after or press Shift + Enter to type before the widget":"Prima Enter para escrever depois ou Shift + Enter para escrever antes do widget",Previous:"Anterior",Purple:"Roxo",Red:"Vermelho",Redo:"Refazer","Remove color":"Remover cor","Replace image with file manager":"Substituir imagem com o gestor de ficheiros","Replace with file manager":"Substituir com o gestor de ficheiros","Restore default":"Restaurar predefinição","Rich Text Editor":"Editor de texto avançado","Rich Text Editor. Editing area: %0":"Editor de Texto Formatado. Área de edição: %0",Save:"Guardar","Select all":"Selecionar todos","Show more items":"Mostrar mais itens","These keyboard shortcuts allow for quick access to content editing features.":"Estes atalhos de teclado permitem aceder rapidamente às funcionalidades de edição de conteúdo.","Toggle caption off":"Desativar legenda","Toggle caption on":"Ativar legenda",Turquoise:"Turquesa",Undo:"Desfazer","Upload in progress":"Carregamento em progresso","Use the following keystrokes for more efficient navigation in the CKEditor 5 user interface.":"Para navegar de forma mais eficiente pela interface de utilizador do CKEditor 5, utilize os seguintes batimentos de teclas.","User interface and content navigation keystrokes":"Batimentos de teclas para navegar pela interface de utilizador e pelo conteúdo",White:"Branco","Widget toolbar":"Barra de ferramentas do widget","With file manager":"Com o gestor de ficheiros",Yellow:"Amarelo"}),o.getPluralForm=function(e){return 1!=e}}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));