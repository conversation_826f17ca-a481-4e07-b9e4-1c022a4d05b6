!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{"Break text":"Break text","Caption for image: %0":"Caption for image: %0","Caption for the image":"Caption for the image","Centered image":"Centered image","Change image text alternative":"Change image text alternative",Custom:"Custom","Custom image size":"Custom image size","Enter image caption":"Enter image caption","Error during image upload":"Error during image upload","From computer":"From computer","Full size image":"Full size image",Image:"Image","Image from computer":"Image from computer","Image resize list":"Image resize list","Image toolbar":"Image toolbar","Image upload complete":"Image upload complete","Image via URL":"Image via URL","image widget":"image widget","In line":"In line","Insert image":"Insert image","Insert image via URL":"Insert image via URL","Insert via URL":"Insert via URL","Left aligned image":"Left aligned image",Original:"Original","Replace from computer":"Replace from computer","Replace image":"Replace image","Replace image from computer":"Replace image from computer","Resize image":"Resize image","Resize image (in %0)":"Resize image (in %0)","Resize image to %0":"Resize image to %0","Resize image to the original size":"Resize image to the original size","Right aligned image":"Right aligned image","Side image":"Side image","Text alternative":"Text alternative","The value must not be empty.":"The value must not be empty.","The value should be a plain number.":"The value should be a plain number.","Update image URL":"Update image URL","Upload failed":"Upload failed","Upload from computer":"Upload from computer","Upload image from computer":"Upload image from computer","Uploading image":"Uploading image","Via URL":"Via URL","Wrap text":"Wrap text","You have no image upload permissions.":"You have no image upload permissions."})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={501:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var n=i(758),o=i.n(n),r=i(935),a=i.n(r)()(o());a.push([e.id,'.ck-vertical-form .ck-button:after{bottom:-1px;content:"";position:absolute;right:-1px;top:-1px;width:0;z-index:1}.ck-vertical-form .ck-button:focus:after{display:none}@media screen and (max-width:600px){.ck.ck-responsive-form .ck-button:after{bottom:-1px;content:"";position:absolute;right:-1px;top:-1px;width:0;z-index:1}.ck.ck-responsive-form .ck-button:focus:after{display:none}}.ck-vertical-form>.ck-button:nth-last-child(2):after{border-right:1px solid var(--ck-color-base-border)}.ck.ck-responsive-form{padding:var(--ck-spacing-large)}.ck.ck-responsive-form:focus{outline:none}[dir=ltr] .ck.ck-responsive-form>:not(:first-child),[dir=rtl] .ck.ck-responsive-form>:not(:last-child){margin-left:var(--ck-spacing-standard)}@media screen and (max-width:600px){.ck.ck-responsive-form{padding:0;width:calc(var(--ck-input-width)*.8)}.ck.ck-responsive-form .ck-labeled-field-view{margin:var(--ck-spacing-large) var(--ck-spacing-large) 0}.ck.ck-responsive-form .ck-labeled-field-view .ck-input-number,.ck.ck-responsive-form .ck-labeled-field-view .ck-input-text{min-width:0;width:100%}.ck.ck-responsive-form .ck-labeled-field-view .ck-labeled-field-view__error{white-space:normal}.ck.ck-responsive-form>.ck-button:nth-last-child(2):after{border-right:1px solid var(--ck-color-base-border)}.ck.ck-responsive-form>.ck-button:last-child,.ck.ck-responsive-form>.ck-button:nth-last-child(2){border-radius:0;margin-top:var(--ck-spacing-large);padding:var(--ck-spacing-standard)}.ck.ck-responsive-form>.ck-button:last-child:not(:focus),.ck.ck-responsive-form>.ck-button:nth-last-child(2):not(:focus){border-top:1px solid var(--ck-color-base-border)}[dir=ltr] .ck.ck-responsive-form>.ck-button:last-child,[dir=ltr] .ck.ck-responsive-form>.ck-button:nth-last-child(2),[dir=rtl] .ck.ck-responsive-form>.ck-button:last-child,[dir=rtl] .ck.ck-responsive-form>.ck-button:nth-last-child(2){margin-left:0}[dir=rtl] .ck.ck-responsive-form>.ck-button:last-child:last-of-type,[dir=rtl] .ck.ck-responsive-form>.ck-button:nth-last-child(2):last-of-type{border-right:1px solid var(--ck-color-base-border)}}',""]);const s=a},934:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var n=i(758),o=i.n(n),r=i(935),a=i.n(r)()(o());a.push([e.id,".ck-content .image{clear:both;display:table;margin:.9em auto;min-width:50px;text-align:center}.ck-content .image img{display:block;height:auto;margin:0 auto;max-width:100%;min-width:100%}.ck-content .image-inline{align-items:flex-start;display:inline-flex;max-width:100%}.ck-content .image-inline picture{display:flex}.ck-content .image-inline img,.ck-content .image-inline picture{flex-grow:1;flex-shrink:1;max-width:100%}.ck.ck-editor__editable .image>figcaption.ck-placeholder:before{overflow:hidden;padding-left:inherit;padding-right:inherit;text-overflow:ellipsis;white-space:nowrap}.ck.ck-editor__editable .image{z-index:1}.ck.ck-editor__editable .image.ck-widget_selected{z-index:2}.ck.ck-editor__editable .image-inline{z-index:1}.ck.ck-editor__editable .image-inline.ck-widget_selected{z-index:2}.ck.ck-editor__editable .image-inline.ck-widget_selected ::selection{display:none}.ck.ck-editor__editable .image-inline img{height:auto}.ck.ck-editor__editable td .image-inline img,.ck.ck-editor__editable th .image-inline img{max-width:none}",""]);const s=a},406:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var n=i(758),o=i.n(n),r=i(935),a=i.n(r)()(o());a.push([e.id,":root{--ck-color-image-caption-background:#f7f7f7;--ck-color-image-caption-text:#333;--ck-color-image-caption-highlighted-background:#fd0}.ck-content .image>figcaption{background-color:var(--ck-color-image-caption-background);caption-side:bottom;color:var(--ck-color-image-caption-text);display:table-caption;font-size:.75em;outline-offset:-1px;padding:.6em;word-break:break-word}@media (forced-colors:active){.ck-content .image>figcaption{background-color:unset;color:unset}}@media (forced-colors:none){.ck.ck-editor__editable .image>figcaption.image__caption_highlighted{animation:ck-image-caption-highlight .6s ease-out}}@media (prefers-reduced-motion:reduce){.ck.ck-editor__editable .image>figcaption.image__caption_highlighted{animation:none}}@keyframes ck-image-caption-highlight{0%{background-color:var(--ck-color-image-caption-highlighted-background)}to{background-color:var(--ck-color-image-caption-background)}}",""]);const s=a},429:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var n=i(758),o=i.n(n),r=i(935),a=i.n(r)()(o());a.push([e.id,".ck.ck-image-custom-resize-form{align-items:flex-start;display:flex;flex-direction:row;flex-wrap:nowrap}.ck.ck-image-custom-resize-form .ck-labeled-field-view{display:inline-block}.ck.ck-image-custom-resize-form .ck-label{display:none}@media screen and (max-width:600px){.ck.ck-image-custom-resize-form{flex-wrap:wrap}.ck.ck-image-custom-resize-form .ck-labeled-field-view{flex-basis:100%}.ck.ck-image-custom-resize-form .ck-button{flex-basis:50%}}",""]);const s=a},489:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var n=i(758),o=i.n(n),r=i(935),a=i.n(r)()(o());a.push([e.id,".ck.ck-image-insert-url{padding:var(--ck-spacing-large) var(--ck-spacing-large) 0;width:400px}.ck.ck-image-insert-url .ck-image-insert-url__action-row{display:grid;grid-template-columns:repeat(2,1fr)}:root{--ck-image-insert-insert-by-url-width:250px}.ck.ck-image-insert-url{--ck-input-width:100%}.ck.ck-image-insert-url .ck-image-insert-url__action-row{grid-column-gap:var(--ck-spacing-large);margin-top:var(--ck-spacing-large)}.ck.ck-image-insert-url .ck-image-insert-url__action-row .ck-button-cancel,.ck.ck-image-insert-url .ck-image-insert-url__action-row .ck-button-save{justify-content:center;min-width:auto}.ck.ck-image-insert-url .ck-image-insert-url__action-row .ck-button .ck-button__label{color:var(--ck-color-text)}.ck.ck-image-insert-form>.ck.ck-button{display:block;width:100%}[dir=ltr] .ck.ck-image-insert-form>.ck.ck-button{text-align:left}[dir=rtl] .ck.ck-image-insert-form>.ck.ck-button{text-align:right}.ck.ck-image-insert-form>.ck.ck-collapsible{min-width:var(--ck-image-insert-insert-by-url-width)}.ck.ck-image-insert-form>.ck.ck-collapsible:not(:first-child){border-top:1px solid var(--ck-color-base-border)}.ck.ck-image-insert-form>.ck.ck-collapsible:not(:last-child){border-bottom:1px solid var(--ck-color-base-border)}.ck.ck-image-insert-form>.ck.ck-image-insert-url{min-width:var(--ck-image-insert-insert-by-url-width);padding:var(--ck-spacing-large)}.ck.ck-image-insert-form:focus{outline:none}",""]);const s=a},571:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var n=i(758),o=i.n(n),r=i(935),a=i.n(r)()(o());a.push([e.id,".ck.ck-editor__editable img.image_placeholder{background-size:100% 100%}",""]);const s=a},278:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var n=i(758),o=i.n(n),r=i(935),a=i.n(r)()(o());a.push([e.id,".ck-content img.image_resized{height:auto}.ck-content .image.image_resized{box-sizing:border-box;display:block;max-width:100%}.ck-content .image.image_resized img{width:100%}.ck-content .image.image_resized>figcaption{display:block}.ck.ck-editor__editable td .image-inline.image_resized img,.ck.ck-editor__editable th .image-inline.image_resized img{max-width:100%}[dir=ltr] .ck.ck-button.ck-button_with-text.ck-resize-image-button .ck-button__icon{margin-right:var(--ck-spacing-standard)}[dir=rtl] .ck.ck-button.ck-button_with-text.ck-resize-image-button .ck-button__icon{margin-left:var(--ck-spacing-standard)}.ck.ck-dropdown .ck-button.ck-resize-image-button .ck-button__label{width:4em}",""]);const s=a},895:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var n=i(758),o=i.n(n),r=i(935),a=i.n(r)()(o());a.push([e.id,":root{--ck-image-style-spacing:1.5em;--ck-inline-image-style-spacing:calc(var(--ck-image-style-spacing)/2)}.ck-content .image.image-style-block-align-left,.ck-content .image.image-style-block-align-right{max-width:calc(100% - var(--ck-image-style-spacing))}.ck-content .image.image-style-align-left,.ck-content .image.image-style-align-right{clear:none}.ck-content .image.image-style-side{float:right;margin-left:var(--ck-image-style-spacing);max-width:50%}.ck-content .image.image-style-align-left{float:left;margin-right:var(--ck-image-style-spacing)}.ck-content .image.image-style-align-right{float:right;margin-left:var(--ck-image-style-spacing)}.ck-content .image.image-style-block-align-right{margin-left:auto;margin-right:0}.ck-content .image.image-style-block-align-left{margin-left:0;margin-right:auto}.ck-content .image-style-align-center{margin-left:auto;margin-right:auto}.ck-content .image-style-align-left{float:left;margin-right:var(--ck-image-style-spacing)}.ck-content .image-style-align-right{float:right;margin-left:var(--ck-image-style-spacing)}.ck-content p+.image.image-style-align-left,.ck-content p+.image.image-style-align-right,.ck-content p+.image.image-style-side{margin-top:0}.ck-content .image-inline.image-style-align-left,.ck-content .image-inline.image-style-align-right{margin-bottom:var(--ck-inline-image-style-spacing);margin-top:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-left{margin-right:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-right{margin-left:var(--ck-inline-image-style-spacing)}.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__action:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__arrow:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover),.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__action:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__arrow:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover){background-color:var(--ck-color-button-on-background)}.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__action:not(.ck-disabled):after,.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__arrow:not(.ck-disabled):after,.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover):after,.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__action:not(.ck-disabled):after,.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__arrow:not(.ck-disabled):after,.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover):after{display:none}.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open:hover>.ck-splitbutton__action:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open:hover>.ck-splitbutton__arrow:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open:hover>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover){background-color:var(--ck-color-button-on-hover-background)}",""]);const s=a},854:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var n=i(758),o=i.n(n),r=i(935),a=i.n(r)()(o());a.push([e.id,'.ck-image-upload-complete-icon{border-radius:50%;display:block;position:absolute;right:min(var(--ck-spacing-medium),6%);top:min(var(--ck-spacing-medium),6%);z-index:1}.ck-image-upload-complete-icon:after{content:"";position:absolute}:root{--ck-color-image-upload-icon:#fff;--ck-color-image-upload-icon-background:#008a00;--ck-image-upload-icon-size:20;--ck-image-upload-icon-width:2px;--ck-image-upload-icon-is-visible:clamp(0px,100% - 50px,1px)}.ck-image-upload-complete-icon{animation-delay:0s,3s;animation-duration:.5s,.5s;animation-fill-mode:forwards,forwards;animation-name:ck-upload-complete-icon-show,ck-upload-complete-icon-hide;background:var(--ck-color-image-upload-icon-background);font-size:calc(1px*var(--ck-image-upload-icon-size));height:calc(var(--ck-image-upload-icon-is-visible)*var(--ck-image-upload-icon-size));opacity:0;overflow:hidden;width:calc(var(--ck-image-upload-icon-is-visible)*var(--ck-image-upload-icon-size))}.ck-image-upload-complete-icon:after{animation-delay:.5s;animation-duration:.5s;animation-fill-mode:forwards;animation-name:ck-upload-complete-icon-check;border-right:var(--ck-image-upload-icon-width) solid var(--ck-color-image-upload-icon);border-top:var(--ck-image-upload-icon-width) solid var(--ck-color-image-upload-icon);box-sizing:border-box;height:0;left:25%;opacity:0;top:50%;transform:scaleX(-1) rotate(135deg);transform-origin:left top;width:0}@media (prefers-reduced-motion:reduce){.ck-image-upload-complete-icon{animation-duration:0s}.ck-image-upload-complete-icon:after{animation:none;height:.45em;opacity:1;width:.3em}}@keyframes ck-upload-complete-icon-show{0%{opacity:0}to{opacity:1}}@keyframes ck-upload-complete-icon-hide{0%{opacity:1}to{opacity:0}}@keyframes ck-upload-complete-icon-check{0%{height:0;opacity:1;width:0}33%{height:0;width:.3em}to{height:.45em;opacity:1;width:.3em}}',""]);const s=a},424:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var n=i(758),o=i.n(n),r=i(935),a=i.n(r)()(o());a.push([e.id,'.ck .ck-upload-placeholder-loader{align-items:center;display:flex;justify-content:center;left:0;position:absolute;top:0}.ck .ck-upload-placeholder-loader:before{content:"";position:relative}:root{--ck-color-upload-placeholder-loader:#b3b3b3;--ck-upload-placeholder-loader-size:32px;--ck-upload-placeholder-image-aspect-ratio:2.8}.ck .ck-image-upload-placeholder{margin:0;width:100%}.ck .ck-image-upload-placeholder.image-inline{width:calc(var(--ck-upload-placeholder-loader-size)*2*var(--ck-upload-placeholder-image-aspect-ratio))}.ck .ck-image-upload-placeholder img{aspect-ratio:var(--ck-upload-placeholder-image-aspect-ratio)}.ck .ck-upload-placeholder-loader{height:100%;width:100%}.ck .ck-upload-placeholder-loader:before{animation:ck-upload-placeholder-loader 1s linear infinite;border-radius:50%;border-right:2px solid transparent;border-top:3px solid var(--ck-color-upload-placeholder-loader);height:var(--ck-upload-placeholder-loader-size);width:var(--ck-upload-placeholder-loader-size)}@keyframes ck-upload-placeholder-loader{to{transform:rotate(1turn)}}',""]);const s=a},184:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var n=i(758),o=i.n(n),r=i(935),a=i.n(r)()(o());a.push([e.id,".ck.ck-editor__editable .image,.ck.ck-editor__editable .image-inline{position:relative}.ck.ck-editor__editable .image .ck-progress-bar,.ck.ck-editor__editable .image-inline .ck-progress-bar{left:0;position:absolute;top:0}.ck.ck-editor__editable .image-inline.ck-appear,.ck.ck-editor__editable .image.ck-appear{animation:fadeIn .7s}@media (prefers-reduced-motion:reduce){.ck.ck-editor__editable .image-inline.ck-appear,.ck.ck-editor__editable .image.ck-appear{animation:none;opacity:1}}.ck.ck-editor__editable .image .ck-progress-bar,.ck.ck-editor__editable .image-inline .ck-progress-bar{background:var(--ck-color-upload-bar-background);height:2px;transition:width .1s;width:0}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}",""]);const s=a},285:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var n=i(758),o=i.n(n),r=i(935),a=i.n(r)()(o());a.push([e.id,".ck.ck-text-alternative-form{display:flex;flex-direction:row;flex-wrap:nowrap}.ck.ck-text-alternative-form .ck-labeled-field-view{display:inline-block}.ck.ck-text-alternative-form .ck-label{display:none}@media screen and (max-width:600px){.ck.ck-text-alternative-form{flex-wrap:wrap}.ck.ck-text-alternative-form .ck-labeled-field-view{flex-basis:100%}.ck.ck-text-alternative-form .ck-button{flex-basis:50%}}",""]);const s=a},935:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var i="",n=void 0!==t[5];return t[4]&&(i+="@supports (".concat(t[4],") {")),t[2]&&(i+="@media ".concat(t[2]," {")),n&&(i+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),i+=e(t),n&&(i+="}"),t[2]&&(i+="}"),t[4]&&(i+="}"),i})).join("")},t.i=function(e,i,n,o,r){"string"==typeof e&&(e=[[null,e,void 0]]);var a={};if(n)for(var s=0;s<this.length;s++){var l=this[s][0];null!=l&&(a[l]=!0)}for(var c=0;c<e.length;c++){var u=[].concat(e[c]);n&&a[u[0]]||(void 0!==r&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=r),i&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=i):u[2]=i),o&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=o):u[4]="".concat(o)),t.push(u))}},t}},758:e=>{"use strict";e.exports=function(e){return e[1]}},591:e=>{"use strict";var t=[];function i(e){for(var i=-1,n=0;n<t.length;n++)if(t[n].identifier===e){i=n;break}return i}function n(e,n){for(var r={},a=[],s=0;s<e.length;s++){var l=e[s],c=n.base?l[0]+n.base:l[0],u=r[c]||0,g="".concat(c," ").concat(u);r[c]=u+1;var m=i(g),d={css:l[1],media:l[2],sourceMap:l[3],supports:l[4],layer:l[5]};if(-1!==m)t[m].references++,t[m].updater(d);else{var p=o(d,n);n.byIndex=s,t.splice(s,0,{identifier:g,updater:p,references:1})}a.push(g)}return a}function o(e,t){var i=t.domAPI(t);i.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;i.update(e=t)}else i.remove()}}e.exports=function(e,o){var r=n(e=e||[],o=o||{});return function(e){e=e||[];for(var a=0;a<r.length;a++){var s=i(r[a]);t[s].references--}for(var l=n(e,o),c=0;c<r.length;c++){var u=i(r[c]);0===t[u].references&&(t[u].updater(),t.splice(u,1))}r=l}}},128:e=>{"use strict";var t={};e.exports=function(e,i){var n=function(e){if(void 0===t[e]){var i=document.querySelector(e);if(window.HTMLIFrameElement&&i instanceof window.HTMLIFrameElement)try{i=i.contentDocument.head}catch(e){i=null}t[e]=i}return t[e]}(e);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(i)}},51:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},21:e=>{"use strict";e.exports=function(e,t){Object.keys(t).forEach((function(i){e.setAttribute(i,t[i])}))}},639:e=>{"use strict";var t,i=(t=[],function(e,i){return t[e]=i,t.filter(Boolean).join("\n")});function n(e,t,n,o){var r;if(n)r="";else{r="",o.supports&&(r+="@supports (".concat(o.supports,") {")),o.media&&(r+="@media ".concat(o.media," {"));var a=void 0!==o.layer;a&&(r+="@layer".concat(o.layer.length>0?" ".concat(o.layer):""," {")),r+=o.css,a&&(r+="}"),o.media&&(r+="}"),o.supports&&(r+="}")}if(e.styleSheet)e.styleSheet.cssText=i(t,r);else{var s=document.createTextNode(r),l=e.childNodes;l[t]&&e.removeChild(l[t]),l.length?e.insertBefore(s,l[t]):e.appendChild(s)}}var o={singleton:null,singletonCounter:0};e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=o.singletonCounter++,i=o.singleton||(o.singleton=e.insertStyleElement(e));return{update:function(e){n(i,t,!1,e)},remove:function(e){n(i,t,!0,e)}}}},331:(e,t,i)=>{e.exports=i(237)("./src/clipboard.js")},782:(e,t,i)=>{e.exports=i(237)("./src/core.js")},783:(e,t,i)=>{e.exports=i(237)("./src/engine.js")},834:(e,t,i)=>{e.exports=i(237)("./src/typing.js")},311:(e,t,i)=>{e.exports=i(237)("./src/ui.js")},251:(e,t,i)=>{e.exports=i(237)("./src/undo.js")},260:(e,t,i)=>{e.exports=i(237)("./src/upload.js")},584:(e,t,i)=>{e.exports=i(237)("./src/utils.js")},901:(e,t,i)=>{e.exports=i(237)("./src/widget.js")},237:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function i(n){var o=t[n];if(void 0!==o)return o.exports;var r=t[n]={id:n,exports:{}};return e[n](r,r.exports,i),r.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";i.r(n),i.d(n,{AutoImage:()=>k,Image:()=>se,ImageBlock:()=>oe,ImageBlockEditing:()=>Q,ImageCaption:()=>pe,ImageCaptionEditing:()=>ue,ImageCaptionUI:()=>ge,ImageCaptionUtils:()=>le,ImageCustomResizeUI:()=>yo,ImageEditing:()=>q,ImageInline:()=>ae,ImageInsert:()=>Ne,ImageInsertUI:()=>te,ImageInsertViaUrl:()=>Fe,ImageResize:()=>Eo,ImageResizeButtons:()=>uo,ImageResizeEditing:()=>lo,ImageResizeHandles:()=>fo,ImageSizeAttributes:()=>H,ImageStyle:()=>Do,ImageStyleEditing:()=>Oo,ImageStyleUI:()=>jo,ImageTextAlternative:()=>N,ImageTextAlternativeEditing:()=>w,ImageTextAlternativeUI:()=>F,ImageToolbar:()=>Mo,ImageUpload:()=>Pe,ImageUploadEditing:()=>Ue,ImageUploadProgress:()=>Ee,ImageUploadUI:()=>ke,ImageUtils:()=>h,PictureEditing:()=>Wo,createImageTypeRegExp:()=>he});var e=i(782),t=i(331),o=i(783),r=i(251),a=i(834),s=i(584),l=i(901);function c(e){return e.createContainerElement("figure",{class:"image"},[e.createEmptyElement("img"),e.createSlot("children")])}function u(e,t){const i=e.plugins.get("ImageUtils"),n=e.plugins.has("ImageInlineEditing")&&e.plugins.has("ImageBlockEditing");return e=>{if(!i.isInlineImageView(e))return null;if(!n)return o(e);return("block"==e.getStyle("display")||e.findAncestor(i.isBlockImageView)?"imageBlock":"imageInline")!==t?null:o(e)};function o(e){const t={name:!0};return e.hasAttribute("src")&&(t.attributes=["src"]),t}}function g(e,t){const i=(0,s.first)(t.getSelectedBlocks());return!i||e.isObject(i)||i.isEmpty&&"listItem"!=i.name?"imageBlock":"imageInline"}function m(e){return e&&e.endsWith("px")?parseInt(e):null}function d(e){const t=m(e.getStyle("width")),i=m(e.getStyle("height"));return!(!t||!i)}const p=/^(image|image-inline)$/;class h extends e.Plugin{constructor(){super(...arguments),this._domEmitter=new((0,s.DomEmitterMixin)())}static get pluginName(){return"ImageUtils"}static get isOfficialPlugin(){return!0}isImage(e){return this.isInlineImage(e)||this.isBlockImage(e)}isInlineImageView(e){return!!e&&e.is("element","img")}isBlockImageView(e){return!!e&&e.is("element","figure")&&e.hasClass("image")}insertImage(e={},t=null,i=null,n={}){const o=this.editor,r=o.model,a=r.document.selection,s=f(o,t||a,i);e={...Object.fromEntries(a.getAttributes()),...e};for(const t in e)r.schema.checkAttribute(s,t)||delete e[t];return r.change((i=>{const{setImageSizes:o=!0}=n,a=i.createElement(s,e);return r.insertObject(a,t,null,{setSelection:"on",findOptimalPosition:t||"imageInline"==s?void 0:"auto"}),a.parent?(o&&this.setImageNaturalSizeAttributes(a),a):null}))}setImageNaturalSizeAttributes(e){const t=e.getAttribute("src");t&&(e.getAttribute("width")||e.getAttribute("height")||this.editor.model.change((i=>{const n=new s.global.window.Image;this._domEmitter.listenTo(n,"load",(()=>{e.getAttribute("width")||e.getAttribute("height")||this.editor.model.enqueueChange(i.batch,(t=>{t.setAttribute("width",n.naturalWidth,e),t.setAttribute("height",n.naturalHeight,e)})),this._domEmitter.stopListening(n,"load")})),n.src=t})))}getClosestSelectedImageWidget(e){const t=e.getFirstPosition();if(!t)return null;const i=e.getSelectedElement();if(i&&this.isImageWidget(i))return i;let n=t.parent;for(;n;){if(n.is("element")&&this.isImageWidget(n))return n;n=n.parent}return null}getClosestSelectedImageElement(e){const t=e.getSelectedElement();return this.isImage(t)?t:e.getFirstPosition().findAncestor("imageBlock")}getImageWidgetFromImageView(e){return e.findAncestor({classes:p})}isImageAllowed(){const e=this.editor.model.document.selection;return function(e,t){const i=f(e,t,null);if("imageBlock"==i){const i=function(e,t){const i=(0,l.findOptimalInsertionRange)(e,t),n=i.start.parent;if(n.isEmpty&&!n.is("element","$root"))return n.parent;return n}(t,e.model);if(e.model.schema.checkChild(i,"imageBlock"))return!0}else if(e.model.schema.checkChild(t.focus,"imageInline"))return!0;return!1}(this.editor,e)&&function(e){return[...e.focus.getAncestors()].every((e=>!e.is("element","imageBlock")))}(e)}toImageWidget(e,t,i){t.setCustomProperty("image",!0,e);return(0,l.toWidget)(e,t,{label:()=>{const t=this.findViewImgElement(e).getAttribute("alt");return t?`${t} ${i}`:i}})}isImageWidget(e){return!!e.getCustomProperty("image")&&(0,l.isWidget)(e)}isBlockImage(e){return!!e&&e.is("element","imageBlock")}isInlineImage(e){return!!e&&e.is("element","imageInline")}findViewImgElement(e){if(this.isInlineImageView(e))return e;const t=this.editor.editing.view;for(const{item:i}of t.createRangeIn(e))if(this.isInlineImageView(i))return i}destroy(){return this._domEmitter.stopListening(),super.destroy()}}function f(e,t,i){const n=e.model.schema,o=e.config.get("image.insert.type");return e.plugins.has("ImageBlockEditing")?e.plugins.has("ImageInlineEditing")?i||("inline"===o?"imageInline":"auto"!==o?"imageBlock":t.is("selection")?g(n,t):n.checkChild(t,"imageInline")?"imageInline":"imageBlock"):"imageBlock":"imageInline"}const b=new RegExp(String(/^(http(s)?:\/\/)?[\w-]+\.[\w.~:/[\]@!$&'()*+,;=%-]+/.source+/\.(jpg|jpeg|png|gif|ico|webp|JPG|JPEG|PNG|GIF|ICO|WEBP)/.source+/(\?[\w.~:/[\]@!$&'()*+,;=%-]*)?/.source+/(#[\w.~:/[\]@!$&'()*+,;=%-]*)?$/.source));class k extends e.Plugin{static get requires(){return[t.Clipboard,h,r.Undo,a.Delete]}static get pluginName(){return"AutoImage"}static get isOfficialPlugin(){return!0}constructor(e){super(e),this._timeoutId=null,this._positionToInsert=null}init(){const e=this.editor,t=e.model.document,i=e.plugins.get("ClipboardPipeline");this.listenTo(i,"inputTransformation",(()=>{const e=t.selection.getFirstRange(),i=o.LivePosition.fromPosition(e.start);i.stickiness="toPrevious";const n=o.LivePosition.fromPosition(e.end);n.stickiness="toNext",t.once("change:data",(()=>{this._embedImageBetweenPositions(i,n),i.detach(),n.detach()}),{priority:"high"})})),e.commands.get("undo").on("execute",(()=>{this._timeoutId&&(s.global.window.clearTimeout(this._timeoutId),this._positionToInsert.detach(),this._timeoutId=null,this._positionToInsert=null)}),{priority:"high"})}_embedImageBetweenPositions(e,t){const i=this.editor,n=new o.LiveRange(e,t),r=n.getWalker({ignoreElementEnd:!0}),a=Object.fromEntries(i.model.document.selection.getAttributes()),s=this.editor.plugins.get("ImageUtils");let l="";for(const e of r)e.item.is("$textProxy")&&(l+=e.item.data);l=l.trim(),l.match(b)?(this._positionToInsert=o.LivePosition.fromPosition(e),this._timeoutId=setTimeout((()=>{if(!i.commands.get("insertImage").isEnabled)return void n.detach();i.model.change((e=>{let t;this._timeoutId=null,e.remove(n),n.detach(),"$graveyard"!==this._positionToInsert.root.rootName&&(t=this._positionToInsert.toPosition()),s.insertImage({...a,src:l},t),this._positionToInsert.detach(),this._positionToInsert=null}));i.plugins.get("Delete").requestUndoOnBackspace()}),100)):n.detach()}}class v extends e.Command{refresh(){const e=this.editor.plugins.get("ImageUtils").getClosestSelectedImageElement(this.editor.model.document.selection);this.isEnabled=!!e,this.isEnabled&&e.hasAttribute("alt")?this.value=e.getAttribute("alt"):this.value=!1}execute(e){const t=this.editor,i=t.plugins.get("ImageUtils"),n=t.model,o=i.getClosestSelectedImageElement(n.document.selection);n.change((t=>{t.setAttribute("alt",e.newValue,o)}))}}class w extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImageTextAlternativeEditing"}static get isOfficialPlugin(){return!0}init(){this.editor.commands.add("imageTextAlternative",new v(this.editor))}}var I=i(311),_=i(591),y=i.n(_),A=i(639),x=i.n(A),E=i(128),C=i.n(E),S=i(21),B=i.n(S),V=i(51),T=i.n(V),z=i(285),U={attributes:{"data-cke":!0}};U.setAttributes=B(),U.insert=C().bind(null,"head"),U.domAPI=x(),U.insertStyleElement=T();y()(z.A,U);z.A&&z.A.locals&&z.A.locals;var O=i(501),P={attributes:{"data-cke":!0}};P.setAttributes=B(),P.insert=C().bind(null,"head"),P.domAPI=x(),P.insertStyleElement=T();y()(O.A,P);O.A&&O.A.locals&&O.A.locals;class R extends I.View{constructor(t){super(t);const i=this.locale.t;this.focusTracker=new s.FocusTracker,this.keystrokes=new s.KeystrokeHandler,this.labeledInput=this._createLabeledInputView(),this.saveButtonView=this._createButton(i("Save"),e.icons.check,"ck-button-save"),this.saveButtonView.type="submit",this.cancelButtonView=this._createButton(i("Cancel"),e.icons.cancel,"ck-button-cancel","cancel"),this._focusables=new I.ViewCollection,this._focusCycler=new I.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.setTemplate({tag:"form",attributes:{class:["ck","ck-text-alternative-form","ck-responsive-form"],tabindex:"-1"},children:[this.labeledInput,this.saveButtonView,this.cancelButtonView]})}render(){super.render(),this.keystrokes.listenTo(this.element),(0,I.submitHandler)({view:this}),[this.labeledInput,this.saveButtonView,this.cancelButtonView].forEach((e=>{this._focusables.add(e),this.focusTracker.add(e.element)}))}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}_createButton(e,t,i,n){const o=new I.ButtonView(this.locale);return o.set({label:e,icon:t,tooltip:!0}),o.extendTemplate({attributes:{class:i}}),n&&o.delegate("execute").to(this,n),o}_createLabeledInputView(){const e=this.locale.t,t=new I.LabeledFieldView(this.locale,I.createLabeledInputText);return t.label=e("Text alternative"),t}}function j(e){const t=e.editing.view,i=I.BalloonPanelView.defaultPositions,n=e.plugins.get("ImageUtils");return{target:t.domConverter.mapViewToDom(n.getClosestSelectedImageWidget(t.document.selection)),positions:[i.northArrowSouth,i.northArrowSouthWest,i.northArrowSouthEast,i.southArrowNorth,i.southArrowNorthWest,i.southArrowNorthEast,i.viewportStickyNorth]}}class F extends e.Plugin{static get requires(){return[I.ContextualBalloon]}static get pluginName(){return"ImageTextAlternativeUI"}static get isOfficialPlugin(){return!0}init(){this._createButton()}destroy(){super.destroy(),this._form&&this._form.destroy()}_createButton(){const t=this.editor,i=t.t;t.ui.componentFactory.add("imageTextAlternative",(n=>{const o=t.commands.get("imageTextAlternative"),r=new I.ButtonView(n);return r.set({label:i("Change image text alternative"),icon:e.icons.textAlternative,tooltip:!0}),r.bind("isEnabled").to(o,"isEnabled"),r.bind("isOn").to(o,"value",(e=>!!e)),this.listenTo(r,"execute",(()=>{this._showForm()})),r}))}_createForm(){const e=this.editor,t=e.editing.view.document,i=e.plugins.get("ImageUtils");this._balloon=this.editor.plugins.get("ContextualBalloon"),this._form=new((0,I.CssTransitionDisablerMixin)(R))(e.locale),this._form.render(),this.listenTo(this._form,"submit",(()=>{e.execute("imageTextAlternative",{newValue:this._form.labeledInput.fieldView.element.value}),this._hideForm(!0)})),this.listenTo(this._form,"cancel",(()=>{this._hideForm(!0)})),this._form.keystrokes.set("Esc",((e,t)=>{this._hideForm(!0),t()})),this.listenTo(e.ui,"update",(()=>{i.getClosestSelectedImageWidget(t.selection)?this._isVisible&&function(e){const t=e.plugins.get("ContextualBalloon");if(e.plugins.get("ImageUtils").getClosestSelectedImageWidget(e.editing.view.document.selection)){const i=j(e);t.updatePosition(i)}}(e):this._hideForm(!0)})),(0,I.clickOutsideHandler)({emitter:this._form,activator:()=>this._isVisible,contextElements:()=>[this._balloon.view.element],callback:()=>this._hideForm()})}_showForm(){if(this._isVisible)return;this._form||this._createForm();const e=this.editor,t=e.commands.get("imageTextAlternative"),i=this._form.labeledInput;this._form.disableCssTransitions(),this._isInBalloon||this._balloon.add({view:this._form,position:j(e)}),i.fieldView.value=i.fieldView.element.value=t.value||"",this._form.labeledInput.fieldView.select(),this._form.enableCssTransitions()}_hideForm(e=!1){this._isInBalloon&&(this._form.focusTracker.isFocused&&this._form.saveButtonView.focus(),this._balloon.remove(this._form),e&&this.editor.editing.view.focus())}get _isVisible(){return!!this._balloon&&this._balloon.visibleView===this._form}get _isInBalloon(){return!!this._balloon&&this._balloon.hasView(this._form)}}class N extends e.Plugin{static get requires(){return[w,F]}static get pluginName(){return"ImageTextAlternative"}static get isOfficialPlugin(){return!0}}function L(e,t){const i=(t,i,n)=>{if(!n.consumable.consume(i.item,t.name))return;const o=n.writer,r=n.mapper.toViewElement(i.item),a=e.findViewImgElement(r);null===i.attributeNewValue?(o.removeAttribute("srcset",a),o.removeAttribute("sizes",a)):i.attributeNewValue&&(o.setAttribute("srcset",i.attributeNewValue,a),o.setAttribute("sizes","100vw",a))};return e=>{e.on(`attribute:srcset:${t}`,i)}}function D(e,t,i){const n=(t,i,n)=>{if(!n.consumable.consume(i.item,t.name))return;const o=n.writer,r=n.mapper.toViewElement(i.item),a=e.findViewImgElement(r);o.setAttribute(i.attributeKey,i.attributeNewValue||"",a)};return e=>{e.on(`attribute:${i}:${t}`,n)}}class M extends o.Observer{observe(e){this.listenTo(e,"load",((e,t)=>{const i=t.target;this.checkShouldIgnoreEventFromTarget(i)||"IMG"==i.tagName&&this._fireEvents(t)}),{useCapture:!0})}stopObserving(e){this.stopListening(e)}_fireEvents(e){this.isEnabled&&(this.document.fire("layoutChanged"),this.document.fire("imageLoaded",e))}}class W extends e.Command{constructor(e){super(e);const t=e.config.get("image.insert.type");e.plugins.has("ImageBlockEditing")||"block"===t&&(0,s.logWarning)("image-block-plugin-required"),e.plugins.has("ImageInlineEditing")||"inline"===t&&(0,s.logWarning)("image-inline-plugin-required")}refresh(){const e=this.editor.plugins.get("ImageUtils");this.isEnabled=e.isImageAllowed()}execute(e){const t=(0,s.toArray)(e.source),i=this.editor.model.document.selection,n=this.editor.plugins.get("ImageUtils"),o=Object.fromEntries(i.getAttributes());t.forEach(((t,r)=>{const a=i.getSelectedElement();if("string"==typeof t&&(t={src:t}),r&&a&&n.isImage(a)){const i=this.editor.model.createPositionAfter(a);n.insertImage({...t,...o},i,e.imageType)}else n.insertImage({...t,...o},null,e.imageType)}))}}class $ extends e.Command{constructor(e){super(e),this.decorate("cleanupImage")}refresh(){const e=this.editor.plugins.get("ImageUtils"),t=this.editor.model.document.selection.getSelectedElement();this.isEnabled=e.isImage(t),this.value=this.isEnabled?t.getAttribute("src"):null}execute(e){const t=this.editor.model.document.selection.getSelectedElement(),i=this.editor.plugins.get("ImageUtils");this.editor.model.change((n=>{n.setAttribute("src",e.source,t),this.cleanupImage(n,t),i.setImageNaturalSizeAttributes(t)}))}cleanupImage(e,t){e.removeAttribute("srcset",t),e.removeAttribute("sizes",t),e.removeAttribute("sources",t),e.removeAttribute("width",t),e.removeAttribute("height",t),e.removeAttribute("alt",t)}}class q extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImageEditing"}static get isOfficialPlugin(){return!0}init(){const e=this.editor,t=e.conversion;e.editing.view.addObserver(M),t.for("upcast").attributeToAttribute({view:{name:"img",key:"alt"},model:"alt"}).attributeToAttribute({view:{name:"img",key:"srcset"},model:"srcset"});const i=new W(e),n=new $(e);e.commands.add("insertImage",i),e.commands.add("replaceImageSource",n),e.commands.add("imageInsert",i)}}class H extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImageSizeAttributes"}static get isOfficialPlugin(){return!0}afterInit(){this._registerSchema(),this._registerConverters("imageBlock"),this._registerConverters("imageInline")}_registerSchema(){this.editor.plugins.has("ImageBlockEditing")&&this.editor.model.schema.extend("imageBlock",{allowAttributes:["width","height"]}),this.editor.plugins.has("ImageInlineEditing")&&this.editor.model.schema.extend("imageInline",{allowAttributes:["width","height"]})}_registerConverters(e){const t=this.editor,i=t.plugins.get("ImageUtils"),n="imageBlock"===e?"figure":"img";function o(t,n,o,r){t.on(`attribute:${n}:${e}`,((t,n,a)=>{if(!a.consumable.consume(n.item,t.name))return;const s=a.writer,l=a.mapper.toViewElement(n.item),c=i.findViewImgElement(l);if(null!==n.attributeNewValue?s.setAttribute(o,n.attributeNewValue,c):s.removeAttribute(o,c),n.item.hasAttribute("sources"))return;const u=n.item.hasAttribute("resizedWidth");if("imageInline"===e&&!u&&!r)return;const g=n.item.getAttribute("width"),m=n.item.getAttribute("height");g&&m&&s.setStyle("aspect-ratio",`${g}/${m}`,c)}))}t.conversion.for("upcast").attributeToAttribute({view:{name:n,styles:{width:/.+/}},model:{key:"width",value:e=>d(e)?m(e.getStyle("width")):null}}).attributeToAttribute({view:{name:n,key:"width"},model:"width"}).attributeToAttribute({view:{name:n,styles:{height:/.+/}},model:{key:"height",value:e=>d(e)?m(e.getStyle("height")):null}}).attributeToAttribute({view:{name:n,key:"height"},model:"height"}),t.conversion.for("editingDowncast").add((e=>{o(e,"width","width",!0),o(e,"height","height",!0)})),t.conversion.for("dataDowncast").add((e=>{o(e,"width","width",!1),o(e,"height","height",!1)}))}}class K extends e.Command{constructor(e,t){super(e),this._modelElementName=t}refresh(){const e=this.editor.plugins.get("ImageUtils"),t=e.getClosestSelectedImageElement(this.editor.model.document.selection);"imageBlock"===this._modelElementName?this.isEnabled=e.isInlineImage(t):this.isEnabled=e.isBlockImage(t)}execute(e={}){const t=this.editor,i=this.editor.model,n=t.plugins.get("ImageUtils"),o=n.getClosestSelectedImageElement(i.document.selection),r=Object.fromEntries(o.getAttributes());return r.src||r.uploadId?i.change((t=>{const{setImageSizes:a=!0}=e,s=Array.from(i.markers).filter((e=>e.getRange().containsItem(o))),l=n.insertImage(r,i.createSelection(o,"on"),this._modelElementName,{setImageSizes:a});if(!l)return null;const c=t.createRangeOn(l);for(const e of s){const i=e.getRange(),n="$graveyard"!=i.root.rootName?i.getJoined(c,!0):c;t.updateMarker(e,{range:n})}return{oldElement:o,newElement:l}})):null}}var G=i(571),J={attributes:{"data-cke":!0}};J.setAttributes=B(),J.insert=C().bind(null,"head"),J.domAPI=x(),J.insertStyleElement=T();y()(G.A,J);G.A&&G.A.locals&&G.A.locals;class Y extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImagePlaceholder"}static get isOfficialPlugin(){return!0}afterInit(){this._setupSchema(),this._setupConversion(),this._setupLoadListener()}_setupSchema(){const e=this.editor.model.schema;e.isRegistered("imageBlock")&&e.extend("imageBlock",{allowAttributes:["placeholder"]}),e.isRegistered("imageInline")&&e.extend("imageInline",{allowAttributes:["placeholder"]})}_setupConversion(){const e=this.editor,t=e.conversion,i=e.plugins.get("ImageUtils");t.for("editingDowncast").add((e=>{e.on("attribute:placeholder",((e,t,n)=>{if(!n.consumable.test(t.item,e.name))return;if(!t.item.is("element","imageBlock")&&!t.item.is("element","imageInline"))return;n.consumable.consume(t.item,e.name);const o=n.writer,r=n.mapper.toViewElement(t.item),a=i.findViewImgElement(r);t.attributeNewValue?(o.addClass("image_placeholder",a),o.setStyle("background-image",`url(${t.attributeNewValue})`,a),o.setCustomProperty("editingPipeline:doNotReuseOnce",!0,a)):(o.removeClass("image_placeholder",a),o.removeStyle("background-image",a))}))}))}_setupLoadListener(){const e=this.editor,t=e.model,i=e.editing,n=i.view,o=e.plugins.get("ImageUtils");n.addObserver(M),this.listenTo(n.document,"imageLoaded",((e,r)=>{const a=n.domConverter.mapDomToView(r.target);if(!a)return;const s=o.getImageWidgetFromImageView(a);if(!s)return;const l=i.mapper.toModelElement(s);l&&l.hasAttribute("placeholder")&&t.enqueueChange({isUndoable:!1},(e=>{e.removeAttribute("placeholder",l)}))}))}}class Q extends e.Plugin{static get requires(){return[q,H,h,Y,t.ClipboardPipeline]}static get pluginName(){return"ImageBlockEditing"}static get isOfficialPlugin(){return!0}init(){const e=this.editor;e.model.schema.register("imageBlock",{inheritAllFrom:"$blockObject",allowAttributes:["alt","src","srcset"]}),this._setupConversion(),e.plugins.has("ImageInlineEditing")&&(e.commands.add("imageTypeBlock",new K(this.editor,"imageBlock")),this._setupClipboardIntegration())}_setupConversion(){const e=this.editor,t=e.t,i=e.conversion,n=e.plugins.get("ImageUtils");i.for("dataDowncast").elementToStructure({model:"imageBlock",view:(e,{writer:t})=>c(t)}),i.for("editingDowncast").elementToStructure({model:"imageBlock",view:(e,{writer:i})=>n.toImageWidget(c(i),i,t("image widget"))}),i.for("downcast").add(D(n,"imageBlock","src")).add(D(n,"imageBlock","alt")).add(L(n,"imageBlock")),i.for("upcast").elementToElement({view:u(e,"imageBlock"),model:(e,{writer:t})=>t.createElement("imageBlock",e.hasAttribute("src")?{src:e.getAttribute("src")}:void 0)}).add(function(e){const t=(t,i,n)=>{if(!n.consumable.test(i.viewItem,{name:!0,classes:"image"}))return;const o=e.findViewImgElement(i.viewItem);if(!o||!n.consumable.test(o,{name:!0}))return;n.consumable.consume(i.viewItem,{name:!0,classes:"image"});const r=n.convertItem(o,i.modelCursor),a=(0,s.first)(r.modelRange.getItems());a?(n.convertChildren(i.viewItem,a),n.updateConversionResult(a,i)):n.consumable.revert(i.viewItem,{name:!0,classes:"image"})};return e=>{e.on("element:figure",t)}}(n))}_setupClipboardIntegration(){const e=this.editor,t=e.model,i=e.editing.view,n=e.plugins.get("ImageUtils"),r=e.plugins.get("ClipboardPipeline");this.listenTo(r,"inputTransformation",((r,a)=>{const s=Array.from(a.content.getChildren());let l;if(!s.every(n.isInlineImageView))return;l=a.targetRanges?e.editing.mapper.toModelRange(a.targetRanges[0]):t.document.selection.getFirstRange();const c=t.createSelection(l);if("imageBlock"===g(t.schema,c)){const e=new o.UpcastWriter(i.document),t=s.map((t=>e.createElement("figure",{class:"image"},t)));a.content=e.createDocumentFragment(t)}})),this.listenTo(r,"contentInsertion",((e,i)=>{"paste"===i.method&&t.change((e=>{const t=e.createRangeIn(i.content);for(const e of t.getItems())e.is("element","imageBlock")&&n.setImageNaturalSizeAttributes(e)}))}))}}var X=i(489),Z={attributes:{"data-cke":!0}};Z.setAttributes=B(),Z.insert=C().bind(null,"head"),Z.domAPI=x(),Z.insertStyleElement=T();y()(X.A,Z);X.A&&X.A.locals&&X.A.locals;class ee extends I.View{constructor(e,t=[]){super(e),this.focusTracker=new s.FocusTracker,this.keystrokes=new s.KeystrokeHandler,this._focusables=new I.ViewCollection,this.children=this.createCollection(),this._focusCycler=new I.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}});for(const e of t)this.children.add(e),this._focusables.add(e),e instanceof I.CollapsibleView&&this._focusables.addMany(e.children);this.setTemplate({tag:"form",attributes:{class:["ck","ck-image-insert-form"],tabindex:-1},children:this.children})}render(){super.render(),(0,I.submitHandler)({view:this});for(const e of this._focusables)this.focusTracker.add(e.element);this.keystrokes.listenTo(this.element);const e=e=>e.stopPropagation();this.keystrokes.set("arrowright",e),this.keystrokes.set("arrowleft",e),this.keystrokes.set("arrowup",e),this.keystrokes.set("arrowdown",e)}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}focus(){this._focusCycler.focusFirst()}}class te extends e.Plugin{static get pluginName(){return"ImageInsertUI"}static get isOfficialPlugin(){return!0}static get requires(){return[h]}constructor(e){super(e),this._integrations=new Map,e.config.define("image.insert.integrations",["upload","assetManager","url"])}init(){const e=this.editor,t=e.model.document.selection,i=e.plugins.get("ImageUtils");this.set("isImageSelected",!1),this.listenTo(e.model.document,"change",(()=>{this.isImageSelected=i.isImage(t.getSelectedElement())}));const n=e=>this._createToolbarComponent(e);e.ui.componentFactory.add("insertImage",n),e.ui.componentFactory.add("imageInsert",n),e.ui.componentFactory.add("menuBar:insertImage",(e=>this._createMenuBarComponent(e)))}registerIntegration({name:e,observable:t,buttonViewCreator:i,formViewCreator:n,menuBarButtonViewCreator:o,requiresForm:r=!1}){this._integrations.has(e)&&(0,s.logWarning)("image-insert-integration-exists",{name:e}),this._integrations.set(e,{observable:t,buttonViewCreator:i,menuBarButtonViewCreator:o,formViewCreator:n,requiresForm:r})}_createToolbarComponent(e){const t=this.editor,i=e.t,n=this._prepareIntegrations();if(!n.length)return null;let o;const r=n[0];if(1==n.length){if(!r.requiresForm)return r.buttonViewCreator(!0);o=r.buttonViewCreator(!0)}else{const t=r.buttonViewCreator(!1);o=new I.SplitButtonView(e,t),o.tooltip=!0,o.bind("label").to(this,"isImageSelected",(e=>i(e?"Replace image":"Insert image")))}const a=this.dropdownView=(0,I.createDropdown)(e,o),s=n.map((({observable:e})=>"function"==typeof e?e():e));return a.bind("isEnabled").toMany(s,"isEnabled",((...e)=>e.some((e=>e)))),a.once("change:isOpen",(()=>{const e=n.map((({formViewCreator:e})=>e(1==n.length))),i=new ee(t.locale,e);a.panelView.children.add(i)})),a}_createMenuBarComponent(t){const i=t.t,n=this._prepareIntegrations();if(!n.length)return null;let o;const r=n[0];if(1==n.length)o=r.menuBarButtonViewCreator(!0);else{o=new I.MenuBarMenuView(t);const r=new I.MenuBarMenuListView(t);o.panelView.children.add(r),o.buttonView.set({icon:e.icons.image,label:i("Image")});for(const e of n){const i=new I.MenuBarMenuListItemView(t,o),n=e.menuBarButtonViewCreator(!1);i.children.add(n),r.items.add(i)}}return o}_prepareIntegrations(){const e=this.editor.config.get("image.insert.integrations"),t=[];if(!e.length)return(0,s.logWarning)("image-insert-integrations-not-specified"),t;for(const i of e)this._integrations.has(i)?t.push(this._integrations.get(i)):["upload","assetManager","url"].includes(i)||(0,s.logWarning)("image-insert-unknown-integration",{item:i});return t.length||(0,s.logWarning)("image-insert-integrations-not-registered"),t}}var ie=i(934),ne={attributes:{"data-cke":!0}};ne.setAttributes=B(),ne.insert=C().bind(null,"head"),ne.domAPI=x(),ne.insertStyleElement=T();y()(ie.A,ne);ie.A&&ie.A.locals&&ie.A.locals;class oe extends e.Plugin{static get requires(){return[Q,l.Widget,N,te]}static get pluginName(){return"ImageBlock"}static get isOfficialPlugin(){return!0}}class re extends e.Plugin{static get requires(){return[q,H,h,Y,t.ClipboardPipeline]}static get pluginName(){return"ImageInlineEditing"}static get isOfficialPlugin(){return!0}init(){const e=this.editor;e.model.schema.register("imageInline",{inheritAllFrom:"$inlineObject",allowAttributes:["alt","src","srcset"],disallowIn:["caption"]}),this._setupConversion(),e.plugins.has("ImageBlockEditing")&&(e.commands.add("imageTypeInline",new K(this.editor,"imageInline")),this._setupClipboardIntegration())}_setupConversion(){const e=this.editor,t=e.t,i=e.conversion,n=e.plugins.get("ImageUtils");i.for("dataDowncast").elementToElement({model:"imageInline",view:(e,{writer:t})=>t.createEmptyElement("img")}),i.for("editingDowncast").elementToStructure({model:"imageInline",view:(e,{writer:i})=>n.toImageWidget(function(e){return e.createContainerElement("span",{class:"image-inline"},e.createEmptyElement("img"))}(i),i,t("image widget"))}),i.for("downcast").add(D(n,"imageInline","src")).add(D(n,"imageInline","alt")).add(L(n,"imageInline")),i.for("upcast").elementToElement({view:u(e,"imageInline"),model:(e,{writer:t})=>t.createElement("imageInline",e.hasAttribute("src")?{src:e.getAttribute("src")}:void 0)})}_setupClipboardIntegration(){const e=this.editor,t=e.model,i=e.editing.view,n=e.plugins.get("ImageUtils"),r=e.plugins.get("ClipboardPipeline");this.listenTo(r,"inputTransformation",((r,a)=>{const s=Array.from(a.content.getChildren());let l;if(!s.every(n.isBlockImageView))return;l=a.targetRanges?e.editing.mapper.toModelRange(a.targetRanges[0]):t.document.selection.getFirstRange();const c=t.createSelection(l);if("imageInline"===g(t.schema,c)){const e=new o.UpcastWriter(i.document),t=s.map((t=>1===t.childCount?(Array.from(t.getAttributes()).forEach((i=>e.setAttribute(...i,n.findViewImgElement(t)))),t.getChild(0)):t));a.content=e.createDocumentFragment(t)}})),this.listenTo(r,"contentInsertion",((e,i)=>{"paste"===i.method&&t.change((e=>{const t=e.createRangeIn(i.content);for(const e of t.getItems())e.is("element","imageInline")&&n.setImageNaturalSizeAttributes(e)}))}))}}class ae extends e.Plugin{static get requires(){return[re,l.Widget,N,te]}static get pluginName(){return"ImageInline"}static get isOfficialPlugin(){return!0}}class se extends e.Plugin{static get requires(){return[oe,ae]}static get pluginName(){return"Image"}static get isOfficialPlugin(){return!0}}class le extends e.Plugin{static get pluginName(){return"ImageCaptionUtils"}static get isOfficialPlugin(){return!0}static get requires(){return[h]}getCaptionFromImageModelElement(e){for(const t of e.getChildren())if(t&&t.is("element","caption"))return t;return null}getCaptionFromModelSelection(e){const t=this.editor.plugins.get("ImageUtils"),i=e.getFirstPosition().findAncestor("caption");return i&&t.isBlockImage(i.parent)?i:null}matchImageCaptionViewElement(e){const t=this.editor.plugins.get("ImageUtils");return"figcaption"==e.name&&t.isBlockImageView(e.parent)?{name:!0}:null}}class ce extends e.Command{refresh(){const e=this.editor,t=e.plugins.get("ImageCaptionUtils"),i=e.plugins.get("ImageUtils");if(!e.plugins.has(Q))return this.isEnabled=!1,void(this.value=!1);const n=e.model.document.selection,o=n.getSelectedElement();if(!o){const e=t.getCaptionFromModelSelection(n);return this.isEnabled=!!e,void(this.value=!!e)}this.isEnabled=i.isImage(o),this.isEnabled?this.value=!!t.getCaptionFromImageModelElement(o):this.value=!1}execute(e={}){const{focusCaptionOnShow:t}=e;this.editor.model.change((e=>{this.value?this._hideImageCaption(e):this._showImageCaption(e,t)}))}_showImageCaption(e,t){const i=this.editor.model.document.selection,n=this.editor.plugins.get("ImageCaptionEditing"),o=this.editor.plugins.get("ImageUtils");let r=i.getSelectedElement();const a=n._getSavedCaption(r);o.isInlineImage(r)&&(this.editor.execute("imageTypeBlock"),r=i.getSelectedElement());const s=a||e.createElement("caption");e.append(s,r),t&&e.setSelection(s,"in")}_hideImageCaption(e){const t=this.editor,i=t.model.document.selection,n=t.plugins.get("ImageCaptionEditing"),o=t.plugins.get("ImageCaptionUtils");let r,a=i.getSelectedElement();a?r=o.getCaptionFromImageModelElement(a):(r=o.getCaptionFromModelSelection(i),a=r.parent),n._saveCaption(a,r),e.setSelection(a,"on"),e.remove(r)}}class ue extends e.Plugin{static get requires(){return[h,le]}static get pluginName(){return"ImageCaptionEditing"}static get isOfficialPlugin(){return!0}constructor(e){super(e),this._savedCaptionsMap=new WeakMap}init(){const e=this.editor,t=e.model.schema;t.isRegistered("caption")?t.extend("caption",{allowIn:"imageBlock"}):t.register("caption",{allowIn:"imageBlock",allowContentOf:"$block",isLimit:!0}),e.commands.add("toggleImageCaption",new ce(this.editor)),this._setupConversion(),this._setupImageTypeCommandsIntegration(),this._registerCaptionReconversion()}_setupConversion(){const e=this.editor,t=e.editing.view,i=e.plugins.get("ImageUtils"),n=e.plugins.get("ImageCaptionUtils"),r=e.t;e.conversion.for("upcast").elementToElement({view:e=>n.matchImageCaptionViewElement(e),model:"caption"}),e.conversion.for("dataDowncast").elementToElement({model:"caption",view:(e,{writer:t})=>i.isBlockImage(e.parent)?t.createContainerElement("figcaption"):null}),e.conversion.for("editingDowncast").elementToElement({model:"caption",view:(e,{writer:n})=>{if(!i.isBlockImage(e.parent))return null;const a=n.createEditableElement("figcaption");n.setCustomProperty("imageCaption",!0,a),a.placeholder=r("Enter image caption"),(0,o.enablePlaceholder)({view:t,element:a,keepOnFocus:!0});const s=e.parent.getAttribute("alt"),c=s?r("Caption for image: %0",[s]):r("Caption for the image");return(0,l.toWidgetEditable)(a,n,{label:c})}})}_setupImageTypeCommandsIntegration(){const e=this.editor,t=e.plugins.get("ImageUtils"),i=e.plugins.get("ImageCaptionUtils"),n=e.commands.get("imageTypeInline"),o=e.commands.get("imageTypeBlock"),r=e=>{if(!e.return)return;const{oldElement:n,newElement:o}=e.return;if(!n)return;if(t.isBlockImage(n)){const e=i.getCaptionFromImageModelElement(n);if(e)return void this._saveCaption(o,e)}const r=this._getSavedCaption(n);r&&this._saveCaption(o,r)};n&&this.listenTo(n,"execute",r,{priority:"low"}),o&&this.listenTo(o,"execute",r,{priority:"low"})}_getSavedCaption(e){const t=this._savedCaptionsMap.get(e);return t?o.Element.fromJSON(t):null}_saveCaption(e,t){this._savedCaptionsMap.set(e,t.toJSON())}_registerCaptionReconversion(){const e=this.editor,t=e.model,i=e.plugins.get("ImageUtils"),n=e.plugins.get("ImageCaptionUtils");t.document.on("change:data",(()=>{const o=t.document.differ.getChanges();for(const t of o){if("alt"!==t.attributeKey)continue;const o=t.range.start.nodeAfter;if(i.isBlockImage(o)){const t=n.getCaptionFromImageModelElement(o);if(!t)return;e.editing.reconvertItem(t)}}}))}}class ge extends e.Plugin{static get requires(){return[le]}static get pluginName(){return"ImageCaptionUI"}static get isOfficialPlugin(){return!0}init(){const t=this.editor,i=t.editing.view,n=t.plugins.get("ImageCaptionUtils"),o=t.t;t.ui.componentFactory.add("toggleImageCaption",(r=>{const a=t.commands.get("toggleImageCaption"),s=new I.ButtonView(r);return s.set({icon:e.icons.caption,tooltip:!0,isToggleable:!0}),s.bind("isOn","isEnabled").to(a,"value","isEnabled"),s.bind("label").to(a,"value",(e=>o(e?"Toggle caption off":"Toggle caption on"))),this.listenTo(s,"execute",(()=>{t.execute("toggleImageCaption",{focusCaptionOnShow:!0});const e=n.getCaptionFromModelSelection(t.model.document.selection);if(e){const n=t.editing.mapper.toViewElement(e);i.scrollToTheSelection(),i.change((e=>{e.addClass("image__caption_highlighted",n)}))}t.editing.view.focus()})),s}))}}var me=i(406),de={attributes:{"data-cke":!0}};de.setAttributes=B(),de.insert=C().bind(null,"head"),de.domAPI=x(),de.insertStyleElement=T();y()(me.A,de);me.A&&me.A.locals&&me.A.locals;class pe extends e.Plugin{static get requires(){return[ue,ge]}static get pluginName(){return"ImageCaption"}static get isOfficialPlugin(){return!0}}function he(e){const t=e.map((e=>e.replace("+","\\+")));return new RegExp(`^image\\/(${t.join("|")})$`)}function fe(e){return new Promise(((t,i)=>{const n=e.getAttribute("src");fetch(n).then((e=>e.blob())).then((e=>{const i=be(e,n),o=i.replace("image/",""),r=new File([e],`image.${o}`,{type:i});t(r)})).catch((e=>e&&"TypeError"===e.name?function(e){return function(e){return new Promise(((t,i)=>{const n=s.global.document.createElement("img");n.addEventListener("load",(()=>{const e=s.global.document.createElement("canvas");e.width=n.width,e.height=n.height;e.getContext("2d").drawImage(n,0,0),e.toBlob((e=>e?t(e):i()))})),n.addEventListener("error",(()=>i())),n.src=e}))}(e).then((t=>{const i=be(t,e),n=i.replace("image/","");return new File([t],`image.${n}`,{type:i})}))}(n).then(t).catch(i):i(e)))}))}function be(e,t){return e.type?e.type:t.match(/data:(image\/\w+);base64/)?t.match(/data:(image\/\w+);base64/)[1].toLowerCase():"image/jpeg"}class ke extends e.Plugin{static get pluginName(){return"ImageUploadUI"}static get isOfficialPlugin(){return!0}init(){const e=this.editor;e.ui.componentFactory.add("uploadImage",(()=>this._createToolbarButton())),e.ui.componentFactory.add("imageUpload",(()=>this._createToolbarButton())),e.ui.componentFactory.add("menuBar:uploadImage",(()=>this._createMenuBarButton("standalone"))),e.plugins.has("ImageInsertUI")&&e.plugins.get("ImageInsertUI").registerIntegration({name:"upload",observable:()=>e.commands.get("uploadImage"),buttonViewCreator:()=>this._createToolbarButton(),formViewCreator:()=>this._createDropdownButton(),menuBarButtonViewCreator:e=>this._createMenuBarButton(e?"insertOnly":"insertNested")})}_createButton(t){const i=this.editor,n=i.locale,o=i.commands.get("uploadImage"),r=i.config.get("image.upload.types"),a=he(r),s=new t(i.locale),l=n.t;return s.set({acceptedType:r.map((e=>`image/${e}`)).join(","),allowMultipleFiles:!0,label:l("Upload from computer"),icon:e.icons.imageUpload}),s.bind("isEnabled").to(o),s.on("done",((e,t)=>{const n=Array.from(t).filter((e=>a.test(e.type)));n.length&&(i.execute("uploadImage",{file:n}),i.editing.view.focus())})),s}_createToolbarButton(){const e=this.editor.locale.t,t=this.editor.plugins.get("ImageInsertUI"),i=this.editor.commands.get("uploadImage"),n=this._createButton(I.FileDialogButtonView);return n.tooltip=!0,n.bind("label").to(t,"isImageSelected",i,"isAccessAllowed",((t,i)=>e(i?t?"Replace image from computer":"Upload image from computer":"You have no image upload permissions."))),n}_createDropdownButton(){const e=this.editor.locale.t,t=this.editor.plugins.get("ImageInsertUI"),i=this._createButton(I.FileDialogButtonView);return i.withText=!0,i.bind("label").to(t,"isImageSelected",(t=>e(t?"Replace from computer":"Upload from computer"))),i.on("execute",(()=>{t.dropdownView.isOpen=!1})),i}_createMenuBarButton(e){const t=this.editor.locale.t,i=this._createButton(I.MenuBarMenuListItemFileDialogButtonView);switch(i.withText=!0,e){case"standalone":i.label=t("Image from computer");break;case"insertOnly":i.label=t("Image");break;case"insertNested":i.label=t("From computer")}return i}}var ve=i(260),we=i(184),Ie={attributes:{"data-cke":!0}};Ie.setAttributes=B(),Ie.insert=C().bind(null,"head"),Ie.domAPI=x(),Ie.insertStyleElement=T();y()(we.A,Ie);we.A&&we.A.locals&&we.A.locals;var _e=i(854),ye={attributes:{"data-cke":!0}};ye.setAttributes=B(),ye.insert=C().bind(null,"head"),ye.domAPI=x(),ye.insertStyleElement=T();y()(_e.A,ye);_e.A&&_e.A.locals&&_e.A.locals;var Ae=i(424),xe={attributes:{"data-cke":!0}};xe.setAttributes=B(),xe.insert=C().bind(null,"head"),xe.domAPI=x(),xe.insertStyleElement=T();y()(Ae.A,xe);Ae.A&&Ae.A.locals&&Ae.A.locals;class Ee extends e.Plugin{static get pluginName(){return"ImageUploadProgress"}static get isOfficialPlugin(){return!0}constructor(e){super(e),this.uploadStatusChange=(e,t,i)=>{const n=this.editor,o=t.item,r=o.getAttribute("uploadId");if(!i.consumable.consume(t.item,e.name))return;const a=n.plugins.get("ImageUtils"),s=n.plugins.get(ve.FileRepository),l=r?t.attributeNewValue:null,c=this.placeholder,u=n.editing.mapper.toViewElement(o),g=i.writer;if("reading"==l)return Ce(u,g),void Se(a,c,u,g);if("uploading"==l){const e=s.loaders.get(r);return Ce(u,g),void(e?(Be(u,g),function(e,t,i,n){const o=function(e){const t=e.createUIElement("div",{class:"ck-progress-bar"});return e.setCustomProperty("progressBar",!0,t),t}(t);t.insert(t.createPositionAt(e,"end"),o),i.on("change:uploadedPercent",((e,t,i)=>{n.change((e=>{e.setStyle("width",i+"%",o)}))}))}(u,g,e,n.editing.view),function(e,t,i,n){if(n.data){const o=e.findViewImgElement(t);i.setAttribute("src",n.data,o)}}(a,u,g,e)):Se(a,c,u,g))}"complete"==l&&s.loaders.get(r)&&function(e,t,i){const n=t.createUIElement("div",{class:"ck-image-upload-complete-icon"});t.insert(t.createPositionAt(e,"end"),n),setTimeout((()=>{i.change((e=>e.remove(e.createRangeOn(n))))}),3e3)}(u,g,n.editing.view),function(e,t){Te(e,t,"progressBar")}(u,g),Be(u,g),function(e,t){t.removeClass("ck-appear",e)}(u,g)},this.placeholder="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="}init(){const e=this.editor;e.plugins.has("ImageBlockEditing")&&e.editing.downcastDispatcher.on("attribute:uploadStatus:imageBlock",this.uploadStatusChange),e.plugins.has("ImageInlineEditing")&&e.editing.downcastDispatcher.on("attribute:uploadStatus:imageInline",this.uploadStatusChange)}}function Ce(e,t){e.hasClass("ck-appear")||t.addClass("ck-appear",e)}function Se(e,t,i,n){i.hasClass("ck-image-upload-placeholder")||n.addClass("ck-image-upload-placeholder",i);const o=e.findViewImgElement(i);o.getAttribute("src")!==t&&n.setAttribute("src",t,o),Ve(i,"placeholder")||n.insert(n.createPositionAfter(o),function(e){const t=e.createUIElement("div",{class:"ck-upload-placeholder-loader"});return e.setCustomProperty("placeholder",!0,t),t}(n))}function Be(e,t){e.hasClass("ck-image-upload-placeholder")&&t.removeClass("ck-image-upload-placeholder",e),Te(e,t,"placeholder")}function Ve(e,t){for(const i of e.getChildren())if(i.getCustomProperty(t))return i}function Te(e,t,i){const n=Ve(e,i);n&&t.remove(t.createRangeOn(n))}class ze extends e.Command{constructor(e){super(e),this.set("isAccessAllowed",!0)}refresh(){const e=this.editor,t=e.plugins.get("ImageUtils"),i=e.model.document.selection.getSelectedElement();this.isEnabled=t.isImageAllowed()||t.isImage(i)}execute(e){const t=(0,s.toArray)(e.file),i=this.editor.model.document.selection,n=this.editor.plugins.get("ImageUtils"),o=Object.fromEntries(i.getAttributes());t.forEach(((e,t)=>{const r=i.getSelectedElement();if(t&&r&&n.isImage(r)){const t=this.editor.model.createPositionAfter(r);this._uploadImage(e,o,t)}else this._uploadImage(e,o)}))}_uploadImage(e,t,i){const n=this.editor,o=n.plugins.get(ve.FileRepository).createLoader(e),r=n.plugins.get("ImageUtils");o&&r.insertImage({...t,uploadId:o.id},i)}}class Ue extends e.Plugin{static get requires(){return[ve.FileRepository,I.Notification,t.ClipboardPipeline,h]}static get pluginName(){return"ImageUploadEditing"}static get isOfficialPlugin(){return!0}constructor(e){super(e),e.config.define("image",{upload:{types:["jpeg","png","gif","bmp","webp","tiff"]}}),this._uploadImageElements=new Map}init(){const e=this.editor,t=e.model.document,i=e.conversion,n=e.plugins.get(ve.FileRepository),r=e.plugins.get("ImageUtils"),a=e.plugins.get("ClipboardPipeline"),s=he(e.config.get("image.upload.types")),l=new ze(e);e.commands.add("uploadImage",l),e.commands.add("imageUpload",l),i.for("upcast").attributeToAttribute({view:{name:"img",key:"uploadId"},model:"uploadId"}),this.listenTo(e.editing.view.document,"clipboardInput",((t,i)=>{if(n=i.dataTransfer,Array.from(n.types).includes("text/html")&&""!==n.getData("text/html"))return;var n;const o=Array.from(i.dataTransfer.files).filter((e=>!!e&&s.test(e.type)));if(!o.length)return;t.stop(),e.model.change((t=>{i.targetRanges&&t.setSelection(i.targetRanges.map((t=>e.editing.mapper.toModelRange(t)))),e.execute("uploadImage",{file:o})}));if(!e.commands.get("uploadImage").isAccessAllowed){const t=e.plugins.get("Notification"),i=e.locale.t;t.showWarning(i("You have no image upload permissions."),{namespace:"image"})}})),this.listenTo(a,"inputTransformation",((t,i)=>{const a=Array.from(e.editing.view.createRangeIn(i.content)).map((e=>e.item)).filter((e=>function(e,t){return!(!e.isInlineImageView(t)||!t.getAttribute("src")||!t.getAttribute("src").match(/^data:image\/\w+;base64,/g)&&!t.getAttribute("src").match(/^blob:/g))}(r,e)&&!e.getAttribute("uploadProcessed"))).map((e=>({promise:fe(e),imageElement:e})));if(!a.length)return;const s=new o.UpcastWriter(e.editing.view.document);for(const e of a){s.setAttribute("uploadProcessed",!0,e.imageElement);const t=n.createLoader(e.promise);t&&(s.setAttribute("src","",e.imageElement),s.setAttribute("uploadId",t.id,e.imageElement))}})),e.editing.view.document.on("dragover",((e,t)=>{t.preventDefault()})),t.on("change",(()=>{const i=t.differ.getChanges({includeChangesInGraveyard:!0}).reverse(),o=new Set;for(const t of i)if("insert"==t.type&&"$text"!=t.name){const i=t.position.nodeAfter,r="$graveyard"==t.position.root.rootName;for(const t of Oe(e,i)){const e=t.getAttribute("uploadId");if(!e)continue;const i=n.loaders.get(e);i&&(r?o.has(e)||i.abort():(o.add(e),this._uploadImageElements.set(e,t),"idle"==i.status&&this._readAndUpload(i)))}}})),this.on("uploadComplete",((e,{imageElement:t,data:i})=>{const n=i.urls?i.urls:i;this.editor.model.change((e=>{e.setAttribute("src",n.default,t),this._parseAndSetSrcsetAttributeOnImage(n,t,e),r.setImageNaturalSizeAttributes(t)}))}),{priority:"low"})}afterInit(){const e=this.editor.model.schema;this.editor.plugins.has("ImageBlockEditing")&&e.extend("imageBlock",{allowAttributes:["uploadId","uploadStatus"]}),this.editor.plugins.has("ImageInlineEditing")&&e.extend("imageInline",{allowAttributes:["uploadId","uploadStatus"]})}_readAndUpload(e){const t=this.editor,i=t.model,n=t.locale.t,o=t.plugins.get(ve.FileRepository),r=t.plugins.get(I.Notification),a=t.plugins.get("ImageUtils"),l=this._uploadImageElements;return i.enqueueChange({isUndoable:!1},(t=>{t.setAttribute("uploadStatus","reading",l.get(e.id))})),e.read().then((()=>{const o=e.upload(),r=l.get(e.id);if(s.env.isSafari){const e=t.editing.mapper.toViewElement(r),i=a.findViewImgElement(e);t.editing.view.once("render",(()=>{if(!i.parent)return;const e=t.editing.view.domConverter.mapViewToDom(i.parent);if(!e)return;const n=e.style.display;e.style.display="none",e._ckHack=e.offsetHeight,e.style.display=n}))}return t.ui&&t.ui.ariaLiveAnnouncer.announce(n("Uploading image")),i.enqueueChange({isUndoable:!1},(e=>{e.setAttribute("uploadStatus","uploading",r)})),o})).then((o=>{i.enqueueChange({isUndoable:!1},(i=>{const r=l.get(e.id);i.setAttribute("uploadStatus","complete",r),t.ui&&t.ui.ariaLiveAnnouncer.announce(n("Image upload complete")),this.fire("uploadComplete",{data:o,imageElement:r})})),c()})).catch((o=>{if(t.ui&&t.ui.ariaLiveAnnouncer.announce(n("Error during image upload")),"error"!==e.status&&"aborted"!==e.status)throw o;"error"==e.status&&o&&r.showWarning(o,{title:n("Upload failed"),namespace:"upload"}),i.enqueueChange({isUndoable:!1},(t=>{const i=l.get(e.id);i&&"$graveyard"!==i.root.rootName&&t.remove(i)})),c()}));function c(){i.enqueueChange({isUndoable:!1},(t=>{const i=l.get(e.id);t.removeAttribute("uploadId",i),t.removeAttribute("uploadStatus",i),l.delete(e.id)})),o.destroyLoader(e)}}_parseAndSetSrcsetAttributeOnImage(e,t,i){let n=0;const o=Object.keys(e).filter((e=>{const t=parseInt(e,10);if(!isNaN(t))return n=Math.max(n,t),!0})).map((t=>`${e[t]} ${t}w`)).join(", ");if(""!=o){const e={srcset:o};t.hasAttribute("width")||t.hasAttribute("height")||(e.width=n),i.setAttributes(e,t)}}}function Oe(e,t){const i=e.plugins.get("ImageUtils");return Array.from(e.model.createRangeOn(t)).filter((e=>i.isImage(e.item))).map((e=>e.item))}class Pe extends e.Plugin{static get pluginName(){return"ImageUpload"}static get isOfficialPlugin(){return!0}static get requires(){return[Ue,ke,Ee]}}class Re extends I.View{constructor(e){super(e),this.set("imageURLInputValue",""),this.set("isImageSelected",!1),this.set("isEnabled",!0),this.keystrokes=new s.KeystrokeHandler,this.urlInputView=this._createUrlInputView(),this.setTemplate({tag:"div",attributes:{class:["ck","ck-image-insert-url"]},children:[this.urlInputView,{tag:"div",attributes:{class:["ck","ck-image-insert-url__action-row"]}}]})}render(){super.render(),this.keystrokes.listenTo(this.element)}destroy(){super.destroy(),this.keystrokes.destroy()}_createUrlInputView(){const e=this.locale,t=e.t,i=new I.LabeledFieldView(e,I.createLabeledInputText);return i.bind("label").to(this,"isImageSelected",(e=>t(e?"Update image URL":"Insert image via URL"))),i.bind("isEnabled").to(this),i.fieldView.inputMode="url",i.fieldView.placeholder="https://example.com/image.png",i.fieldView.bind("value").to(this,"imageURLInputValue",(e=>e||"")),i.fieldView.on("input",(()=>{this.imageURLInputValue=i.fieldView.element.value.trim()})),i}focus(){this.urlInputView.focus()}}class je extends e.Plugin{static get pluginName(){return"ImageInsertViaUrlUI"}static get isOfficialPlugin(){return!0}static get requires(){return[te,I.Dialog]}init(){this.editor.ui.componentFactory.add("insertImageViaUrl",(()=>this._createToolbarButton())),this.editor.ui.componentFactory.add("menuBar:insertImageViaUrl",(()=>this._createMenuBarButton("standalone")))}afterInit(){this._imageInsertUI=this.editor.plugins.get("ImageInsertUI"),this._imageInsertUI.registerIntegration({name:"url",observable:()=>this.editor.commands.get("insertImage"),buttonViewCreator:()=>this._createToolbarButton(),formViewCreator:()=>this._createDropdownButton(),menuBarButtonViewCreator:e=>this._createMenuBarButton(e?"insertOnly":"insertNested")})}_createInsertUrlButton(t){const i=new t(this.editor.locale);return i.icon=e.icons.imageUrl,i.on("execute",(()=>{this._showModal()})),i}_createToolbarButton(){const e=this.editor.locale.t,t=this._createInsertUrlButton(I.ButtonView);return t.tooltip=!0,t.bind("label").to(this._imageInsertUI,"isImageSelected",(t=>e(t?"Update image URL":"Insert image via URL"))),t}_createDropdownButton(){const e=this.editor.locale.t,t=this._createInsertUrlButton(I.ButtonView);return t.withText=!0,t.bind("label").to(this._imageInsertUI,"isImageSelected",(t=>e(t?"Update image URL":"Insert via URL"))),t}_createMenuBarButton(e){const t=this.editor.locale.t,i=this._createInsertUrlButton(I.MenuBarMenuListItemButtonView);switch(i.withText=!0,e){case"standalone":i.label=t("Image via URL");break;case"insertOnly":i.label=t("Image");break;case"insertNested":i.label=t("Via URL")}return i}_createInsertUrlView(){const e=this.editor,t=e.locale,i=e.commands.get("replaceImageSource"),n=e.commands.get("insertImage"),o=new Re(t);return o.bind("isImageSelected").to(this._imageInsertUI),o.bind("isEnabled").toMany([n,i],"isEnabled",((...e)=>e.some((e=>e)))),o}_showModal(){const e=this.editor,t=e.locale.t,i=e.plugins.get("Dialog");this._formView||(this._formView=this._createInsertUrlView(),this._formView.on("submit",(()=>this._handleSave())));const n=e.commands.get("replaceImageSource");this._formView.imageURLInputValue=n.value||"",i.show({id:"insertImageViaUrl",title:this._imageInsertUI.isImageSelected?t("Update image URL"):t("Insert image via URL"),isModal:!0,content:this._formView,actionButtons:[{label:t("Cancel"),withText:!0,onExecute:()=>i.hide()},{label:t("Accept"),class:"ck-button-action",withText:!0,onExecute:()=>this._handleSave()}]})}_handleSave(){this.editor.commands.get("replaceImageSource").isEnabled?this.editor.execute("replaceImageSource",{source:this._formView.imageURLInputValue}):this.editor.execute("insertImage",{source:this._formView.imageURLInputValue}),this.editor.plugins.get("Dialog").hide()}}class Fe extends e.Plugin{static get pluginName(){return"ImageInsertViaUrl"}static get isOfficialPlugin(){return!0}static get requires(){return[je,te]}}class Ne extends e.Plugin{static get pluginName(){return"ImageInsert"}static get isOfficialPlugin(){return!0}static get requires(){return[Pe,Fe,te]}}const Le=function(e,t){for(var i=-1,n=null==e?0:e.length,o=Array(n);++i<n;)o[i]=t(e[i],i,e);return o};const De=function(){this.__data__=[],this.size=0};const Me=function(e,t){return e===t||e!=e&&t!=t};const We=function(e,t){for(var i=e.length;i--;)if(Me(e[i][0],t))return i;return-1};var $e=Array.prototype.splice;const qe=function(e){var t=this.__data__,i=We(t,e);return!(i<0)&&(i==t.length-1?t.pop():$e.call(t,i,1),--this.size,!0)};const He=function(e){var t=this.__data__,i=We(t,e);return i<0?void 0:t[i][1]};const Ke=function(e){return We(this.__data__,e)>-1};const Ge=function(e,t){var i=this.__data__,n=We(i,e);return n<0?(++this.size,i.push([e,t])):i[n][1]=t,this};function Je(e){var t=-1,i=null==e?0:e.length;for(this.clear();++t<i;){var n=e[t];this.set(n[0],n[1])}}Je.prototype.clear=De,Je.prototype.delete=qe,Je.prototype.get=He,Je.prototype.has=Ke,Je.prototype.set=Ge;const Ye=Je;const Qe=function(){this.__data__=new Ye,this.size=0};const Xe=function(e){var t=this.__data__,i=t.delete(e);return this.size=t.size,i};const Ze=function(e){return this.__data__.get(e)};const et=function(e){return this.__data__.has(e)};const tt="object"==typeof global&&global&&global.Object===Object&&global;var it="object"==typeof self&&self&&self.Object===Object&&self;const nt=tt||it||Function("return this")();const ot=nt.Symbol;var rt=Object.prototype,at=rt.hasOwnProperty,st=rt.toString,lt=ot?ot.toStringTag:void 0;const ct=function(e){var t=at.call(e,lt),i=e[lt];try{e[lt]=void 0;var n=!0}catch(e){}var o=st.call(e);return n&&(t?e[lt]=i:delete e[lt]),o};var ut=Object.prototype.toString;const gt=function(e){return ut.call(e)};var mt=ot?ot.toStringTag:void 0;const dt=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":mt&&mt in Object(e)?ct(e):gt(e)};const pt=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)};const ht=function(e){if(!pt(e))return!1;var t=dt(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t};const ft=nt["__core-js_shared__"];var bt,kt=(bt=/[^.]+$/.exec(ft&&ft.keys&&ft.keys.IE_PROTO||""))?"Symbol(src)_1."+bt:"";const vt=function(e){return!!kt&&kt in e};var wt=Function.prototype.toString;const It=function(e){if(null!=e){try{return wt.call(e)}catch(e){}try{return e+""}catch(e){}}return""};var _t=/^\[object .+?Constructor\]$/,yt=Function.prototype,At=Object.prototype,xt=yt.toString,Et=At.hasOwnProperty,Ct=RegExp("^"+xt.call(Et).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const St=function(e){return!(!pt(e)||vt(e))&&(ht(e)?Ct:_t).test(It(e))};const Bt=function(e,t){return null==e?void 0:e[t]};const Vt=function(e,t){var i=Bt(e,t);return St(i)?i:void 0};const Tt=Vt(nt,"Map");const zt=Vt(Object,"create");const Ut=function(){this.__data__=zt?zt(null):{},this.size=0};const Ot=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t};var Pt=Object.prototype.hasOwnProperty;const Rt=function(e){var t=this.__data__;if(zt){var i=t[e];return"__lodash_hash_undefined__"===i?void 0:i}return Pt.call(t,e)?t[e]:void 0};var jt=Object.prototype.hasOwnProperty;const Ft=function(e){var t=this.__data__;return zt?void 0!==t[e]:jt.call(t,e)};const Nt=function(e,t){var i=this.__data__;return this.size+=this.has(e)?0:1,i[e]=zt&&void 0===t?"__lodash_hash_undefined__":t,this};function Lt(e){var t=-1,i=null==e?0:e.length;for(this.clear();++t<i;){var n=e[t];this.set(n[0],n[1])}}Lt.prototype.clear=Ut,Lt.prototype.delete=Ot,Lt.prototype.get=Rt,Lt.prototype.has=Ft,Lt.prototype.set=Nt;const Dt=Lt;const Mt=function(){this.size=0,this.__data__={hash:new Dt,map:new(Tt||Ye),string:new Dt}};const Wt=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};const $t=function(e,t){var i=e.__data__;return Wt(t)?i["string"==typeof t?"string":"hash"]:i.map};const qt=function(e){var t=$t(this,e).delete(e);return this.size-=t?1:0,t};const Ht=function(e){return $t(this,e).get(e)};const Kt=function(e){return $t(this,e).has(e)};const Gt=function(e,t){var i=$t(this,e),n=i.size;return i.set(e,t),this.size+=i.size==n?0:1,this};function Jt(e){var t=-1,i=null==e?0:e.length;for(this.clear();++t<i;){var n=e[t];this.set(n[0],n[1])}}Jt.prototype.clear=Mt,Jt.prototype.delete=qt,Jt.prototype.get=Ht,Jt.prototype.has=Kt,Jt.prototype.set=Gt;const Yt=Jt;const Qt=function(e,t){var i=this.__data__;if(i instanceof Ye){var n=i.__data__;if(!Tt||n.length<199)return n.push([e,t]),this.size=++i.size,this;i=this.__data__=new Yt(n)}return i.set(e,t),this.size=i.size,this};function Xt(e){var t=this.__data__=new Ye(e);this.size=t.size}Xt.prototype.clear=Qe,Xt.prototype.delete=Xe,Xt.prototype.get=Ze,Xt.prototype.has=et,Xt.prototype.set=Qt;const Zt=Xt;const ei=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this};const ti=function(e){return this.__data__.has(e)};function ii(e){var t=-1,i=null==e?0:e.length;for(this.__data__=new Yt;++t<i;)this.add(e[t])}ii.prototype.add=ii.prototype.push=ei,ii.prototype.has=ti;const ni=ii;const oi=function(e,t){for(var i=-1,n=null==e?0:e.length;++i<n;)if(t(e[i],i,e))return!0;return!1};const ri=function(e,t){return e.has(t)};const ai=function(e,t,i,n,o,r){var a=1&i,s=e.length,l=t.length;if(s!=l&&!(a&&l>s))return!1;var c=r.get(e),u=r.get(t);if(c&&u)return c==t&&u==e;var g=-1,m=!0,d=2&i?new ni:void 0;for(r.set(e,t),r.set(t,e);++g<s;){var p=e[g],h=t[g];if(n)var f=a?n(h,p,g,t,e,r):n(p,h,g,e,t,r);if(void 0!==f){if(f)continue;m=!1;break}if(d){if(!oi(t,(function(e,t){if(!ri(d,t)&&(p===e||o(p,e,i,n,r)))return d.push(t)}))){m=!1;break}}else if(p!==h&&!o(p,h,i,n,r)){m=!1;break}}return r.delete(e),r.delete(t),m};const si=nt.Uint8Array;const li=function(e){var t=-1,i=Array(e.size);return e.forEach((function(e,n){i[++t]=[n,e]})),i};const ci=function(e){var t=-1,i=Array(e.size);return e.forEach((function(e){i[++t]=e})),i};var ui=ot?ot.prototype:void 0,gi=ui?ui.valueOf:void 0;const mi=function(e,t,i,n,o,r,a){switch(i){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!r(new si(e),new si(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return Me(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var s=li;case"[object Set]":var l=1&n;if(s||(s=ci),e.size!=t.size&&!l)return!1;var c=a.get(e);if(c)return c==t;n|=2,a.set(e,t);var u=ai(s(e),s(t),n,o,r,a);return a.delete(e),u;case"[object Symbol]":if(gi)return gi.call(e)==gi.call(t)}return!1};const di=function(e,t){for(var i=-1,n=t.length,o=e.length;++i<n;)e[o+i]=t[i];return e};const pi=Array.isArray;const hi=function(e,t,i){var n=t(e);return pi(e)?n:di(n,i(e))};const fi=function(e,t){for(var i=-1,n=null==e?0:e.length,o=0,r=[];++i<n;){var a=e[i];t(a,i,e)&&(r[o++]=a)}return r};const bi=function(){return[]};var ki=Object.prototype.propertyIsEnumerable,vi=Object.getOwnPropertySymbols;const wi=vi?function(e){return null==e?[]:(e=Object(e),fi(vi(e),(function(t){return ki.call(e,t)})))}:bi;const Ii=function(e,t){for(var i=-1,n=Array(e);++i<e;)n[i]=t(i);return n};const _i=function(e){return null!=e&&"object"==typeof e};const yi=function(e){return _i(e)&&"[object Arguments]"==dt(e)};var Ai=Object.prototype,xi=Ai.hasOwnProperty,Ei=Ai.propertyIsEnumerable;const Ci=yi(function(){return arguments}())?yi:function(e){return _i(e)&&xi.call(e,"callee")&&!Ei.call(e,"callee")};const Si=function(){return!1};var Bi="object"==typeof exports&&exports&&!exports.nodeType&&exports,Vi=Bi&&"object"==typeof module&&module&&!module.nodeType&&module,Ti=Vi&&Vi.exports===Bi?nt.Buffer:void 0;const zi=(Ti?Ti.isBuffer:void 0)||Si;var Ui=/^(?:0|[1-9]\d*)$/;const Oi=function(e,t){var i=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==i||"symbol"!=i&&Ui.test(e))&&e>-1&&e%1==0&&e<t};const Pi=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991};var Ri={};Ri["[object Float32Array]"]=Ri["[object Float64Array]"]=Ri["[object Int8Array]"]=Ri["[object Int16Array]"]=Ri["[object Int32Array]"]=Ri["[object Uint8Array]"]=Ri["[object Uint8ClampedArray]"]=Ri["[object Uint16Array]"]=Ri["[object Uint32Array]"]=!0,Ri["[object Arguments]"]=Ri["[object Array]"]=Ri["[object ArrayBuffer]"]=Ri["[object Boolean]"]=Ri["[object DataView]"]=Ri["[object Date]"]=Ri["[object Error]"]=Ri["[object Function]"]=Ri["[object Map]"]=Ri["[object Number]"]=Ri["[object Object]"]=Ri["[object RegExp]"]=Ri["[object Set]"]=Ri["[object String]"]=Ri["[object WeakMap]"]=!1;const ji=function(e){return _i(e)&&Pi(e.length)&&!!Ri[dt(e)]};const Fi=function(e){return function(t){return e(t)}};var Ni="object"==typeof exports&&exports&&!exports.nodeType&&exports,Li=Ni&&"object"==typeof module&&module&&!module.nodeType&&module,Di=Li&&Li.exports===Ni&&tt.process,Mi=function(){try{var e=Li&&Li.require&&Li.require("util").types;return e||Di&&Di.binding&&Di.binding("util")}catch(e){}}();var Wi=Mi&&Mi.isTypedArray;const $i=Wi?Fi(Wi):ji;var qi=Object.prototype.hasOwnProperty;const Hi=function(e,t){var i=pi(e),n=!i&&Ci(e),o=!i&&!n&&zi(e),r=!i&&!n&&!o&&$i(e),a=i||n||o||r,s=a?Ii(e.length,String):[],l=s.length;for(var c in e)!t&&!qi.call(e,c)||a&&("length"==c||o&&("offset"==c||"parent"==c)||r&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Oi(c,l))||s.push(c);return s};var Ki=Object.prototype;const Gi=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Ki)};const Ji=function(e,t){return function(i){return e(t(i))}}(Object.keys,Object);var Yi=Object.prototype.hasOwnProperty;const Qi=function(e){if(!Gi(e))return Ji(e);var t=[];for(var i in Object(e))Yi.call(e,i)&&"constructor"!=i&&t.push(i);return t};const Xi=function(e){return null!=e&&Pi(e.length)&&!ht(e)};const Zi=function(e){return Xi(e)?Hi(e):Qi(e)};const en=function(e){return hi(e,Zi,wi)};var tn=Object.prototype.hasOwnProperty;const nn=function(e,t,i,n,o,r){var a=1&i,s=en(e),l=s.length;if(l!=en(t).length&&!a)return!1;for(var c=l;c--;){var u=s[c];if(!(a?u in t:tn.call(t,u)))return!1}var g=r.get(e),m=r.get(t);if(g&&m)return g==t&&m==e;var d=!0;r.set(e,t),r.set(t,e);for(var p=a;++c<l;){var h=e[u=s[c]],f=t[u];if(n)var b=a?n(f,h,u,t,e,r):n(h,f,u,e,t,r);if(!(void 0===b?h===f||o(h,f,i,n,r):b)){d=!1;break}p||(p="constructor"==u)}if(d&&!p){var k=e.constructor,v=t.constructor;k==v||!("constructor"in e)||!("constructor"in t)||"function"==typeof k&&k instanceof k&&"function"==typeof v&&v instanceof v||(d=!1)}return r.delete(e),r.delete(t),d};const on=Vt(nt,"DataView");const rn=Vt(nt,"Promise");const an=Vt(nt,"Set");const sn=Vt(nt,"WeakMap");var ln="[object Map]",cn="[object Promise]",un="[object Set]",gn="[object WeakMap]",mn="[object DataView]",dn=It(on),pn=It(Tt),hn=It(rn),fn=It(an),bn=It(sn),kn=dt;(on&&kn(new on(new ArrayBuffer(1)))!=mn||Tt&&kn(new Tt)!=ln||rn&&kn(rn.resolve())!=cn||an&&kn(new an)!=un||sn&&kn(new sn)!=gn)&&(kn=function(e){var t=dt(e),i="[object Object]"==t?e.constructor:void 0,n=i?It(i):"";if(n)switch(n){case dn:return mn;case pn:return ln;case hn:return cn;case fn:return un;case bn:return gn}return t});const vn=kn;var wn="[object Arguments]",In="[object Array]",_n="[object Object]",yn=Object.prototype.hasOwnProperty;const An=function(e,t,i,n,o,r){var a=pi(e),s=pi(t),l=a?In:vn(e),c=s?In:vn(t),u=(l=l==wn?_n:l)==_n,g=(c=c==wn?_n:c)==_n,m=l==c;if(m&&zi(e)){if(!zi(t))return!1;a=!0,u=!1}if(m&&!u)return r||(r=new Zt),a||$i(e)?ai(e,t,i,n,o,r):mi(e,t,l,i,n,o,r);if(!(1&i)){var d=u&&yn.call(e,"__wrapped__"),p=g&&yn.call(t,"__wrapped__");if(d||p){var h=d?e.value():e,f=p?t.value():t;return r||(r=new Zt),o(h,f,i,n,r)}}return!!m&&(r||(r=new Zt),nn(e,t,i,n,o,r))};const xn=function e(t,i,n,o,r){return t===i||(null==t||null==i||!_i(t)&&!_i(i)?t!=t&&i!=i:An(t,i,n,o,e,r))};const En=function(e,t,i,n){var o=i.length,r=o,a=!n;if(null==e)return!r;for(e=Object(e);o--;){var s=i[o];if(a&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++o<r;){var l=(s=i[o])[0],c=e[l],u=s[1];if(a&&s[2]){if(void 0===c&&!(l in e))return!1}else{var g=new Zt;if(n)var m=n(c,u,l,e,t,g);if(!(void 0===m?xn(u,c,3,n,g):m))return!1}}return!0};const Cn=function(e){return e==e&&!pt(e)};const Sn=function(e){for(var t=Zi(e),i=t.length;i--;){var n=t[i],o=e[n];t[i]=[n,o,Cn(o)]}return t};const Bn=function(e,t){return function(i){return null!=i&&(i[e]===t&&(void 0!==t||e in Object(i)))}};const Vn=function(e){var t=Sn(e);return 1==t.length&&t[0][2]?Bn(t[0][0],t[0][1]):function(i){return i===e||En(i,e,t)}};const Tn=function(e){return"symbol"==typeof e||_i(e)&&"[object Symbol]"==dt(e)};var zn=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Un=/^\w*$/;const On=function(e,t){if(pi(e))return!1;var i=typeof e;return!("number"!=i&&"symbol"!=i&&"boolean"!=i&&null!=e&&!Tn(e))||(Un.test(e)||!zn.test(e)||null!=t&&e in Object(t))};function Pn(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var i=function(){var n=arguments,o=t?t.apply(this,n):n[0],r=i.cache;if(r.has(o))return r.get(o);var a=e.apply(this,n);return i.cache=r.set(o,a)||r,a};return i.cache=new(Pn.Cache||Yt),i}Pn.Cache=Yt;const Rn=Pn;var jn=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Fn=/\\(\\)?/g;const Nn=function(e){var t=Rn(e,(function(e){return 500===i.size&&i.clear(),e})),i=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(jn,(function(e,i,n,o){t.push(n?o.replace(Fn,"$1"):i||e)})),t}));var Ln=ot?ot.prototype:void 0,Dn=Ln?Ln.toString:void 0;const Mn=function e(t){if("string"==typeof t)return t;if(pi(t))return Le(t,e)+"";if(Tn(t))return Dn?Dn.call(t):"";var i=t+"";return"0"==i&&1/t==-1/0?"-0":i};const Wn=function(e){return null==e?"":Mn(e)};const $n=function(e,t){return pi(e)?e:On(e,t)?[e]:Nn(Wn(e))};const qn=function(e){if("string"==typeof e||Tn(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t};const Hn=function(e,t){for(var i=0,n=(t=$n(t,e)).length;null!=e&&i<n;)e=e[qn(t[i++])];return i&&i==n?e:void 0};const Kn=function(e,t,i){var n=null==e?void 0:Hn(e,t);return void 0===n?i:n};const Gn=function(e,t){return null!=e&&t in Object(e)};const Jn=function(e,t,i){for(var n=-1,o=(t=$n(t,e)).length,r=!1;++n<o;){var a=qn(t[n]);if(!(r=null!=e&&i(e,a)))break;e=e[a]}return r||++n!=o?r:!!(o=null==e?0:e.length)&&Pi(o)&&Oi(a,o)&&(pi(e)||Ci(e))};const Yn=function(e,t){return null!=e&&Jn(e,t,Gn)};const Qn=function(e,t){return On(e)&&Cn(t)?Bn(qn(e),t):function(i){var n=Kn(i,e);return void 0===n&&n===t?Yn(i,e):xn(t,n,3)}};const Xn=function(e){return e};const Zn=function(e){return function(t){return null==t?void 0:t[e]}};const eo=function(e){return function(t){return Hn(t,e)}};const to=function(e){return On(e)?Zn(qn(e)):eo(e)};const io=function(e){return"function"==typeof e?e:null==e?Xn:"object"==typeof e?pi(e)?Qn(e[0],e[1]):Vn(e):to(e)};const no=function(e){return function(t,i,n){for(var o=-1,r=Object(t),a=n(t),s=a.length;s--;){var l=a[e?s:++o];if(!1===i(r[l],l,r))break}return t}}();const oo=function(e,t){return function(i,n){if(null==i)return i;if(!Xi(i))return e(i,n);for(var o=i.length,r=t?o:-1,a=Object(i);(t?r--:++r<o)&&!1!==n(a[r],r,a););return i}}((function(e,t){return e&&no(e,t,Zi)}));const ro=function(e,t){var i=-1,n=Xi(e)?Array(e.length):[];return oo(e,(function(e,o,r){n[++i]=t(e,o,r)})),n};const ao=function(e,t){return(pi(e)?Le:ro)(e,io(t,3))};class so extends e.Command{refresh(){const e=this.editor,t=e.plugins.get("ImageUtils").getClosestSelectedImageElement(e.model.document.selection);this.isEnabled=!!t,t&&t.hasAttribute("resizedWidth")?this.value={width:t.getAttribute("resizedWidth"),height:null}:this.value=null}execute(e){const t=this.editor,i=t.model,n=t.plugins.get("ImageUtils"),o=n.getClosestSelectedImageElement(i.document.selection);this.value={width:e.width,height:null},o&&i.change((t=>{t.setAttribute("resizedWidth",e.width,o),t.removeAttribute("resizedHeight",o),n.setImageNaturalSizeAttributes(o)}))}}class lo extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImageResizeEditing"}static get isOfficialPlugin(){return!0}constructor(e){super(e),e.config.define("image",{resizeUnit:"%",resizeOptions:[{name:"resizeImage:original",value:null,icon:"original"},{name:"resizeImage:custom",value:"custom",icon:"custom"},{name:"resizeImage:25",value:"25",icon:"small"},{name:"resizeImage:50",value:"50",icon:"medium"},{name:"resizeImage:75",value:"75",icon:"large"}]})}init(){const e=this.editor,t=new so(e);this._registerConverters("imageBlock"),this._registerConverters("imageInline"),e.commands.add("resizeImage",t),e.commands.add("imageResize",t)}afterInit(){this._registerSchema()}_registerSchema(){this.editor.plugins.has("ImageBlockEditing")&&this.editor.model.schema.extend("imageBlock",{allowAttributes:["resizedWidth","resizedHeight"]}),this.editor.plugins.has("ImageInlineEditing")&&this.editor.model.schema.extend("imageInline",{allowAttributes:["resizedWidth","resizedHeight"]})}_registerConverters(e){const t=this.editor,i=t.plugins.get("ImageUtils");t.conversion.for("downcast").add((t=>t.on(`attribute:resizedWidth:${e}`,((e,t,i)=>{if(!i.consumable.consume(t.item,e.name))return;const n=i.writer,o=i.mapper.toViewElement(t.item);null!==t.attributeNewValue?(n.setStyle("width",t.attributeNewValue,o),n.addClass("image_resized",o)):(n.removeStyle("width",o),n.removeClass("image_resized",o))})))),t.conversion.for("dataDowncast").attributeToAttribute({model:{name:e,key:"resizedHeight"},view:e=>({key:"style",value:{height:e}})}),t.conversion.for("editingDowncast").add((t=>t.on(`attribute:resizedHeight:${e}`,((t,n,o)=>{if(!o.consumable.consume(n.item,t.name))return;const r=o.writer,a=o.mapper.toViewElement(n.item),s="imageInline"===e?i.findViewImgElement(a):a;null!==n.attributeNewValue?r.setStyle("height",n.attributeNewValue,s):r.removeStyle("height",s)})))),t.conversion.for("upcast").attributeToAttribute({view:{name:"imageBlock"===e?"figure":"img",styles:{width:/.+/}},model:{key:"resizedWidth",value:e=>d(e)?null:e.getStyle("width")}}),t.conversion.for("upcast").attributeToAttribute({view:{name:"imageBlock"===e?"figure":"img",styles:{height:/.+/}},model:{key:"resizedHeight",value:e=>d(e)?null:e.getStyle("height")}})}}const co=(()=>({small:e.icons.objectSizeSmall,medium:e.icons.objectSizeMedium,large:e.icons.objectSizeLarge,custom:e.icons.objectSizeCustom,original:e.icons.objectSizeFull}))();class uo extends e.Plugin{static get requires(){return[lo]}static get pluginName(){return"ImageResizeButtons"}static get isOfficialPlugin(){return!0}constructor(e){super(e),this._resizeUnit=e.config.get("image.resizeUnit")}init(){const e=this.editor,t=e.config.get("image.resizeOptions"),i=e.commands.get("resizeImage");this.bind("isEnabled").to(i);for(const e of t)this._registerImageResizeButton(e);this._registerImageResizeDropdown(t)}_registerImageResizeButton(e){const t=this.editor,{name:i,value:n,icon:o}=e;t.ui.componentFactory.add(i,(i=>{const r=new I.ButtonView(i),a=t.commands.get("resizeImage"),l=this._getOptionLabelValue(e,!0);if(!co[o])throw new s.CKEditorError("imageresizebuttons-missing-icon",t,e);if(r.set({label:l,icon:co[o],tooltip:l,isToggleable:!0}),r.bind("isEnabled").to(this),t.plugins.has("ImageCustomResizeUI")&&go(e)){const e=t.plugins.get("ImageCustomResizeUI");this.listenTo(r,"execute",(()=>{e._showForm(this._resizeUnit)}))}else{const e=n?n+this._resizeUnit:null;r.bind("isOn").to(a,"value",mo(e)),this.listenTo(r,"execute",(()=>{t.execute("resizeImage",{width:e})}))}return r}))}_registerImageResizeDropdown(e){const t=this.editor,i=t.t,n=e.find((e=>!e.value)),o=o=>{const r=t.commands.get("resizeImage"),a=(0,I.createDropdown)(o,I.DropdownButtonView),s=a.buttonView,l=i("Resize image");return s.set({tooltip:l,commandValue:n.value,icon:co.medium,isToggleable:!0,label:this._getOptionLabelValue(n),withText:!0,class:"ck-resize-image-button",ariaLabel:l,ariaLabelledBy:void 0}),s.bind("label").to(r,"value",(e=>e&&e.width?e.width:this._getOptionLabelValue(n))),a.bind("isEnabled").to(this),(0,I.addListToDropdown)(a,(()=>this._getResizeDropdownListItemDefinitions(e,r)),{ariaLabel:i("Image resize list"),role:"menu"}),this.listenTo(a,"execute",(e=>{"onClick"in e.source?e.source.onClick():(t.execute(e.source.commandName,{width:e.source.commandValue}),t.editing.view.focus())})),a};t.ui.componentFactory.add("resizeImage",o),t.ui.componentFactory.add("imageResize",o)}_getOptionLabelValue(e,t=!1){const i=this.editor.t;return e.label?e.label:t?go(e)?i("Custom image size"):e.value?i("Resize image to %0",e.value+this._resizeUnit):i("Resize image to the original size"):go(e)?i("Custom"):e.value?e.value+this._resizeUnit:i("Original")}_getResizeDropdownListItemDefinitions(e,t){const{editor:i}=this,n=new s.Collection,o=e.map((e=>go(e)?{...e,valueWithUnits:"custom"}:e.value?{...e,valueWithUnits:`${e.value}${this._resizeUnit}`}:{...e,valueWithUnits:null}));for(const e of o){let r=null;if(i.plugins.has("ImageCustomResizeUI")&&go(e)){const n=i.plugins.get("ImageCustomResizeUI");r={type:"button",model:new I.ViewModel({label:this._getOptionLabelValue(e),role:"menuitemradio",withText:!0,icon:null,onClick:()=>{n._showForm(this._resizeUnit)}})};const a=ao(o,"valueWithUnits");r.model.bind("isOn").to(t,"value",po(a))}else r={type:"button",model:new I.ViewModel({commandName:"resizeImage",commandValue:e.valueWithUnits,label:this._getOptionLabelValue(e),role:"menuitemradio",withText:!0,icon:null})},r.model.bind("isOn").to(t,"value",mo(e.valueWithUnits));r.model.bind("isEnabled").to(t,"isEnabled"),n.add(r)}return n}}function go(e){return"custom"===e.value}function mo(e){return t=>null===e&&t===e||null!==t&&t.width===e}function po(e){return t=>!e.some((e=>mo(e)(t)))}const ho="image_resized";class fo extends e.Plugin{static get requires(){return[l.WidgetResize,h]}static get pluginName(){return"ImageResizeHandles"}static get isOfficialPlugin(){return!0}init(){const e=this.editor.commands.get("resizeImage");this.bind("isEnabled").to(e),this._setupResizerCreator()}_setupResizerCreator(){const e=this.editor,t=e.editing.view,i=e.plugins.get("ImageUtils");t.addObserver(M),this.listenTo(t.document,"imageLoaded",((n,o)=>{if(!o.target.matches("figure.image.ck-widget > img,figure.image.ck-widget > picture > img,figure.image.ck-widget > a > img,figure.image.ck-widget > a > picture > img,span.image-inline.ck-widget > img,span.image-inline.ck-widget > picture > img"))return;const r=e.editing.view.domConverter,a=r.domToView(o.target),s=i.getImageWidgetFromImageView(a);let c=this.editor.plugins.get(l.WidgetResize).getResizerByViewElement(s);if(c)return void c.redraw();const u=e.editing.mapper,g=u.toModelElement(s);c=e.plugins.get(l.WidgetResize).attachTo({unit:e.config.get("image.resizeUnit"),modelElement:g,viewElement:s,editor:e,getHandleHost:e=>e.querySelector("img"),getResizeHost:()=>r.mapViewToDom(u.toViewElement(g)),isCentered:()=>"alignCenter"==g.getAttribute("imageStyle"),onCommit(i){t.change((e=>{e.removeClass(ho,s)})),e.execute("resizeImage",{width:i})}}),c.on("updateSize",(()=>{s.hasClass(ho)||t.change((e=>{e.addClass(ho,s)}));const e="imageInline"===g.name?a:s;e.getStyle("height")&&t.change((t=>{t.removeStyle("height",e)}))})),c.bind("isEnabled").to(this)}))}}function bo(e){if(!e)return null;const[,t,i]=e.trim().match(/([.,\d]+)(%|px)$/)||[],n=Number.parseFloat(t);return Number.isNaN(n)?null:{value:n,unit:i}}function ko(e,t,i){return"px"===i?{value:t.value,unit:"px"}:{value:t.value/e*100,unit:"%"}}function vo(e){const{editing:t}=e,i=e.plugins.get("ImageUtils").getClosestSelectedImageElement(e.model.document.selection);if(!i)return null;const n=t.mapper.toViewElement(i);return{model:i,view:n,dom:t.view.domConverter.mapViewToDom(n)}}var wo=i(429),Io={attributes:{"data-cke":!0}};Io.setAttributes=B(),Io.insert=C().bind(null,"head"),Io.domAPI=x(),Io.insertStyleElement=T();y()(wo.A,Io);wo.A&&wo.A.locals&&wo.A.locals;class _o extends I.View{constructor(t,i,n){super(t);const o=this.locale.t;this.focusTracker=new s.FocusTracker,this.keystrokes=new s.KeystrokeHandler,this.unit=i,this.labeledInput=this._createLabeledInputView(),this.saveButtonView=this._createButton(o("Save"),e.icons.check,"ck-button-save"),this.saveButtonView.type="submit",this.cancelButtonView=this._createButton(o("Cancel"),e.icons.cancel,"ck-button-cancel","cancel"),this._focusables=new I.ViewCollection,this._validators=n,this._focusCycler=new I.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.setTemplate({tag:"form",attributes:{class:["ck","ck-image-custom-resize-form","ck-responsive-form"],tabindex:"-1"},children:[this.labeledInput,this.saveButtonView,this.cancelButtonView]})}render(){super.render(),this.keystrokes.listenTo(this.element),(0,I.submitHandler)({view:this}),[this.labeledInput,this.saveButtonView,this.cancelButtonView].forEach((e=>{this._focusables.add(e),this.focusTracker.add(e.element)}))}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}_createButton(e,t,i,n){const o=new I.ButtonView(this.locale);return o.set({label:e,icon:t,tooltip:!0}),o.extendTemplate({attributes:{class:i}}),n&&o.delegate("execute").to(this,n),o}_createLabeledInputView(){const e=this.locale.t,t=new I.LabeledFieldView(this.locale,I.createLabeledInputNumber);return t.label=e("Resize image (in %0)",this.unit),t.fieldView.set({step:.1}),t}isValid(){this.resetFormStatus();for(const e of this._validators){const t=e(this);if(t)return this.labeledInput.errorText=t,!1}return!0}resetFormStatus(){this.labeledInput.errorText=null}get rawSize(){const{element:e}=this.labeledInput.fieldView;return e?e.value:null}get parsedSize(){const{rawSize:e}=this;if(null===e)return null;const t=Number.parseFloat(e);return Number.isNaN(t)?null:t}get sizeWithUnits(){const{parsedSize:e,unit:t}=this;return null===e?null:`${e}${t}`}}class yo extends e.Plugin{static get requires(){return[I.ContextualBalloon]}static get pluginName(){return"ImageCustomResizeUI"}static get isOfficialPlugin(){return!0}destroy(){super.destroy(),this._form&&this._form.destroy()}_createForm(e){const t=this.editor;this._balloon=this.editor.plugins.get("ContextualBalloon"),this._form=new((0,I.CssTransitionDisablerMixin)(_o))(t.locale,e,function(e){const t=e.t;return[e=>""===e.rawSize.trim()?t("The value must not be empty."):null===e.parsedSize?t("The value should be a plain number."):void 0]}(t)),this._form.render(),this.listenTo(this._form,"submit",(()=>{this._form.isValid()&&(t.execute("resizeImage",{width:this._form.sizeWithUnits}),this._hideForm(!0))})),this.listenTo(this._form.labeledInput,"change:errorText",(()=>{t.ui.update()})),this.listenTo(this._form,"cancel",(()=>{this._hideForm(!0)})),this._form.keystrokes.set("Esc",((e,t)=>{this._hideForm(!0),t()})),(0,I.clickOutsideHandler)({emitter:this._form,activator:()=>this._isVisible,contextElements:()=>[this._balloon.view.element],callback:()=>this._hideForm()})}_showForm(e){if(this._isVisible)return;this._form||this._createForm(e);const t=this.editor,i=this._form.labeledInput;this._form.disableCssTransitions(),this._form.resetFormStatus(),this._isInBalloon||this._balloon.add({view:this._form,position:j(t)});const n=function(e,t){const i=vo(e);if(!i)return null;const n=bo(i.model.getAttribute("resizedWidth")||null);return n?n.unit===t?n:ko((0,l.calculateResizeHostAncestorWidth)(i.dom),{unit:"px",value:new s.Rect(i.dom).width},t):null}(t,e),o=n?n.value.toFixed(1):"",r=function(e,t){const i=vo(e);if(!i)return null;const n=(0,l.calculateResizeHostAncestorWidth)(i.dom),o=bo(window.getComputedStyle(i.dom).minWidth)||{value:1,unit:"px"};return{unit:t,lower:Math.max(.1,ko(n,o,t).value),upper:"px"===t?n:100}}(t,e);i.fieldView.value=i.fieldView.element.value=o,r&&Object.assign(i.fieldView,{min:r.lower.toFixed(1),max:Math.ceil(r.upper).toFixed(1)}),this._form.labeledInput.fieldView.select(),this._form.enableCssTransitions()}_hideForm(e=!1){this._isInBalloon&&(this._form.focusTracker.isFocused&&this._form.saveButtonView.focus(),this._balloon.remove(this._form),e&&this.editor.editing.view.focus())}get _isVisible(){return!!this._balloon&&this._balloon.visibleView===this._form}get _isInBalloon(){return!!this._balloon&&this._balloon.hasView(this._form)}}var Ao=i(278),xo={attributes:{"data-cke":!0}};xo.setAttributes=B(),xo.insert=C().bind(null,"head"),xo.domAPI=x(),xo.insertStyleElement=T();y()(Ao.A,xo);Ao.A&&Ao.A.locals&&Ao.A.locals;class Eo extends e.Plugin{static get requires(){return[lo,fo,yo,uo]}static get pluginName(){return"ImageResize"}static get isOfficialPlugin(){return!0}}class Co extends e.Command{constructor(e,t){super(e),this._defaultStyles={imageBlock:!1,imageInline:!1},this._styles=new Map(t.map((e=>{if(e.isDefault)for(const t of e.modelElements)this._defaultStyles[t]=e.name;return[e.name,e]})))}refresh(){const e=this.editor.plugins.get("ImageUtils").getClosestSelectedImageElement(this.editor.model.document.selection);this.isEnabled=!!e,this.isEnabled?e.hasAttribute("imageStyle")?this.value=e.getAttribute("imageStyle"):this.value=this._defaultStyles[e.name]:this.value=!1}execute(e={}){const t=this.editor,i=t.model,n=t.plugins.get("ImageUtils");i.change((t=>{const o=e.value,{setImageSizes:r=!0}=e;let a=n.getClosestSelectedImageElement(i.document.selection);o&&this.shouldConvertImageType(o,a)&&(this.editor.execute(n.isBlockImage(a)?"imageTypeInline":"imageTypeBlock",{setImageSizes:r}),a=n.getClosestSelectedImageElement(i.document.selection)),!o||this._styles.get(o).isDefault?t.removeAttribute("imageStyle",a):t.setAttribute("imageStyle",o,a),r&&n.setImageNaturalSizeAttributes(a)}))}shouldConvertImageType(e,t){return!this._styles.get(e).modelElements.includes(t.name)}}const So={get inline(){return{name:"inline",title:"In line",icon:e.icons.objectInline,modelElements:["imageInline"],isDefault:!0}},get alignLeft(){return{name:"alignLeft",title:"Left aligned image",icon:e.icons.objectLeft,modelElements:["imageBlock","imageInline"],className:"image-style-align-left"}},get alignBlockLeft(){return{name:"alignBlockLeft",title:"Left aligned image",icon:e.icons.objectBlockLeft,modelElements:["imageBlock"],className:"image-style-block-align-left"}},get alignCenter(){return{name:"alignCenter",title:"Centered image",icon:e.icons.objectCenter,modelElements:["imageBlock"],className:"image-style-align-center"}},get alignRight(){return{name:"alignRight",title:"Right aligned image",icon:e.icons.objectRight,modelElements:["imageBlock","imageInline"],className:"image-style-align-right"}},get alignBlockRight(){return{name:"alignBlockRight",title:"Right aligned image",icon:e.icons.objectBlockRight,modelElements:["imageBlock"],className:"image-style-block-align-right"}},get block(){return{name:"block",title:"Centered image",icon:e.icons.objectCenter,modelElements:["imageBlock"],isDefault:!0}},get side(){return{name:"side",title:"Side image",icon:e.icons.objectRight,modelElements:["imageBlock"],className:"image-style-side"}}},Bo=(()=>({full:e.icons.objectFullWidth,left:e.icons.objectBlockLeft,right:e.icons.objectBlockRight,center:e.icons.objectCenter,inlineLeft:e.icons.objectLeft,inlineRight:e.icons.objectRight,inline:e.icons.objectInline}))(),Vo=[{name:"imageStyle:wrapText",title:"Wrap text",defaultItem:"imageStyle:alignLeft",items:["imageStyle:alignLeft","imageStyle:alignRight"]},{name:"imageStyle:breakText",title:"Break text",defaultItem:"imageStyle:block",items:["imageStyle:alignBlockLeft","imageStyle:block","imageStyle:alignBlockRight"]}];function To(e){(0,s.logWarning)("image-style-configuration-definition-invalid",e)}const zo={normalizeStyles:function(e){return(e.configuredStyles.options||[]).map((e=>function(e){e="string"==typeof e?So[e]?{...So[e]}:{name:e}:function(e,t){const i={...t};for(const n in e)Object.prototype.hasOwnProperty.call(t,n)||(i[n]=e[n]);return i}(So[e.name],e);"string"==typeof e.icon&&(e.icon=Bo[e.icon]||e.icon);return e}(e))).filter((t=>function(e,{isBlockPluginLoaded:t,isInlinePluginLoaded:i}){const{modelElements:n,name:o}=e;if(!(n&&n.length&&o))return To({style:e}),!1;{const o=[t?"imageBlock":null,i?"imageInline":null];if(!n.some((e=>o.includes(e))))return(0,s.logWarning)("image-style-missing-dependency",{style:e,missingPlugins:n.map((e=>"imageBlock"===e?"ImageBlockEditing":"ImageInlineEditing"))}),!1}return!0}(t,e)))},getDefaultStylesConfiguration:function(e,t){return e&&t?{options:["inline","alignLeft","alignRight","alignCenter","alignBlockLeft","alignBlockRight","block","side"]}:e?{options:["block","side"]}:t?{options:["inline","alignLeft","alignRight"]}:{}},getDefaultDropdownDefinitions:function(e){return e.has("ImageBlockEditing")&&e.has("ImageInlineEditing")?[...Vo]:[]},warnInvalidStyle:To,DEFAULT_OPTIONS:So,DEFAULT_ICONS:Bo,DEFAULT_DROPDOWN_DEFINITIONS:Vo};function Uo(e,t){for(const i of t)if(i.name===e)return i}class Oo extends e.Plugin{static get pluginName(){return"ImageStyleEditing"}static get isOfficialPlugin(){return!0}static get requires(){return[h]}init(){const{normalizeStyles:e,getDefaultStylesConfiguration:t}=zo,i=this.editor,n=i.plugins.has("ImageBlockEditing"),o=i.plugins.has("ImageInlineEditing");i.config.define("image.styles",t(n,o)),this.normalizedStyles=e({configuredStyles:i.config.get("image.styles"),isBlockPluginLoaded:n,isInlinePluginLoaded:o}),this._setupConversion(n,o),this._setupPostFixer(),i.commands.add("imageStyle",new Co(i,this.normalizedStyles))}_setupConversion(e,t){const i=this.editor,n=i.model.schema,o=(r=this.normalizedStyles,(e,t,i)=>{if(!i.consumable.consume(t.item,e.name))return;const n=Uo(t.attributeNewValue,r),o=Uo(t.attributeOldValue,r),a=i.mapper.toViewElement(t.item),s=i.writer;o&&s.removeClass(o.className,a),n&&s.addClass(n.className,a)});var r;const a=function(e){const t={imageInline:e.filter((e=>!e.isDefault&&e.modelElements.includes("imageInline"))),imageBlock:e.filter((e=>!e.isDefault&&e.modelElements.includes("imageBlock")))};return(e,i,n)=>{if(!i.modelRange)return;const o=i.viewItem,r=(0,s.first)(i.modelRange.getItems());if(r&&n.schema.checkAttribute(r,"imageStyle"))for(const e of t[r.name])n.consumable.consume(o,{classes:e.className})&&n.writer.setAttribute("imageStyle",e.name,r)}}(this.normalizedStyles);i.editing.downcastDispatcher.on("attribute:imageStyle",o),i.data.downcastDispatcher.on("attribute:imageStyle",o),e&&(n.extend("imageBlock",{allowAttributes:"imageStyle"}),i.data.upcastDispatcher.on("element:figure",a,{priority:"low"})),t&&(n.extend("imageInline",{allowAttributes:"imageStyle"}),i.data.upcastDispatcher.on("element:img",a,{priority:"low"}))}_setupPostFixer(){const e=this.editor,t=e.model.document,i=e.plugins.get(h),n=new Map(this.normalizedStyles.map((e=>[e.name,e])));t.registerPostFixer((e=>{let o=!1;for(const r of t.differ.getChanges())if("insert"==r.type||"attribute"==r.type&&"imageStyle"==r.attributeKey){let t="insert"==r.type?r.position.nodeAfter:r.range.start.nodeAfter;if(t&&t.is("element","paragraph")&&t.childCount>0&&(t=t.getChild(0)),!i.isImage(t))continue;const a=t.getAttribute("imageStyle");if(!a)continue;const s=n.get(a);s&&s.modelElements.includes(t.name)||(e.removeAttribute("imageStyle",t),o=!0)}return o}))}}var Po=i(895),Ro={attributes:{"data-cke":!0}};Ro.setAttributes=B(),Ro.insert=C().bind(null,"head"),Ro.domAPI=x(),Ro.insertStyleElement=T();y()(Po.A,Ro);Po.A&&Po.A.locals&&Po.A.locals;class jo extends e.Plugin{static get requires(){return[Oo]}static get pluginName(){return"ImageStyleUI"}static get isOfficialPlugin(){return!0}get localizedDefaultStylesTitles(){const e=this.editor.t;return{"Wrap text":e("Wrap text"),"Break text":e("Break text"),"In line":e("In line"),"Full size image":e("Full size image"),"Side image":e("Side image"),"Left aligned image":e("Left aligned image"),"Centered image":e("Centered image"),"Right aligned image":e("Right aligned image")}}init(){const e=this.editor.plugins,t=this.editor.config.get("image.toolbar")||[],i=Fo(e.get("ImageStyleEditing").normalizedStyles,this.localizedDefaultStylesTitles);for(const e of i)this._createButton(e);const n=Fo([...t.filter(pt),...zo.getDefaultDropdownDefinitions(e)],this.localizedDefaultStylesTitles);for(const e of n)this._createDropdown(e,i)}_createDropdown(e,t){const i=this.editor.ui.componentFactory;i.add(e.name,(n=>{let o;const{defaultItem:r,items:a,title:s}=e,l=a.filter((e=>t.find((({name:t})=>No(t)===e)))).map((e=>{const t=i.create(e);return e===r&&(o=t),t}));a.length!==l.length&&zo.warnInvalidStyle({dropdown:e});const c=(0,I.createDropdown)(n,I.SplitButtonView),u=c.buttonView,g=u.arrowView;return(0,I.addToolbarToDropdown)(c,l,{enableActiveItemFocusOnDropdownOpen:!0}),u.set({label:Lo(s,o.label),class:null,tooltip:!0}),g.unbind("label"),g.set({label:s}),u.bind("icon").toMany(l,"isOn",((...e)=>{const t=e.findIndex(Xn);return t<0?o.icon:l[t].icon})),u.bind("label").toMany(l,"isOn",((...e)=>{const t=e.findIndex(Xn);return Lo(s,t<0?o.label:l[t].label)})),u.bind("isOn").toMany(l,"isOn",((...e)=>e.some(Xn))),u.bind("class").toMany(l,"isOn",((...e)=>e.some(Xn)?"ck-splitbutton_flatten":void 0)),u.on("execute",(()=>{l.some((({isOn:e})=>e))?c.isOpen=!c.isOpen:o.fire("execute")})),c.bind("isEnabled").toMany(l,"isEnabled",((...e)=>e.some(Xn))),this.listenTo(c,"execute",(()=>{this.editor.editing.view.focus()})),c}))}_createButton(e){const t=e.name;this.editor.ui.componentFactory.add(No(t),(i=>{const n=this.editor.commands.get("imageStyle"),o=new I.ButtonView(i);return o.set({label:e.title,icon:e.icon,tooltip:!0,isToggleable:!0}),o.bind("isEnabled").to(n,"isEnabled"),o.bind("isOn").to(n,"value",(e=>e===t)),o.on("execute",this._executeCommand.bind(this,t)),o}))}_executeCommand(e){this.editor.execute("imageStyle",{value:e}),this.editor.editing.view.focus()}}function Fo(e,t){for(const i of e)t[i.title]&&(i.title=t[i.title]);return e}function No(e){return`imageStyle:${e}`}function Lo(e,t){return(e?e+": ":"")+t}class Do extends e.Plugin{static get requires(){return[Oo,jo]}static get pluginName(){return"ImageStyle"}static get isOfficialPlugin(){return!0}}class Mo extends e.Plugin{static get requires(){return[l.WidgetToolbarRepository,h]}static get pluginName(){return"ImageToolbar"}static get isOfficialPlugin(){return!0}afterInit(){const e=this.editor,t=e.t,i=e.plugins.get(l.WidgetToolbarRepository),n=e.plugins.get("ImageUtils");var o;i.register("image",{ariaLabel:t("Image toolbar"),items:(o=e.config.get("image.toolbar")||[],o.map((e=>pt(e)?e.name:e))),getRelatedElement:e=>n.getClosestSelectedImageWidget(e)})}}class Wo extends e.Plugin{static get requires(){return[q,h]}static get pluginName(){return"PictureEditing"}static get isOfficialPlugin(){return!0}afterInit(){const e=this.editor;e.plugins.has("ImageBlockEditing")&&e.model.schema.extend("imageBlock",{allowAttributes:["sources"]}),e.plugins.has("ImageInlineEditing")&&e.model.schema.extend("imageInline",{allowAttributes:["sources"]}),this._setupConversion(),this._setupImageUploadEditingIntegration()}_setupConversion(){const e=this.editor,t=e.conversion,i=e.plugins.get("ImageUtils");t.for("upcast").add(function(e){const t=["srcset","media","type","sizes"],i=(i,n,o)=>{const r=n.viewItem;if(!o.consumable.test(r,{name:!0}))return;const a=new Map;for(const e of r.getChildren())if(e.is("element","source")){const i={};for(const n of t)e.hasAttribute(n)&&o.consumable.test(e,{attributes:n})&&(i[n]=e.getAttribute(n));Object.keys(i).length&&a.set(e,i)}const l=e.findViewImgElement(r);if(!l)return;let c=n.modelCursor.parent;if(!c.is("element","imageBlock")){const e=o.convertItem(l,n.modelCursor);n.modelRange=e.modelRange,n.modelCursor=e.modelCursor,c=(0,s.first)(e.modelRange.getItems())}o.consumable.consume(r,{name:!0});for(const[e,t]of a)o.consumable.consume(e,{attributes:Object.keys(t)});a.size&&o.writer.setAttribute("sources",Array.from(a.values()),c),o.convertChildren(r,c)};return e=>{e.on("element:picture",i)}}(i)),t.for("downcast").add(function(e){const t=(t,i,n)=>{if(!n.consumable.consume(i.item,t.name))return;const o=n.writer,r=n.mapper.toViewElement(i.item),a=e.findViewImgElement(r),s=i.attributeNewValue;if(s&&s.length){const e=[];let t=a.parent;for(;t&&t.is("attributeElement");){const i=t.parent;o.unwrap(o.createRangeOn(a),t),e.unshift(t),t=i}const i=a.parent.is("element","picture"),n=i?a.parent:o.createContainerElement("picture",null);i||o.insert(o.createPositionBefore(a),n),o.remove(o.createRangeIn(n)),o.insert(o.createPositionAt(n,"end"),s.map((e=>o.createEmptyElement("source",e)))),o.move(o.createRangeOn(a),o.createPositionAt(n,"end"));for(const t of e)o.wrap(o.createRangeOn(n),t)}else if(a.parent.is("element","picture")){const e=a.parent;o.move(o.createRangeOn(a),o.createPositionBefore(e)),o.remove(e)}};return e=>{e.on("attribute:sources:imageBlock",t),e.on("attribute:sources:imageInline",t)}}(i))}_setupImageUploadEditingIntegration(){const e=this.editor;if(!e.plugins.has("ImageUploadEditing"))return;const t=e.plugins.get("ImageUploadEditing");this.listenTo(t,"uploadComplete",((t,{imageElement:i,data:n})=>{const o=n.sources;o&&e.model.change((e=>{e.setAttributes({sources:o},i)}))}))}}})(),(window.CKEditor5=window.CKEditor5||{}).image=n})();