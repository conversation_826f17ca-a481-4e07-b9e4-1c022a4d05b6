!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{"Cannot access default workspace.":"Cannot access default workspace.","Cannot determine a category for the uploaded file.":"Cannot determine a category for the uploaded file.","Edit image":"Edit image","Failed to determine category of edited image.":"Failed to determine category of edited image.","Open file manager":"Open file manager","Processing the edited image.":"Processing the edited image.","Server failed to process the image.":"Server failed to process the image.","You have no image editing permissions.":"You have no image editing permissions."})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2025, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={21:e=>{"use strict";e.exports=function(e,t){Object.keys(t).forEach((function(i){e.setAttribute(i,t[i])}))}},51:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},128:e=>{"use strict";var t={};e.exports=function(e,i){var r=function(e){if(void 0===t[e]){var i=document.querySelector(e);if(window.HTMLIFrameElement&&i instanceof window.HTMLIFrameElement)try{i=i.contentDocument.head}catch(e){i=null}t[e]=i}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(i)}},237:e=>{"use strict";e.exports=CKEditor5.dll},260:(e,t,i)=>{e.exports=i(237)("./src/upload.js")},311:(e,t,i)=>{e.exports=i(237)("./src/ui.js")},355:(e,t,i)=>{e.exports=i(237)("./src/icons.js")},584:(e,t,i)=>{e.exports=i(237)("./src/utils.js")},591:e=>{"use strict";var t=[];function i(e){for(var i=-1,r=0;r<t.length;r++)if(t[r].identifier===e){i=r;break}return i}function r(e,r){for(var n={},s=[],a=0;a<e.length;a++){var c=e[a],l=r.base?c[0]+r.base:c[0],d=n[l]||0,u="".concat(l," ").concat(d);n[l]=d+1;var g=i(u),m={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==g)t[g].references++,t[g].updater(m);else{var h=o(m,r);r.byIndex=a,t.splice(a,0,{identifier:u,updater:h,references:1})}s.push(u)}return s}function o(e,t){var i=t.domAPI(t);i.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;i.update(e=t)}else i.remove()}}e.exports=function(e,o){var n=r(e=e||[],o=o||{});return function(e){e=e||[];for(var s=0;s<n.length;s++){var a=i(n[s]);t[a].references--}for(var c=r(e,o),l=0;l<n.length;l++){var d=i(n[l]);0===t[d].references&&(t[d].updater(),t.splice(d,1))}n=c}}},639:e=>{"use strict";var t,i=(t=[],function(e,i){return t[e]=i,t.filter(Boolean).join("\n")});function r(e,t,r,o){var n;if(r)n="";else{n="",o.supports&&(n+="@supports (".concat(o.supports,") {")),o.media&&(n+="@media ".concat(o.media," {"));var s=void 0!==o.layer;s&&(n+="@layer".concat(o.layer.length>0?" ".concat(o.layer):""," {")),n+=o.css,s&&(n+="}"),o.media&&(n+="}"),o.supports&&(n+="}")}if(e.styleSheet)e.styleSheet.cssText=i(t,n);else{var a=document.createTextNode(n),c=e.childNodes;c[t]&&e.removeChild(c[t]),c.length?e.insertBefore(a,c[t]):e.appendChild(a)}}var o={singleton:null,singletonCounter:0};e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=o.singletonCounter++,i=o.singleton||(o.singleton=e.insertStyleElement(e));return{update:function(e){r(i,t,!1,e)},remove:function(e){r(i,t,!0,e)}}}},758:e=>{"use strict";e.exports=function(e){return e[1]}},782:(e,t,i)=>{e.exports=i(237)("./src/core.js")},783:(e,t,i)=>{e.exports=i(237)("./src/engine.js")},935:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var i="",r=void 0!==t[5];return t[4]&&(i+="@supports (".concat(t[4],") {")),t[2]&&(i+="@media ".concat(t[2]," {")),r&&(i+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),i+=e(t),r&&(i+="}"),t[2]&&(i+="}"),t[4]&&(i+="}"),i})).join("")},t.i=function(e,i,r,o,n){"string"==typeof e&&(e=[[null,e,void 0]]);var s={};if(r)for(var a=0;a<this.length;a++){var c=this[a][0];null!=c&&(s[c]=!0)}for(var l=0;l<e.length;l++){var d=[].concat(e[l]);r&&s[d[0]]||(void 0!==n&&(void 0===d[5]||(d[1]="@layer".concat(d[5].length>0?" ".concat(d[5]):""," {").concat(d[1],"}")),d[5]=n),i&&(d[2]?(d[1]="@media ".concat(d[2]," {").concat(d[1],"}"),d[2]=i):d[2]=i),o&&(d[4]?(d[1]="@supports (".concat(d[4],") {").concat(d[1],"}"),d[4]=o):d[4]="".concat(o)),t.push(d))}},t}},957:(e,t,i)=>{"use strict";i.d(t,{A:()=>a});var r=i(758),o=i.n(r),n=i(935),s=i.n(n)()(o());s.push([e.id,':root{--ck-image-processing-highlight-color:#f9fafa;--ck-image-processing-background-color:#e3e5e8}.ck.ck-editor__editable .image.image-processing{position:relative}.ck.ck-editor__editable .image.image-processing:before{animation:ck-image-processing-animation 2s linear infinite;background:linear-gradient(90deg,var(--ck-image-processing-background-color),var(--ck-image-processing-highlight-color),var(--ck-image-processing-background-color));background-size:200% 100%;content:"";height:100%;left:0;position:absolute;top:0;width:100%;z-index:1}.ck.ck-editor__editable .image.image-processing img{height:100%}@keyframes ck-image-processing-animation{0%{background-position:200% 0}to{background-position:-200% 0}}',""]);const a=s}},t={};function i(r){var o=t[r];if(void 0!==o)return o.exports;var n=t[r]={id:r,exports:{}};return e[r](n,n.exports,i),n.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{"use strict";i.r(r),i.d(r,{CKBox:()=>F,CKBoxEditing:()=>P,CKBoxImageEdit:()=>ae,CKBoxImageEditEditing:()=>W,CKBoxImageEditUI:()=>Y,CKBoxUI:()=>n});var e=i(782),t=i(311),o=i(355);class n extends e.Plugin{static get pluginName(){return"CKBoxUI"}static get isOfficialPlugin(){return!0}afterInit(){const e=this.editor;e.commands.get("ckbox")&&(e.ui.componentFactory.add("ckbox",(()=>this._createFileToolbarButton())),e.ui.componentFactory.add("menuBar:ckbox",(()=>this._createFileMenuBarButton())),e.plugins.has("ImageInsertUI")&&e.plugins.get("ImageInsertUI").registerIntegration({name:"assetManager",observable:()=>e.commands.get("ckbox"),buttonViewCreator:()=>this._createImageToolbarButton(),formViewCreator:()=>this._createImageDropdownButton(),menuBarButtonViewCreator:e=>this._createImageMenuBarButton(e?"insertOnly":"insertNested")}))}_createButton(e){const t=this.editor,i=new e(t.locale),r=t.commands.get("ckbox");return i.bind("isOn","isEnabled").to(r,"value","isEnabled"),i.on("execute",(()=>{t.execute("ckbox")})),i}_createFileToolbarButton(){const e=this.editor.locale.t,i=this._createButton(t.ButtonView);return i.icon=o.IconBrowseFiles,i.label=e("Open file manager"),i.tooltip=!0,i}_createImageToolbarButton(){const e=this.editor.locale.t,i=this.editor.plugins.get("ImageInsertUI"),r=this._createButton(t.ButtonView);return r.icon=o.IconImageAssetManager,r.bind("label").to(i,"isImageSelected",(t=>e(t?"Replace image with file manager":"Insert image with file manager"))),r.tooltip=!0,r}_createImageDropdownButton(){const e=this.editor.locale.t,i=this.editor.plugins.get("ImageInsertUI"),r=this._createButton(t.ButtonView);return r.icon=o.IconImageAssetManager,r.withText=!0,r.bind("label").to(i,"isImageSelected",(t=>e(t?"Replace with file manager":"Insert with file manager"))),r.on("execute",(()=>{i.dropdownView.isOpen=!1})),r}_createFileMenuBarButton(){const e=this.editor.locale.t,i=this._createButton(t.MenuBarMenuListItemButtonView);return i.icon=o.IconBrowseFiles,i.withText=!0,i.label=e("File"),i}_createImageMenuBarButton(e){const i=this.editor.locale.t,r=this.editor.locale.t,n=this._createButton(t.MenuBarMenuListItemButtonView);switch(n.icon=o.IconImageAssetManager,n.withText=!0,e){case"insertOnly":n.label=i("Image");break;case"insertNested":n.label=r("With file manager")}return n}}var s=i(783),a=i(584),c=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","#","$","%","*","+",",","-",".",":",";","=","?","@","[","]","^","_","{","|","}","~"],l=e=>{let t=0;for(let i=0;i<e.length;i++){let r=e[i];t=83*t+c.indexOf(r)}return t},d=e=>{let t=e/255;return t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)},u=e=>{let t=Math.max(0,Math.min(1,e));return t<=.0031308?Math.trunc(12.92*t*255+.5):Math.trunc(255*(1.055*Math.pow(t,.4166666666666667)-.055)+.5)},g=(e,t)=>(e=>e<0?-1:1)(e)*Math.pow(Math.abs(e),t),m=class extends Error{constructor(e){super(e),this.name="ValidationError",this.message=e}},h=e=>{if(!e||e.length<6)throw new m("The blurhash string must be at least 6 characters");let t=l(e[0]),i=Math.floor(t/9)+1,r=t%9+1;if(e.length!==4+2*r*i)throw new m(`blurhash length mismatch: length is ${e.length} but it should be ${4+2*r*i}`)},p=e=>{let t=e>>8&255,i=255&e;return[d(e>>16),d(t),d(i)]},f=(e,t)=>{let i=Math.floor(e/361),r=Math.floor(e/19)%19,o=e%19;return[g((i-9)/9,2)*t,g((r-9)/9,2)*t,g((o-9)/9,2)*t]},b=(e,t,i,r)=>{h(e),r|=1;let o=l(e[0]),n=Math.floor(o/9)+1,s=o%9+1,a=(l(e[1])+1)/166,c=new Array(s*n);for(let t=0;t<c.length;t++)if(0===t){let i=l(e.substring(2,6));c[t]=p(i)}else{let i=l(e.substring(4+2*t,6+2*t));c[t]=f(i,a*r)}let d=4*t,g=new Uint8ClampedArray(d*i);for(let e=0;e<i;e++)for(let r=0;r<t;r++){let o=0,a=0,l=0;for(let d=0;d<n;d++)for(let n=0;n<s;n++){let u=Math.cos(Math.PI*r*n/t)*Math.cos(Math.PI*e*d/i),g=c[n+d*s];o+=g[0]*u,a+=g[1]*u,l+=g[2]*u}let m=u(o),h=u(a),p=u(l);g[4*r+0+e*d]=m,g[4*r+1+e*d]=h,g[4*r+2+e*d]=p,g[4*r+3+e*d]=255}return g};function k(e){const t=[];let i=0;for(const r in e){const o=parseInt(r,10);isNaN(o)||(o>i&&(i=o),t.push(`${e[r]} ${r}w`))}const r=[{srcset:t.join(","),sizes:`(max-width: ${i}px) 100vw, ${i}px`,type:"image/webp"}];return{imageFallbackUrl:e.default,imageSources:r}}function w({url:e,method:t="GET",data:i,onUploadProgress:r,signal:o,authorization:n}){const s=new XMLHttpRequest;s.open(t,e.toString()),s.setRequestHeader("Authorization",n),s.setRequestHeader("CKBox-Version","CKEditor 5"),s.responseType="json";const a=()=>{s.abort()};return new Promise(((e,t)=>{o.throwIfAborted(),o.addEventListener("abort",a),s.addEventListener("loadstart",(()=>{o.addEventListener("abort",a)})),s.addEventListener("loadend",(()=>{o.removeEventListener("abort",a)})),s.addEventListener("error",(()=>{t()})),s.addEventListener("abort",(()=>{t()})),s.addEventListener("load",(()=>{const i=s.response;if(!i||i.statusCode>=400)return t(i&&i.message);e(i)})),r&&s.upload.addEventListener("progress",(e=>{r(e)})),s.send(i)}))}const I={"image/gif":"gif","image/jpeg":"jpg","image/png":"png","image/webp":"webp","image/bmp":"bmp","image/tiff":"tiff"};class x extends e.Command{_chosenAssets=new Set;_wrapper=null;constructor(e){super(e),this._initListeners()}refresh(){this.value=this._getValue(),this.isEnabled=this._checkEnabled()}execute(){this.fire("ckbox:open")}_getValue(){return null!==this._wrapper}_checkEnabled(){const e=this.editor.commands.get("insertImage"),t=this.editor.commands.get("link");return!(!e.isEnabled&&!t.isEnabled)}_prepareOptions(){const e=this.editor.config.get("ckbox"),t=e.dialog,i=e.categories,r=e.view,o=e.upload;return{theme:e.theme,language:e.language,tokenUrl:e.tokenUrl,serviceOrigin:e.serviceOrigin,forceDemoLabel:e.forceDemoLabel,choosableFileExtensions:e.choosableFileExtensions,assets:{onChoose:e=>this.fire("ckbox:choose",e)},dialog:{onClose:()=>this.fire("ckbox:close"),width:t&&t.width,height:t&&t.height},categories:i&&{icons:i.icons},view:r&&{openLastView:r.openLastView,startupFolderId:r.startupFolderId,startupCategoryId:r.startupCategoryId,hideMaximizeButton:r.hideMaximizeButton},upload:o&&{componentsHideTimeout:o.componentsHideTimeout,dialogMinimizeTimeout:o.dialogMinimizeTimeout}}}_initListeners(){const e=this.editor,t=e.model,i=!e.config.get("ckbox.ignoreDataId"),r=e.config.get("ckbox.downloadableFiles");this.on("ckbox",(()=>{this.refresh()}),{priority:"low"}),this.on("ckbox:open",(()=>{this.isEnabled&&!this.value&&(this._wrapper=(0,a.createElement)(document,"div",{class:"ck ckbox-wrapper"}),document.body.appendChild(this._wrapper),window.CKBox.mount(this._wrapper,this._prepareOptions()))})),this.on("ckbox:close",(()=>{this.value&&(this._wrapper.remove(),this._wrapper=null,e.editing.view.focus())})),this.on("ckbox:choose",((o,n)=>{if(!this.isEnabled)return;const s=e.commands.get("insertImage"),a=e.commands.get("link"),c=function({downloadableFilesConfig:e,assets:t,isImageAllowed:i,isLinkAllowed:r}){return t.map((t=>function(e){const t=e.data.metadata;if(!t)return!1;return t.width&&t.height}(t)?{id:t.data.id,type:"image",attributes:v(t)}:{id:t.data.id,type:"link",attributes:y(t,e)})).filter((e=>"image"===e.type?i:r))}({assets:n,downloadableFilesConfig:r,isImageAllowed:s.isEnabled,isLinkAllowed:a.isEnabled}),l=c.length;0!==l&&(t.change((e=>{for(const t of c){const r=t===c[l-1],o=1===l;this._insertAsset(t,r,e,o),i&&(setTimeout((()=>this._chosenAssets.delete(t)),1e3),this._chosenAssets.add(t))}})),e.editing.view.focus())})),this.listenTo(e,"destroy",(()=>{this.fire("ckbox:close"),this._chosenAssets.clear()}))}_insertAsset(e,t,i,r){const o=this.editor.model.document.selection;i.removeSelectionAttribute("linkHref"),"image"===e.type?this._insertImage(e):this._insertLink(e,i,r),t||i.setSelection(o.getLastPosition())}_insertImage(e){const t=this.editor,{imageFallbackUrl:i,imageSources:r,imageTextAlternative:o,imageWidth:n,imageHeight:s,imagePlaceholder:a}=e.attributes;t.execute("insertImage",{source:{src:i,sources:r,alt:o,width:n,height:s,...a?{placeholder:a}:null}})}_insertLink(e,t,i){const r=this.editor,o=r.model,n=o.document.selection,{linkName:s,linkHref:c}=e.attributes;if(n.isCollapsed){const e=(0,a.toMap)(n.getAttributes()),l=t.createText(s,e);if(!i){const e=n.getLastPosition(),i=e.parent;"paragraph"===i.name&&i.isEmpty||r.execute("insertParagraph",{position:e});const s=o.insertContent(l);return t.setSelection(s),void r.execute("link",c)}const d=o.insertContent(l);t.setSelection(d)}r.execute("link",c)}}function v(e){const{imageFallbackUrl:t,imageSources:i}=k(e.data.imageUrls),{description:r,width:o,height:n,blurHash:s}=e.data.metadata,a=function(e){if(e)try{const t="32px",i=document.createElement("canvas");i.setAttribute("width",t),i.setAttribute("height",t);const r=i.getContext("2d");if(!r)return;const o=r.createImageData(32,32),n=b(e,32,32);return o.data.set(n),r.putImageData(o,0,0),i.toDataURL()}catch{return}}(s);return{imageFallbackUrl:t,imageSources:i,imageTextAlternative:r||"",imageWidth:o,imageHeight:n,...a?{imagePlaceholder:a}:null}}function y(e,t){return{linkName:e.data.name,linkHref:_(e,t)}}function _(e,t){const i=new URL(e.data.url);return function(e,t){if("function"==typeof t)return t(e);return!0}(e,t)&&i.searchParams.set("download","true"),i.toString()}var A=i(260);class E extends e.Plugin{_token;static get pluginName(){return"CKBoxUtils"}static get isOfficialPlugin(){return!0}static get requires(){return["CloudServices"]}init(){const e=this.editor,t=!!e.config.get("ckbox"),i=!!window.CKBox;if(!t&&!i)return;e.config.define("ckbox",{serviceOrigin:"https://api.ckbox.io",defaultUploadCategories:null,ignoreDataId:!1,language:e.locale.uiLanguage,theme:"lark",tokenUrl:e.config.get("cloudServices.tokenUrl")});const r=e.plugins.get("CloudServices"),o=e.config.get("cloudServices.tokenUrl"),n=e.config.get("ckbox.tokenUrl");if(!n)throw new a.CKEditorError("ckbox-plugin-missing-token-url",this);this._token=n==o?Promise.resolve(r.token):r.registerTokenUrl(n),this._token=this._token.then((async e=>(await this._authorizePrivateCategoriesAccess(e.value),e)))}getToken(){return this._token}async getWorkspaceId(){const e=(0,this.editor.t)("Cannot access default workspace."),t=this.editor.config.get("ckbox.defaultUploadWorkspaceId"),i=function(e,t){const[,i]=e.value.split("."),r=JSON.parse(atob(i)),o=r.auth?.ckbox?.workspaces||[r.aud];return t?"superadmin"==r.auth?.ckbox?.role||o.includes(t)?t:null:o[0]}(await this._token,t);if(null==i)throw(0,a.logError)("ckbox-access-default-workspace-error"),e;return i}async getCategoryIdForFile(e,t){const i=(0,this.editor.t)("Cannot determine a category for the uploaded file."),r=this.editor.config.get("ckbox.defaultUploadCategories"),o=this._getAvailableCategories(t),n="string"==typeof e?(s=await async function(e,t){try{const i=await fetch(e,{method:"HEAD",cache:"force-cache",...t});return i.ok&&i.headers.get("content-type")||""}catch{return""}}(e,t),I[s]):e.name.match(/\.(?<ext>[^.]+)$/).groups.ext.toLowerCase();var s;const a=await o;if(!a)throw i;if(r){const e=Object.keys(r).find((e=>r[e].find((e=>e.toLowerCase()==n))));if(e){const t=a.find((t=>t.id===e||t.name===e));if(!t)throw i;return t.id}}const c=a.find((e=>e.extensions.find((e=>e.toLowerCase()==n))));if(!c)throw i;return c.id}async _getAvailableCategories(e){const t=this.editor,i=this._token,{signal:r}=e,o=t.config.get("ckbox.serviceOrigin"),n=await this.getWorkspaceId();try{const e=[];let t,i=0;do{const r=await s(i);e.push(...r.items),t=r.totalCount-(i+50),i+=50}while(t>0);return e}catch{return r.throwIfAborted(),void(0,a.logError)("ckbox-fetch-category-http-error")}async function s(e){const t=new URL("categories",o);return t.searchParams.set("limit",String(50)),t.searchParams.set("offset",String(e)),t.searchParams.set("workspaceId",n),w({url:t,signal:r,authorization:(await i).value})}}async _authorizePrivateCategoriesAccess(e){const t=this.editor.config.get("ckbox.serviceOrigin"),i=new FormData;i.set("token",e),await fetch(`${t}/categories/authorizePrivateAccess`,{method:"POST",credentials:"include",mode:"no-cors",body:i})}}class C extends e.Plugin{static get requires(){return["ImageUploadEditing","ImageUploadProgress",A.FileRepository,P]}static get pluginName(){return"CKBoxUploadAdapter"}static get isOfficialPlugin(){return!0}async afterInit(){const e=this.editor,t=!!e.config.get("ckbox"),i=!!window.CKBox;if(!t&&!i)return;const r=e.plugins.get(A.FileRepository),o=e.plugins.get(E);r.createUploadAdapter=t=>new O(t,e,o);const n=!e.config.get("ckbox.ignoreDataId"),s=e.plugins.get("ImageUploadEditing");n&&s.on("uploadComplete",((t,{imageElement:i,data:r})=>{e.model.change((e=>{e.setAttribute("ckboxImageId",r.ckboxImageId,i)}))}))}}class O{loader;token;editor;controller;serviceOrigin;ckboxUtils;constructor(e,t,i){this.loader=e,this.token=i.getToken(),this.ckboxUtils=i,this.editor=t,this.controller=new AbortController,this.serviceOrigin=t.config.get("ckbox.serviceOrigin")}async upload(){const e=this.ckboxUtils,t=this.editor.t,i=await this.loader.file,r=await e.getCategoryIdForFile(i,{signal:this.controller.signal}),o=new URL("assets",this.serviceOrigin),n=new FormData;o.searchParams.set("workspaceId",await e.getWorkspaceId()),n.append("categoryId",r),n.append("file",i);return w({method:"POST",url:o,data:n,onUploadProgress:e=>{e.lengthComputable&&(this.loader.uploadTotal=e.total,this.loader.uploaded=e.loaded)},signal:this.controller.signal,authorization:(await this.token).value}).then((async e=>{const t=k(e.imageUrls);return{ckboxImageId:e.id,default:t.imageFallbackUrl,sources:t.imageSources}})).catch((()=>{const e=t("Cannot upload file:")+` ${i.name}.`;return Promise.reject(e)}))}abort(){this.controller.abort()}}const B="NoPermission";class P extends e.Plugin{static get pluginName(){return"CKBoxEditing"}static get isOfficialPlugin(){return!0}static get requires(){return["LinkEditing","PictureEditing",C,E]}init(){const e=this.editor;this._shouldBeInitialised()&&(this._checkImagePlugins(),L()&&e.commands.add("ckbox",new x(e)),async function(e){const t=e.plugins.get(E),i=e.config.get("ckbox.serviceOrigin"),r=new URL("permissions",i),{value:o}=await t.getToken(),n=await w({url:r,authorization:o,signal:(new AbortController).signal});return Object.values(n).some((e=>e["asset:create"]))}(e).then((e=>{e||this._blockImageCommands()})))}afterInit(){const e=this.editor;this._shouldBeInitialised()&&(e.config.get("ckbox.ignoreDataId")||(this._initSchema(),this._initConversion(),this._initFixers()))}_shouldBeInitialised(){return!!this.editor.config.get("ckbox")||L()}_blockImageCommands(){const e=this.editor,t=e.commands.get("uploadImage"),i=e.commands.get("ckboxImageEdit");t&&(t.isAccessAllowed=!1,t.forceDisabled(B)),i&&i.forceDisabled(B)}_checkImagePlugins(){const e=this.editor;e.plugins.has("ImageBlockEditing")||e.plugins.has("ImageInlineEditing")||(0,a.logError)("ckbox-plugin-image-feature-missing",e)}_initSchema(){const e=this.editor.model.schema;e.extend("$text",{allowAttributes:"ckboxLinkId"}),e.isRegistered("imageBlock")&&e.extend("imageBlock",{allowAttributes:["ckboxImageId","ckboxLinkId"]}),e.isRegistered("imageInline")&&e.extend("imageInline",{allowAttributes:["ckboxImageId","ckboxLinkId"]}),e.addAttributeCheck((e=>{if(!e.last.getAttribute("linkHref"))return!1}),"ckboxLinkId")}_initConversion(){const e=this.editor;e.conversion.for("downcast").add((e=>{e.on("attribute:ckboxLinkId:imageBlock",((e,t,i)=>{const{writer:r,mapper:o,consumable:n}=i;if(!n.consume(t.item,e.name))return;const s=[...o.toViewElement(t.item).getChildren()].find((e=>"a"===e.name));s&&(t.item.hasAttribute("ckboxLinkId")?r.setAttribute("data-ckbox-resource-id",t.item.getAttribute("ckboxLinkId"),s):r.removeAttribute("data-ckbox-resource-id",s))}),{priority:"low"}),e.on("attribute:ckboxLinkId",((e,t,i)=>{const{writer:r,mapper:o,consumable:n}=i;if(n.consume(t.item,e.name)){if(t.attributeOldValue){const e=j(r,t.attributeOldValue);r.unwrap(o.toViewRange(t.range),e)}if(t.attributeNewValue){const e=j(r,t.attributeNewValue);if(t.item.is("selection")){const t=r.document.selection;r.wrap(t.getFirstRange(),e)}else r.wrap(o.toViewRange(t.range),e)}}}),{priority:"low"})})),e.conversion.for("upcast").add((e=>{e.on("element:a",((e,t,i)=>{const{writer:r,consumable:o}=i;if(!t.viewItem.getAttribute("href"))return;if(!o.consume(t.viewItem,{attributes:["data-ckbox-resource-id"]}))return;const n=t.viewItem.getAttribute("data-ckbox-resource-id");if(n)if(t.modelRange)for(let e of t.modelRange.getItems())e.is("$textProxy")&&(e=e.textNode),U(e)&&r.setAttribute("ckboxLinkId",n,e);else{const e=t.modelCursor.nodeBefore||t.modelCursor.parent;r.setAttribute("ckboxLinkId",n,e)}}),{priority:"low"})})),e.conversion.for("downcast").attributeToAttribute({model:"ckboxImageId",view:"data-ckbox-resource-id"}),e.conversion.for("upcast").elementToAttribute({model:{key:"ckboxImageId",value:e=>e.getAttribute("data-ckbox-resource-id")},view:{attributes:{"data-ckbox-resource-id":/[\s\S]+/}}});const t=e.commands.get("replaceImageSource");t&&this.listenTo(t,"cleanupImage",((e,[t,i])=>{t.removeAttribute("ckboxImageId",i)}))}_initFixers(){const e=this.editor,t=e.model,i=t.document.selection;t.document.registerPostFixer(function(e){return t=>{let i=!1;const r=e.model,o=e.commands.get("ckbox");if(!o)return i;for(const e of r.document.differ.getChanges()){if("insert"!==e.type&&"attribute"!==e.type)continue;const r="insert"===e.type?new s.Range(e.position,e.position.getShiftedBy(e.length)):e.range,n="attribute"===e.type&&"linkHref"===e.attributeKey&&null===e.attributeNewValue;for(const e of r.getItems()){if(n&&e.hasAttribute("ckboxLinkId")){t.removeAttribute("ckboxLinkId",e),i=!0;continue}const r=S(e,o._chosenAssets);for(const o of r){const r="image"===o.type?"ckboxImageId":"ckboxLinkId";o.id!==e.getAttribute(r)&&(t.setAttribute(r,o.id,e),i=!0)}}}return i}}(e)),t.document.registerPostFixer(function(e){return t=>!(e.hasAttribute("linkHref")||!e.hasAttribute("ckboxLinkId"))&&(t.removeSelectionAttribute("ckboxLinkId"),!0)}(i))}}function S(e,t){const i=e.is("element","imageInline")||e.is("element","imageBlock"),r=e.hasAttribute("linkHref");return[...t].filter((t=>"image"===t.type&&i?t.attributes.imageFallbackUrl===e.getAttribute("src"):"link"===t.type&&r?t.attributes.linkHref===e.getAttribute("linkHref"):void 0))}function j(e,t){const i=e.createAttributeElement("a",{"data-ckbox-resource-id":t},{priority:5});return e.setCustomProperty("link",!0,i),i}function U(e){return!!e.is("$text")||!(!e.is("element","imageInline")&&!e.is("element","imageBlock"))}function L(){return!!window.CKBox}class F extends e.Plugin{static get pluginName(){return"CKBox"}static get isOfficialPlugin(){return!0}static get requires(){return[P,n]}}function T(e){if(!e||"object"!=typeof e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}function M(e){return Object.getOwnPropertySymbols(e).filter((t=>Object.prototype.propertyIsEnumerable.call(e,t)))}function N(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}const R="[object Arguments]",K="[object Object]";function V(e,t,i,r,o,n,s){const a=s(e,t,i,r,o,n);if(void 0!==a)return a;if(typeof e==typeof t)switch(typeof e){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return e===t;case"number":return e===t||Object.is(e,t);case"object":return D(e,t,n,s)}return D(e,t,n,s)}function D(e,t,i,r){if(Object.is(e,t))return!0;let o=N(e),n=N(t);if(o===R&&(o=K),n===R&&(n=K),o!==n)return!1;switch(o){case"[object String]":return e.toString()===t.toString();case"[object Number]":{const i=e.valueOf(),r=t.valueOf();return(s=i)===(a=r)||Number.isNaN(s)&&Number.isNaN(a)}case"[object Boolean]":case"[object Date]":case"[object Symbol]":return Object.is(e.valueOf(),t.valueOf());case"[object RegExp]":return e.source===t.source&&e.flags===t.flags;case"[object Function]":return e===t}var s,a;const c=(i=i??new Map).get(e),l=i.get(t);if(null!=c&&null!=l)return c===t;i.set(e,t),i.set(t,e);try{switch(o){case"[object Map]":if(e.size!==t.size)return!1;for(const[o,n]of e.entries())if(!t.has(o)||!V(n,t.get(o),o,e,t,i,r))return!1;return!0;case"[object Set]":{if(e.size!==t.size)return!1;const o=Array.from(e.values()),n=Array.from(t.values());for(let s=0;s<o.length;s++){const a=o[s],c=n.findIndex((o=>V(a,o,void 0,e,t,i,r)));if(-1===c)return!1;n.splice(c,1)}return!0}case"[object Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":case"[object BigUint64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object BigInt64Array]":case"[object Float32Array]":case"[object Float64Array]":if("undefined"!=typeof Buffer&&Buffer.isBuffer(e)!==Buffer.isBuffer(t))return!1;if(e.length!==t.length)return!1;for(let o=0;o<e.length;o++)if(!V(e[o],t[o],o,e,t,i,r))return!1;return!0;case"[object ArrayBuffer]":return e.byteLength===t.byteLength&&D(new Uint8Array(e),new Uint8Array(t),i,r);case"[object DataView]":return e.byteLength===t.byteLength&&e.byteOffset===t.byteOffset&&D(new Uint8Array(e),new Uint8Array(t),i,r);case"[object Error]":return e.name===t.name&&e.message===t.message;case K:{if(!(D(e.constructor,t.constructor,i,r)||T(e)&&T(t)))return!1;const o=[...Object.keys(e),...M(e)],n=[...Object.keys(t),...M(t)];if(o.length!==n.length)return!1;for(let n=0;n<o.length;n++){const s=o[n],a=e[s];if(!Object.hasOwn(t,s))return!1;if(!V(a,t[s],s,e,t,i,r))return!1}return!0}default:return!1}}finally{i.delete(e),i.delete(t)}}function H(){}function z(e,t){return function(e,t,i){return V(e,t,void 0,void 0,void 0,void 0,i)}(e,t,H)}function $(e){if(Array.isArray(e)){const t=e.map($);return e=>t.some((t=>t(e)))}if("origin"==e){const e=a.global.window.location.origin;return t=>new URL(t,a.global.document.baseURI).origin==e}if("function"==typeof e)return e;if(e instanceof RegExp)return t=>!(!t.match(e)&&!t.replace(/^https?:\/\//,"").match(e));return()=>!1}class q extends e.Command{_wrapper=null;_processInProgress=new Set;_canEdit;_prepareOptions;_updateUiDelayed=(0,a.delay)((()=>this.editor.ui.update()),0);constructor(e){super(e),this.value=!1,this._canEdit=function(e){const t=$(e);return e=>!(!e.is("element","imageInline")&&!e.is("element","imageBlock"))&&(!!e.hasAttribute("ckboxImageId")||!!e.hasAttribute("src")&&t(e.getAttribute("src")))}(e.config.get("ckbox.allowExternalImagesEditing")),this._prepareOptions=(0,a.abortableDebounce)(((e,t)=>this._prepareOptionsAbortable(e,t))),this._prepareListeners()}refresh(){const e=this.editor;this.value=this._getValue();const t=e.model.document.selection.getSelectedElement();this.isEnabled=!!t&&this._canEdit(t)&&!this._checkIfElementIsBeingProcessed(t)}execute(){if(this._getValue())return;const e=(0,a.createElement)(document,"div",{class:"ck ckbox-wrapper"});this._wrapper=e,this.value=!0,document.body.appendChild(this._wrapper);const i={element:this.editor.model.document.selection.getSelectedElement(),controller:new AbortController};this._prepareOptions(i).then((t=>window.CKBox.mountImageEditor(e,t)),(e=>{const i=this.editor,r=i.t;i.plugins.get(t.Notification).showWarning(r("Failed to determine category of edited image."),{namespace:"ckbox"}),console.error(e),this._handleImageEditorClose()}))}destroy(){this._handleImageEditorClose(),this._prepareOptions.abort(),this._updateUiDelayed.cancel();for(const e of this._processInProgress.values())e.controller.abort();super.destroy()}_getValue(){return null!==this._wrapper}async _prepareOptionsAbortable(e,t){const i=this.editor,r=i.config.get("ckbox"),o=i.plugins.get(E),{element:n}=t;let s;const a=n.getAttribute("ckboxImageId");if(a)s={assetId:a};else{const t=new URL(n.getAttribute("src"),document.baseURI).href;s={imageUrl:t,uploadCategoryId:await o.getCategoryIdForFile(t,{signal:e})}}return{...s,imageEditing:{allowOverwrite:!1},tokenUrl:r.tokenUrl,...r.serviceOrigin&&{serviceOrigin:r.serviceOrigin},onClose:()=>this._handleImageEditorClose(),onSave:e=>this._handleImageEditorSave(t,e)}}_prepareListeners(){this.listenTo(this.editor.model.document,"change:data",(()=>{this._getProcessingStatesOfDeletedImages().forEach((e=>{e.controller.abort()}))}))}_getProcessingStatesOfDeletedImages(){const e=[];for(const t of this._processInProgress.values())"$graveyard"==t.element.root.rootName&&e.push(t);return e}_checkIfElementIsBeingProcessed(e){for(const{element:t}of this._processInProgress)if(z(t,e))return!0;return!1}_handleImageEditorClose(){this._wrapper&&(this._wrapper.remove(),this._wrapper=null,this.editor.editing.view.focus(),this._updateUiDelayed(),this.refresh())}_handleImageEditorSave(i,r){const o=this.editor.locale.t,n=this.editor.plugins.get(t.Notification),s=this.editor.plugins.get(e.PendingActions),c=s.add(o("Processing the edited image."));this._processInProgress.add(i),this._showImageProcessingIndicator(i.element,r),this.refresh(),this._waitForAssetProcessed(r.data.id,i.controller.signal).then((e=>{this._replaceImage(i.element,e)}),(e=>{this.editor.editing.reconvertItem(i.element),i.controller.signal.aborted||(!e||e instanceof a.CKEditorError?n.showWarning(o("Server failed to process the image."),{namespace:"ckbox"}):console.error(e))})).finally((()=>{this._processInProgress.delete(i),s.remove(c),this.refresh()}))}async _getAssetStatusFromServer(e,t){const i=this.editor.plugins.get(E),r=new URL("assets/"+e,this.editor.config.get("ckbox.serviceOrigin")),o=await w({url:r,signal:t,authorization:(await i.getToken()).value}),n=o.metadata.metadataProcessingStatus;if(!n||"queued"==n)throw new a.CKEditorError("ckbox-image-not-processed");return{data:{...o}}}async _waitForAssetProcessed(e,t){const i=await(0,a.retry)((()=>this._getAssetStatusFromServer(e,t)),{signal:t,maxAttempts:5});if("success"!=i.data.metadata.metadataProcessingStatus)throw new a.CKEditorError("ckbox-image-processing-failed");return i}_showImageProcessingIndicator(e,t){const i=this.editor;i.editing.view.change((r=>{const o=i.editing.mapper.toViewElement(e),n=this.editor.plugins.get("ImageUtils").findViewImgElement(o);r.removeStyle("aspect-ratio",n),r.setAttribute("width",t.data.metadata.width,n),r.setAttribute("height",t.data.metadata.height,n),r.setStyle("width",`${t.data.metadata.width}px`,n),r.setStyle("height",`${t.data.metadata.height}px`,n),r.addClass("image-processing",o)}))}_replaceImage(e,t){const i=this.editor,{imageFallbackUrl:r,imageSources:o,imageWidth:n,imageHeight:s,imagePlaceholder:a}=v(t),c=Array.from(i.model.document.selection.getRanges());i.model.change((l=>{l.setSelection(e,"on"),i.execute("insertImage",{imageType:e.is("element","imageInline")?"imageInline":null,source:{src:r,sources:o,width:n,height:s,...a?{placeholder:a}:null,...e.hasAttribute("alt")?{alt:e.getAttribute("alt")}:null}});const d=e.getChildren();e=i.model.document.selection.getSelectedElement();for(const t of d)l.append(l.cloneElement(t),e);l.setAttribute("ckboxImageId",t.data.id,e),l.setSelection(c)}))}}class W extends e.Plugin{static get pluginName(){return"CKBoxImageEditEditing"}static get isOfficialPlugin(){return!0}static get requires(){return[P,E,e.PendingActions,t.Notification,"ImageUtils","ImageEditing"]}init(){const{editor:e}=this;e.commands.add("ckboxImageEdit",new q(e))}}class Y extends e.Plugin{static get pluginName(){return"CKBoxImageEditUI"}static get isOfficialPlugin(){return!0}init(){const e=this.editor;e.ui.componentFactory.add("ckboxImageEdit",(i=>{const r=e.commands.get("ckboxImageEdit"),n=e.commands.get("uploadImage"),s=new t.ButtonView(i),a=i.t;return s.set({icon:o.IconCkboxImageEdit,tooltip:!0}),s.bind("label").to(n,"isAccessAllowed",(e=>a(e?"Edit image":"You have no image editing permissions."))),s.bind("isOn").to(r,"value",r,"isEnabled",((e,t)=>e&&t)),s.bind("isEnabled").to(r),this.listenTo(s,"execute",(()=>{e.execute("ckboxImageEdit"),e.editing.view.focus()})),s}))}}var G=i(591),J=i.n(G),X=i(639),Q=i.n(X),Z=i(128),ee=i.n(Z),te=i(21),ie=i.n(te),re=i(51),oe=i.n(re),ne=i(957),se={attributes:{"data-cke":!0}};se.setAttributes=ie(),se.insert=ee().bind(null,"head"),se.domAPI=Q(),se.insertStyleElement=oe();J()(ne.A,se);ne.A&&ne.A.locals&&ne.A.locals;class ae extends e.Plugin{static get pluginName(){return"CKBoxImageEdit"}static get isOfficialPlugin(){return!0}static get requires(){return[W,Y]}}})(),(window.CKEditor5=window.CKEditor5||{}).ckbox=r})();