{"version": 3, "file": "position-min.js", "names": ["factory", "define", "amd", "j<PERSON><PERSON><PERSON>", "$", "cachedScrollbarWidth", "max", "Math", "abs", "rhorizontal", "rvertical", "roffset", "rposition", "rpercent", "_position", "fn", "position", "getOffsets", "offsets", "width", "height", "parseFloat", "test", "parseCss", "element", "property", "parseInt", "css", "isWindow", "obj", "window", "getDimensions", "elem", "raw", "nodeType", "offset", "top", "left", "scrollTop", "scrollLeft", "preventDefault", "pageY", "pageX", "outerWidth", "outerHeight", "scrollbarWidth", "undefined", "w1", "w2", "div", "innerDiv", "children", "append", "offsetWidth", "clientWidth", "remove", "getScrollInfo", "within", "overflowX", "isDocument", "overflowY", "hasOverflowX", "scrollWidth", "scrollHeight", "getWithinInfo", "withinElement", "isElemWindow", "options", "of", "apply", "this", "arguments", "atOffset", "targetWidth", "targetHeight", "targetOffset", "basePosition", "dimensions", "target", "extend", "document", "find", "scrollInfo", "collision", "split", "at", "each", "horizontalOffset", "verticalOffset", "pos", "length", "concat", "exec", "collisionPosition", "using", "el<PERSON><PERSON><PERSON><PERSON>", "elemHeight", "marginLeft", "marginTop", "collisionWidth", "collisionHeight", "myOffset", "my", "i", "dir", "ui", "props", "right", "bottom", "feedback", "horizontal", "vertical", "important", "call", "fit", "data", "newOverRight", "withinOffset", "collisionPosLeft", "overLeft", "overRight", "newOverBottom", "collisionPosTop", "overTop", "overBottom", "flip", "newOverLeft", "offsetLeft", "newOverTop", "offsetTop", "flipfit"], "sources": ["position.js"], "mappings": ";;;;;;;;;;CAiBA,SAAYA,GACX,aAEuB,mBAAXC,QAAyBA,OAAOC,IAG3CD,OAAQ,CAAE,SAAU,aAAeD,GAInCA,EAASG,OAET,CAZF,EAYK,SAAUC,GACf,aA8dA,OA5dA,WACA,IAAIC,EACHC,EAAMC,KAAKD,IACXE,EAAMD,KAAKC,IACXC,EAAc,oBACdC,EAAY,oBACZC,EAAU,wBACVC,EAAY,OACZC,EAAW,KACXC,EAAYV,EAAEW,GAAGC,SAElB,SAASC,EAAYC,EAASC,EAAOC,GACpC,MAAO,CACNC,WAAYH,EAAS,KAAUL,EAASS,KAAMJ,EAAS,IAAQC,EAAQ,IAAM,GAC7EE,WAAYH,EAAS,KAAUL,EAASS,KAAMJ,EAAS,IAAQE,EAAS,IAAM,GAEhF,CAEA,SAASG,EAAUC,EAASC,GAC3B,OAAOC,SAAUtB,EAAEuB,IAAKH,EAASC,GAAY,KAAQ,CACtD,CAEA,SAASG,EAAUC,GAClB,OAAc,MAAPA,GAAeA,IAAQA,EAAIC,MACnC,CAEA,SAASC,EAAeC,GACvB,IAAIC,EAAMD,EAAM,GAChB,OAAsB,IAAjBC,EAAIC,SACD,CACNf,MAAOa,EAAKb,QACZC,OAAQY,EAAKZ,SACbe,OAAQ,CAAEC,IAAK,EAAGC,KAAM,IAGrBT,EAAUK,GACP,CACNd,MAAOa,EAAKb,QACZC,OAAQY,EAAKZ,SACbe,OAAQ,CAAEC,IAAKJ,EAAKM,YAAaD,KAAML,EAAKO,eAGzCN,EAAIO,eACD,CACNrB,MAAO,EACPC,OAAQ,EACRe,OAAQ,CAAEC,IAAKH,EAAIQ,MAAOJ,KAAMJ,EAAIS,QAG/B,CACNvB,MAAOa,EAAKW,aACZvB,OAAQY,EAAKY,cACbT,OAAQH,EAAKG,SAEf,CAEA/B,EAAEY,SAAW,CACZ6B,eAAgB,WACf,QAA8BC,IAAzBzC,EACJ,OAAOA,EAER,IAAI0C,EAAIC,EACPC,EAAM7C,EAAG,6IAGT8C,EAAWD,EAAIE,WAAY,GAc5B,OAZA/C,EAAG,QAASgD,OAAQH,GACpBF,EAAKG,EAASG,YACdJ,EAAItB,IAAK,WAAY,UAIhBoB,KAFLC,EAAKE,EAASG,eAGbL,EAAKC,EAAK,GAAIK,aAGfL,EAAIM,SAEKlD,EAAuB0C,EAAKC,CACtC,EACAQ,cAAe,SAAUC,GACxB,IAAIC,EAAYD,EAAO7B,UAAY6B,EAAOE,WAAa,GACrDF,EAAOjC,QAAQG,IAAK,cACrBiC,EAAYH,EAAO7B,UAAY6B,EAAOE,WAAa,GAClDF,EAAOjC,QAAQG,IAAK,cACrBkC,EAA6B,WAAdH,GACE,SAAdA,GAAwBD,EAAOtC,MAAQsC,EAAOjC,QAAS,GAAIsC,YAG/D,MAAO,CACN3C,MAH6B,WAAdyC,GACE,SAAdA,GAAwBH,EAAOrC,OAASqC,EAAOjC,QAAS,GAAIuC,aAEzC3D,EAAEY,SAAS6B,iBAAmB,EACpDzB,OAAQyC,EAAezD,EAAEY,SAAS6B,iBAAmB,EAEvD,EACAmB,cAAe,SAAUxC,GACxB,IAAIyC,EAAgB7D,EAAGoB,GAAWM,QACjCoC,EAAetC,EAAUqC,EAAe,IACxCN,IAAeM,EAAe,IAAuC,IAAhCA,EAAe,GAAI/B,SAEzD,MAAO,CACNV,QAASyC,EACTrC,SAAUsC,EACVP,WAAYA,EACZxB,QALa+B,IAAiBP,EAKVvD,EAAGoB,GAAUW,SAAW,CAAEE,KAAM,EAAGD,IAAK,GAC5DG,WAAY0B,EAAc1B,aAC1BD,UAAW2B,EAAc3B,YACzBnB,MAAO8C,EAActB,aACrBvB,OAAQ6C,EAAcrB,cAExB,GAGDxC,EAAEW,GAAGC,SAAW,SAAUmD,GACzB,IAAMA,IAAYA,EAAQC,GACzB,OAAOtD,EAAUuD,MAAOC,KAAMC,WAM/B,IAAIC,EAAUC,EAAaC,EAAcC,EAAcC,EAAcC,EAGpEC,EAA+B,iBALhCX,EAAU/D,EAAE2E,OAAQ,CAAC,EAAGZ,IAKCC,GACvBhE,EAAG4E,UAAWC,KAAMd,EAAQC,IAC5BhE,EAAG+D,EAAQC,IAEZX,EAASrD,EAAEY,SAASgD,cAAeG,EAAQV,QAC3CyB,EAAa9E,EAAEY,SAASwC,cAAeC,GACvC0B,GAAchB,EAAQgB,WAAa,QAASC,MAAO,KACnDlE,EAAU,CAAC,EAoEZ,OAlEA2D,EAAa9C,EAAe+C,GACvBA,EAAQ,GAAItC,iBAGhB2B,EAAQkB,GAAK,YAEdZ,EAAcI,EAAW1D,MACzBuD,EAAeG,EAAWzD,OAC1BuD,EAAeE,EAAW1C,OAG1ByC,EAAexE,EAAE2E,OAAQ,CAAC,EAAGJ,GAI7BvE,EAAEkF,KAAM,CAAE,KAAM,OAAQ,WACvB,IACCC,EACAC,EAFGC,GAAQtB,EAASG,OAAU,IAAKc,MAAO,KAIvB,IAAfK,EAAIC,SACRD,EAAMhF,EAAYa,KAAMmE,EAAK,IAC5BA,EAAIE,OAAQ,CAAE,WACdjF,EAAUY,KAAMmE,EAAK,IACpB,CAAE,UAAWE,OAAQF,GACrB,CAAE,SAAU,WAEfA,EAAK,GAAMhF,EAAYa,KAAMmE,EAAK,IAAQA,EAAK,GAAM,SACrDA,EAAK,GAAM/E,EAAUY,KAAMmE,EAAK,IAAQA,EAAK,GAAM,SAGnDF,EAAmB5E,EAAQiF,KAAMH,EAAK,IACtCD,EAAiB7E,EAAQiF,KAAMH,EAAK,IACpCvE,EAASoD,MAAS,CACjBiB,EAAmBA,EAAkB,GAAM,EAC3CC,EAAiBA,EAAgB,GAAM,GAIxCrB,EAASG,MAAS,CACjB1D,EAAUgF,KAAMH,EAAK,IAAO,GAC5B7E,EAAUgF,KAAMH,EAAK,IAAO,GAE9B,IAG0B,IAArBN,EAAUO,SACdP,EAAW,GAAMA,EAAW,IAGJ,UAApBhB,EAAQkB,GAAI,GAChBT,EAAavC,MAAQoC,EACU,WAApBN,EAAQkB,GAAI,KACvBT,EAAavC,MAAQoC,EAAc,GAGX,WAApBN,EAAQkB,GAAI,GAChBT,EAAaxC,KAAOsC,EACW,WAApBP,EAAQkB,GAAI,KACvBT,EAAaxC,KAAOsC,EAAe,GAGpCF,EAAWvD,EAAYC,EAAQmE,GAAIZ,EAAaC,GAChDE,EAAavC,MAAQmC,EAAU,GAC/BI,EAAaxC,KAAOoC,EAAU,GAEvBF,KAAKgB,MAAM,WACjB,IAAIO,EAAmBC,EACtB9D,EAAO5B,EAAGkE,MACVyB,EAAY/D,EAAKW,aACjBqD,EAAahE,EAAKY,cAClBqD,EAAa1E,EAAU+C,KAAM,cAC7B4B,EAAY3E,EAAU+C,KAAM,aAC5B6B,EAAiBJ,EAAYE,EAAa1E,EAAU+C,KAAM,eACzDY,EAAW/D,MACZiF,EAAkBJ,EAAaE,EAAY3E,EAAU+C,KAAM,gBAC1DY,EAAW9D,OACZJ,EAAWZ,EAAE2E,OAAQ,CAAC,EAAGH,GACzByB,EAAWpF,EAAYC,EAAQoF,GAAItE,EAAKW,aAAcX,EAAKY,eAEnC,UAApBuB,EAAQmC,GAAI,GAChBtF,EAASqB,MAAQ0D,EACc,WAApB5B,EAAQmC,GAAI,KACvBtF,EAASqB,MAAQ0D,EAAY,GAGL,WAApB5B,EAAQmC,GAAI,GAChBtF,EAASoB,KAAO4D,EACe,WAApB7B,EAAQmC,GAAI,KACvBtF,EAASoB,KAAO4D,EAAa,GAG9BhF,EAASqB,MAAQgE,EAAU,GAC3BrF,EAASoB,KAAOiE,EAAU,GAE1BR,EAAoB,CACnBI,WAAYA,EACZC,UAAWA,GAGZ9F,EAAEkF,KAAM,CAAE,OAAQ,QAAS,SAAUiB,EAAGC,GAClCpG,EAAEqG,GAAGzF,SAAUmE,EAAWoB,KAC9BnG,EAAEqG,GAAGzF,SAAUmE,EAAWoB,IAAOC,GAAOxF,EAAU,CACjDyD,YAAaA,EACbC,aAAcA,EACdqB,UAAWA,EACXC,WAAYA,EACZH,kBAAmBA,EACnBM,eAAgBA,EAChBC,gBAAiBA,EACjBjE,OAAQ,CAAEqC,EAAU,GAAM6B,EAAU,GAAK7B,EAAW,GAAM6B,EAAU,IACpEC,GAAInC,EAAQmC,GACZjB,GAAIlB,EAAQkB,GACZ5B,OAAQA,EACRzB,KAAMA,GAGT,IAEKmC,EAAQ2B,QAGZA,EAAQ,SAAUY,GACjB,IAAIrE,EAAOsC,EAAatC,KAAOrB,EAASqB,KACvCsE,EAAQtE,EAAOoC,EAAcsB,EAC7B3D,EAAMuC,EAAavC,IAAMpB,EAASoB,IAClCwE,EAASxE,EAAMsC,EAAesB,EAC9Ba,EAAW,CACV/B,OAAQ,CACPtD,QAASsD,EACTzC,KAAMsC,EAAatC,KACnBD,IAAKuC,EAAavC,IAClBjB,MAAOsD,EACPrD,OAAQsD,GAETlD,QAAS,CACRA,QAASQ,EACTK,KAAMrB,EAASqB,KACfD,IAAKpB,EAASoB,IACdjB,MAAO4E,EACP3E,OAAQ4E,GAETc,WAAYH,EAAQ,EAAI,OAAStE,EAAO,EAAI,QAAU,SACtD0E,SAAUH,EAAS,EAAI,MAAQxE,EAAM,EAAI,SAAW,UAEjDqC,EAAcsB,GAAavF,EAAK6B,EAAOsE,GAAUlC,IACrDoC,EAASC,WAAa,UAElBpC,EAAesB,GAAcxF,EAAK4B,EAAMwE,GAAWlC,IACvDmC,EAASE,SAAW,UAEhBzG,EAAKE,EAAK6B,GAAQ7B,EAAKmG,IAAYrG,EAAKE,EAAK4B,GAAO5B,EAAKoG,IAC7DC,EAASG,UAAY,aAErBH,EAASG,UAAY,WAEtB7C,EAAQ2B,MAAMmB,KAAM3C,KAAMoC,EAAOG,EAClC,GAGD7E,EAAKG,OAAQ/B,EAAE2E,OAAQ/D,EAAU,CAAE8E,MAAOA,IAC3C,GACD,EAEA1F,EAAEqG,GAAGzF,SAAW,CACfkG,IAAK,CACJ7E,KAAM,SAAUrB,EAAUmG,GACzB,IAMCC,EANG3D,EAAS0D,EAAK1D,OACjB4D,EAAe5D,EAAO7B,SAAW6B,EAAOlB,WAAakB,EAAOtB,OAAOE,KACnEM,EAAac,EAAOtC,MACpBmG,EAAmBtG,EAASqB,KAAO8E,EAAKtB,kBAAkBI,WAC1DsB,EAAWF,EAAeC,EAC1BE,EAAYF,EAAmBH,EAAKhB,eAAiBxD,EAAa0E,EAI9DF,EAAKhB,eAAiBxD,EAGrB4E,EAAW,GAAKC,GAAa,GACjCJ,EAAepG,EAASqB,KAAOkF,EAAWJ,EAAKhB,eAAiBxD,EAC/D0E,EACDrG,EAASqB,MAAQkF,EAAWH,GAI5BpG,EAASqB,KADEmF,EAAY,GAAKD,GAAY,EACxBF,EAIXE,EAAWC,EACCH,EAAe1E,EAAawE,EAAKhB,eAEjCkB,EAKPE,EAAW,EACtBvG,EAASqB,MAAQkF,EAGNC,EAAY,EACvBxG,EAASqB,MAAQmF,EAIjBxG,EAASqB,KAAO/B,EAAKU,EAASqB,KAAOiF,EAAkBtG,EAASqB,KAElE,EACAD,IAAK,SAAUpB,EAAUmG,GACxB,IAMCM,EANGhE,EAAS0D,EAAK1D,OACjB4D,EAAe5D,EAAO7B,SAAW6B,EAAOnB,UAAYmB,EAAOtB,OAAOC,IAClEQ,EAAcuE,EAAK1D,OAAOrC,OAC1BsG,EAAkB1G,EAASoB,IAAM+E,EAAKtB,kBAAkBK,UACxDyB,EAAUN,EAAeK,EACzBE,EAAaF,EAAkBP,EAAKf,gBAAkBxD,EAAcyE,EAIhEF,EAAKf,gBAAkBxD,EAGtB+E,EAAU,GAAKC,GAAc,GACjCH,EAAgBzG,EAASoB,IAAMuF,EAAUR,EAAKf,gBAAkBxD,EAC/DyE,EACDrG,EAASoB,KAAOuF,EAAUF,GAI1BzG,EAASoB,IADEwF,EAAa,GAAKD,GAAW,EACzBN,EAIVM,EAAUC,EACCP,EAAezE,EAAcuE,EAAKf,gBAElCiB,EAKNM,EAAU,EACrB3G,EAASoB,KAAOuF,EAGLC,EAAa,EACxB5G,EAASoB,KAAOwF,EAIhB5G,EAASoB,IAAM9B,EAAKU,EAASoB,IAAMsF,EAAiB1G,EAASoB,IAE/D,GAEDyF,KAAM,CACLxF,KAAM,SAAUrB,EAAUmG,GACzB,IAkBCC,EACAU,EAnBGrE,EAAS0D,EAAK1D,OACjB4D,EAAe5D,EAAOtB,OAAOE,KAAOoB,EAAOlB,WAC3CI,EAAac,EAAOtC,MACpB4G,EAAatE,EAAO7B,SAAW6B,EAAOlB,WAAakB,EAAOtB,OAAOE,KACjEiF,EAAmBtG,EAASqB,KAAO8E,EAAKtB,kBAAkBI,WAC1DsB,EAAWD,EAAmBS,EAC9BP,EAAYF,EAAmBH,EAAKhB,eAAiBxD,EAAaoF,EAClE1B,EAA4B,SAAjBc,EAAKb,GAAI,IAClBa,EAAKpB,UACW,UAAjBoB,EAAKb,GAAI,GACRa,EAAKpB,UACL,EACFvB,EAA4B,SAAjB2C,EAAK9B,GAAI,GACnB8B,EAAK1C,YACY,UAAjB0C,EAAK9B,GAAI,IACP8B,EAAK1C,YACN,EACFtC,GAAU,EAAIgF,EAAKhF,OAAQ,GAIvBoF,EAAW,IACfH,EAAepG,EAASqB,KAAOgE,EAAW7B,EAAWrC,EAASgF,EAAKhB,eAClExD,EAAa0E,GACM,GAAKD,EAAe5G,EAAK+G,MAC5CvG,EAASqB,MAAQgE,EAAW7B,EAAWrC,GAE7BqF,EAAY,KACvBM,EAAc9G,EAASqB,KAAO8E,EAAKtB,kBAAkBI,WAAaI,EACjE7B,EAAWrC,EAAS4F,GACF,GAAKvH,EAAKsH,GAAgBN,KAC5CxG,EAASqB,MAAQgE,EAAW7B,EAAWrC,EAG1C,EACAC,IAAK,SAAUpB,EAAUmG,GACxB,IAmBCa,EACAP,EApBGhE,EAAS0D,EAAK1D,OACjB4D,EAAe5D,EAAOtB,OAAOC,IAAMqB,EAAOnB,UAC1CM,EAAca,EAAOrC,OACrB6G,EAAYxE,EAAO7B,SAAW6B,EAAOnB,UAAYmB,EAAOtB,OAAOC,IAC/DsF,EAAkB1G,EAASoB,IAAM+E,EAAKtB,kBAAkBK,UACxDyB,EAAUD,EAAkBO,EAC5BL,EAAaF,EAAkBP,EAAKf,gBAAkBxD,EAAcqF,EAEpE5B,EADuB,QAAjBc,EAAKb,GAAI,IAEba,EAAKnB,WACW,WAAjBmB,EAAKb,GAAI,GACRa,EAAKnB,WACL,EACFxB,EAA4B,QAAjB2C,EAAK9B,GAAI,GACnB8B,EAAKzC,aACY,WAAjByC,EAAK9B,GAAI,IACP8B,EAAKzC,aACN,EACFvC,GAAU,EAAIgF,EAAKhF,OAAQ,GAGvBwF,EAAU,IACdF,EAAgBzG,EAASoB,IAAMiE,EAAW7B,EAAWrC,EAASgF,EAAKf,gBAClExD,EAAcyE,GACM,GAAKI,EAAgBjH,EAAKmH,MAC9C3G,EAASoB,KAAOiE,EAAW7B,EAAWrC,GAE5ByF,EAAa,KACxBI,EAAahH,EAASoB,IAAM+E,EAAKtB,kBAAkBK,UAAYG,EAAW7B,EACzErC,EAAS8F,GACQ,GAAKzH,EAAKwH,GAAeJ,KAC1C5G,EAASoB,KAAOiE,EAAW7B,EAAWrC,EAGzC,GAED+F,QAAS,CACR7F,KAAM,WACLjC,EAAEqG,GAAGzF,SAAS6G,KAAKxF,KAAKgC,MAAOC,KAAMC,WACrCnE,EAAEqG,GAAGzF,SAASkG,IAAI7E,KAAKgC,MAAOC,KAAMC,UACrC,EACAnC,IAAK,WACJhC,EAAEqG,GAAGzF,SAAS6G,KAAKzF,IAAIiC,MAAOC,KAAMC,WACpCnE,EAAEqG,GAAGzF,SAASkG,IAAI9E,IAAIiC,MAAOC,KAAMC,UACpC,GAIA,CA1dF,GA4dOnE,EAAEqG,GAAGzF,QAEZ"}