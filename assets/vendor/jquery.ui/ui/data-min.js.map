{"version": 3, "file": "data-min.js", "names": ["factory", "define", "amd", "j<PERSON><PERSON><PERSON>", "$", "extend", "expr", "pseudos", "data", "createPseudo", "dataName", "elem", "i", "match"], "sources": ["data.js"], "mappings": ";;;;;;;;CAcA,SAAYA,GACX,aAEuB,mBAAXC,QAAyBA,OAAOC,IAG3CD,OAAQ,CAAE,SAAU,aAAeD,GAInCA,EAASG,OAET,CAZF,EAYK,SAAUC,GACf,aAEA,OAAOA,EAAEC,OAAQD,EAAEE,KAAKC,QAAS,CAChCC,KAAMJ,EAAEE,KAAKG,aACZL,EAAEE,KAAKG,cAAc,SAAUC,GAC9B,OAAO,SAAUC,GAChB,QAASP,EAAEI,KAAMG,EAAMD,EACxB,CACD,IAGA,SAAUC,EAAMC,EAAGC,GAClB,QAAST,EAAEI,KAAMG,EAAME,EAAO,GAC/B,GAEF"}