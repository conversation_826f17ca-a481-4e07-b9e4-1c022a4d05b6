/*!
 * jQuery UI Effects Size 1.13.2
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 */
!function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery","../version","../effect"],t):t(jQuery)}((function(t){"use strict";return t.effects.define("size",(function(e,i){var o,f,s,n=t(this),r=["fontSize"],h=["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"],c=["borderLeftWidth","borderRightWidth","paddingLeft","paddingRight"],d=e.mode,a="effect"!==d,g=e.scale||"both",u=e.origin||["middle","center"],m=n.css("position"),y=n.position(),l=t.effects.scaledDimensions(n),p=e.from||l,x=e.to||t.effects.scaledDimensions(n,0);t.effects.createPlaceholder(n),"show"===d&&(s=p,p=x,x=s),f={from:{y:p.height/l.height,x:p.width/l.width},to:{y:x.height/l.height,x:x.width/l.width}},"box"!==g&&"both"!==g||(f.from.y!==f.to.y&&(p=t.effects.setTransition(n,h,f.from.y,p),x=t.effects.setTransition(n,h,f.to.y,x)),f.from.x!==f.to.x&&(p=t.effects.setTransition(n,c,f.from.x,p),x=t.effects.setTransition(n,c,f.to.x,x))),"content"!==g&&"both"!==g||f.from.y!==f.to.y&&(p=t.effects.setTransition(n,r,f.from.y,p),x=t.effects.setTransition(n,r,f.to.y,x)),u&&(o=t.effects.getBaseline(u,l),p.top=(l.outerHeight-p.outerHeight)*o.y+y.top,p.left=(l.outerWidth-p.outerWidth)*o.x+y.left,x.top=(l.outerHeight-x.outerHeight)*o.y+y.top,x.left=(l.outerWidth-x.outerWidth)*o.x+y.left),delete p.outerHeight,delete p.outerWidth,n.css(p),"content"!==g&&"both"!==g||(h=h.concat(["marginTop","marginBottom"]).concat(r),c=c.concat(["marginLeft","marginRight"]),n.find("*[width]").each((function(){var i=t(this),o=t.effects.scaledDimensions(i),s={height:o.height*f.from.y,width:o.width*f.from.x,outerHeight:o.outerHeight*f.from.y,outerWidth:o.outerWidth*f.from.x},n={height:o.height*f.to.y,width:o.width*f.to.x,outerHeight:o.height*f.to.y,outerWidth:o.width*f.to.x};f.from.y!==f.to.y&&(s=t.effects.setTransition(i,h,f.from.y,s),n=t.effects.setTransition(i,h,f.to.y,n)),f.from.x!==f.to.x&&(s=t.effects.setTransition(i,c,f.from.x,s),n=t.effects.setTransition(i,c,f.to.x,n)),a&&t.effects.saveStyle(i),i.css(s),i.animate(n,e.duration,e.easing,(function(){a&&t.effects.restoreStyle(i)}))}))),n.animate(x,{queue:!1,duration:e.duration,easing:e.easing,complete:function(){var e=n.offset();0===x.opacity&&n.css("opacity",p.opacity),a||(n.css("position","static"===m?"relative":m).offset(e),t.effects.saveStyle(n)),i()}})}))}));
//# sourceMappingURL=effect-size-min.js.map