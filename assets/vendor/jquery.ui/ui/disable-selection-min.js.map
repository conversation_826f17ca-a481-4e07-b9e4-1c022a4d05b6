{"version": 3, "file": "disable-selection-min.js", "names": ["factory", "define", "amd", "j<PERSON><PERSON><PERSON>", "$", "fn", "extend", "disableSelection", "eventType", "document", "createElement", "this", "on", "event", "preventDefault", "enableSelection", "off"], "sources": ["disable-selection.js"], "mappings": ";;;;;;;;CAeA,SAAYA,GACX,aAEuB,mBAAXC,QAAyBA,OAAOC,IAG3CD,OAAQ,CAAE,SAAU,aAAeD,GAInCA,EAASG,OAET,CAZF,EAYK,SAAUC,GACf,aAEA,OAAOA,EAAEC,GAAGC,OAAQ,CACnBC,kBACKC,EAAY,kBAAmBC,SAASC,cAAe,OAC1D,cACA,YAEM,WACN,OAAOC,KAAKC,GAAIJ,EAAY,wBAAwB,SAAUK,GAC7DA,EAAMC,gBACP,GACD,GAGDC,gBAAiB,WAChB,OAAOJ,KAAKK,IAAK,uBAClB,IAdkB,IACbR,CAgBN"}