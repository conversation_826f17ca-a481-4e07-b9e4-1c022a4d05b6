{"version": 3, "file": "unique-id-min.js", "names": ["factory", "define", "amd", "j<PERSON><PERSON><PERSON>", "$", "fn", "extend", "uniqueId", "uuid", "this", "each", "id", "removeUniqueId", "test", "removeAttr"], "sources": ["unique-id.js"], "mappings": ";;;;;;;;CAcA,SAAYA,GACX,aAEuB,mBAAXC,QAAyBA,OAAOC,IAG3CD,OAAQ,CAAE,SAAU,aAAeD,GAInCA,EAASG,OAET,CAZF,EAYK,SAAUC,GACf,aAEA,OAAOA,EAAEC,GAAGC,OAAQ,CACnBC,UACKC,EAAO,EAEJ,WACN,OAAOC,KAAKC,MAAM,WACXD,KAAKE,KACVF,KAAKE,GAAK,YAAeH,EAE3B,GACD,GAGDI,eAAgB,WACf,OAAOH,KAAKC,MAAM,WACZ,cAAcG,KAAMJ,KAAKE,KAC7BP,EAAGK,MAAOK,WAAY,KAExB,GACD,IAlBU,IACLN,CAoBN"}