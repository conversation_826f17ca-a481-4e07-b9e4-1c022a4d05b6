{"version": 3, "file": "droppable-min.js", "names": ["factory", "define", "amd", "j<PERSON><PERSON><PERSON>", "$", "widget", "version", "widgetEventPrefix", "options", "accept", "addClasses", "greedy", "scope", "tolerance", "activate", "deactivate", "drop", "out", "over", "_create", "proportions", "o", "this", "isover", "isout", "d", "is", "arguments", "length", "width", "element", "offsetWidth", "height", "offsetHeight", "_addToManager", "_addClass", "ui", "d<PERSON><PERSON>", "droppables", "push", "_splice", "i", "splice", "_destroy", "_setOption", "key", "value", "_super", "_activate", "event", "draggable", "current", "_addActiveClass", "_trigger", "_deactivate", "_removeActiveClass", "_over", "currentItem", "call", "_addHoverClass", "_out", "_removeHoverClass", "_drop", "custom", "childrenIntersection", "find", "not", "each", "inst", "droppable", "disabled", "intersect", "extend", "offset", "c", "helper", "position", "positionAbs", "_removeClass", "isOverAxis", "x", "reference", "size", "toleranceMode", "x1", "absolute", "left", "margins", "y1", "top", "x2", "helperProportions", "y2", "l", "t", "r", "b", "pageY", "pageX", "default", "prepareOffsets", "j", "m", "type", "list", "addBack", "droppablesLoop", "visible", "css", "dropped", "slice", "dragStart", "parentsUntil", "on", "refreshPositions", "drag", "<PERSON><PERSON><PERSON><PERSON>", "parentInstance", "parent", "intersects", "parents", "filter", "dragStop", "off", "uiBackCompat", "hoverClass", "activeClass", "addClass", "removeClass"], "sources": ["droppable.js"], "mappings": ";;;;;;;;CAeA,SAAYA,GACX,aAEuB,mBAAXC,QAAyBA,OAAOC,IAG3CD,OAAQ,CACP,SACA,cACA,UACA,aACA,aACED,GAIHA,EAASG,OAET,CAlBF,EAkBK,SAAUC,GACf,aAkdA,OAhdAA,EAAEC,OAAQ,eAAgB,CACzBC,QAAS,SACTC,kBAAmB,OACnBC,QAAS,CACRC,OAAQ,IACRC,YAAY,EACZC,QAAQ,EACRC,MAAO,UACPC,UAAW,YAGXC,SAAU,KACVC,WAAY,KACZC,KAAM,KACNC,IAAK,KACLC,KAAM,MAEPC,QAAS,WAER,IAAIC,EACHC,EAAIC,KAAKd,QACTC,EAASY,EAAEZ,OAEZa,KAAKC,QAAS,EACdD,KAAKE,OAAQ,EAEbF,KAAKb,OAA2B,mBAAXA,EAAwBA,EAAS,SAAUgB,GAC/D,OAAOA,EAAEC,GAAIjB,EACd,EAEAa,KAAKF,YAAc,WAClB,IAAKO,UAAUC,OAOd,OAAOR,IAENA,EAAc,CACbS,MAAOP,KAAKQ,QAAS,GAAIC,YACzBC,OAAQV,KAAKQ,QAAS,GAAIG,eAR5Bb,EAAcO,UAAW,EAW3B,EAEAL,KAAKY,cAAeb,EAAET,OAEjBS,EAAEX,YACNY,KAAKa,UAAW,eAGlB,EAEAD,cAAe,SAAUtB,GAGxBR,EAAEgC,GAAGC,UAAUC,WAAY1B,GAAUR,EAAEgC,GAAGC,UAAUC,WAAY1B,IAAW,GAC3ER,EAAEgC,GAAGC,UAAUC,WAAY1B,GAAQ2B,KAAMjB,KAC1C,EAEAkB,QAAS,SAAUxB,GAElB,IADA,IAAIyB,EAAI,EACAA,EAAIzB,EAAKY,OAAQa,IACnBzB,EAAMyB,KAAQnB,MAClBN,EAAK0B,OAAQD,EAAG,EAGnB,EAEAE,SAAU,WACT,IAAI3B,EAAOZ,EAAEgC,GAAGC,UAAUC,WAAYhB,KAAKd,QAAQI,OAEnDU,KAAKkB,QAASxB,EACf,EAEA4B,WAAY,SAAUC,EAAKC,GAE1B,GAAa,WAARD,EACJvB,KAAKb,OAA0B,mBAAVqC,EAAuBA,EAAQ,SAAUrB,GAC7D,OAAOA,EAAEC,GAAIoB,EACd,OACM,GAAa,UAARD,EAAkB,CAC7B,IAAI7B,EAAOZ,EAAEgC,GAAGC,UAAUC,WAAYhB,KAAKd,QAAQI,OAEnDU,KAAKkB,QAASxB,GACdM,KAAKY,cAAeY,EACrB,CAEAxB,KAAKyB,OAAQF,EAAKC,EACnB,EAEAE,UAAW,SAAUC,GACpB,IAAIC,EAAY9C,EAAEgC,GAAGC,UAAUc,QAE/B7B,KAAK8B,kBACAF,GACJ5B,KAAK+B,SAAU,WAAYJ,EAAO3B,KAAKc,GAAIc,GAE7C,EAEAI,YAAa,SAAUL,GACtB,IAAIC,EAAY9C,EAAEgC,GAAGC,UAAUc,QAE/B7B,KAAKiC,qBACAL,GACJ5B,KAAK+B,SAAU,aAAcJ,EAAO3B,KAAKc,GAAIc,GAE/C,EAEAM,MAAO,SAAUP,GAEhB,IAAIC,EAAY9C,EAAEgC,GAAGC,UAAUc,QAGzBD,IAAeA,EAAUO,aAC7BP,EAAUpB,SAAW,KAAQR,KAAKQ,QAAS,IAIxCR,KAAKb,OAAOiD,KAAMpC,KAAKQ,QAAS,GAAOoB,EAAUO,aACpDP,EAAUpB,WACXR,KAAKqC,iBACLrC,KAAK+B,SAAU,OAAQJ,EAAO3B,KAAKc,GAAIc,IAGzC,EAEAU,KAAM,SAAUX,GAEf,IAAIC,EAAY9C,EAAEgC,GAAGC,UAAUc,QAGzBD,IAAeA,EAAUO,aAC7BP,EAAUpB,SAAW,KAAQR,KAAKQ,QAAS,IAIxCR,KAAKb,OAAOiD,KAAMpC,KAAKQ,QAAS,GAAOoB,EAAUO,aACpDP,EAAUpB,WACXR,KAAKuC,oBACLvC,KAAK+B,SAAU,MAAOJ,EAAO3B,KAAKc,GAAIc,IAGxC,EAEAY,MAAO,SAAUb,EAAOc,GAEvB,IAAIb,EAAYa,GAAU3D,EAAEgC,GAAGC,UAAUc,QACxCa,GAAuB,EAGxB,SAAMd,IAAeA,EAAUO,aAC7BP,EAAUpB,SAAW,KAAQR,KAAKQ,QAAS,MAI7CR,KAAKQ,QACHmC,KAAM,uBACNC,IAAK,0BACLC,MAAM,WACN,IAAIC,EAAOhE,EAAGkB,MAAO+C,UAAW,YAChC,GACCD,EAAK5D,QAAQG,SACZyD,EAAK5D,QAAQ8D,UACdF,EAAK5D,QAAQI,QAAUsC,EAAU1C,QAAQI,OACzCwD,EAAK3D,OAAOiD,KACXU,EAAKtC,QAAS,GAAOoB,EAAUO,aAAeP,EAAUpB,UAEzD1B,EAAEgC,GAAGmC,UACJrB,EACA9C,EAAEoE,OAAQJ,EAAM,CAAEK,OAAQL,EAAKtC,QAAQ2C,WACvCL,EAAK5D,QAAQK,UAAWoC,GAIzB,OADAe,GAAuB,GAChB,CAET,KACIA,MAIA1C,KAAKb,OAAOiD,KAAMpC,KAAKQ,QAAS,GACjCoB,EAAUO,aAAeP,EAAUpB,WACtCR,KAAKiC,qBACLjC,KAAKuC,oBAELvC,KAAK+B,SAAU,OAAQJ,EAAO3B,KAAKc,GAAIc,IAChC5B,KAAKQ,UAKd,EAEAM,GAAI,SAAUsC,GACb,MAAO,CACNxB,UAAawB,EAAEjB,aAAeiB,EAAE5C,QAChC6C,OAAQD,EAAEC,OACVC,SAAUF,EAAEE,SACZH,OAAQC,EAAEG,YAEZ,EAIAlB,eAAgB,WACfrC,KAAKa,UAAW,qBACjB,EAEA0B,kBAAmB,WAClBvC,KAAKwD,aAAc,qBACpB,EAEA1B,gBAAiB,WAChB9B,KAAKa,UAAW,sBACjB,EAEAoB,mBAAoB,WACnBjC,KAAKwD,aAAc,sBACpB,IAGD1E,EAAEgC,GAAGmC,UAAY,WAChB,SAASQ,EAAYC,EAAGC,EAAWC,GAClC,OAASF,GAAKC,GAAiBD,EAAMC,EAAYC,CAClD,CAEA,OAAO,SAAUhC,EAAWmB,EAAWc,EAAelC,GAErD,IAAMoB,EAAUI,OACf,OAAO,EAGR,IAAIW,GAAOlC,EAAU2B,aACnB3B,EAAU0B,SAASS,UAAWC,KAAOpC,EAAUqC,QAAQD,KACxDE,GAAOtC,EAAU2B,aAChB3B,EAAU0B,SAASS,UAAWI,IAAMvC,EAAUqC,QAAQE,IACvDC,EAAKN,EAAKlC,EAAUyC,kBAAkB9D,MACtC+D,EAAKJ,EAAKtC,EAAUyC,kBAAkB3D,OACtC6D,EAAIxB,EAAUI,OAAOa,KACrBQ,EAAIzB,EAAUI,OAAOgB,IACrBM,EAAIF,EAAIxB,EAAUjD,cAAcS,MAChCmE,EAAIF,EAAIzB,EAAUjD,cAAcY,OAEjC,OAASmD,GACT,IAAK,MACJ,OAASU,GAAKT,GAAMM,GAAMK,GAAKD,GAAKN,GAAMI,GAAMI,EACjD,IAAK,YACJ,OAASH,EAAIT,EAAOlC,EAAUyC,kBAAkB9D,MAAQ,GACvD6D,EAAOxC,EAAUyC,kBAAkB9D,MAAQ,EAAMkE,GACjDD,EAAIN,EAAOtC,EAAUyC,kBAAkB3D,OAAS,GAChD4D,EAAO1C,EAAUyC,kBAAkB3D,OAAS,EAAMgE,EACpD,IAAK,UACJ,OAAOjB,EAAY9B,EAAMgD,MAAOH,EAAGzB,EAAUjD,cAAcY,SAC1D+C,EAAY9B,EAAMiD,MAAOL,EAAGxB,EAAUjD,cAAcS,OACtD,IAAK,QACJ,OACG2D,GAAMM,GAAKN,GAAMQ,GACjBJ,GAAME,GAAKF,GAAMI,GACjBR,EAAKM,GAAKF,EAAKI,KAEfZ,GAAMS,GAAKT,GAAMW,GACjBL,GAAMG,GAAKH,GAAMK,GACjBX,EAAKS,GAAKH,EAAKK,GAEnB,QACC,OAAO,EAET,CACC,CA/Ce,GAoDjB3F,EAAEgC,GAAGC,UAAY,CAChBc,QAAS,KACTb,WAAY,CAAE6D,QAAW,IACzBC,eAAgB,SAAUN,EAAG7C,GAE5B,IAAIR,EAAG4D,EACNC,EAAIlG,EAAEgC,GAAGC,UAAUC,WAAYwD,EAAEtF,QAAQI,QAAW,GACpD2F,EAAOtD,EAAQA,EAAMsD,KAAO,KAC5BC,GAASV,EAAErC,aAAeqC,EAAEhE,SAAUmC,KAAM,uBAAwBwC,UAErEC,EAAgB,IAAMjE,EAAI,EAAGA,EAAI6D,EAAE1E,OAAQa,IAG1C,KAAK6D,EAAG7D,GAAIjC,QAAQ8D,UAAcwB,IAAMQ,EAAG7D,GAAIhC,OAAOiD,KAAM4C,EAAG7D,GAAIX,QAAS,GACxEgE,EAAErC,aAAeqC,EAAEhE,UADvB,CAMA,IAAMuE,EAAI,EAAGA,EAAIG,EAAK5E,OAAQyE,IAC7B,GAAKG,EAAMH,KAAQC,EAAG7D,GAAIX,QAAS,GAAM,CACxCwE,EAAG7D,GAAIrB,cAAcY,OAAS,EAC9B,SAAS0E,CACV,CAGDJ,EAAG7D,GAAIkE,QAA8C,SAApCL,EAAG7D,GAAIX,QAAQ8E,IAAK,WAC/BN,EAAG7D,GAAIkE,UAKC,cAATJ,GACJD,EAAG7D,GAAIO,UAAUU,KAAM4C,EAAG7D,GAAKQ,GAGhCqD,EAAG7D,GAAIgC,OAAS6B,EAAG7D,GAAIX,QAAQ2C,SAC/B6B,EAAG7D,GAAIrB,YAAa,CACnBS,MAAOyE,EAAG7D,GAAIX,QAAS,GAAIC,YAC3BC,OAAQsE,EAAG7D,GAAIX,QAAS,GAAIG,eAvB7B,CA4BF,EACAjB,KAAM,SAAUkC,EAAWD,GAE1B,IAAI4D,GAAU,EAqBd,OAlBAzG,EAAE+D,MAAQ/D,EAAEgC,GAAGC,UAAUC,WAAYY,EAAU1C,QAAQI,QAAW,IAAKkG,SAAS,WAEzExF,KAAKd,WAGLc,KAAKd,QAAQ8D,UAAYhD,KAAKqF,SAClCvG,EAAEgC,GAAGmC,UAAWrB,EAAW5B,KAAMA,KAAKd,QAAQK,UAAWoC,KAC1D4D,EAAUvF,KAAKwC,MAAMJ,KAAMpC,KAAM2B,IAAW4D,IAGvCvF,KAAKd,QAAQ8D,UAAYhD,KAAKqF,SAAWrF,KAAKb,OAAOiD,KAAMpC,KAAKQ,QAAS,GAC3EoB,EAAUO,aAAeP,EAAUpB,WACtCR,KAAKE,OAAQ,EACbF,KAAKC,QAAS,EACdD,KAAKgC,YAAYI,KAAMpC,KAAM2B,IAG/B,IACO4D,CAER,EACAE,UAAW,SAAU7D,EAAWD,GAI/BC,EAAUpB,QAAQkF,aAAc,QAASC,GAAI,oBAAoB,WAC1D/D,EAAU1C,QAAQ0G,kBACvB9G,EAAEgC,GAAGC,UAAU+D,eAAgBlD,EAAWD,EAE5C,GACD,EACAkE,KAAM,SAAUjE,EAAWD,GAIrBC,EAAU1C,QAAQ0G,kBACtB9G,EAAEgC,GAAGC,UAAU+D,eAAgBlD,EAAWD,GAI3C7C,EAAE+D,KAAM/D,EAAEgC,GAAGC,UAAUC,WAAYY,EAAU1C,QAAQI,QAAW,IAAI,WAEnE,IAAKU,KAAKd,QAAQ8D,WAAYhD,KAAK8F,aAAgB9F,KAAKqF,QAAxD,CAIA,IAAIU,EAAgBzG,EAAO0G,EAC1BC,EAAanH,EAAEgC,GAAGmC,UAAWrB,EAAW5B,KAAMA,KAAKd,QAAQK,UAAWoC,GACtEyB,GAAK6C,GAAcjG,KAAKC,OACvB,QACEgG,IAAejG,KAAKC,OAAS,SAAW,KACtCmD,IAIDpD,KAAKd,QAAQG,SAGjBC,EAAQU,KAAKd,QAAQI,OACrB0G,EAAShG,KAAKQ,QAAQ0F,QAAS,uBAAwBC,QAAQ,WAC9D,OAAOrH,EAAGkB,MAAO+C,UAAW,YAAa7D,QAAQI,QAAUA,CAC5D,KAEYgB,UACXyF,EAAiBjH,EAAGkH,EAAQ,IAAMjD,UAAW,aAC9B+C,YAAsB,WAAN1C,IAK5B2C,GAAwB,WAAN3C,IACtB2C,EAAe9F,QAAS,EACxB8F,EAAe7F,OAAQ,EACvB6F,EAAezD,KAAKF,KAAM2D,EAAgBpE,IAG3C3B,KAAMoD,IAAM,EACZpD,KAAY,UAANoD,EAAgB,SAAW,UAAY,EAC7CpD,KAAY,WAANoD,EAAiB,QAAU,QAAShB,KAAMpC,KAAM2B,GAGjDoE,GAAwB,UAAN3C,IACtB2C,EAAe7F,OAAQ,EACvB6F,EAAe9F,QAAS,EACxB8F,EAAe7D,MAAME,KAAM2D,EAAgBpE,IAxC5C,CA0CD,GAED,EACAyE,SAAU,SAAUxE,EAAWD,GAC9BC,EAAUpB,QAAQkF,aAAc,QAASW,IAAK,oBAIxCzE,EAAU1C,QAAQ0G,kBACvB9G,EAAEgC,GAAGC,UAAU+D,eAAgBlD,EAAWD,EAE5C,IAKuB,IAAnB7C,EAAEwH,cAGNxH,EAAEC,OAAQ,eAAgBD,EAAEgC,GAAGiC,UAAW,CACzC7D,QAAS,CACRqH,YAAY,EACZC,aAAa,GAEd1E,gBAAiB,WAChB9B,KAAKyB,SACAzB,KAAKd,QAAQsH,aACjBxG,KAAKQ,QAAQiG,SAAUzG,KAAKd,QAAQsH,YAEtC,EACAvE,mBAAoB,WACnBjC,KAAKyB,SACAzB,KAAKd,QAAQsH,aACjBxG,KAAKQ,QAAQkG,YAAa1G,KAAKd,QAAQsH,YAEzC,EACAnE,eAAgB,WACfrC,KAAKyB,SACAzB,KAAKd,QAAQqH,YACjBvG,KAAKQ,QAAQiG,SAAUzG,KAAKd,QAAQqH,WAEtC,EACAhE,kBAAmB,WAClBvC,KAAKyB,SACAzB,KAAKd,QAAQqH,YACjBvG,KAAKQ,QAAQkG,YAAa1G,KAAKd,QAAQqH,WAEzC,IAIKzH,EAAEgC,GAAGiC,SAEZ"}