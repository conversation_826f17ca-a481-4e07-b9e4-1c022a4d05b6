parameters:
  route_normalizer_enabled: true
services:
  redirect.repository:
    class: <PERSON><PERSON>al\redirect\RedirectRepository
    arguments: ['@entity_type.manager', '@database', '@config.factory']
    tags:
      - { name: backend_overridable }
  Drupal\redirect\RedirectRepository: '@redirect.repository'
  redirect.checker:
    class: Drupal\redirect\RedirectChecker
    arguments: ['@config.factory', '@state', '@access_manager', '@current_user', '@router.route_provider']
  Drupal\redirect\RedirectChecker: '@redirect.checker'
  redirect.request_subscriber:
    class: Drupal\redirect\EventSubscriber\RedirectRequestSubscriber
    arguments: ['@redirect.repository', '@language_manager', '@config.factory', '@path_alias.manager', '@module_handler', '@entity_type.manager', '@redirect.checker', '@router.request_context', '@path_processor_manager']
    tags:
      - { name: event_subscriber }
  redirect.settings_cache_tag:
    class: Drupal\redirect\EventSubscriber\RedirectSettingsCacheTag
    arguments: ['@cache_tags.invalidator']
    tags:
      - { name: event_subscriber }
  redirect.route_normalizer_request_subscriber:
    class: Drupal\redirect\EventSubscriber\RouteNormalizerRequestSubscriber
    arguments: ['@url_generator', '@path.matcher', '@config.factory', '@redirect.checker', '@path_alias.manager', '@path.current']
    tags:
      - { name: event_subscriber }
  redirect.route_subscriber:
    class: Drupal\redirect\Routing\RouteSubscriber
    tags:
      - { name: event_subscriber }
