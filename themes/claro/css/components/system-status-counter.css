/*
 * DO NOT EDIT THIS FILE.
 * See the following change record for more information,
 * https://www.drupal.org/node/3084859
 * @preserve
 */
/**
 * @file
 * Styles for the system status counter component.
 */
.system-status-counter {
  --system-status-counter-status-icon: #e6e4df;
  --system-status-counter-status-icon-error: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23dc2323'%3e%3cpath d='M8.002 1c-3.868 0-7.002 3.134-7.002 7s3.134 7 7.002 7c3.865 0 7-3.134 7-7s-3.135-7-7-7zm4.025 9.284c.062.063.1.149.1.239 0 .091-.037.177-.1.24l-1.262 1.262c-.064.062-.15.1-.24.1s-.176-.036-.24-.1l-2.283-2.283-2.286 2.283c-.064.062-.15.1-.24.1s-.176-.036-.24-.1l-1.261-1.262c-.063-.062-.1-.148-.1-.24 0-.088.036-.176.1-.238l2.283-2.285-2.283-2.284c-.063-.064-.1-.15-.1-.24s.036-.176.1-.24l1.262-1.262c.063-.063.149-.1.24-.1.089 0 .176.036.24.1l2.285 2.284 2.283-2.284c.064-.063.15-.1.24-.1s.176.036.24.1l1.262 1.262c.062.063.1.149.1.24 0 .089-.037.176-.1.24l-2.283 2.284 2.283 2.284z'/%3e%3c/svg%3e");
  --system-status-counter-status-icon-warning: url("data:image/svg+xml,%3csvg fill='%23e29700' height='16' width='16' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m14.66 12.316-5.316-10.633c-.738-1.476-1.946-1.476-2.685 0l-5.317 10.633c-.738 1.477.008 2.684 1.658 2.684h10.002c1.65 0 2.396-1.207 1.658-2.684zm-7.66-8.316h2.002v5h-2.002zm2.252 8.615c0 .344-.281.625-.625.625h-1.25c-.345 0-.626-.281-.626-.625v-1.239c0-.344.281-.625.626-.625h1.25c.344 0 .625.281.625.625z'/%3e%3c/svg%3e");
  --system-status-counter-status-icon-checked: url("data:image/svg+xml,%3csvg fill='%2373b355' height='16' width='16' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m6.464 13.676c-.194.194-.513.194-.707 0l-4.96-4.955c-.194-.193-.194-.513 0-.707l1.405-1.407c.194-.195.512-.195.707 0l2.849 2.848c.194.193.513.193.707 0l6.629-6.626c.195-.194.514-.194.707 0l1.404 1.404c.193.194.193.513 0 .707z'/%3e%3c/svg%3e");

  display: inline-block;
  overflow-y: hidden;
  box-sizing: border-box;
  inline-size: 100%;
  white-space: nowrap;
}
.system-status-counter__status-icon {
  display: inline-block;
  block-size: 4.0625rem;
  inline-size: 3.75rem;
  vertical-align: middle;
}
.system-status-counter__status-icon::before {
  display: block;
  block-size: 100%;
  inline-size: 100%;
  content: "";
  background-repeat: no-repeat;
  background-position: right center;
  background-size: 2.5rem;
}
@media (forced-colors: active) {
  .system-status-counter__status-icon::before {
    background-color: canvastext;
    background-image: none;
    mask-repeat: no-repeat;
    mask-position: right center;
    mask-size: 2.5rem;
  }
}
[dir="rtl"] .system-status-counter__status-icon::before {
  background-position: left center;
}
.system-status-counter__status-icon--error::before {
  background-image: var(--system-status-counter-status-icon-error);
}
.system-status-counter__status-icon--warning::before {
  background-image: var(--system-status-counter-status-icon-warning);
}
.system-status-counter__status-icon--checked::before {
  background-image: var(--system-status-counter-status-icon-checked);
}
@media (forced-colors: active) {
  .system-status-counter__status-icon--error::before {
    mask-image: var(--system-status-counter-status-icon-error);
  }

  .system-status-counter__status-icon--warning::before {
    mask-image: var(--system-status-counter-status-icon-warning);
  }

  .system-status-counter__status-icon--checked::before {
    mask-image: var(--system-status-counter-status-icon-checked);
  }
}
.system-status-counter__status-title {
  display: inline-block;
  padding: 0 1.125rem;
  vertical-align: middle;
  font-size: 1.125em;
  font-weight: bold;
  line-height: 1em;
}
.system-status-counter__title-count {
  display: block;
  margin-block-end: 0.5rem;
}
.system-status-counter__details {
  display: block;
  text-transform: none;
  font-size: var(--font-size-s);
  font-weight: normal;
  line-height: 1.5;
}
@media screen and (min-width: 61rem) {
  .system-status-report-counters__item {
    padding-block: var(--space-s);
  }

  .system-status-counter__status-icon,
  .system-status-counter {
    block-size: 4.0625rem;
  }

  .system-status-counter__status-icon {
    inline-size: 4.0625rem;
  }
}
