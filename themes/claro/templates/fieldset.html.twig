{#
/**
 * @file
 * Theme override for a fieldset element and its children.
 *
 * Available variables:
 * - attributes: HTML attributes for the fieldset element.
 * - errors: (optional) Any errors for this fieldset element, may not be set.
 * - required: <PERSON><PERSON><PERSON> indicating whether the fieldset element is required.
 * - legend: The legend element containing the following properties:
 *   - title: Title of the fieldset, intended for use as the text of the legend.
 *   - attributes: HTML attributes to apply to the legend.
 * - description: The description element containing the following properties:
 *   - content: The description content of the fieldset.
 *   - attributes: HTML attributes to apply to the description container.
 * - description_display: Description display setting. It can have these values:
 *   - before: The description is output before the element.
 *   - after: The description is output after the element (default).
 *   - invisible: The description is output after the element, hidden visually
 *     but available to screen readers.
 * - children: The rendered child elements of the fieldset.
 * - prefix: The content to add before the fieldset children.
 * - suffix: The content to add after the fieldset children.
 *
 * @see template_preprocess_fieldset()
 * @see claro_preprocess_fieldset()
 */
#}
{%
  set classes = [
    'fieldset',
    attributes.hasClass('fieldgroup') ? 'fieldset--group',
    'js-form-item',
    'form-item',
    'js-form-wrapper',
    'form-wrapper',
  ]
%}
{%
  set wrapper_classes = [
    'fieldset__wrapper',
    attributes.hasClass('fieldgroup') ? 'fieldset__wrapper--group',
  ]
%}
{%
  set legend_span_classes = [
    'fieldset__label',
    attributes.hasClass('fieldgroup') ? 'fieldset__label--group',
    required ? 'js-form-required',
    required ? 'form-required',
  ]
%}
{%
  set legend_classes = [
    'fieldset__legend',
    attributes.hasClass('fieldgroup') and not attributes.hasClass('form-composite') ? 'fieldset__legend--group',
    attributes.hasClass('form-composite') ? 'fieldset__legend--composite',
    title_display == 'invisible' ? 'fieldset__legend--invisible' : 'fieldset__legend--visible',
  ]
%}
{%
  set description_classes = [
   'fieldset__description',
  ]
%}

<fieldset{{ attributes.addClass(classes) }}>
  {# Always wrap fieldset legends in a <span> for CSS positioning. #}
  {% if legend.title %}
  <legend{{ legend.attributes.addClass(legend_classes) }}>
    <span{{ legend_span.attributes.addClass(legend_span_classes) }}>{{ legend.title }}</span>
  </legend>
  {% endif %}

  <div{{ content_attributes.addClass(wrapper_classes) }}>
    {% if description_display == 'before' and description.content %}
      <div{{ description.attributes.addClass(description_classes) }}>{{ description.content }}</div>
    {% endif %}
    {% if inline_items %}
      <div class="container-inline">
    {% endif %}

    {% if prefix %}
      <span class="fieldset__prefix">{{ prefix }}</span>
    {% endif %}
    {{ children }}
    {% if suffix %}
      <span class="fieldset__suffix">{{ suffix }}</span>
    {% endif %}
    {% if errors %}
      <div class="fieldset__error-message">
        {{ errors }}
      </div>
    {% endif %}
    {% if description_display in ['after', 'invisible'] and description.content %}
      <div{{ description.attributes.addClass(description_classes) }}>{{ description.content }}</div>
    {% endif %}

    {% if inline_items %}
      </div>
    {% endif %}
  </div>
</fieldset>
