/**
 * @file
 * Embedded Media.
 */

@import "../base/media-queries.pcss.css";

figure {
  background: var(--color--gray-100);
}

figcaption {
  padding-block: var(--sp0-5);
  padding-inline-start: var(--sp0-5);
  padding-inline-end: var(--sp0-5);
  color: var(--color-text-neutral-medium);
  background: var(--color--gray-100);
  font-family: var(--font-serif);
  font-size: 14px;
  font-style: italic;
  line-height: var(--sp);

  @media (--sm) {
    padding-block: var(--sp);
    padding-inline-start: var(--sp);
    padding-inline-end: var(--sp);
  }
}

.align-right {
  float: none; /* Override core's align.module.css. */
  max-width: 100%;
  margin-block: var(--sp3);
  margin-inline-start: 0;
  margin-inline-end: 0;

  @media (--grid-md) {
    float: right; /* LTR */
    max-width: 50%;
    margin-block-start: var(--sp);
    margin-block-end: var(--sp);
    margin-inline-start: var(--sp);
    margin-inline-end: 0;

    /**
     * Chromium and Webkit do not yet support flow relative logical properties,
     * such as float: inline-end. However, PostCSS Logical does not compile this
     * value, so we accommodate by not using these.
     *
     * @see https://caniuse.com/mdn-css_properties_clear_flow_relative_values
     * @see https://github.com/csstools/postcss-plugins/issues/632
     */
    &:dir(rtl) {
      float: left;
    }
  }
}

/* Pull out of grid if nested in content narrow layout. */
.layout--content-narrow .align-right,
.layout--pass--content-narrow > * .align-right {
  /* @todo this can be simplified. */
  @media (--grid-md) {
    margin-inline-end: calc(-1 * ((var(--grid-col-width) + var(--grid-gap))));
  }

  @media (--lg) {
    margin-inline-end: calc(-2 * ((var(--grid-col-width) + var(--grid-gap))));
  }

  @media (--nav) {
    margin-inline-end: calc(-3 * ((var(--grid-col-width) + var(--grid-gap))));
  }

  @media (--grid-max) {
    margin-inline-end: calc(-3 * ((var(--grid-col-width) + var(--grid-gap))));
  }
}

.align-left {
  float: none; /* Override core's align.module.css. */
  max-width: 100%;
  margin-block-start: var(--sp3);
  margin-block-end: var(--sp3);
  margin-inline-start: 0;
  margin-inline-end: 0;

  @media (--grid-md) {
    float: left; /* LTR */
    max-width: 50%;
    margin-block-start: var(--sp);
    margin-block-end: var(--sp);
    margin-inline-start: 0;
    margin-inline-end: var(--sp2); /* Extra right margins in case of aligning next to lists. */

    /**
     * Chromium and Webkit do not yet support flow relative logical properties,
     * such as float: inline-end. However, PostCSS Logical does not compile this
     * value, so we accommodate by not using these.
     *
     * @see https://caniuse.com/mdn-css_properties_clear_flow_relative_values
     * @see https://github.com/csstools/postcss-plugins/issues/632
     */
    &:dir(rtl) {
      float: right;
    }
  }
}

/* Pull out of grid if nested in content narrow layout. */
.layout--content-narrow .align-left,
.layout--pass--content-narrow > * .align-left {
  @media (--grid-md) {
    margin-inline-start: calc(-1 * ((var(--grid-col-width) + var(--grid-gap))));
  }
}

.align-center img,
.align-center video,
.align-center audio {
  margin-inline: auto;
}

.media-oembed-content {
  display: block;
  max-width: 100%;
}
