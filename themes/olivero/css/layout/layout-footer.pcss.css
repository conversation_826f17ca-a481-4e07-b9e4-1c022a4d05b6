/**
 * @file
 * Grid system definition for the footer top and footer bottom layouts.
 */

@import "../base/media-queries.pcss.css";

/**
 * Creates stacking context ensuring that child elements can never appear in
 * front of mobile navigation.
 */
.site-footer {
  position: relative;
  z-index: 1;
}

.site-footer__inner {
  padding-block: var(--sp2);

  @media (--nav) {
    padding-block: var(--sp4) var(--sp13);
  }
}

.region--footer_top__inner,
.region--footer_bottom__inner {
  & > * {
    margin-block-end: var(--sp2);

    @media (--md) {
      flex: 1;
      margin-block-end: 0;

      &:not(:last-child) {
        margin-inline-end: var(--sp2);
      }
    }
  }

  @media (--md) {
    display: flex;
    flex-wrap: wrap;
  }
}
