global-styling:
  version: VERSION
  js:
    js/bg-lazy-load.js: {}
  # js/popper.min.js: {}
  #  js/bootstrap.min.js: {}
  # js/barrio.js: {}
    js/custom.js: {}
  dependencies:
    - core/jquery
    - core/drupal
    - core/js-cookie
    - bootstrap4grow/bootstrap.style
    - core/once
    - bootstrap4grow/justified-gallery

global:
  version: VERSION
  css:
    component:
      css/global.css: {}
  dependencies:
    - bootstrap4grow/bootstrap.runtime
    - bootstrap4grow/pagesense

frontpage:
  version: VERSION
  css:
    component:
      css/frontpage.min.css: {}

npxtraining:
  version: VERSION
  css:
    component:
      css/npxtraining.min.css: {}

landing:
  version: VERSION
  css:
    component:
      css/landing.min.css: {}

page:
  version: VERSION
  css:
    component:
      css/page.min.css: {}

article:
  version: VERSION
  css:
    component:
      css/article.min.css: {}

coaching:
  version: VERSION
  css:
    component:
      css/coaching.min.css: {}

npxquiz:
  version: VERSION
  css:
    component:
      css/npxquiz.min.css: {}

book:
  version: VERSION
  css:
    component:
      css/book.min.css: {}

reviews:
  version: VERSION
  css:
    component:
      css/reviews.min.css: {}

references:
  version: VERSION
  css:
    component:
      css/references.min.css: {}

business_library:
  version: VERSION
  css:
    component:
      css/business_library.min.css: {}

npxsurvey:
  version: VERSION
  css:
    component:
      css/npxsurvey.min.css: {}

webform:
  version: VERSION
  css:
    component:
      css/webform.min.css: {}

##
#### bootstrap-chunks
##

bootstrap.style:
  version: 5.1.0
  css:
    theme:
      bootstrap/dist/css/main.css: {}

bootstrap.tables:
  version: 5.1.0
  css:
    component:
      bootstrap/dist/css/tables.css: { }

bootstrap.forms:
  version: 5.1.0
  css:
    component:
      bootstrap/dist/css/forms.css: { }

bootstrap.breadcrumbs:
  version: 5.1.0
  css:
    component:
      bootstrap/dist/css/breadcrumbs.css: { }

bootstrap.nav:
  version: 5.1.0
  css:
    component:
      bootstrap/dist/css/nav.css: { }

bootstrap.navbar:
  version: 5.1.0
  css:
    component:
      bootstrap/dist/css/navbar.css: { }

bootstrap.card:
  version: 5.1.0
  css:
    component:
      bootstrap/dist/css/card.css: { }

bootstrap.pagination:
  version: 5.1.0
  css:
    component:
      bootstrap/dist/css/pagination.css: { }

bootstrap.badge:
  version: 5.1.0
  css:
    component:
      bootstrap/dist/css/badge.css: { }

bootstrap.button-group:
  version: 5.1.0
  css:
    component:
      bootstrap/dist/css/button-group.css: { }

bootstrap.progress:
  version: 5.1.0
  css:
    component:
      bootstrap/dist/css/progress.css: { }

bootstrap.spinner:
  version: 5.1.0
  css:
    component:
      bootstrap/dist/css/progress.css: { }

bootstrap.runtime:
  version: 5.1.0
  js:
    bootstrap/dist/js/runtime.js:
      minified: true
      scope: footer
  dependencies:
    - core/drupal

bootstrap.alert:
  version: 5.1.0
  js:
    bootstrap/dist/js/alert.js:
      minified: true
      scope: footer
  css:
    component:
      bootstrap/dist/css/alert.css: {}
  dependencies:
    - bootstrap4grow/bootstrap.runtime

bootstrap.button:
  version: 5.1.0
  js:
    bootstrap/dist/js/button.js:
      minified: true
      scope: footer
  css:
    component:
      bootstrap/dist/css/buttons.css: { }
  dependencies:
    - bootstrap4grow/bootstrap.runtime

bootstrap.carousel:
  version: 5.1.0
  js:
    bootstrap/dist/js/carousel.js:
      minified: true
      scope: footer
  css:
    component:
      bootstrap/dist/css/carousel.css: { }
  dependencies:
    - bootstrap4grow/bootstrap.runtime

bootstrap.collapse:
  version: 5.1.0
  js:
    bootstrap/dist/js/collapse.js:
      minified: true
      scope: footer
  css:
    component:
      bootstrap/dist/css/accordion.css: { }
  dependencies:
    - bootstrap4grow/bootstrap.runtime

bootstrap.dropdown:
  version: 5.1.0
  js:
    bootstrap/dist/js/dropdown.js:
      minified: true
      scope: footer
  css:
    component:
      bootstrap/dist/css/dropdown.css: { }
  dependencies:
    - bootstrap4grow/bootstrap.runtime

bootstrap.modal:
  version: 5.1.0
  js:
    bootstrap/dist/js/modal.js:
      minified: true
      scope: footer
  css:
    component:
      bootstrap/dist/css/modal.css: { }
  dependencies:
    - bootstrap4grow/bootstrap.runtime

bootstrap.offcanvas:
  version: 5.1.0
  js:
    bootstrap/dist/js/offcanvas.js:
      minified: true
      scope: footer
  css:
    component:
      bootstrap/dist/css/offcanvas.css: { }
  dependencies:
    - bootstrap4grow/bootstrap.runtime

bootstrap.popover:
  version: 5.1.0
  js:
    bootstrap/dist/js/popover.js:
      minified: true
      scope: footer
  css:
    component:
      bootstrap/dist/css/popover.css: { }
  dependencies:
    - bootstrap4grow/bootstrap.tooltip


bootstrap.scrollspy:
  version: 5.1.0
  js:
    bootstrap/dist/js/scrollspy.js:
      minified: true
      scope: footer
  dependencies:
    - bootstrap4grow/bootstrap.runtime

bootstrap.tab:
  version: 5.1.0
  js:
    bootstrap/dist/js/tab.js:
      minified: true
      scope: footer
  dependencies:
    - bootstrap4grow/bootstrap.runtime

bootstrap.toast:
  version: 5.1.0
  js:
    bootstrap/dist/js/toast.js:
      minified: true
      scope: footer
  css:
    component:
      bootstrap/dist/css/toast.css: { }
  dependencies:
    - bootstrap4grow/bootstrap.runtime

bootstrap.tooltip:
  version: 5.1.0
  js:
    bootstrap/dist/js/tooltip.js:
      minified: true
      scope: footer
  css:
    component:
      bootstrap/dist/css/tooltip.css: { }
  dependencies:
    - bootstrap4grow/bootstrap.runtime
justified-gallery:
  css:
    theme:
      css/justifiedGallery.min.css: {}
  js:
    js/jquery.justifiedGallery.min.js: {}

pagesense:
  version: VERSION
  js:
    https://cdn-eu.pagesense.io/js/4growspzoo/005709c02b4f49788974a8f8e6c73bf4.js: { type: external, minified: true }
  header: true
