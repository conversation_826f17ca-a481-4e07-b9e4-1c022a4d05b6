@import "../bootstrap/scss/common/base";
@import "pages/training/module_npx_training_terms_block";
@import "pages/training/sekcja_szkolenie_grupa_1";
@import "pages/training/module_npx_training_form_tab";
@import "pages/training/sekcja_szkolenie_grupa_faq";
@import "pages/training/sekcja_szkolenie_grupa_6";
@import "pages/training/module_npx_program_tabs";
@import "pages/training/paragraph_type_arguments";
@import "pages/training/sekcja_opinie";
@import "pages/training/sekcja_logo";
@import "pages/training/sekcja_blog";

.narrow {
  max-width: 1200px;
  padding-left: spacer(6);
  padding-right: spacer(6);
  margin: 0 auto;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
  h2 {
    margin-left: 0;
    margin-right: 0;
    @include media-breakpoint-up(xla) {
      margin-left: -3vw;
      margin-right: -3vw;
    }
    @include media-breakpoint-up(xxl) {
      margin-left: -106px;
      margin-right: -106px;
    }
  }
}
.w100 {
  max-width: 100vw;
  padding: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}
.n-breadcrumb {
  display: inline-block;
}
.inner {
  max-width: $max-width-container;
  padding-left: spacer(6);
  padding-right: spacer(6);
  margin: 0 auto;
}
div.node--type-landing-page .field--name-body {
  padding-top: 1.5rem;
}
body h2 {
  margin-top: 0;
  padding-top: 2rem;
}
body h2.field-label-above::after {
  display: block;
  left: 50%;
  margin-left: -20px;
  position: absolute;
  content: " ";
  width: 40px;
  height: 2px;
  background: #fecc09;
  bottom: -15px;
  @include media-breakpoint-up(lg) {
    left: 0;
    margin-left: 0;
  }
}
.npx-program-tabs-wrapper .nav-tabs > li {
  @include media-breakpoint-up(md) {
    max-width: 100%;
    flex: 1 0 100%;
  }
  @include media-breakpoint-up(lg) {
    max-width: 100%;
    flex: 1 0 100%;
  }
  @include media-breakpoint-up(xl) {
    max-width: 49%;
    flex: 1 0 49%;
  }
}
.npx-training-form-tab-header {
  background-size: 100% 200px;
}
.npx-training-form-tab-header, .npx-training-form-tab-content {
  @include media-breakpoint-up(md) {
    flex: 1 0 50%;
  }
}
.npx-training-form-tab-header-inner {
  height: 100%;
}
.npx-training-form-tab-header-top {
  height: 200px;
}
.npx-training-form-tab-header-info-inner {
  &.h5 {
    font-weight: bold;
    font-size: 15px;
  }
  span {
    color: #ffc60c;
  }
}
.npx-training-form-tab-header-inner {
  @include media-breakpoint-up(md) {
    border-top-right-radius: 0;
  }
}

#szkolenie-grupa-6 {
  background: #f1fbfc;
}

.npx-program-tabs-wrapper .nav-tabs li .npx-blocks-program-tab-wrapper > a.npx-no-autolink-with-link {
  margin-bottom: 0;
  .n-tab-header {
    height: 100%;
  }
}

.npx-program-tabs-wrapper .nav-tabs li .npx-training-form-tab-content a:not(.npx-no-autolink) {
  background: transparent;
  text-decoration: underline;
  margin-bottom: 0;
}
.load-more-terms-wrapper {
  position: relative;
}
#szkolenie-grupa-1 .field--name-field-ts-opis {
  @include media-breakpoint-down(xl) {
    display: inline-block;
  }
}
