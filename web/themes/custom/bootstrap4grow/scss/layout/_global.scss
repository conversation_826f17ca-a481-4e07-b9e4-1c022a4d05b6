.container.npx-gray-bg {
  background: #f4f4f4;
  padding: spacer(25px 3);
  box-sizing: border-box;
  color: #333;
}
#cboxOverlay {
  background: #006bd9;
  opacity: .85 !important;
}
#cboxClose {
  position: absolute;
  bottom: auto;
  top: 5px;
  right: 5px;
  background: url("../images/close.png") no-repeat 0 0;
  width: 30px;
  height: 30px;
  text-indent: -9999px;
}
#cboxWrapper {
  border-radius: 0;
}
#cboxContent {
  padding: 70px 5px 5px;
  border-radius: 0;
  @include media-breakpoint-up(sm) {
    padding: spacer(70px 6 6);
  }
  @include media-breakpoint-up(lg) {
    padding: spacer(70px 60px 6);
  }
}
.red-text, .npx-red-text {
  color: #B3002F;
  font-weight: bold;
}
