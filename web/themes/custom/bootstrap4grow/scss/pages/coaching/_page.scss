.node--type-coaching {
  #page {
    @include page-bg;
  }
  .papper_wrapper, #main-wrapper {
    @include page-bg-cover;
  }
  .view-wstep-do-tresci p {
    font-size: 20px;
  }
  h1 {
    line-height: $h1-font-size;
    font-weight: 700;
    text-shadow: 2px 2px 0 #000;
    border-bottom: 2px solid #fff;
    padding: spacer(0 0 3);
  }
  .featured-top {
    color: #fff;
    background: 0 0;
    background-size: cover;
    padding: spacer(4 0 32px);
  }
  .region-featured-top {
    color: #fff;
    @include limiter;
    h1, ul, p, h3 {
      @include media-breakpoint-up(xl) {
        width: 60%;
      }
    }
    ul {
      list-style-image: url(../images/check-white.svg);
    }
    li {
      font-size: 20px;
    }
    h3 {
      font-size: 30px;
      color: #000;
    }
  }
  a:link, a:visited {
    color: #4c4c4c;
  }
  .field__label {
    font-size: $h2-font-size;
    font-weight: 700;
  }
  h2, .field__label {
    @include h2other;
  }
  #main {
    ul {
      padding-left: spacer(4);
    }
    li {
      list-style-image: url(../images/punktor1a.png);
      line-height: 130%;
      margin: 0 0 5px;
    }
  }
  .ds-2col > .group-left {
    float: none;
    width: 100%;
  }
  .readmore-js-link {
    display: none;
  }
  h2.field-label-above::after {
    display: none;
  }
}
