.paragraph--type-arguments {
  margin-top: 60px;
  flex-direction: column;
  @include media-breakpoint-up(sm) {
    flex-direction: row;
  }
  .group-left {
    width: 100%;
    height: 100px;
    @include media-breakpoint-up(lg) {
      width: 45%;
    }
  }
  .group-right {
    width: 100%;
    height: auto;
    background: transparent;
    @include media-breakpoint-up(lg) {
      width: 55%;
      height: 100px;
      background: #f4f7f5;
      border-radius: 100px 0 0 100px;
    }
  }
  a.n-get-pdf {
    padding-right: spacer(7);
    @include media-breakpoint-up(lg) {
      padding-right: 0;
    }
  }
  .field--name-field-arguments-right {
    padding-left: spacer(5);
    max-width: 400px;
    margin-left: auto;
    border-left: #000 1px solid;
    @include media-breakpoint-up(lg) {
      padding-left: spacer(7);
    }
    p {
      font-size: 11.2px;
      line-height: 16px;
    }
  }
  .field--name-field-arguments-left {
    max-width: 300px;
    padding-left: 50px;
    font-size: 19.2px;
    &::before {
      content: "";
      position: absolute;
      width: 100px;
      height: 100px;
      top: 0;
      left: 0;
      z-index: -1;
      background: #f4f7f5;
      border-radius: 100px;
    }
  }
}
