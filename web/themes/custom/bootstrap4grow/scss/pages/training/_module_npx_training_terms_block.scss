::-webkit-scrollbar {
  -webkit-appearance: none;
}

::-webkit-scrollbar:vertical {
  width: 12px;
}

::-webkit-scrollbar:horizontal {
  height: 12px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, .2);
  border-radius: 10px;
  border: 2px solid #ffffff;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #ffffff;
}

.training-terms-block {
  width: 800px;
  @include media-breakpoint-up(lg) {
    width: 100%;
  }
  &-wrapper {
    overflow-x: scroll;
    @include media-breakpoint-up(lg) {
      overflow-x: auto;
    }
  }
  &-td-1 {
    width: 16%;
  }
  &-td-2 {
    width: 20%;
  }
  &-td-3 {
    width: 12%;
  }
  &-td-4 {
    width: 6%;
  }
  &-td-5 {
    width: 12%;
  }
  &-h4 {
    font-size: $font-size-base;
  }
  &-with-sustable-table {
    margin: spacer(5px 0 2 0);
  }
  &-th {
    padding-bottom: spacer(2);
  }
  &-td-clickable::after {
    content: "";
    width: 13px;
    height: 7px;
    display: inline-block;
    background: transparent no-repeat url(../images/menu-arrow.png) 0 0;
    margin-left: spacer(2);
    -moz-transition: -moz-transform 300ms ease;
    -o-transition: -o-transform 300ms ease;
    -webkit-transition: -webkit-transform 300ms ease;
    transition: transform 300ms ease;
    -moz-transform: scaleY(-1);
    -ms-transform: scaleY(-1);
    -webkit-transform: scaleY(-1);
    transform: scaleY(-1);
  }
  &-td-clickable.open::after {
    -moz-transform: scaleY(1);
    -ms-transform: scaleY(1);
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
  }
  &-npx-form-button {
    padding: spacer(2 3);
  }
  .ask-for-course-closed {
    width: 90%;
    max-width: 400px;
    padding: spacer(2 3);
  }
}
.load-more-terms {
  border: 2px solid $link-color;
  margin: spacer(-24px auto 6);
  border-radius: 6px;
  width: 185px;
  background-color: #fff;
  &-bg {
    height: 0;
    background: #d0d8db;
    margin-top: 70px;
  }
  &-wrapper {
    position: absolute;
    width: calc( 100vw - 40px );
    @include media-breakpoint-up(lg) {
      position: relative;
      width: auto;
    }
  }
}

