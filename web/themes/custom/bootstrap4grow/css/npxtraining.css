@charset "UTF-8";
/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

.node--type-npxtraining h2 {
  font-weight: 700;
  font-size: 2rem;
  line-height: 1.5;
  padding: 0;
  margin: 5rem 0 2.8125rem;
  position: relative;
  display: block;
}
.node--type-npxtraining .ajax-progress-throbber {
  background: transparent;
}
.node--type-npxtraining .ajax-progress-throbber.ajax-progress {
  display: block;
  position: absolute;
  left: 0;
  padding-top: 0.3125rem;
  top: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  z-index: 100;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
}

@media (max-width: 74.99875rem) {
  .ds-2col-fluid > .group-left {
    width: 100%;
  }
}
.ds-2col-fluid > .group-right {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  min-height: 100%;
}
.ds-2col-fluid > .group-right .obraz {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: " ";
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
  background-color: transparent !important;
  background-position-y: 53px !important;
  z-index: 10;
}

.field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy div[class*=field-name-field-field-tytul-sekcji-] h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy div[class*=field-name-field-tytul-sekcji-] h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy .sekcja > h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy h2.field__label::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy h2.field-label-above::after, body div[class*=field--name-field-tytul-sekcji-] h2::after, body div[class*=field-name-field-field-tytul-sekcji-] h2::after, body div[class*=field-name-field-tytul-sekcji-] h2::after, body .sekcja > h2::after, body h2.field__label::after, body h2.field-label-above::after {
  display: block;
  left: 50%;
  margin-left: -1.25rem;
  position: absolute;
  content: " ";
  width: 40px;
  height: 2px;
  background: #fecc09;
  bottom: -15px;
}
@media (min-width: 62rem) {
  .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy div[class*=field-name-field-field-tytul-sekcji-] h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy div[class*=field-name-field-tytul-sekcji-] h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy .sekcja > h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy h2.field__label::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy h2.field-label-above::after, body div[class*=field--name-field-tytul-sekcji-] h2::after, body div[class*=field-name-field-field-tytul-sekcji-] h2::after, body div[class*=field-name-field-tytul-sekcji-] h2::after, body .sekcja > h2::after, body h2.field__label::after, body h2.field-label-above::after {
    left: 0;
    margin-left: 0;
  }
}

.narrow {
  max-width: 1200px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
}
.narrow h2 {
  margin-left: 0;
  margin-right: 0;
}
@media (min-width: 81.25rem) {
  .narrow h2 {
    margin-left: -3vw;
    margin-right: -3vw;
  }
}
@media (min-width: 100rem) {
  .narrow h2 {
    margin-left: -6.625rem;
    margin-right: -6.625rem;
  }
}

.sekcja {
  max-width: 1415px;
  padding: 1.25rem;
  margin: 0 auto;
  overflow: hidden;
}
@media (min-width: 36rem) {
  .sekcja {
    padding: 1.875rem;
  }
}
.sekcja.w100 {
  max-width: 100vw;
  padding: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

[id^=szkolenie-grupa-]::before {
  display: block;
  content: " ";
  margin-top: -3.125rem;
  height: 50px;
  visibility: hidden;
}

#szkolenie-grupa-2 .slick__arrow {
  bottom: 0;
  top: unset;
}
#szkolenie-grupa-2 .slick-next {
  right: 0;
  position: absolute;
  left: auto;
}
#szkolenie-grupa-2 .slick-dots {
  display: inline-block;
  position: relative;
  bottom: -15px;
}
#szkolenie-grupa-2 .slick-prev:hover, #szkolenie-grupa-2 .slick-prev:focus, #szkolenie-grupa-2 .slick-next:hover, #szkolenie-grupa-2 .slick-next:focus {
  background-image: url("../images/arrows.png");
  border-radius: 0;
}
#szkolenie-grupa-2 .slick {
  max-width: 600px;
  margin: 0 auto;
}
#szkolenie-grupa-2 .npx-program-button {
  margin-top: 0;
}
@media (min-width: 36rem) {
  #szkolenie-grupa-2 .ds-3col-stacked-fluid > .group-middle {
    width: calc(100% - 110px);
  }
}

.field--name-field-nasza-jakosc-twoj-komfort .y-box-outer {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  z-index: 10;
}
.field--name-field-nasza-jakosc-twoj-komfort .y-box-inner {
  margin: 0 5%;
  padding: 1.875rem;
  background-color: #fff;
  -webkit-box-shadow: 1px 1px 40px #ebedec;
  box-shadow: 1px 1px 40px #ebedec;
  border-radius: 5px;
  text-align: center;
  font-size: 1.25rem;
  line-height: 1.5;
  font-weight: bold;
  text-align: center;
}
@media (min-width: 36rem) {
  .field--name-field-nasza-jakosc-twoj-komfort .y-box-inner {
    margin: 0 1%;
    padding: 1.875rem 2.5rem 1.875rem;
  }
}
@media (min-width: 62rem) {
  .field--name-field-nasza-jakosc-twoj-komfort .y-box-inner {
    margin: 0 1.875rem;
    padding: 1.875rem 4.0625rem 2.5rem;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}
.field--name-field-nasza-jakosc-twoj-komfort .y-box-inner-1 {
  padding: 0.125rem 0.5rem;
  background: #B3002B;
  color: #fff;
  font-size: 0.625rem;
  line-height: 1rem;
  display: inline-block;
}
.field--name-field-nasza-jakosc-twoj-komfort .y-box-inner-2 {
  display: block;
  margin: 0.625rem 0 0 0;
}
.field--name-field-nasza-jakosc-twoj-komfort .y-box-inner-2 strong {
  font-style: normal;
}
.field--name-field-nasza-jakosc-twoj-komfort .y-image {
  height: 60vw;
  background: transparent url("../images/4grow-sala-mobile.jpg") no-repeat center center;
  -webkit-background-size: cover;
          background-size: cover;
  margin-top: -3vw;
  position: relative;
  z-index: 1;
  margin-bottom: 1rem;
}
@media (min-width: 36rem) {
  .field--name-field-nasza-jakosc-twoj-komfort .y-image {
    margin-top: -6vw;
    -webkit-background-size: cover;
            background-size: cover;
  }
}
@media (min-width: 62rem) {
  .field--name-field-nasza-jakosc-twoj-komfort .y-image {
    margin-top: -10.65rem;
    height: 33vw;
    background: transparent url("../images/4grow-sala-szkoleniowa-desktop.jpg") no-repeat center center;
    -webkit-background-size: cover;
            background-size: cover;
  }
}

.page-node-5593 .field--name-field-nasza-jakosc-twoj-komfort .y-image {
  background: transparent url("../images/sala_antystres_bg.jpg") no-repeat center center;
  -webkit-background-size: cover;
          background-size: cover;
}

@media (min-width: 100rem) {
  .container.field-name-field-tytul-sekcji-g4 {
    max-width: 1415px;
  }
}
.field--name-field-npxtraining-paragraf-trene > .field__item {
  margin-bottom: 2rem;
}

.paragraph--type-trener-do-szkolenia-par .wrapper-1 {
  position: relative;
  margin: 0 0 1.25rem;
  text-align: center;
}
.paragraph--type-trener-do-szkolenia-par .view-mode-bootstrap_carousel.ds-1col {
  padding: 0.625rem;
}
.paragraph--type-trener-do-szkolenia-par .wrapper-1 .wrapper-2 {
  z-index: 2;
  top: 6%;
  position: relative;
  right: 0;
  width: 100%;
  height: auto;
  display: block;
  margin: 0.9375rem 0 0 0;
  text-align: center;
}
@media (min-width: 36rem) {
  .paragraph--type-trener-do-szkolenia-par .wrapper-1 .wrapper-2 {
    position: absolute;
    right: 5%;
    width: 50%;
    height: 90%;
    -webkit-box-align: center;
    -webkit-align-items: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-align-content: center;
        -ms-flex-line-pack: center;
            align-content: center;
    -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
            flex-wrap: wrap;
  }
}
.paragraph--type-trener-do-szkolenia-par .wrapper-1 .wrapper-2 h3 {
  line-height: 1.2;
  margin: 0 0 0.75rem;
  padding: 0;
}
.paragraph--type-trener-do-szkolenia-par.ds-2col img {
  max-width: 100%;
}
.paragraph--type-trener-do-szkolenia-par .view-mode-bootstrap_carousel.ds-1col {
  padding: 0.625rem;
}
.paragraph--type-trener-do-szkolenia-par.ds-2col > .group-right {
  width: 100%;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
@media (min-width: 62rem) {
  .paragraph--type-trener-do-szkolenia-par.ds-2col > .group-right {
    float: right;
    padding: 0 0 0 3%;
    width: 50%;
  }
}
.paragraph--type-trener-do-szkolenia-par.ds-2col > .group-left {
  width: 100%;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
@media (min-width: 62rem) {
  .paragraph--type-trener-do-szkolenia-par.ds-2col > .group-left {
    float: left;
    width: 50%;
    padding: 0 3% 0 0;
  }
}

#szkolenie-grupa-5 .slick-dots {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: -1.5rem;
}
#szkolenie-grupa-5 .slick--less .slick-track {
  text-align: left;
}
@media (min-width: 36rem) {
  #szkolenie-grupa-5 .slick--less .slick-track {
    text-align: center;
  }
}
#szkolenie-grupa-5 .field--name-field-zajawka {
  padding: 0 0.625rem;
}
#szkolenie-grupa-5 .field--name-field-zajawka p {
  margin: 0 0 0.5rem;
  font-size: 80%;
}
#szkolenie-grupa-5 .field--name-field-zajawka > p:first-child {
  margin-left: -0.625rem;
  margin-right: -0.625rem;
}
#szkolenie-grupa-5 .field--name-field-zajawka > p:first-child img {
  width: 100%;
}
#szkolenie-grupa-5 .slick__arrow {
  bottom: 15px;
  top: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
#szkolenie-grupa-5 .draggable {
  max-height: 410px;
}
#szkolenie-grupa-5 .view-mode-bootstrap_carousel.ds-1col > .inner {
  border: 1px solid #d0d8db;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
#szkolenie-grupa-5 .field--name-field-link-do-profilu-trenera {
  display: none;
}
#szkolenie-grupa-5 .field--name-field-npxtrainer-position {
  text-align: center;
  margin-bottom: 1.5rem;
}
#szkolenie-grupa-5 h3 {
  font-size: 140%;
  line-height: 1.2;
  margin: 1rem 0 0.75rem;
  text-align: center;
}
#szkolenie-grupa-5 .field--name-field-opis-trenera {
  max-height: none;
}
#szkolenie-grupa-5 .field--name-field-opis-trenera span {
  font-size: 1rem;
  color: #343a40;
  line-height: 1.5rem;
  font-family: Muli, sans-serif;
  text-align: left;
  word-break: unset;
  display: inline;
}

#szkolenie-grupa-6 {
  max-width: 100vw;
  padding: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}
#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .item-list {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 5.625rem auto;
}

.field--name-dynamic-block-fieldnode-ds-training-program-block .field-label-above:first-of-type:not(:last-of-type):after {
  display: none;
}

#szkolenie-grupa-8 #npx-bottom-wrapper input {
  width: 100%;
  margin-left: 0.125rem;
  font-size: 0.875rem;
  padding-left: 0.5rem;
}
#szkolenie-grupa-8 #npx-bottom-wrapper textarea {
  width: 100%;
  height: 200px;
}
@media (min-width: 36rem) {
  #szkolenie-grupa-8 #npx-bottom-wrapper textarea {
    height: auto;
  }
}
#szkolenie-grupa-8 #npx-bottom-wrapper button {
  text-transform: uppercase;
}
#szkolenie-grupa-8 #npx-training-form .edit-npx-training-date > .fieldset-wrapper > div > .form-item {
  margin-bottom: 1.25rem;
}
#szkolenie-grupa-8 .npx-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  background: #fff;
  border: 1px solid #034b7d;
  cursor: pointer;
  width: auto;
  margin: 0 auto;
  display: inline-block;
}
#szkolenie-grupa-8 .npx-spoiler-toggle.show-icon:before {
  content: "ROZWIŃ OPIS";
}
#szkolenie-grupa-8 .npx-spoiler-toggle.hide-icon:before {
  content: "ZWIŃ OPIS";
}
#szkolenie-grupa-8 .npx-spoiler-content {
  font-size: inherit;
}
#szkolenie-grupa-8 .npx-spoiler-content a.npx-autolink {
  color: inherit;
  text-decoration: none;
}
#szkolenie-grupa-8 .n-spoiler-toggle-wrapper .n-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  background: #fff;
  border: 1px solid #034b7d;
  cursor: pointer;
  display: inline-block;
}

#npx-online-training-wrapper .fieldset-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
#npx-online-training-wrapper .fieldset-wrapper .field-prefix {
  -webkit-align-self: center;
      -ms-flex-item-align: center;
          align-self: center;
}
#npx-online-training-wrapper .fieldset-wrapper #edit-npx-online-training {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 48rem) {
  #npx-online-training-wrapper .fieldset-wrapper #edit-npx-online-training {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
#npx-online-training-wrapper .fieldset-wrapper #edit-npx-online-training .js-form-item-npx-online-training {
  margin: 0.5rem 0;
  padding-left: 0;
}
@media (min-width: 48rem) {
  #npx-online-training-wrapper .fieldset-wrapper #edit-npx-online-training .js-form-item-npx-online-training {
    margin: auto 0.625rem;
  }
}

.npx-fv-paper-wrapper .radio {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.npx-fv-paper-wrapper .fieldset-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.npx-fv-paper-wrapper .fieldset-wrapper .form-radios {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.npx-fv-paper-wrapper .fieldset-wrapper .form-radios .js-form-item-npx-fv-paper {
  margin: auto 0.625rem;
}

.npx-fv-paper-info {
  font-size: 1rem !important;
}

.npx-location-info-wrapper.npx-noborder {
  padding-bottom: 1.25rem;
}

#npx-training-form .npx-form-additional-description {
  font-size: 0.875rem;
  top: -1.25rem;
  position: relative;
}
#npx-training-form [id^=edit-fields-wrapper] {
  display: block;
  margin-right: -0.3125rem;
  margin-left: -0.3125rem;
  overflow: hidden;
}
@media (min-width: 48rem) {
  #npx-training-form [id^=edit-fields-wrapper] {
    margin-top: 0.5rem;
  }
}
#npx-training-form [id^=edit-fields-wrapper] .js-form-item {
  max-width: 99%;
  margin-top: 0.5rem;
}
@media (min-width: 62rem) {
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item {
    float: left;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:not(:last-of-type) {
    margin-right: 0.125rem;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(1) {
    width: 9%;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(2), #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(5) {
    width: 10%;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(3) {
    width: 20%;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(4) {
    width: 23%;
  }
}
#npx-training-form .field-suffix {
  display: none;
}
#npx-training-form .npx-form-error {
  display: none;
}
#npx-training-form #npx-bottom-wrapper {
  display: none;
  background-color: #f7f9f8;
}
#npx-training-form #npx-bottom-wrapper.n-webinar-mode .npx-online-training-header, #npx-training-form #npx-bottom-wrapper.n-webinar-mode .form-item-npx-online-training {
  display: none;
}
@media (min-width: 62rem) {
  #npx-training-form .npx-form-additional-description, #npx-training-form .form-item-npx-fv-comment {
    max-width: calc(72% + 17px);
  }
}
#npx-training-form legend {
  font-size: 1rem;
  margin-bottom: 0;
}
#npx-training-form h4 {
  font-size: 1rem;
  font-weight: 700;
  margin: 0;
}
#npx-training-form .fieldset-wrapper .field-prefix {
  -webkit-align-self: center;
      -ms-flex-item-align: center;
          align-self: center;
}
#npx-training-form .npx-fv-paper-wrapper .field-prefix {
  margin-right: 0.5rem;
}
@media (min-width: 48rem) {
  #npx-training-form .npx-fv-paper-wrapper .field-prefix {
    margin-top: -0.5rem;
  }
}
#npx-training-form.with-bottom-wrapper #npx-bottom-wrapper {
  display: block;
}
#npx-training-form.with-bottom-wrapper #npx-expand-bottom-wrapper {
  display: none !important;
}
#npx-training-form.with-bottom-wrapper #npx-expand-bottom-wrapper-online {
  display: none !important;
}
#npx-training-form div.form-item-npx-training {
  display: none;
}
@media (min-width: 48rem) {
  #npx-training-form #npx-top-wrapper {
    padding-bottom: 0.625rem;
  }
}
#npx-training-form.n-hide-hotel .form-item-npx-hotel-info {
  display: none;
}
#npx-training-form div.npx-border-green {
  margin-bottom: 1.25rem;
}
#npx-training-form div.npx-border-green-inner {
  border: #c8dc32 1px solid;
  padding: 0.3125rem 0.625rem;
}
#npx-training-form div.npx-border-gray {
  margin-bottom: 1.25rem;
}
#npx-training-form div.npx-border-gray-inner {
  border: #d4d8db 1px solid;
  padding: 0.3125rem 0.625rem;
}
#npx-training-form input#edit-npx-accept-4:invalid + label {
  color: #fc5353;
}
#npx-training-form a#npx-expand-bottom-wrapper-online {
  width: 100%;
  margin-left: 0;
  margin-right: 0;
  margin-top: 0;
  text-align: center;
  margin-bottom: 1rem;
}
@media (min-width: 48rem) {
  #npx-training-form a#npx-expand-bottom-wrapper-online {
    margin-right: 1rem;
    width: auto;
    text-align: left;
  }
}

.npx-float::after {
  clear: both;
  content: ".";
  display: block;
  height: 0;
  visibility: hidden;
}

.npx-training-type .field-prefix {
  margin-right: 1rem;
}

#szkolenie-grupa-14 {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}
@media (min-width: 87.5rem) {
  #szkolenie-grupa-14 {
    max-width: 1415px;
    width: 100%;
    margin: 0 auto;
    left: 0;
    right: 0;
  }
}

.field--name-field-question {
  position: relative;
  display: inline-block;
  cursor: pointer;
  padding-left: 2rem;
}
.field--name-field-question.active:before {
  -webkit-transform: rotate(-45deg);
       -o-transform: rotate(-45deg);
          transform: rotate(-45deg);
  top: 28px;
}
.field--name-field-question:before {
  position: absolute;
  top: 22px;
  cursor: pointer;
  content: "";
  display: inline-block;
  width: 11px;
  height: 11px;
  border-right: 2px solid #343a40;
  border-top: 2px solid #343a40;
  -webkit-transform: rotate(135deg);
  -o-transform: rotate(135deg);
     transform: rotate(135deg);
  margin-right: 0.5em;
  margin-left: 1em;
  max-width: 12px;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 auto;
  -webkit-flex: 1 0 auto;
          flex: 1 0 auto;
  -ms-flex-item-align: center;
  -webkit-align-self: center;
          align-self: center;
  left: -12px;
  -webkit-transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
}
.field--name-field-question h3, .field--name-field-question p, .field--name-field-question h4 {
  margin: 0.625rem 0;
}

.field--name-field-answer h3, .field--name-field-answer h4 {
  margin: 0 0 0.625rem 0;
}

.g18.sekcja.kontakt-email {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  max-width: 100vw;
  background: #f3f3f5;
}
.g18.sekcja.kontakt-email .ajax-progress-throbber {
  background: transparent;
}

.npx-mailcontact-header h2 {
  font-size: 1.5rem;
}

@media (min-width: 62rem) {
  .field--name-field-loga-firm {
    max-height: 106px;
    overflow: hidden;
  }
}
.field--name-field-loga-firm .field__item.col-auto {
  -webkit-transform: scale(0.7);
       -o-transform: scale(0.7);
          transform: scale(0.7);
}
@media (min-width: 36rem) {
  .field--name-field-loga-firm .field__item.col-auto {
    -webkit-transform: none;
         -o-transform: none;
            transform: none;
  }
}
.field--name-field-loga-firm img {
  opacity: 0.5;
  height: 40px;
  width: auto;
  margin: 0.1875rem auto;
}

.field--name-field-logo-1-ref {
  padding-bottom: 0.25rem;
}

.field--name-field-logo-2-ref, .field--name-field-logo-1-ref {
  min-height: 60px;
}

@media (min-width: 75rem) {
  .methodology-items {
    margin-top: 1.5rem;
    margin-left: 3rem;
  }
}
.methodology-item:before {
  display: block;
  content: "";
  width: 50%;
  height: 1px;
  background: #d1d1d1;
  position: absolute;
  left: -2rem;
  z-index: -1;
  top: 50px;
}
@media (min-width: 75rem) {
  .methodology-item:before {
    left: -7rem;
    top: 40%;
    width: 9rem;
  }
}
@media (max-width: 74.99875rem) {
  .methodology-item-title {
    display: block;
    width: 100%;
  }
}
@media (min-width: 75rem) {
  .methodology-item-title {
    border-right: #d1d1d1 solid 1px;
    margin-right: 0.9375rem;
    min-width: 110px;
    max-width: 110px;
    margin-bottom: 0;
  }
}
@media (max-width: 74.99875rem) {
  .methodology-image {
    max-width: 420px;
  }
  .methodology-image:before {
    position: absolute;
    content: "";
    width: 110%;
    padding-top: 100%;
    max-width: 462px;
    border-radius: 50%;
    border-bottom: #d1d1d1 solid 2px;
    top: 28px;
    left: -5%;
  }
  .methodology-image img {
    border-radius: 50%;
    -webkit-box-shadow: 0px 9px 34px 1px rgba(66, 68, 90, 0.66);
    box-shadow: 0px 9px 34px 1px rgba(66, 68, 90, 0.66);
    width: 100%;
    height: auto;
  }
}
@media (min-width: 75rem) {
  .methodology-image {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
            flex: 1 0 25%;
  }
}
@media (max-width: 74.99875rem) {
  .methodology-icon {
    width: 100px;
    margin: 0 auto;
  }
}
@media (min-width: 75rem) {
  .methodology-icon {
    max-width: 70px;
    max-height: 70px;
    min-width: 70px;
    min-height: 70px;
    margin-right: 1rem;
  }
}
.methodology-icon img {
  border-radius: 50%;
  -webkit-box-shadow: 8px 8px 24px -7px rgba(66, 68, 90, 0.66);
  box-shadow: 8px 8px 24px -7px rgba(66, 68, 90, 0.66);
}

.field-oni-juz-byli_item_even {
  margin-top: 1.25rem;
  margin-left: auto;
  margin-right: 0;
}

.field--name-field-oni-juz-byli_item {
  max-width: 660px;
  clear: both;
}
.field--name-field-oni-juz-byli_item .group-middle {
  width: 100%;
}
@media (min-width: 36rem) {
  .field--name-field-oni-juz-byli_item .group-middle {
    width: calc(100% - 110px);
  }
}

.opinia .group-footer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  background-image: url("../images/opinia.png");
  background-position: right top;
  background-repeat: no-repeat;
  padding-top: 1.875rem;
}
.opinia .group-footer img {
  border-radius: 50%;
  -webkit-box-shadow: 1px 1px 6px 0 #a1a1a1;
          box-shadow: 1px 1px 6px 0 #a1a1a1;
}
.opinia .field--name-field-linkedin-link {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-end;
      -ms-flex-item-align: end;
          align-self: flex-end;
}
.opinia .field--name-field-linkedin-link a {
  position: relative;
  text-indent: -99999px;
  text-align: left;
  display: block;
  width: 46px;
  height: 11px;
  margin-top: 0.3125rem;
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
  -webkit-align-self: end;
      -ms-flex-item-align: end;
          align-self: end;
}
.opinia .field--name-field-linkedin-link a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/linkedin-logo.png");
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.opinia .n-signature-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-align-content: flex-end;
      -ms-flex-line-pack: end;
          align-content: flex-end;
  margin-right: 1.25rem;
}
.opinia .n-signature-wrapper p {
  margin: 0;
  text-align: right;
}
.opinia .field--name-field-image {
  margin-left: 0;
  padding-right: 0.375rem;
}
.opinia .field--name-field-zajawka p {
  font-size: 0.75rem;
  font-weight: bold;
  line-height: 1.1875rem;
}

.field--name-field-video-embed {
  text-align: center;
}
.field--name-field-video-embed .video-embed-field-lazy-play {
  border: none;
}
.field--name-field-video-embed .video-embed-field-launch-modal {
  max-width: 100%;
  display: inline-block;
}
@media (min-width: 36rem) {
  .field--name-field-video-embed .video-embed-field-launch-modal {
    max-width: 70%;
  }
}
@media (min-width: 48rem) {
  .field--name-field-video-embed .video-embed-field-launch-modal {
    max-width: 60%;
  }
}
@media (min-width: 62rem) {
  .field--name-field-video-embed .video-embed-field-launch-modal {
    max-width: 50%;
  }
}
.field--name-field-video-embed .video-embed-field-launch-modal img {
  max-width: 100%;
  cursor: pointer;
}

.field--name-field-opinie-wideo-ref-field__item {
  max-width: 100%;
}
@media (min-width: 36rem) {
  .field--name-field-opinie-wideo-ref-field__item {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 50%;
        -ms-flex: 1 0 50%;
            flex: 1 0 50%;
    max-width: 50%;
  }
}
@media (min-width: 48rem) {
  .field--name-field-opinie-wideo-ref-field__item {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 31%;
        -ms-flex: 1 0 31%;
            flex: 1 0 31%;
  }
}
.field--name-field-opinie-wideo-ref-field__item .video-embed-field-launch-modal img {
  max-width: 100%;
  cursor: pointer;
}

.node--view-mode-teaser.node--type-wideo {
  border: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.sekcja.wyznaczamy-standardy .npx-form-button-wrapper, body.node--type-page .npx-form-button-wrapper {
  text-align: center;
}
.sekcja.wyznaczamy-standardy a.npx-form-button.npx-autolink, .sekcja.wyznaczamy-standardy a.npx-form-button-inline.npx-autolink, body.node--type-page a.npx-form-button.npx-autolink, body.node--type-page a.npx-form-button-inline.npx-autolink {
  margin-top: 0;
}
.sekcja.wyznaczamy-standardy .field--name-field-extra-tekst-g3 p.npx-hidden-text, body.node--type-page .field--name-field-extra-tekst-g3 p.npx-hidden-text {
  font-size: 1rem;
  font-weight: 300;
}
.sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster, body.node--type-page .field--name-field-wideo-trenera-poster {
  width: 100%;
  height: 100px;
}
@media (min-width: 36rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster, body.node--type-page .field--name-field-wideo-trenera-poster {
    width: 128px;
    height: 128px;
  }
}
@media (min-width: 62rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster, body.node--type-page .field--name-field-wideo-trenera-poster {
    width: 182px;
    height: 182px;
  }
}
.sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster img, body.node--type-page .field--name-field-wideo-trenera-poster img {
  max-width: 100px;
  display: inline-block;
  border-radius: 50%;
  -webkit-box-shadow: 1px 1px 40px #ebedec;
  box-shadow: 1px 1px 40px #ebedec;
}
@media (min-width: 36rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster img, body.node--type-page .field--name-field-wideo-trenera-poster img {
    display: inherit;
    max-width: 128px;
  }
}
@media (min-width: 62rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster img, body.node--type-page .field--name-field-wideo-trenera-poster img {
    max-width: 100%;
  }
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy::before, .sekcja.wyznaczamy-standardy .field--name-field-cechy::after, body.node--type-page .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::after {
  position: absolute;
  content: " ";
  display: block;
  left: -20px;
  top: 0;
  width: calc(100% + 40px);
  height: 100%;
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::before {
  z-index: 0;
  background: transparent url(../images/wyznaczamy-standardy-1.png) repeat-y 0 0;
  -webkit-background-size: 150% auto;
          background-size: 150% auto;
}
@media (max-width: 74.99875rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::before {
    background-position: -100px 0;
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    position: absolute;
  }
}
@media (min-width: 75rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::before {
    -webkit-background-size: 100% auto;
            background-size: 100% auto;
  }
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy::after, body.node--type-page .field--name-field-cechy::after {
  z-index: 1;
  background: transparent url("../images/wyznaczamy-standardy-2.png") repeat-x left bottom;
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy .field__item, body.node--type-page .field--name-field-cechy .field__item {
  z-index: 10;
  clear: both;
  margin-bottom: 1.5625rem;
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy .field__item.field--name-field-opis-trenera, body.node--type-page .field--name-field-cechy .field__item.field--name-field-opis-trenera {
  margin-bottom: 3.125rem;
}
@media (min-width: 36rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-cechy .field__item.field--name-field-opis-trenera, body.node--type-page .field--name-field-cechy .field__item.field--name-field-opis-trenera {
    margin-bottom: 1.5625rem;
  }
}
.sekcja.wyznaczamy-standardy .paragraph--type-cecha-par, body.node--type-page .paragraph--type-cecha-par {
  max-width: 900px;
}

.field_cechy_field_item_even .paragraph--type-cecha-par {
  margin-left: auto;
}

.paragraph--type-cecha-par .field--name-field-opis-trenera {
  width: 100%;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin-bottom: 3.125rem;
}
@media (min-width: 36rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera {
    width: calc(100% - 128px);
    padding: 0 0 0 3.125rem;
  }
}
@media (min-width: 62rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera {
    width: calc(100% - 182px);
    padding: 0 0 0 6.25rem;
  }
}
.paragraph--type-cecha-par .field--name-field-opis-trenera p {
  display: inline-block;
}
.paragraph--type-cecha-par .field--name-field-opis-trenera h3, .paragraph--type-cecha-par .field--name-field-opis-trenera summary {
  position: relative;
  font-size: 1.4rem;
  font-weight: 600;
  line-height: 1.625rem;
  text-align: center;
}
@media (min-width: 36rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera h3, .paragraph--type-cecha-par .field--name-field-opis-trenera summary {
    margin-left: -2.8125rem;
    text-align: left;
    margin-bottom: 0;
  }
}
@media (min-width: 62rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera h3, .paragraph--type-cecha-par .field--name-field-opis-trenera summary {
    margin-left: -5rem;
  }
}
.paragraph--type-cecha-par .field--name-field-opis-trenera h3 sup, .paragraph--type-cecha-par .field--name-field-opis-trenera summary sup {
  display: block;
  max-width: 100px;
  margin: 1.875rem auto;
  padding: 0 0.625rem;
  vertical-align: middle;
  line-height: 1.25rem;
  font-size: 0.625rem;
  color: #fff;
  background-color: #f73965;
  position: relative;
  height: 20px;
}
@media (min-width: 36rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera h3 sup, .paragraph--type-cecha-par .field--name-field-opis-trenera summary sup {
    display: inline-block;
    margin-top: 0;
    margin-left: 0.3125rem;
    vertical-align: sub;
  }
}

a.benefits-linker {
  font-weight: bold;
  line-height: 1.9375rem;
  color: #000;
}
a.benefits-linker:hover, a.benefits-linker:active {
  color: #000;
}
@media (min-width: 36rem) {
  a.benefits-linker {
    margin-left: -2.8125rem;
  }
}
@media (min-width: 62rem) {
  a.benefits-linker {
    margin-left: -5rem;
  }
}

#szkolenie-grupa-13.g13.voucher .narrow h2 {
  margin-left: 0;
  margin-right: 0;
}

.voucher-col-image {
  max-width: 100%;
  margin: 1rem auto;
  text-align: center;
}
@media (min-width: 62rem) {
  .voucher-col-image {
    margin: inherit;
  }
}
.voucher-col-image a {
  margin: 0 auto;
  display: inline-block;
}
.voucher-col-image img {
  max-width: 100%;
}

.voucher-bottom {
  margin-top: 0;
}
@media (min-width: 62rem) {
  .voucher-bottom {
    margin-left: 1rem;
  }
}

.voucher-col-text {
  text-align: center;
  margin-top: 2rem;
}
@media (min-width: 62rem) {
  .voucher-col-text {
    text-align: left;
    margin-top: inherit;
  }
}
.voucher-col-text ul {
  text-align: left;
  list-style-image: url("../images/voucher-check-mark.svg");
}

.g17.sekcja.szybki-kontakt {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  max-width: 100vw;
  background: #f3f3f5;
}

#szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row .no-margin .js-form-type-checkbox {
  margin-left: 0.75rem;
}
@media (max-width: 35.99875rem) {
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row > div.js-form-item {
    text-align: center;
  }
}
@media (min-width: 48rem) {
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row .button.form-submit {
    margin-top: 0.3125rem;
  }
}
@media (min-width: 68.8125rem) {
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row > div.js-form-item, #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row > input {
    width: 33%;
  }
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row .button.form-submit {
    margin-left: 1.5rem;
    margin-top: 2rem;
  }
}

#npx-contact-form-wrapper {
  max-width: 1100px;
}
#npx-contact-form-wrapper .form-type-textfield label {
  width: 100%;
}
#npx-contact-form-wrapper #edit-right-col-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 48rem) {
  #npx-contact-form-wrapper #edit-right-col-row {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
#npx-contact-form-wrapper #edit-right-col-row fieldset {
  margin-left: auto;
  margin-right: auto;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper #edit-right-col-row fieldset {
    margin-left: 0;
    margin-right: 0;
  }
}
@media (min-width: 48rem) {
  #npx-contact-form-wrapper #edit-right-col-row fieldset {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 33%;
        -ms-flex: 1 0 33%;
            flex: 1 0 33%;
  }
}
@media (min-width: 68.8125rem) {
  #npx-contact-form-wrapper #edit-right-col-row fieldset.js-form-item-name {
    padding-left: 0;
  }
}
#npx-contact-form-wrapper .no-margin {
  margin: 0;
  padding: 0;
}
#npx-contact-form-wrapper fieldset {
  max-width: 250px;
}
#npx-contact-form-wrapper .error + div .field-suffix {
  display: block;
}
#npx-contact-form-wrapper .error {
  border-color: red;
}
#npx-contact-form-wrapper .form-checkbox.error + label {
  color: red;
}
#npx-contact-form-wrapper .field-suffix {
  display: none;
  font-size: 0.8125rem;
}
#npx-contact-form-wrapper .npx-contact-thx img {
  height: 60px;
  margin-right: 1.25rem;
}
#npx-contact-form-wrapper .npx-contact-thx .n-big {
  font-size: 1.5rem;
}
#npx-contact-form-wrapper .npx-contact-txt-info {
  margin-bottom: 1rem;
  margin-top: 2.2rem;
  font-size: 1.375rem;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper .npx-contact-txt-info {
    margin-bottom: inherit;
  }
}
#npx-contact-form-wrapper .npx-contact-txt-info .n-sm {
  font-size: 1.125rem;
}
@media (min-width: 81.25rem) {
  #npx-contact-form-wrapper .left-col {
    position: relative;
  }
}
@media (min-width: 81.25rem) {
  #npx-contact-form-wrapper .left-col::before {
    content: "";
    position: absolute;
    top: 45px;
    left: -75px;
    width: 73px;
    height: 78px;
    background: url("../images/call_question_icon.svg") left center no-repeat;
  }
}
#npx-contact-form-wrapper .js-form-type-checkbox input:checked + label {
  background-image: url("../images/checkbox-on.png");
}
#npx-contact-form-wrapper .right-col .row {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper .right-col .row {
    -webkit-box-pack: inherit;
    -webkit-justify-content: inherit;
        -ms-flex-pack: inherit;
            justify-content: inherit;
  }
}
#npx-contact-form-wrapper .button.form-submit {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 3rem auto 1rem;
  margin-top: 3rem;
  font-weight: bold;
  font-size: 0.875rem;
  height: 50px;
  line-height: 1.5rem;
  color: #fff;
  background: var(--secondary);
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  -o-transition: all 500ms;
  -webkit-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  white-space: normal;
  max-width: 210px;
}
#npx-contact-form-wrapper .button.form-submit:hover {
  background-color: #034b7d;
  text-decoration: none;
  color: #fff;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper .button.form-submit {
    margin-left: 0;
  }
}
@media (min-width: 48rem) {
  #npx-contact-form-wrapper .button.form-submit {
    margin-left: 1rem;
  }
}
@media (min-width: 68.8125rem) {
  #npx-contact-form-wrapper .button.form-submit {
    margin-right: 0;
    margin-left: calc(var(--bs-gutter-x) * 0.5);
  }
}
#npx-contact-form-wrapper .button.form-submit::hover {
  background-color: #034b7d;
  text-decoration: none;
}
#npx-contact-form-wrapper .messages {
  display: none !important;
}
#npx-contact-form-wrapper .ajax-progress-throbber {
  background: transparent;
}
#npx-contact-form-wrapper .form-item-phone input {
  background: #fff url("../images/phone_icon.svg") left center no-repeat;
  background-position-x: 8px;
  padding-left: 1.875rem;
}
#npx-contact-form-wrapper .form-item-name input {
  background: #fff url("../images/user_icon.svg") left center no-repeat;
  background-position-x: 8px;
}
#npx-contact-form-wrapper .required-info {
  font-size: 0.75rem;
  text-align: right;
  max-width: 135px;
  padding-right: 0;
}
#npx-contact-form-wrapper .js-form-type-checkbox input {
  display: none;
}
#npx-contact-form-wrapper .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url("../images/checkbox-off.png");
  background-repeat: no-repeat;
  background-position: 0px center;
  display: block;
  margin: 0;
  line-height: 1.25rem;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.simple-popup-blocks-global .spb_center {
  margin-left: -25rem;
  margin-top: -12.5rem;
}

#block-exitpopupblock {
  display: none;
}
#block-exitpopupblock * {
  content-visibility: auto;
}
#block-exitpopupblock > h2 {
  display: none;
}

.popover {
  z-index: 999999 !important;
}
.popover .popover-header {
  margin-top: 0;
  padding-top: 0;
}
.popover a {
  text-decoration: underline;
}

#spb-block-exitpopupblock .js-form-item {
  padding-left: 0;
}
@media (max-width: 35.99875rem) {
  #spb-block-exitpopupblock .left-col, #spb-block-exitpopupblock .right-col {
    margin-bottom: 0 !important;
  }
}
#spb-block-exitpopupblock .form-item-name {
  margin-right: 0.125rem;
}
@media (min-width: 36rem) {
  #spb-block-exitpopupblock .form-item-name input {
    margin-right: -1.25rem;
  }
}
@media (max-width: 35.99875rem) {
  #spb-block-exitpopupblock .form-item-name {
    margin-left: 1.5rem;
  }
}
@media (max-width: 35.99875rem) {
  #spb-block-exitpopupblock .form-type-textfield {
    margin-top: 0;
    margin-bottom: 0 !important;
  }
  #spb-block-exitpopupblock .form-type-textfield input {
    width: 140px;
  }
}
@media (max-width: 47.99875rem) {
  #spb-block-exitpopupblock .wrapper-checkbox-npx {
    margin: 0 auto;
  }
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .left-col::before {
  width: 0;
  height: 0;
  display: none;
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .npx-contact-txt-info {
  margin-top: 1rem;
}
#spb-block-exitpopupblock .col-sm-4.left-col {
  width: 100%;
  -webkit-box-flex: 100%;
  -webkit-flex: 100%;
      -ms-flex: 100%;
          flex: 100%;
  max-width: 100%;
  padding-left: 0;
}
#spb-block-exitpopupblock .col-sm-8.right-col {
  -webkit-box-flex: 100%;
  -webkit-flex: 100%;
      -ms-flex: 100%;
          flex: 100%;
  width: 100%;
  max-width: 100%;
}
#spb-block-exitpopupblock .col-sm-8.right-col .row:last-of-type {
  -webkit-box-ordinal-group: 4;
  -webkit-order: 3;
      -ms-flex-order: 3;
          order: 3;
}
@media (max-width: 47.99875rem) {
  #spb-block-exitpopupblock .col-sm-8.right-col .row:last-of-type {
    max-width: 98%;
  }
}
#spb-block-exitpopupblock .col-sm-8.right-col > .row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
#spb-block-exitpopupblock .spb_close {
  border: 0;
  background: transparent;
  font-size: 1.625rem;
}
@media (max-width: 47.99875rem) {
  #spb-block-exitpopupblock .right-col-npx {
    -webkit-box-pack: center;
    -webkit-justify-content: center;
        -ms-flex-pack: center;
            justify-content: center;
    max-width: 100%;
  }
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .button.form-submit {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 1rem auto 0 0.3125rem;
  width: 100%;
  font-weight: 700;
  font-size: 0.875rem;
  line-height: 1.5rem;
  color: #fff;
  background: #e454ff;
  border: 0;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0;
          box-shadow: 0;
  -webkit-transition: all 500ms;
  -o-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  display: block ruby;
  -webkit-box-ordinal-group: 5;
  -webkit-order: 4;
      -ms-flex-order: 4;
          order: 4;
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .button.form-submit:hover {
  background-color: #c434df;
  text-decoration: none;
}
@media (max-width: 47.99875rem) {
  #spb-block-exitpopupblock #npx-contact-form-wrapper .button.form-submit {
    margin: 0 auto;
  }
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .required-info {
  display: none;
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .form-item {
  margin-top: 0.5rem;
}
#spb-block-exitpopupblock #npx-contact-form-wrapper h5, #spb-block-exitpopupblock #npx-contact-form-wrapper h6 {
  font-weight: 600;
  margin-bottom: 0;
}

.npx-contact-exit-popup-block-wrapper {
  max-width: 1200px;
  color: #000;
}
@media (max-width: 35.99875rem) {
  .npx-contact-exit-popup-block-wrapper .spb-controls {
    right: -2px;
    top: -3px;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 1.25rem 2rem 0;
}
@media (max-width: 35.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container {
    padding: 0.25rem 0.25rem 0;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container h2 {
  margin: 0;
  color: var(--secondary);
  font-size: 3rem;
  font-weight: normal;
  line-height: 3rem;
}
@media (max-width: 47.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container h2 {
    font-size: 1.5rem;
    line-height: 1.75rem;
    padding: 0.5rem 0.25rem 0;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container h3 {
  margin: 1rem 0 !important;
  color: #000 !important;
  font-size: 1.3125rem !important;
}
@media (max-width: 47.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container h3 {
    font-size: 1.25rem !important;
    line-height: 1.5rem;
  }
}
@media (max-width: 35.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container h3 {
    margin: 0.5rem 0 !important;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container p {
  color: #000;
}
@media (max-width: 35.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container p {
    margin: 0.5rem 0;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container hr {
  width: 60%;
  margin-bottom: 0;
  margin-right: auto;
  margin-left: auto;
}
.npx-contact-exit-popup-block-wrapper .nxp-columns-container {
  padding-left: 2rem;
}
.npx-contact-exit-popup-block-wrapper .npx-left-column {
  padding-right: 2.5rem;
}
.npx-contact-exit-popup-block-wrapper .npx-left-column img {
  max-width: 200px;
  margin-right: 2.5rem;
  margin-left: 0.625rem;
}
.npx-contact-exit-popup-block-wrapper .toast-wrapper {
  display: none !important;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .form-submit,
.npx-contact-exit-popup-block-wrapper .npx-right-column form #edit-submit,
.npx-contact-exit-popup-block-wrapper .npx-right-column form #edit-ajax-submit {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 0.625rem auto 0;
  font-weight: bold;
  font-size: 0.875rem;
  line-height: 1.5rem;
  color: #fff;
  background: #e454ff;
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0;
          box-shadow: 0;
  -webkit-transition: all 500ms;
  -o-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .form-submit:hover,
.npx-contact-exit-popup-block-wrapper .npx-right-column form #edit-submit:hover,
.npx-contact-exit-popup-block-wrapper .npx-right-column form #edit-ajax-submit:hover {
  background-color: #c434df;
  text-decoration: none;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper .form-item {
  max-width: 200px;
  margin: 0.625rem 1.25rem 0.625rem 0;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper .form-text {
  color: #808080;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper .form-email {
  color: #808080;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper input {
  max-width: 200px;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper input.error {
  border: 2px solid red !important;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .js-form-type-checkbox input {
  display: none;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .js-form-type-checkbox {
  margin: 0;
  -webkit-box-ordinal-group: 4;
  -webkit-order: 3;
      -ms-flex-order: 3;
          order: 3;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url("../img/checkbox-off.png");
  background-repeat: no-repeat;
  background-position: 0px 2px;
  display: block;
  margin: 0;
  line-height: 1.5rem;
  font-size: 0.875rem;
  font-weight: normal;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .js-form-type-checkbox input:checked + label {
  background-image: url("../img/checkbox-on-blue.png");
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-freshmail-accept + label a {
  color: #000;
  text-decoration: underline;
  cursor: pointer;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-freshmail-accept.form-checkbox.error + label a {
  color: red;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .n-wrapped-btn {
  position: relative;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .n-wrapped-btn span {
  position: absolute;
  top: calc(50% - 7px);
  left: 10px;
  color: #fff;
  font-size: 1.75rem;
  font-weight: bold;
  pointer-events: none;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .n-wrapped-btn input {
  padding-left: 5rem;
  max-width: 320px;
  height: auto;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-align: left;
  font-weight: normal;
  line-height: 0.875rem;
  font-size: 0.75rem;
}
.npx-contact-exit-popup-block-wrapper .npx-msg {
  display: none;
}
.npx-contact-exit-popup-block-wrapper .ajax-progress.ajax-progress-throbber {
  background: transparent;
  margin-top: 5rem;
}

.spb-popup-main-wrapper.spb_center {
  width: 800px !important;
  margin: 0 auto !important;
  -webkit-transform: translate(-50%, -45%);
       -o-transform: translate(-50%, -45%);
          transform: translate(-50%, -45%);
  background-color: #eceeef;
  border: #666 1px solid;
  border-radius: 8px;
  max-height: 100vh;
  overflow-y: auto;
}
@media (max-width: 47.99875rem) {
  .spb-popup-main-wrapper.spb_center {
    max-width: 98%;
  }
}

.spb_close {
  border: none;
  background: transparent;
  font-size: 1.875rem;
}

@media (max-width: 61.99875rem) {
  .block-bootstrap4grow-freshmailpopupexitstronazcytatami-modal.spb_overlay {
    display: none;
  }
}

.npx-dates-table-wrapper {
  margin: 0 0 2.5rem;
}

.npx-date-table-elem {
  padding: 0.9375rem 2.5rem 0.9375rem 4.6875rem;
  border: 1px solid #d0d8db;
  width: 100%;
}
@media (min-width: 36rem) {
  .npx-date-table-elem {
    padding: 0.9375rem 2.5rem 0.9375rem 2.5rem;
    width: auto;
  }
}
.npx-date-table-elem:nth-child(1) {
  background-image: url("../images/radio-on.png");
  background-repeat: no-repeat;
  background-position: 30px 20px;
  padding: 0.9375rem 2.5rem 0.9375rem 4.6875rem;
}
@media (max-width: 35.99875rem) {
  .npx-date-table-elem:nth-child(1) {
    border-bottom: 0;
  }
}
@media (min-width: 36rem) {
  .npx-date-table-elem:nth-child(1) {
    border-right: 0;
  }
}
.npx-date-table-elem::after {
  display: block;
  position: absolute;
  left: 50%;
  top: 0;
  margin-left: -1rem;
  margin-top: -1rem;
  background: transparent url("../images/termin-plus.png") no-repeat 0 0;
  width: 32px;
  height: 32px;
  content: "";
}
@media (min-width: 36rem) {
  .npx-date-table-elem::after {
    left: 0;
    top: 50%;
  }
}
.npx-date-table-elem:nth-child(1)::after {
  display: none;
}

.npx-date-title {
  height: 30px;
  font-size: 1.25rem;
  line-height: 1.875rem;
}

.npx-date-desc {
  height: 20px;
  font-size: inherit;
  line-height: 1.25rem;
  color: #a2a2a2;
}

#npx-training-form .npx-training-date-dates-header {
  line-height: 1.5;
  margin: 2rem 0 1rem 0;
}

.npx-training-date-not-guaranted {
  background: #cfd8dd;
  left: -1px;
  top: calc(100% + 1px);
  line-height: 1.25rem;
  font-size: 0.625rem;
  height: 20px;
}
.npx-training-date-guaranted {
  left: -1px;
  top: calc(100% + 1px);
  background: #fecc09;
  line-height: 1.25rem;
  font-size: 0.625rem;
  height: 20px;
}

.npx-dates-variant-wrapper .ajax-progress.ajax-progress-throbber {
  top: -20px;
  left: -40px;
}
.npx-dates-variant-wrapper .js-form-type-radio input {
  display: none;
}
.npx-dates-variant-wrapper .js-form-type-radio label {
  line-height: 2rem;
  background-color: #fff;
  background-image: url("../images/radio-off.png");
  background-repeat: no-repeat;
  display: block;
  cursor: pointer;
  -webkit-background-size: 25px 25px;
          background-size: 25px;
}
.npx-dates-variant-wrapper .js-form-type-radio .form-radio[type=radio]:checked + label {
  background-color: #fff;
  background-image: url("../images/radio-on.png");
  padding-left: 2.25rem;
}

.npx-counter-wrapper {
  margin-left: -0.25rem;
}

.npx-tabs {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-align-content: stretch;
      -ms-flex-line-pack: stretch;
          align-content: stretch;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 48rem) {
  .npx-tabs {
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}

@media (min-width: 75rem) {
  .npx-box-left {
    padding-right: 1rem;
  }
}
@media (max-width: 74.99875rem) {
  .npx-box-left {
    -webkit-box-ordinal-group: 3;
    -webkit-order: 2;
        -ms-flex-order: 2;
            order: 2;
  }
}

.npx-counter-info {
  color: red;
}

.npx-counter-icon {
  background: transparent url("../images/budzik.png") no-repeat center center;
  display: inline-block;
  width: 20px;
  height: 20px;
  -webkit-background-size: cover;
          background-size: cover;
  margin-right: 0.375rem;
  margin-left: 0.3125rem;
}

#npx-price-info-wrapper {
  font-size: 0.875rem;
}
#npx-price-info-wrapper .form-item-npx-discount-code {
  position: relative;
  display: inline-block;
}
#npx-price-info-wrapper .form-item-npx-discount-code input[type=text] {
  margin: 0;
  height: 32px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-width: 1px;
  width: 100%;
  max-width: 200px;
}
#npx-price-info-wrapper .form-item-npx-discount-code .field-suffix {
  background-color: #fff;
  border: 0;
  display: inline-block;
}
#npx-price-info-wrapper .form-item-npx-discount-code .field-suffix a {
  display: block;
  position: absolute;
  z-index: 10;
  height: 34px;
  background: transparent url("../images/przelicz.png") no-repeat center center;
  width: 20px;
  text-align: left;
  text-indent: -9990px;
  right: 10px;
  top: 0;
  outline: 0;
  border: 0;
  margin-right: 0.25rem;
}
#npx-price-info-wrapper #npx-expand-bottom-wrapper {
  width: 100%;
}
@media (min-width: 48rem) {
  #npx-price-info-wrapper #npx-expand-bottom-wrapper {
    width: auto;
    margin-right: 1rem;
  }
}
#npx-price-info-wrapper .list-group-item {
  border: none;
  display: list-item;
  margin-left: 1.25rem;
  padding: 0;
}
#npx-price-info-wrapper .item-list {
  padding-top: 3rem;
}
#npx-price-info-wrapper li {
  list-style-image: url("../images/li.png");
}
@media (min-width: 48rem) {
  #npx-price-info-wrapper .npx-social-colorbox-link {
    top: -15px;
  }
}
#npx-price-info-wrapper .npx-social-colorbox-link a::before {
  vertical-align: sub;
  height: 20px;
  width: 20px;
  margin: 0 0.3125rem 0 0;
  background: transparent url("../images/price-tag.png") no-repeat center center;
  -webkit-background-size: auto auto;
          background-size: auto;
  background-size: auto;
  -webkit-background-size: cover;
          background-size: cover;
  display: inline-block;
  content: "";
}

@media (min-width: 48rem) {
  .npx-box-left .npx-price-b {
    background-image: url("../images/dziobek.png");
    background-repeat: no-repeat;
    background-position: 0 0;
    margin-top: -0.9375rem;
  }
  .npx-box-left .npx-price-b-a {
    margin-top: 0.9375rem;
  }
}
@media (min-width: 75rem) {
  .npx-box-left .npx-price-b {
    margin-top: 0;
  }
  .npx-box-left .npx-price-b-a {
    margin-top: 0;
  }
}

@media (max-width: 35.99875rem) {
  .npx-price {
    min-height: 150px;
  }
}
.npx-price-a {
  padding: 1.875rem 2.5rem 0 0;
}
.npx-price-a-a {
  line-height: 1.25rem;
  font-size: 0.875rem;
}
.npx-price-a-b {
  font-size: 1.25rem;
}
.npx-price-b {
  width: 100%;
}
@media (min-width: 48rem) {
  .npx-price-b {
    width: 60%;
    padding: inherit;
    padding-top: 3.125rem;
  }
}
@media (min-width: 62rem) {
  .npx-price-b {
    padding-top: 3.125rem;
    width: auto;
  }
}
.npx-price-b-a {
  font-size: 1.25rem;
}
.npx-price-b-b {
  font-size: 1.125rem;
}
.npx-price-b-c {
  color: #a2a2a2;
}
@media (max-width: 47.99875rem) {
  .npx-price-b-c {
    font-size: 0.8125rem;
  }
}
@media (min-width: 75rem) {
  .npx-price-b-c {
    top: 5px;
  }
}

.npx-counter-wrapper {
  top: 7px;
  position: relative;
}
@media (min-width: 48rem) {
  .npx-counter-wrapper {
    top: -10px;
  }
}

.npx-calculation-box {
  padding: 1.875rem 1.875rem 0;
  margin: 0 -1.875rem;
  width: calc(100% + 60px);
  background-color: #f8faf9;
  background-image: url("../images/dziobek2.png");
  background-repeat: no-repeat;
  background-position: 0 0;
}
@media (min-width: 36rem) {
  .npx-calculation-box {
    background-image: url("../images/kreska.png");
    background-repeat: repeat-x;
    background-position: 0 0;
    background-color: transparent;
    padding-top: 0;
  }
}
.npx-calculation-box .list-group-item {
  background: transparent;
  padding-left: 0;
}
.npx-calculation-box input {
  max-width: 200px;
}

#npx-participants-amount-wrapper .description {
  font-size: 1em;
}
#npx-participants-amount-wrapper small.text-muted {
  max-width: calc(100% - 149px);
  float: right;
  color: #000 !important;
  font-size: 1rem;
}
@media (min-width: 36rem) {
  #npx-participants-amount-wrapper small.text-muted {
    float: none;
    max-width: 100%;
  }
}
#npx-participants-amount-wrapper span.ui-spinner {
  display: inline-block;
  position: relative;
  border: 1px solid #d0d8db;
  padding: 0 2.8125rem;
  border-radius: 0;
  margin: 0 0.625rem 0 0;
}
#npx-participants-amount-wrapper span.ui-spinner .form-control:focus {
  background-color: #fff;
  border-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#npx-participants-amount-wrapper span.ui-spinner input {
  border: 0;
  height: 44px;
  line-height: 2.8125rem;
  padding: 0 0.625rem;
  margin: 0;
  border-radius: 0;
  width: 45px;
  text-align: center;
}
@media (min-width: 36rem) {
  #npx-participants-amount-wrapper span.ui-spinner input {
    height: 44px;
  }
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button {
  border: 0;
  outline: 0;
  position: absolute;
  left: 0;
  top: 0;
  height: 45px;
  width: 45px;
  background-color: transparent;
  opacity: 0.85;
  padding: 0;
  margin: 0;
  right: auto;
  background-image: url("../images/spinner-min.png");
  background-position: center center;
  background-repeat: no-repeat;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button:hover {
  background-color: #ebeff2;
  cursor: pointer;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button.ui-corner-tr {
  background-image: url("../images/spinner-plus.png");
  left: auto;
  right: 0;
  border-left: 1px solid #ddd;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button.ui-corner-br {
  border-right: 1px solid #ddd;
}

#szkolenie-grupa-8 a.npx-form-tab {
  margin: 0.9375rem 0;
}
@media (min-width: 48rem) {
  #szkolenie-grupa-8 a.npx-form-tab {
    padding: 1.0625rem;
    margin: 1.25rem 0.9375rem;
  }
}
#szkolenie-grupa-8 .form-item-npx-training {
  display: none;
}

a.npx-form-tab {
  max-width: 340px;
  padding: 0;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
@media (min-width: 48rem) {
  a.npx-form-tab {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 45%;
        -ms-flex: 1 0 45%;
            flex: 1 0 45%;
    max-width: 45%;
  }
}
@media (min-width: 75rem) {
  a.npx-form-tab {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 29%;
        -ms-flex: 1 0 29%;
            flex: 1 0 29%;
    max-width: 29%;
  }
}
a.npx-form-tab:hover {
  -webkit-transform: scale(1.1);
       -o-transform: scale(1.1);
          transform: scale(1.1);
  z-index: 101;
}
a.npx-form-tab.npx-active-tab {
  -webkit-transform: scale(1.1);
       -o-transform: scale(1.1);
          transform: scale(1.1);
  z-index: 100;
}

.npx-form-outer-wrapper {
  -webkit-box-shadow: 0 0 50px 5px #f1f1f1;
  box-shadow: 0 0 50px 5px #f1f1f1;
  line-height: 1.5;
}

#npx-top-wrapper > div:not(#npx-tabs) {
  padding: 0 0.3rem;
}
@media (min-width: 36rem) {
  #npx-top-wrapper > div:not(#npx-tabs) {
    padding: 0 2.5rem;
  }
}

.npx-blocks-program-tab-wrapper {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  border: solid rgba(128, 128, 128, 0.74) 1px;
  border-radius: 4px;
}
@media (min-width: 48rem) {
  .npx-blocks-program-tab-wrapper {
    height: 100%;
  }
}

li a.active .npx-blocks-program-tab-wrapper, li a:hover .npx-blocks-program-tab-wrapper {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
  -webkit-box-shadow: 0 0 15px 0 #888;
          box-shadow: 0 0 15px 0 #888;
}

.npx-program-tabs-wrapper {
  border-radius: 4px;
}
.npx-program-tabs-wrapper .list-group-item {
  border: 0;
}
.npx-program-tabs-wrapper .nav-tabs {
  list-style: none;
  border-bottom: 0;
}
@media (min-width: 48rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 48%;
        -ms-flex: 1 0 48%;
            flex: 1 0 48%;
    max-width: 48%;
  }
}
@media (min-width: 62rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 31%;
        -ms-flex: 1 0 31%;
            flex: 1 0 31%;
    max-width: 31%;
  }
}
@media (min-width: 75rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
            flex: 1 0 25%;
    max-width: 25%;
  }
}
.npx-program-tabs-wrapper .nav-tabs li a {
  text-align: left;
  background: #f3f3f5;
  margin-bottom: 0.9375rem;
}
.npx-program-tabs-wrapper .nav-tabs a {
  text-decoration: none;
  color: #000;
}
.npx-program-tabs-wrapper .nav-tabs a:hover {
  text-decoration: none;
}
.npx-program-tabs-wrapper .nav-tabs a.active {
  text-transform: none;
}
.npx-program-tabs-wrapper .nav-tabs a.active .npx-blocks-program-tab-wrapper {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
  -webkit-box-shadow: 0 0 15px 0 #888;
          box-shadow: 0 0 15px 0 #888;
}

#npx-regular-box-wrapper {
  opacity: 0.65;
}

.npx-box-right:not(.npx-active-box) .npx-price, .npx-box-left:not(.npx-active-box) .npx-price {
  opacity: 0.65;
}
.npx-box-right:not(.npx-active-box) .npx-price-b-a, .npx-box-left:not(.npx-active-box) .npx-price-b-a {
  text-decoration: line-through;
}
@media (max-width: 74.99875rem) {
  .npx-box-right:not(.npx-active-box) .npx-price, .npx-box-left:not(.npx-active-box) .npx-price {
    display: none !important;
  }
}

#npx-regular-box-wrapper .npx-price-b-c {
  top: 0;
}

.npx-active-tab .npx-training-form-tab-wrapper, .npx-form-tab:hover .npx-training-form-tab-wrapper {
  -webkit-box-shadow: 0 0 15px 0 #54534f;
  box-shadow: 0 0 15px 0 #54534f;
  border-radius: 4px;
}

.npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word {
  width: 100%;
  padding: 0 1.25rem;
}
@media (min-width: 36rem) {
  .npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word {
    width: auto;
  }
}
.npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word-last {
  padding-left: 0.625rem;
}
.npx-training-form-type-info-wrapper .npx-spoiler-content {
  font-size: inherit;
}
.npx-training-form-type-info-wrapper .two-columns-template .col-sm ul li {
  list-style-image: url("../images/online-li-yellow.png");
}
.npx-training-form-type-info-wrapper .two-columns-template .col-sm:nth-child(1) ul li {
  list-style-image: url("../images/online-li-blue.png");
}
.npx-training-form-type-info-wrapper .n-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  border: 1px solid #034b7d;
  background-color: #fff;
  cursor: pointer;
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  border: 1px solid #034b7d;
  cursor: pointer;
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle.show-icon::before {
  content: "ROZWIŃ OPIS";
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle.hide-icon::before {
  content: "ZWIŃ OPIS";
}
.npx-training-form-type-info-wrapper .stationary {
  padding: 0.125rem 1rem;
  background: #ffc60c;
  border-radius: 20px;
  border: #ffc60c 2px solid;
  font-weight: 600;
}
.npx-training-form-type-info-wrapper .live-online {
  padding: 0.125rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  background: var(--secondary);
}

.tr-form-stationary {
  padding: 0.1875rem 1rem;
  background: #ffc60c;
  border-radius: 20px;
  font-weight: 800;
  color: #000;
  margin-left: 0.625rem;
  text-transform: uppercase;
  font-size: 0.625rem;
}
.tr-form-online {
  padding: 0.1875rem 1rem;
  border-radius: 20px;
  font-weight: 800;
  background: var(--secondary);
  color: #fff;
  margin-left: 0.625rem;
  text-transform: uppercase;
  font-size: 0.625rem;
}

.npx-variant .fieldset-legend {
  color: #000;
  margin: 2rem 0;
  display: block;
}
.npx-variant h4 {
  line-height: 1.5rem;
}

.npx-training-form-tab-wrapper {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  border: solid rgba(128, 128, 128, 0.44) 1px;
  border-radius: 4px;
  height: 100%;
}
.npx-training-form-tab-wrapper .n-tab-header-inner {
  background: rgba(0, 0, 0, 0.55);
}
.npx-training-form-tab-header-inner {
  border-radius: 4px 4px 0 0;
  min-height: 183px;
  background: rgba(0, 0, 0, 0.55);
}
.npx-training-form-tab-header {
  background-repeat: no-repeat;
  -webkit-background-size: cover;
          background-size: cover;
  border-radius: 4px 4px 0 0;
}
.npx-training-form-tab-header-hours {
  text-shadow: none;
  font-size: 0.75rem;
}
.npx-training-form-tab-header-type {
  font-size: 0.625rem;
}
.npx-training-form-tab-header-type .stationary {
  padding: 0.125rem 1rem;
  background: #ffc60c;
  border-radius: 20px;
  border: #ffc60c 2px solid;
}
.npx-training-form-tab-header-type .live-online {
  padding: 0.125rem 1rem;
  border-radius: 20px;
}
.npx-training-form-tab-header-type .webinar, .npx-training-form-tab-header-type .online {
  padding: 0.125rem 1rem;
  border-radius: 20px;
}
.npx-training-form-tab-header-title h3 {
  font-size: 1.3rem;
  text-shadow: -1px -1px 14px black, 1px -1px 14px black, -1px 1px 14px black, 1px 1px 14px black;
  text-transform: none;
  color: #fff;
  font-weight: 600;
  margin: 2rem 0 1.2rem 0;
}
.npx-training-form-tab-content {
  text-transform: none;
}
.npx-training-form-tab-content p {
  font-size: 0.9375rem;
}
.npx-training-form-tab-content ul {
  padding-left: 0.9375rem;
}
.npx-training-form-tab-content ul li {
  font-size: 0.9375rem;
  text-transform: none;
  font-weight: normal;
  text-align: left;
  list-style-image: url("../images/li_checkmark.png");
  line-height: 1.3rem;
}
.npx-training-form-tab-more {
  margin: auto 0.9375rem 0.625rem 0;
}

#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .tab-content {
  background: #f1fbfc;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  padding: 1.875rem 0;
}
#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .tab-content .tab-pane {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
}

.node--type-landing-page .npx-training-form-tab-header-title h3 {
  margin-top: 0;
}

.ckeditor-accordion-container > dl dt > a > .ckeditor-accordion-toggle::before, .ckeditor-accordion-container > dl dt > a > .ckeditor-accordion-toggle::after {
  background-color: #000;
}

.pdf-program-link img {
  width: 48px;
  height: auto;
}

.program-accordion ul {
  padding-left: 1.2rem;
}
.program-accordion li {
  margin: 0.5rem 0 0.5rem 1.1rem;
}
.program-accordion .pdf-program {
  z-index: 5;
  position: relative;
}
@media (min-width: 62rem) {
  .program-accordion .pdf-program {
    float: right;
  }
}
.program-accordion .pdf-program-link {
  margin-left: 0.625rem;
}
@media (min-width: 36rem) {
  .program-accordion .pdf-program-link {
    margin-left: 1.25rem;
  }
}
.program-accordion .pdf-program-link img {
  width: 38px;
  display: inline-block;
}
@media (min-width: 36rem) {
  .program-accordion .pdf-program-link img {
    width: 48px;
  }
}
.program-accordion .pdf-program-link a {
  margin: 0.9375rem;
}
@media (min-width: 62rem) {
  .program-accordion .pdf-program span {
    display: block;
    margin-bottom: 0.625rem;
  }
}
@media (min-width: 62rem) {
  .program-accordion h2.field-label-above {
    margin-right: 10.625rem;
  }
}
.program-accordion .ckeditor-accordion-container h4 {
  font-size: 1.5rem;
  font-weight: 700;
}
.program-accordion .ckeditor-accordion-container > dl dt > a, .program-accordion .ckeditor-accordion-container > dl dt > a:not(.button) {
  color: inherit;
  border-bottom: #dcdddf 1px solid;
  text-decoration: none;
}
.program-accordion .ckeditor-accordion-container > dl dt > a:hover, .program-accordion .ckeditor-accordion-container > dl dt > a:not(.button):hover {
  color: inherit;
  text-decoration: underline;
}
.program-accordion .ckeditor-accordion-toggler {
  background-color: transparent !important;
  color: inherit;
  font-size: 1.2rem;
}
.program-accordion dl dt > a {
  background-color: #ecedef;
  color: #000;
  border-bottom: #dcdddf 1px solid;
}
.program-accordion dl {
  border: #dcdddf 1px solid;
}

#szkolenie-grupa-6.sekcja.w100.w100limitContent .tab-content {
  background: #f1fbfc;
  padding: 1.875rem 0;
}

.npx-training-form-tab-header {
  position: relative;
}
.npx-training-form-tab-header-inner {
  z-index: 9;
  position: relative;
}
.npx-training-form-tab-header img {
  position: absolute;
  z-index: 2;
}

.npx-more-program-tabs-wrapper {
  background: #f1fbfc;
}
@media (min-width: 48rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 100%;
        -ms-flex: 1 0 100%;
            flex: 1 0 100%;
  }
}
@media (min-width: 62rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 100%;
        -ms-flex: 1 0 100%;
            flex: 1 0 100%;
  }
}
@media (min-width: 75rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    max-width: 49%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 49%;
        -ms-flex: 1 0 49%;
            flex: 1 0 49%;
  }
}

#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .npx-more-program-tabs-wrapper .item-list {
  margin-top: 0;
}

.npx-program-tabs-wrapper .nav-tabs li .npx-blocks-program-tab-wrapper > a.npx-no-autolink-with-link {
  margin-bottom: 0;
}
.npx-program-tabs-wrapper .nav-tabs li .npx-blocks-program-tab-wrapper > a.npx-no-autolink-with-link .n-tab-header {
  height: 100%;
}

.npx-more-program-tabs-wrapper {
  border-radius: 4px;
}
.npx-more-program-tabs-wrapper .list-group-item {
  border: 0;
}
.npx-more-program-tabs-wrapper .nav-tabs {
  list-style: none;
  border-bottom: 0;
}
@media (min-width: 48rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 48%;
        -ms-flex: 1 0 48%;
            flex: 1 0 48%;
    max-width: 48%;
  }
}
@media (min-width: 62rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 31%;
        -ms-flex: 1 0 31%;
            flex: 1 0 31%;
    max-width: 31%;
  }
}
@media (min-width: 75rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
            flex: 1 0 25%;
    max-width: 25%;
  }
}
.npx-more-program-tabs-wrapper .nav-tabs li {
  background: transparent;
}
.npx-more-program-tabs-wrapper .nav-tabs li .npx-blocks-program-tab-wrapper {
  background: #fff;
}
.npx-more-program-tabs-wrapper .nav-tabs li a {
  text-align: left;
  background: #f3f3f5;
  margin-bottom: 0.9375rem;
}
.npx-more-program-tabs-wrapper .nav-tabs a {
  text-decoration: none;
  color: #000;
}
.npx-more-program-tabs-wrapper .nav-tabs a:hover {
  text-decoration: none;
}
.npx-more-program-tabs-wrapper .nav-tabs a.active {
  text-transform: none;
}
.npx-more-program-tabs-wrapper .nav-tabs a.active .npx-blocks-program-tab-wrapper {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
  -webkit-box-shadow: 0 0 15px 0 #888;
          box-shadow: 0 0 15px 0 #888;
}

.npx-more-tabs-txt-wrapper {
  background: #f1fbfc;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  padding: 1.875rem 0;
}
.npx-more-tabs-txt-wrapper .npx-more-tabs-txt {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
}

.npx-more-program-tabs-wrapper .nav-tabs li a.npx-no-autolink,
.npx-more-program-tabs-wrapper .nav-tabs li .npx-training-form-tab-more a {
  background-color: transparent;
}

.npx-more-program-tabs-wrapper .nav-tabs li a:not(.npx-no-autolink) {
  background-color: transparent;
  text-decoration: underline;
}

::-webkit-scrollbar {
  -webkit-appearance: none;
}

::-webkit-scrollbar:vertical {
  width: 12px;
}

::-webkit-scrollbar:horizontal {
  height: 12px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  border: 2px solid #ffffff;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #ffffff;
}

.training-terms-block {
  width: 800px;
}
@media (min-width: 62rem) {
  .training-terms-block {
    width: 100%;
  }
}
.training-terms-block-wrapper {
  overflow-x: scroll;
}
@media (min-width: 62rem) {
  .training-terms-block-wrapper {
    overflow-x: auto;
  }
}
.training-terms-block-td-1 {
  width: 16%;
}
.training-terms-block-td-2 {
  width: 20%;
}
.training-terms-block-td-3 {
  width: 12%;
}
.training-terms-block-td-4 {
  width: 6%;
}
.training-terms-block-td-5 {
  width: 12%;
}
.training-terms-block-h4 {
  font-size: 1rem;
}
.training-terms-block-with-sustable-table {
  margin: 0.3125rem 0 0.625rem 0;
}
.training-terms-block-th {
  padding-bottom: 0.625rem;
}
.training-terms-block-td-clickable::after {
  content: "";
  width: 13px;
  height: 7px;
  display: inline-block;
  background: transparent no-repeat url(../images/menu-arrow.png) 0 0;
  margin-left: 0.625rem;
  -o-transition: -o-transform 300ms ease;
  -webkit-transition: -webkit-transform 300ms ease;
  transition: -webkit-transform 300ms ease;
  transition: transform 300ms ease;
  transition: transform 300ms ease, -webkit-transform 300ms ease, -o-transform 300ms ease;
  -webkit-transform: scaleY(-1);
  -o-transform: scaleY(-1);
     transform: scaleY(-1);
}
.training-terms-block-td-clickable.open::after {
  -webkit-transform: scaleY(1);
  -o-transform: scaleY(1);
     transform: scaleY(1);
}
.training-terms-block-npx-form-button {
  padding: 0.625rem 0.9375rem;
  white-space: nowrap;
}
.training-terms-block .ask-for-course-closed {
  padding: 0.625rem 0.9375rem;
}

.load-more-terms {
  border: 2px solid #0053B3;
  margin: -1.5rem auto 1.875rem;
  border-radius: 6px;
  width: 185px;
  background-color: #fff;
}
.load-more-terms-bg {
  height: 0;
  background: #d0d8db;
  margin-top: 4.375rem;
}
.load-more-terms-wrapper {
  position: absolute;
  width: calc(100vw - 40px);
}
@media (min-width: 62rem) {
  .load-more-terms-wrapper {
    position: relative;
    width: auto;
  }
}

.paragraph--type-arguments {
  margin-top: 3.75rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 36rem) {
  .paragraph--type-arguments {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
.paragraph--type-arguments .group-left {
  width: 100%;
  height: 100px;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .group-left {
    width: 45%;
  }
}
.paragraph--type-arguments .group-right {
  width: 100%;
  height: auto;
  background: transparent;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .group-right {
    width: 55%;
    height: 100px;
    background: #f4f7f5;
    border-radius: 100px 0 0 100px;
  }
}
.paragraph--type-arguments a.n-get-pdf {
  padding-right: 2.5rem;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments a.n-get-pdf {
    padding-right: 0;
  }
}
.paragraph--type-arguments .field--name-field-arguments-right {
  padding-left: 1.25rem;
  max-width: 400px;
  margin-left: auto;
  border-left: #000 1px solid;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .field--name-field-arguments-right {
    padding-left: 2.5rem;
  }
}
.paragraph--type-arguments .field--name-field-arguments-right p {
  font-size: 0.7rem;
  line-height: 1rem;
}
.paragraph--type-arguments .field--name-field-arguments-left {
  max-width: 300px;
  padding-left: 3.125rem;
  font-size: 1.2rem;
}
.paragraph--type-arguments .field--name-field-arguments-left::before {
  content: "";
  position: absolute;
  width: 100px;
  height: 100px;
  top: 0;
  left: 0;
  z-index: -1;
  background: #f4f7f5;
  border-radius: 100px;
}

.inner-menu-sticky #block-npxfloatingbeltblock {
  -webkit-box-shadow: none;
  box-shadow: none;
  border-bottom: 1px solid #ebedec;
}

#block-npxfloatingbeltblock {
  top: 0px;
  display: none;
  z-index: 9999;
  width: 100%;
  left: 0;
  right: 0;
}
@media (min-width: 68.8125rem) {
  #block-npxfloatingbeltblock {
    width: 100vw;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
  }
}
#block-npxfloatingbeltblock * {
  content-visibility: auto;
}

.npx-floating-block-wrapper {
  max-width: 1415px;
}
.npx-floating-block-button-wrapper {
  padding-right: 0.625rem;
}
.npx-floating-block-form-button-wrapper {
  padding-right: 0.625rem;
}
@media (min-width: 48rem) {
  .npx-floating-block-amount-wrapper {
    margin-right: 1rem;
  }
}
@media (min-width: 62rem) {
  .npx-floating-block-amount-wrapper {
    line-height: 4.5rem;
  }
}

#npx-floating-block-wrapper {
  max-width: 1415px;
}
#npx-floating-block-wrapper img {
  max-height: 66px;
}

h2.field-label-above p {
  font-size: inherit;
  font-weight: inherit;
  margin-bottom: inherit;
}

@media (min-width: 62rem) {
  .field--name-field-blog-posts {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
  .field--name-field-blog-posts .node--type-article.node--view-mode-grow3 {
    height: 100%;
    display: block;
    position: relative;
    padding-bottom: 3.5rem;
  }
  .field--name-field-blog-posts .field--name-node-link {
    margin-left: auto;
    margin-right: auto;
  }
  .field--name-field-blog-posts .field__item {
    max-width: 92%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 32%;
        -ms-flex: 1 0 32%;
            flex: 1 0 32%;
  }
}
.field--name-field-blog-posts .field__item {
  text-align: center;
}
.field--name-field-blog-posts h3 {
  margin: 0;
  font-size: 1.25rem;
}
.field--name-field-blog-posts img {
  -webkit-transition: -webkit-transform 0.2s;
  transition: -webkit-transform 0.2s;
  -o-transition: -o-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s, -o-transform 0.2s;
}
.field--name-field-blog-posts img:hover {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
}
.field--name-field-blog-posts .field--name-field-image {
  overflow: hidden;
}
.field--name-field-blog-posts .node--type-article {
  max-width: 450px;
  margin: 0 auto;
}
@media (max-width: 61.99875rem) {
  .field--name-field-blog-posts .node--type-article {
    margin-bottom: 2rem;
  }
}
.field--name-field-blog-posts .node--type-article h3 a {
  color: #000;
}

#block-valentinesexitpopupotherpagesblock, #block-valentinesexitpopuptrainingblock {
    display: none;
}

#spb-block-valentinesexitpopuptrainingblock .spb_top_center.spb-popup-main-wrapper, #spb-block-valentinesexitpopupotherpagesblock .spb_top_center.spb-popup-main-wrapper {
  max-width: 1000px;
  top: 50%;
  -webkit-transform: translateY(-50%);
       -o-transform: translateY(-50%);
          transform: translateY(-50%);
  border: none;
}
@media (max-width: 35.99875rem) {
  #spb-block-valentinesexitpopuptrainingblock .spb_top_center.spb-popup-main-wrapper, #spb-block-valentinesexitpopupotherpagesblock .spb_top_center.spb-popup-main-wrapper {
    max-width: 98%;
  }
}
@media (min-width: 36rem) {
  #spb-block-valentinesexitpopuptrainingblock .spb_top_center.spb-popup-main-wrapper, #spb-block-valentinesexitpopupotherpagesblock .spb_top_center.spb-popup-main-wrapper {
    width: 80% !important;
  }
}

.valentines-fb {
  height: 20%;
  width: 28%;
  right: 7%;
  bottom: 2%;
}

.valentines-link {
  height: 12%;
  width: 22%;
  left: 12%;
  bottom: 4%;
}

.npx-program-tabs-wrapper .nav-tabs li a.npx-no-autolink,
.npx-program-tabs-wrapper .nav-tabs li .npx-training-form-tab-more a {
  background-color: transparent;
}

.npx-program-tabs-wrapper .nav-tabs li a:not(.npx-no-autolink) {
  background-color: transparent;
  text-decoration: underline;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
