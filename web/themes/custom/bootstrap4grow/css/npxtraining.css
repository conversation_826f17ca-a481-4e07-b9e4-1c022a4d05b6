@charset "UTF-8";
/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

.node--type-npxtraining h2 {
  font-weight: 700;
  font-size: 2rem;
  line-height: 1.5;
  padding: 0;
  margin: 5rem 0 2.8125rem;
  position: relative;
  display: block;
}
.node--type-npxtraining .ajax-progress-throbber {
  background: transparent;
}
.node--type-npxtraining .ajax-progress-throbber.ajax-progress {
  display: block;
  position: absolute;
  left: 0;
  padding-top: 0.3125rem;
  top: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  z-index: 100;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
}

@media (max-width: 74.99875rem) {
  .ds-2col-fluid > .group-left {
    width: 100%;
  }
}
.ds-2col-fluid > .group-right {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  min-height: 100%;
}
.ds-2col-fluid > .group-right .obraz {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: " ";
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
  background-color: transparent !important;
  background-position-y: 53px !important;
  z-index: 10;
}

.field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy div[class*=field-name-field-field-tytul-sekcji-] h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy div[class*=field-name-field-tytul-sekcji-] h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy .sekcja > h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy h2.field__label::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy h2.field-label-above::after, body div[class*=field--name-field-tytul-sekcji-] h2::after, body div[class*=field-name-field-field-tytul-sekcji-] h2::after, body div[class*=field-name-field-tytul-sekcji-] h2::after, body .sekcja > h2::after, body h2.field__label::after, body h2.field-label-above::after {
  display: block;
  left: 50%;
  margin-left: -1.25rem;
  position: absolute;
  content: " ";
  width: 40px;
  height: 2px;
  background: #fecc09;
  bottom: -15px;
}
@media (min-width: 62rem) {
  .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy div[class*=field-name-field-field-tytul-sekcji-] h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy div[class*=field-name-field-tytul-sekcji-] h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy .sekcja > h2::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy h2.field__label::after, .field--name-dynamic-block-fieldnode-ds-szkolenie-form-zapisy h2.field-label-above::after, body div[class*=field--name-field-tytul-sekcji-] h2::after, body div[class*=field-name-field-field-tytul-sekcji-] h2::after, body div[class*=field-name-field-tytul-sekcji-] h2::after, body .sekcja > h2::after, body h2.field__label::after, body h2.field-label-above::after {
    left: 0;
    margin-left: 0;
  }
}

.narrow {
  max-width: 1200px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
}
.narrow h2 {
  margin-left: 0;
  margin-right: 0;
}
@media (min-width: 81.25rem) {
  .narrow h2 {
    margin-left: -3vw;
    margin-right: -3vw;
  }
}
@media (min-width: 100rem) {
  .narrow h2 {
    margin-left: -6.625rem;
    margin-right: -6.625rem;
  }
}

.sekcja {
  max-width: 1415px;
  padding: 1.25rem;
  margin: 0 auto;
  overflow: hidden;
}
@media (min-width: 36rem) {
  .sekcja {
    padding: 1.875rem;
  }
}
.sekcja.w100 {
  max-width: 100vw;
  padding: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

[id^=szkolenie-grupa-]::before {
  display: block;
  content: " ";
  margin-top: -3.125rem;
  height: 50px;
  visibility: hidden;
}

#szkolenie-grupa-2 .slick__arrow {
  bottom: 0;
  top: unset;
}
#szkolenie-grupa-2 .slick-next {
  right: 0;
  position: absolute;
  left: auto;
}
#szkolenie-grupa-2 .slick-dots {
  display: inline-block;
  position: relative;
  bottom: -15px;
}
#szkolenie-grupa-2 .slick-prev:hover, #szkolenie-grupa-2 .slick-prev:focus, #szkolenie-grupa-2 .slick-next:hover, #szkolenie-grupa-2 .slick-next:focus {
  background-image: url("../images/arrows.png");
  border-radius: 0;
}
#szkolenie-grupa-2 .slick {
  max-width: 600px;
  margin: 0 auto;
}
#szkolenie-grupa-2 .npx-program-button {
  margin-top: 0;
}
@media (min-width: 36rem) {
  #szkolenie-grupa-2 .ds-3col-stacked-fluid > .group-middle {
    width: calc(100% - 110px);
  }
}

.field--name-field-nasza-jakosc-twoj-komfort .y-box-outer {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  z-index: 10;
}
.field--name-field-nasza-jakosc-twoj-komfort .y-box-inner {
  margin: 0 5%;
  padding: 1.875rem;
  background-color: #fff;
  -webkit-box-shadow: 1px 1px 40px #ebedec;
  box-shadow: 1px 1px 40px #ebedec;
  border-radius: 5px;
  text-align: center;
  font-size: 1.25rem;
  line-height: 1.5;
  font-weight: bold;
  text-align: center;
}
@media (min-width: 36rem) {
  .field--name-field-nasza-jakosc-twoj-komfort .y-box-inner {
    margin: 0 1%;
    padding: 1.875rem 2.5rem 1.875rem;
  }
}
@media (min-width: 62rem) {
  .field--name-field-nasza-jakosc-twoj-komfort .y-box-inner {
    margin: 0 1.875rem;
    padding: 1.875rem 4.0625rem 2.5rem;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}
.field--name-field-nasza-jakosc-twoj-komfort .y-box-inner-1 {
  padding: 0.125rem 0.5rem;
  background: #B3002B;
  color: #fff;
  font-size: 0.625rem;
  line-height: 1rem;
  display: inline-block;
}
.field--name-field-nasza-jakosc-twoj-komfort .y-box-inner-2 {
  display: block;
  margin: 0.625rem 0 0 0;
}
.field--name-field-nasza-jakosc-twoj-komfort .y-box-inner-2 strong {
  font-style: normal;
}
.field--name-field-nasza-jakosc-twoj-komfort .y-image {
  height: 60vw;
  background: transparent url("../images/4grow-sala-mobile.jpg") no-repeat center center;
  -webkit-background-size: cover;
          background-size: cover;
  margin-top: -3vw;
  position: relative;
  z-index: 1;
  margin-bottom: 1rem;
}
@media (min-width: 36rem) {
  .field--name-field-nasza-jakosc-twoj-komfort .y-image {
    margin-top: -6vw;
    -webkit-background-size: cover;
            background-size: cover;
  }
}
@media (min-width: 62rem) {
  .field--name-field-nasza-jakosc-twoj-komfort .y-image {
    margin-top: -10.65rem;
    height: 33vw;
    background: transparent url("../images/4grow-sala-szkoleniowa-desktop.jpg") no-repeat center center;
    -webkit-background-size: cover;
            background-size: cover;
  }
}

.page-node-5593 .field--name-field-nasza-jakosc-twoj-komfort .y-image {
  background: transparent url("../images/sala_antystres_bg.jpg") no-repeat center center;
  -webkit-background-size: cover;
          background-size: cover;
}

@media (min-width: 100rem) {
  .container.field-name-field-tytul-sekcji-g4 {
    max-width: 1415px;
  }
}
.field--name-field-npxtraining-paragraf-trene > .field__item {
  margin-bottom: 2rem;
}

.paragraph--type-trener-do-szkolenia-par .wrapper-1 {
  position: relative;
  margin: 0 0 1.25rem;
  text-align: center;
}
.paragraph--type-trener-do-szkolenia-par .view-mode-bootstrap_carousel.ds-1col {
  padding: 0.625rem;
}
.paragraph--type-trener-do-szkolenia-par .wrapper-1 .wrapper-2 {
  z-index: 2;
  top: 6%;
  position: relative;
  right: 0;
  width: 100%;
  height: auto;
  display: block;
  margin: 0.9375rem 0 0 0;
  text-align: center;
}
@media (min-width: 36rem) {
  .paragraph--type-trener-do-szkolenia-par .wrapper-1 .wrapper-2 {
    position: absolute;
    right: 5%;
    width: 50%;
    height: 90%;
    -webkit-box-align: center;
    -webkit-align-items: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-align-content: center;
        -ms-flex-line-pack: center;
            align-content: center;
    -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
            flex-wrap: wrap;
  }
}
.paragraph--type-trener-do-szkolenia-par .wrapper-1 .wrapper-2 h3 {
  line-height: 1.2;
  margin: 0 0 0.75rem;
  padding: 0;
}
.paragraph--type-trener-do-szkolenia-par.ds-2col img {
  max-width: 100%;
}
.paragraph--type-trener-do-szkolenia-par .view-mode-bootstrap_carousel.ds-1col {
  padding: 0.625rem;
}
.paragraph--type-trener-do-szkolenia-par.ds-2col > .group-right {
  width: 100%;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
@media (min-width: 62rem) {
  .paragraph--type-trener-do-szkolenia-par.ds-2col > .group-right {
    float: right;
    padding: 0 0 0 3%;
    width: 50%;
  }
}
.paragraph--type-trener-do-szkolenia-par.ds-2col > .group-left {
  width: 100%;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
@media (min-width: 62rem) {
  .paragraph--type-trener-do-szkolenia-par.ds-2col > .group-left {
    float: left;
    width: 50%;
    padding: 0 3% 0 0;
  }
}

#szkolenie-grupa-5 .slick-dots {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: -1.5rem;
}
#szkolenie-grupa-5 .slick--less .slick-track {
  text-align: left;
}
@media (min-width: 36rem) {
  #szkolenie-grupa-5 .slick--less .slick-track {
    text-align: center;
  }
}
#szkolenie-grupa-5 .field--name-field-zajawka {
  padding: 0 0.625rem;
}
#szkolenie-grupa-5 .field--name-field-zajawka p {
  margin: 0 0 0.5rem;
  font-size: 80%;
}
#szkolenie-grupa-5 .field--name-field-zajawka > p:first-child {
  margin-left: -0.625rem;
  margin-right: -0.625rem;
}
#szkolenie-grupa-5 .field--name-field-zajawka > p:first-child img {
  width: 100%;
}
#szkolenie-grupa-5 .slick__arrow {
  bottom: 15px;
  top: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
#szkolenie-grupa-5 .draggable {
  max-height: 410px;
}
#szkolenie-grupa-5 .view-mode-bootstrap_carousel.ds-1col > .inner {
  border: 1px solid #d0d8db;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
#szkolenie-grupa-5 .field--name-field-link-do-profilu-trenera {
  display: none;
}
#szkolenie-grupa-5 .field--name-field-npxtrainer-position {
  text-align: center;
  margin-bottom: 1.5rem;
}
#szkolenie-grupa-5 h3 {
  font-size: 140%;
  line-height: 1.2;
  margin: 1rem 0 0.75rem;
  text-align: center;
}
#szkolenie-grupa-5 .field--name-field-opis-trenera {
  max-height: none;
}
#szkolenie-grupa-5 .field--name-field-opis-trenera span {
  font-size: 1rem;
  color: #343a40;
  line-height: 1.5rem;
  font-family: Muli, sans-serif;
  text-align: left;
  word-break: unset;
  display: inline;
}

#szkolenie-grupa-6 {
  max-width: 100vw;
  padding: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}
#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .item-list {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 5.625rem auto;
}

.field--name-dynamic-block-fieldnode-ds-training-program-block .field-label-above:first-of-type:not(:last-of-type):after {
  display: none;
}

#szkolenie-grupa-8 #npx-bottom-wrapper input {
  width: 100%;
  margin-left: 0.125rem;
  font-size: 0.875rem;
  padding-left: 0.5rem;
}
#szkolenie-grupa-8 #npx-bottom-wrapper textarea {
  width: 100%;
  height: 200px;
}
@media (min-width: 36rem) {
  #szkolenie-grupa-8 #npx-bottom-wrapper textarea {
    height: auto;
  }
}
#szkolenie-grupa-8 #npx-bottom-wrapper button {
  text-transform: uppercase;
}
#szkolenie-grupa-8 #npx-training-form .edit-npx-training-date > .fieldset-wrapper > div > .form-item {
  margin-bottom: 1.25rem;
}
#szkolenie-grupa-8 .npx-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  background: #fff;
  border: 1px solid #034b7d;
  cursor: pointer;
  width: auto;
  margin: 0 auto;
  display: inline-block;
}
#szkolenie-grupa-8 .npx-spoiler-toggle.show-icon:before {
  content: "ROZWIŃ OPIS";
}
#szkolenie-grupa-8 .npx-spoiler-toggle.hide-icon:before {
  content: "ZWIŃ OPIS";
}
#szkolenie-grupa-8 .npx-spoiler-content {
  font-size: inherit;
}
#szkolenie-grupa-8 .npx-spoiler-content a.npx-autolink {
  color: inherit;
  text-decoration: none;
}
#szkolenie-grupa-8 .n-spoiler-toggle-wrapper .n-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  background: #fff;
  border: 1px solid #034b7d;
  cursor: pointer;
  display: inline-block;
}

#npx-online-training-wrapper .fieldset-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
#npx-online-training-wrapper .fieldset-wrapper .field-prefix {
  -webkit-align-self: center;
      -ms-flex-item-align: center;
          align-self: center;
}
#npx-online-training-wrapper .fieldset-wrapper #edit-npx-online-training {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 48rem) {
  #npx-online-training-wrapper .fieldset-wrapper #edit-npx-online-training {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
#npx-online-training-wrapper .fieldset-wrapper #edit-npx-online-training .js-form-item-npx-online-training {
  margin: 0.5rem 0;
  padding-left: 0;
}
@media (min-width: 48rem) {
  #npx-online-training-wrapper .fieldset-wrapper #edit-npx-online-training .js-form-item-npx-online-training {
    margin: auto 0.625rem;
  }
}

.npx-fv-paper-wrapper .radio {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.npx-fv-paper-wrapper .fieldset-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.npx-fv-paper-wrapper .fieldset-wrapper .form-radios {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.npx-fv-paper-wrapper .fieldset-wrapper .form-radios .js-form-item-npx-fv-paper {
  margin: auto 0.625rem;
}

.npx-fv-paper-info {
  font-size: 1rem !important;
}

.npx-location-info-wrapper.npx-noborder {
  padding-bottom: 1.25rem;
}

#npx-training-form .npx-form-additional-description {
  font-size: 0.875rem;
  top: -1.25rem;
  position: relative;
}
#npx-training-form [id^=edit-fields-wrapper] {
  display: block;
  margin-right: -0.3125rem;
  margin-left: -0.3125rem;
  overflow: hidden;
}
@media (min-width: 48rem) {
  #npx-training-form [id^=edit-fields-wrapper] {
    margin-top: 0.5rem;
  }
}
#npx-training-form [id^=edit-fields-wrapper] .js-form-item {
  max-width: 99%;
  margin-top: 0.5rem;
}
@media (min-width: 62rem) {
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item {
    float: left;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:not(:last-of-type) {
    margin-right: 0.125rem;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(1) {
    width: 9%;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(2), #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(5) {
    width: 10%;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(3) {
    width: 20%;
  }
  #npx-training-form [id^=edit-fields-wrapper] .js-form-item:nth-child(4) {
    width: 23%;
  }
}
#npx-training-form .field-suffix {
  display: none;
}
#npx-training-form .npx-form-error {
  display: none;
}
#npx-training-form #npx-bottom-wrapper {
  display: none;
  background-color: #f7f9f8;
}
#npx-training-form #npx-bottom-wrapper.n-webinar-mode .npx-online-training-header, #npx-training-form #npx-bottom-wrapper.n-webinar-mode .form-item-npx-online-training {
  display: none;
}
@media (min-width: 62rem) {
  #npx-training-form .npx-form-additional-description, #npx-training-form .form-item-npx-fv-comment {
    max-width: calc(72% + 17px);
  }
}
#npx-training-form legend {
  font-size: 1rem;
  margin-bottom: 0;
}
#npx-training-form h4 {
  font-size: 1rem;
  font-weight: 700;
  margin: 0;
}
#npx-training-form .fieldset-wrapper .field-prefix {
  -webkit-align-self: center;
      -ms-flex-item-align: center;
          align-self: center;
}
#npx-training-form .npx-fv-paper-wrapper .field-prefix {
  margin-right: 0.5rem;
}
@media (min-width: 48rem) {
  #npx-training-form .npx-fv-paper-wrapper .field-prefix {
    margin-top: -0.5rem;
  }
}
#npx-training-form.with-bottom-wrapper #npx-bottom-wrapper {
  display: block;
}
#npx-training-form.with-bottom-wrapper #npx-expand-bottom-wrapper {
  display: none !important;
}
#npx-training-form.with-bottom-wrapper #npx-expand-bottom-wrapper-online {
  display: none !important;
}
#npx-training-form div.form-item-npx-training {
  display: none;
}
@media (min-width: 48rem) {
  #npx-training-form #npx-top-wrapper {
    padding-bottom: 0.625rem;
  }
}
#npx-training-form.n-hide-hotel .form-item-npx-hotel-info {
  display: none;
}
#npx-training-form div.npx-border-green {
  margin-bottom: 1.25rem;
}
#npx-training-form div.npx-border-green-inner {
  border: #c8dc32 1px solid;
  padding: 0.3125rem 0.625rem;
}
#npx-training-form div.npx-border-gray {
  margin-bottom: 1.25rem;
}
#npx-training-form div.npx-border-gray-inner {
  border: #d4d8db 1px solid;
  padding: 0.3125rem 0.625rem;
}
#npx-training-form input#edit-npx-accept-4:invalid + label {
  color: #fc5353;
}
#npx-training-form a#npx-expand-bottom-wrapper-online {
  width: 100%;
  margin-left: 0;
  margin-right: 0;
  margin-top: 0;
  text-align: center;
  margin-bottom: 1rem;
}
@media (min-width: 48rem) {
  #npx-training-form a#npx-expand-bottom-wrapper-online {
    margin-right: 1rem;
    width: auto;
    text-align: left;
  }
}

.npx-float::after {
  clear: both;
  content: ".";
  display: block;
  height: 0;
  visibility: hidden;
}

.npx-training-type .field-prefix {
  margin-right: 1rem;
}

#szkolenie-grupa-14 {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}
@media (min-width: 87.5rem) {
  #szkolenie-grupa-14 {
    max-width: 1415px;
    width: 100%;
    margin: 0 auto;
    left: 0;
    right: 0;
  }
}

.field--name-field-question {
  position: relative;
  display: inline-block;
  cursor: pointer;
  padding-left: 2rem;
}
.field--name-field-question.active:before {
  -webkit-transform: rotate(-45deg);
       -o-transform: rotate(-45deg);
          transform: rotate(-45deg);
  top: 28px;
}
.field--name-field-question:before {
  position: absolute;
  top: 22px;
  cursor: pointer;
  content: "";
  display: inline-block;
  width: 11px;
  height: 11px;
  border-right: 2px solid #343a40;
  border-top: 2px solid #343a40;
  -webkit-transform: rotate(135deg);
  -o-transform: rotate(135deg);
     transform: rotate(135deg);
  margin-right: 0.5em;
  margin-left: 1em;
  max-width: 12px;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 auto;
  -webkit-flex: 1 0 auto;
          flex: 1 0 auto;
  -ms-flex-item-align: center;
  -webkit-align-self: center;
          align-self: center;
  left: -12px;
  -webkit-transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
}
.field--name-field-question h3, .field--name-field-question p, .field--name-field-question h4 {
  margin: 0.625rem 0;
}

.field--name-field-answer h3, .field--name-field-answer h4 {
  margin: 0 0 0.625rem 0;
}

.g18.sekcja.kontakt-email {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  max-width: 100vw;
  background: #f3f3f5;
}
.g18.sekcja.kontakt-email .ajax-progress-throbber {
  background: transparent;
}

.npx-mailcontact-header h2 {
  font-size: 1.5rem;
}

@media (min-width: 62rem) {
  .field--name-field-loga-firm {
    max-height: 106px;
    overflow: hidden;
  }
}
.field--name-field-loga-firm .field__item.col-auto {
  -webkit-transform: scale(0.7);
       -o-transform: scale(0.7);
          transform: scale(0.7);
}
@media (min-width: 36rem) {
  .field--name-field-loga-firm .field__item.col-auto {
    -webkit-transform: none;
         -o-transform: none;
            transform: none;
  }
}
.field--name-field-loga-firm img {
  opacity: 0.5;
  height: 40px;
  width: auto;
  margin: 0.1875rem auto;
}

.field--name-field-logo-1-ref {
  padding-bottom: 0.25rem;
}

.field--name-field-logo-2-ref, .field--name-field-logo-1-ref {
  min-height: 60px;
}

@media (min-width: 75rem) {
  .methodology-items {
    margin-top: 1.5rem;
    margin-left: 3rem;
  }
}
.methodology-item:before {
  display: block;
  content: "";
  width: 50%;
  height: 1px;
  background: #d1d1d1;
  position: absolute;
  left: -2rem;
  z-index: -1;
  top: 50px;
}
@media (min-width: 75rem) {
  .methodology-item:before {
    left: -7rem;
    top: 40%;
    width: 9rem;
  }
}
@media (max-width: 74.99875rem) {
  .methodology-item-title {
    display: block;
    width: 100%;
  }
}
@media (min-width: 75rem) {
  .methodology-item-title {
    border-right: #d1d1d1 solid 1px;
    margin-right: 0.9375rem;
    min-width: 110px;
    max-width: 110px;
    margin-bottom: 0;
  }
}
@media (max-width: 74.99875rem) {
  .methodology-image {
    max-width: 420px;
  }
  .methodology-image:before {
    position: absolute;
    content: "";
    width: 110%;
    padding-top: 100%;
    max-width: 462px;
    border-radius: 50%;
    border-bottom: #d1d1d1 solid 2px;
    top: 28px;
    left: -5%;
  }
  .methodology-image img {
    border-radius: 50%;
    -webkit-box-shadow: 0px 9px 34px 1px rgba(66, 68, 90, 0.66);
    box-shadow: 0px 9px 34px 1px rgba(66, 68, 90, 0.66);
    width: 100%;
    height: auto;
  }
}
@media (min-width: 75rem) {
  .methodology-image {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
            flex: 1 0 25%;
  }
}
@media (max-width: 74.99875rem) {
  .methodology-icon {
    width: 100px;
    margin: 0 auto;
  }
}
@media (min-width: 75rem) {
  .methodology-icon {
    max-width: 70px;
    max-height: 70px;
    min-width: 70px;
    min-height: 70px;
    margin-right: 1rem;
  }
}
.methodology-icon img {
  border-radius: 50%;
  -webkit-box-shadow: 8px 8px 24px -7px rgba(66, 68, 90, 0.66);
  box-shadow: 8px 8px 24px -7px rgba(66, 68, 90, 0.66);
}

.field-oni-juz-byli_item_even {
  margin-top: 1.25rem;
  margin-left: auto;
  margin-right: 0;
}

.field--name-field-oni-juz-byli_item {
  max-width: 660px;
  clear: both;
}
.field--name-field-oni-juz-byli_item .group-middle {
  width: 100%;
}
@media (min-width: 36rem) {
  .field--name-field-oni-juz-byli_item .group-middle {
    width: calc(100% - 110px);
  }
}

.opinia .group-footer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  background-image: url("../images/opinia.png");
  background-position: right top;
  background-repeat: no-repeat;
  padding-top: 1.875rem;
}
.opinia .group-footer img {
  border-radius: 50%;
  -webkit-box-shadow: 1px 1px 6px 0 #a1a1a1;
          box-shadow: 1px 1px 6px 0 #a1a1a1;
}
.opinia .field--name-field-linkedin-link {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-end;
      -ms-flex-item-align: end;
          align-self: flex-end;
}
.opinia .field--name-field-linkedin-link a {
  position: relative;
  text-indent: -99999px;
  text-align: left;
  display: block;
  width: 46px;
  height: 11px;
  margin-top: 0.3125rem;
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
  -webkit-align-self: end;
      -ms-flex-item-align: end;
          align-self: end;
}
.opinia .field--name-field-linkedin-link a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/linkedin-logo.png");
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.opinia .n-signature-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-align-content: flex-end;
      -ms-flex-line-pack: end;
          align-content: flex-end;
  margin-right: 1.25rem;
}
.opinia .n-signature-wrapper p {
  margin: 0;
  text-align: right;
}
.opinia .field--name-field-image {
  margin-left: 0;
  padding-right: 0.375rem;
}
.opinia .field--name-field-zajawka p {
  font-size: 0.75rem;
  font-weight: bold;
  line-height: 1.1875rem;
}

.field--name-field-video-embed .video-embed-field-lazy-play {
  border: none;
}

.field--name-field-opinie-wideo-ref-field__item {
  max-width: 100%;
}
@media (min-width: 36rem) {
  .field--name-field-opinie-wideo-ref-field__item {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 50%;
        -ms-flex: 1 0 50%;
            flex: 1 0 50%;
    max-width: 50%;
  }
}
@media (min-width: 48rem) {
  .field--name-field-opinie-wideo-ref-field__item {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 31%;
        -ms-flex: 1 0 31%;
            flex: 1 0 31%;
  }
}
.field--name-field-opinie-wideo-ref-field__item .video-embed-field-launch-modal img {
  max-width: 100%;
  cursor: pointer;
}

.node--view-mode-teaser.node--type-wideo {
  border: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.sekcja.wyznaczamy-standardy .npx-form-button-wrapper, body.node--type-page .npx-form-button-wrapper {
  text-align: center;
}
.sekcja.wyznaczamy-standardy a.npx-form-button.npx-autolink, .sekcja.wyznaczamy-standardy a.npx-form-button-inline.npx-autolink, body.node--type-page a.npx-form-button.npx-autolink, body.node--type-page a.npx-form-button-inline.npx-autolink {
  margin-top: 0;
}
.sekcja.wyznaczamy-standardy .field--name-field-extra-tekst-g3 p.npx-hidden-text, body.node--type-page .field--name-field-extra-tekst-g3 p.npx-hidden-text {
  font-size: 1rem;
  font-weight: 300;
}
.sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster, body.node--type-page .field--name-field-wideo-trenera-poster {
  width: 100%;
  height: 100px;
}
@media (min-width: 36rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster, body.node--type-page .field--name-field-wideo-trenera-poster {
    width: 128px;
    height: 128px;
  }
}
@media (min-width: 62rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster, body.node--type-page .field--name-field-wideo-trenera-poster {
    width: 182px;
    height: 182px;
  }
}
.sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster img, body.node--type-page .field--name-field-wideo-trenera-poster img {
  max-width: 100px;
  display: inline-block;
  border-radius: 50%;
  -webkit-box-shadow: 1px 1px 40px #ebedec;
  box-shadow: 1px 1px 40px #ebedec;
}
@media (min-width: 36rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster img, body.node--type-page .field--name-field-wideo-trenera-poster img {
    display: inherit;
    max-width: 128px;
  }
}
@media (min-width: 62rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-wideo-trenera-poster img, body.node--type-page .field--name-field-wideo-trenera-poster img {
    max-width: 100%;
  }
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy::before, .sekcja.wyznaczamy-standardy .field--name-field-cechy::after, body.node--type-page .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::after {
  position: absolute;
  content: " ";
  display: block;
  left: -20px;
  top: 0;
  width: calc(100% + 40px);
  height: 100%;
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::before {
  z-index: 0;
  background: transparent url(../images/wyznaczamy-standardy-1.png) repeat-y 0 0;
  -webkit-background-size: 150% auto;
          background-size: 150% auto;
}
@media (max-width: 74.99875rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::before {
    background-position: -100px 0;
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    position: absolute;
  }
}
@media (min-width: 75rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-cechy::before, body.node--type-page .field--name-field-cechy::before {
    -webkit-background-size: 100% auto;
            background-size: 100% auto;
  }
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy::after, body.node--type-page .field--name-field-cechy::after {
  z-index: 1;
  background: transparent url("../images/wyznaczamy-standardy-2.png") repeat-x left bottom;
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy .field__item, body.node--type-page .field--name-field-cechy .field__item {
  z-index: 10;
  clear: both;
  margin-bottom: 1.5625rem;
}
.sekcja.wyznaczamy-standardy .field--name-field-cechy .field__item.field--name-field-opis-trenera, body.node--type-page .field--name-field-cechy .field__item.field--name-field-opis-trenera {
  margin-bottom: 3.125rem;
}
@media (min-width: 36rem) {
  .sekcja.wyznaczamy-standardy .field--name-field-cechy .field__item.field--name-field-opis-trenera, body.node--type-page .field--name-field-cechy .field__item.field--name-field-opis-trenera {
    margin-bottom: 1.5625rem;
  }
}
.sekcja.wyznaczamy-standardy .paragraph--type-cecha-par, body.node--type-page .paragraph--type-cecha-par {
  max-width: 900px;
}

.field_cechy_field_item_even .paragraph--type-cecha-par {
  margin-left: auto;
}

.paragraph--type-cecha-par .field--name-field-opis-trenera {
  width: 100%;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin-bottom: 3.125rem;
}
@media (min-width: 36rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera {
    width: calc(100% - 128px);
    padding: 0 0 0 3.125rem;
  }
}
@media (min-width: 62rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera {
    width: calc(100% - 182px);
    padding: 0 0 0 6.25rem;
  }
}
.paragraph--type-cecha-par .field--name-field-opis-trenera p {
  display: inline-block;
}
.paragraph--type-cecha-par .field--name-field-opis-trenera h3, .paragraph--type-cecha-par .field--name-field-opis-trenera summary {
  position: relative;
  font-size: 1.4rem;
  font-weight: 600;
  line-height: 1.625rem;
  text-align: center;
}
@media (min-width: 36rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera h3, .paragraph--type-cecha-par .field--name-field-opis-trenera summary {
    margin-left: -2.8125rem;
    text-align: left;
    margin-bottom: 0;
  }
}
@media (min-width: 62rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera h3, .paragraph--type-cecha-par .field--name-field-opis-trenera summary {
    margin-left: -5rem;
  }
}
.paragraph--type-cecha-par .field--name-field-opis-trenera h3 sup, .paragraph--type-cecha-par .field--name-field-opis-trenera summary sup {
  display: block;
  max-width: 100px;
  margin: 1.875rem auto;
  padding: 0 0.625rem;
  vertical-align: middle;
  line-height: 1.25rem;
  font-size: 0.625rem;
  color: #fff;
  background-color: #f73965;
  position: relative;
  height: 20px;
}
@media (min-width: 36rem) {
  .paragraph--type-cecha-par .field--name-field-opis-trenera h3 sup, .paragraph--type-cecha-par .field--name-field-opis-trenera summary sup {
    display: inline-block;
    margin-top: 0;
    margin-left: 0.3125rem;
    vertical-align: sub;
  }
}

a.benefits-linker {
  font-weight: bold;
  line-height: 1.9375rem;
  color: #000;
}
a.benefits-linker:hover, a.benefits-linker:active {
  color: #000;
}
@media (min-width: 36rem) {
  a.benefits-linker {
    margin-left: -2.8125rem;
  }
}
@media (min-width: 62rem) {
  a.benefits-linker {
    margin-left: -5rem;
  }
}

#szkolenie-grupa-13.g13.voucher .narrow h2 {
  margin-left: 0;
  margin-right: 0;
}

.voucher-col-image {
  max-width: 100%;
  margin: 1rem auto;
  text-align: center;
}
@media (min-width: 62rem) {
  .voucher-col-image {
    margin: inherit;
  }
}
.voucher-col-image a {
  margin: 0 auto;
  display: inline-block;
}
.voucher-col-image img {
  max-width: 100%;
}

.voucher-bottom {
  margin-top: 0;
}
@media (min-width: 62rem) {
  .voucher-bottom {
    margin-left: 1rem;
  }
}

.voucher-col-text {
  text-align: center;
  margin-top: 2rem;
}
@media (min-width: 62rem) {
  .voucher-col-text {
    text-align: left;
    margin-top: inherit;
  }
}
.voucher-col-text ul {
  text-align: left;
  list-style-image: url("../images/voucher-check-mark.svg");
}

.g17.sekcja.szybki-kontakt {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  max-width: 100vw;
  background: #f3f3f5;
}

#szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row .no-margin .js-form-type-checkbox {
  margin-left: 0.75rem;
}
@media (max-width: 35.99875rem) {
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row > div.js-form-item {
    text-align: center;
  }
}
@media (min-width: 48rem) {
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row .button.form-submit {
    margin-top: 0.3125rem;
  }
}
@media (min-width: 68.8125rem) {
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row > div.js-form-item, #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row > input {
    width: 33%;
  }
  #szkolenie-grupa-17 #npx-contact-form-wrapper #edit-right-col-row .button.form-submit {
    margin-left: 1.5rem;
    margin-top: 2rem;
  }
}

#npx-contact-form-wrapper {
  max-width: 1100px;
}
#npx-contact-form-wrapper .form-type-textfield label {
  width: 100%;
}
#npx-contact-form-wrapper #edit-right-col-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 48rem) {
  #npx-contact-form-wrapper #edit-right-col-row {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
#npx-contact-form-wrapper #edit-right-col-row fieldset {
  margin-left: auto;
  margin-right: auto;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper #edit-right-col-row fieldset {
    margin-left: 0;
    margin-right: 0;
  }
}
@media (min-width: 48rem) {
  #npx-contact-form-wrapper #edit-right-col-row fieldset {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 33%;
        -ms-flex: 1 0 33%;
            flex: 1 0 33%;
  }
}
@media (min-width: 68.8125rem) {
  #npx-contact-form-wrapper #edit-right-col-row fieldset.js-form-item-name {
    padding-left: 0;
  }
}
#npx-contact-form-wrapper .no-margin {
  margin: 0;
  padding: 0;
}
#npx-contact-form-wrapper fieldset {
  max-width: 250px;
}
#npx-contact-form-wrapper .error + div .field-suffix {
  display: block;
}
#npx-contact-form-wrapper .error {
  border-color: red;
}
#npx-contact-form-wrapper .form-checkbox.error + label {
  color: red;
}
#npx-contact-form-wrapper .field-suffix {
  display: none;
  font-size: 0.8125rem;
}
#npx-contact-form-wrapper .npx-contact-thx img {
  height: 60px;
  margin-right: 1.25rem;
}
#npx-contact-form-wrapper .npx-contact-thx .n-big {
  font-size: 1.5rem;
}
#npx-contact-form-wrapper .npx-contact-txt-info {
  margin-bottom: 1rem;
  margin-top: 2.2rem;
  font-size: 1.375rem;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper .npx-contact-txt-info {
    margin-bottom: inherit;
  }
}
#npx-contact-form-wrapper .npx-contact-txt-info .n-sm {
  font-size: 1.125rem;
}
@media (min-width: 81.25rem) {
  #npx-contact-form-wrapper .left-col {
    position: relative;
  }
}
@media (min-width: 81.25rem) {
  #npx-contact-form-wrapper .left-col::before {
    content: "";
    position: absolute;
    top: 45px;
    left: -75px;
    width: 73px;
    height: 78px;
    background: url("../images/call_question_icon.svg") left center no-repeat;
  }
}
#npx-contact-form-wrapper .js-form-type-checkbox input:checked + label {
  background-image: url("../images/checkbox-on.png");
}
#npx-contact-form-wrapper .right-col .row {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper .right-col .row {
    -webkit-box-pack: inherit;
    -webkit-justify-content: inherit;
        -ms-flex-pack: inherit;
            justify-content: inherit;
  }
}
#npx-contact-form-wrapper .button.form-submit {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 3rem auto 1rem;
  margin-top: 3rem;
  font-weight: bold;
  font-size: 0.875rem;
  height: 50px;
  line-height: 1.5rem;
  color: #fff;
  background: var(--secondary);
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  -o-transition: all 500ms;
  -webkit-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  white-space: normal;
  max-width: 210px;
}
#npx-contact-form-wrapper .button.form-submit:hover {
  background-color: #034b7d;
  text-decoration: none;
  color: #fff;
}
@media (min-width: 36rem) {
  #npx-contact-form-wrapper .button.form-submit {
    margin-left: 0;
  }
}
@media (min-width: 48rem) {
  #npx-contact-form-wrapper .button.form-submit {
    margin-left: 1rem;
  }
}
@media (min-width: 68.8125rem) {
  #npx-contact-form-wrapper .button.form-submit {
    margin-right: 0;
    margin-left: calc(var(--bs-gutter-x) * 0.5);
  }
}
#npx-contact-form-wrapper .button.form-submit::hover {
  background-color: #034b7d;
  text-decoration: none;
}
#npx-contact-form-wrapper .messages {
  display: none !important;
}
#npx-contact-form-wrapper .ajax-progress-throbber {
  background: transparent;
}
#npx-contact-form-wrapper .form-item-phone input {
  background: #fff url("../images/phone_icon.svg") left center no-repeat;
  background-position-x: 8px;
  padding-left: 1.875rem;
}
#npx-contact-form-wrapper .form-item-name input {
  background: #fff url("../images/user_icon.svg") left center no-repeat;
  background-position-x: 8px;
}
#npx-contact-form-wrapper .required-info {
  font-size: 0.75rem;
  text-align: right;
  max-width: 135px;
  padding-right: 0;
}
#npx-contact-form-wrapper .js-form-type-checkbox input {
  display: none;
}
#npx-contact-form-wrapper .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url("../images/checkbox-off.png");
  background-repeat: no-repeat;
  background-position: 0px center;
  display: block;
  margin: 0;
  line-height: 1.25rem;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.simple-popup-blocks-global .spb_center {
  margin-left: -25rem;
  margin-top: -12.5rem;
}

#block-exitpopupblock {
  display: none;
}
#block-exitpopupblock * {
  content-visibility: auto;
}
#block-exitpopupblock > h2 {
  display: none;
}

.popover {
  z-index: 999999 !important;
}
.popover .popover-header {
  margin-top: 0;
  padding-top: 0;
}
.popover a {
  text-decoration: underline;
}

#spb-block-exitpopupblock .js-form-item {
  padding-left: 0;
}
@media (max-width: 35.99875rem) {
  #spb-block-exitpopupblock .left-col, #spb-block-exitpopupblock .right-col {
    margin-bottom: 0 !important;
  }
}
#spb-block-exitpopupblock .form-item-name {
  margin-right: 0.125rem;
}
@media (min-width: 36rem) {
  #spb-block-exitpopupblock .form-item-name input {
    margin-right: -1.25rem;
  }
}
@media (max-width: 35.99875rem) {
  #spb-block-exitpopupblock .form-item-name {
    margin-left: 1.5rem;
  }
}
@media (max-width: 35.99875rem) {
  #spb-block-exitpopupblock .form-type-textfield {
    margin-top: 0;
    margin-bottom: 0 !important;
  }
  #spb-block-exitpopupblock .form-type-textfield input {
    width: 140px;
  }
}
@media (max-width: 47.99875rem) {
  #spb-block-exitpopupblock .wrapper-checkbox-npx {
    margin: 0 auto;
  }
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .left-col::before {
  width: 0;
  height: 0;
  display: none;
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .npx-contact-txt-info {
  margin-top: 1rem;
}
#spb-block-exitpopupblock .col-sm-4.left-col {
  width: 100%;
  -webkit-box-flex: 100%;
  -webkit-flex: 100%;
      -ms-flex: 100%;
          flex: 100%;
  max-width: 100%;
  padding-left: 0;
}
#spb-block-exitpopupblock .col-sm-8.right-col {
  -webkit-box-flex: 100%;
  -webkit-flex: 100%;
      -ms-flex: 100%;
          flex: 100%;
  width: 100%;
  max-width: 100%;
}
#spb-block-exitpopupblock .col-sm-8.right-col .row:last-of-type {
  -webkit-box-ordinal-group: 4;
  -webkit-order: 3;
      -ms-flex-order: 3;
          order: 3;
}
@media (max-width: 47.99875rem) {
  #spb-block-exitpopupblock .col-sm-8.right-col .row:last-of-type {
    max-width: 98%;
  }
}
#spb-block-exitpopupblock .col-sm-8.right-col > .row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
#spb-block-exitpopupblock .spb_close {
  border: 0;
  background: transparent;
  font-size: 1.625rem;
}
@media (max-width: 47.99875rem) {
  #spb-block-exitpopupblock .right-col-npx {
    -webkit-box-pack: center;
    -webkit-justify-content: center;
        -ms-flex-pack: center;
            justify-content: center;
    max-width: 100%;
  }
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .button.form-submit {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 1rem auto 0 0.3125rem;
  width: 100%;
  font-weight: 700;
  font-size: 0.875rem;
  line-height: 1.5rem;
  color: #fff;
  background: #e454ff;
  border: 0;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0;
          box-shadow: 0;
  -webkit-transition: all 500ms;
  -o-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  display: block ruby;
  -webkit-box-ordinal-group: 5;
  -webkit-order: 4;
      -ms-flex-order: 4;
          order: 4;
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .button.form-submit:hover {
  background-color: #c434df;
  text-decoration: none;
}
@media (max-width: 47.99875rem) {
  #spb-block-exitpopupblock #npx-contact-form-wrapper .button.form-submit {
    margin: 0 auto;
  }
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .required-info {
  display: none;
}
#spb-block-exitpopupblock #npx-contact-form-wrapper .form-item {
  margin-top: 0.5rem;
}
#spb-block-exitpopupblock #npx-contact-form-wrapper h5, #spb-block-exitpopupblock #npx-contact-form-wrapper h6 {
  font-weight: 600;
  margin-bottom: 0;
}

.npx-contact-exit-popup-block-wrapper {
  max-width: 1200px;
  color: #000;
}
@media (max-width: 35.99875rem) {
  .npx-contact-exit-popup-block-wrapper .spb-controls {
    right: -2px;
    top: -3px;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 1.25rem 2rem 0;
}
@media (max-width: 35.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container {
    padding: 0.25rem 0.25rem 0;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container h2 {
  margin: 0;
  color: var(--secondary);
  font-size: 3rem;
  font-weight: normal;
  line-height: 3rem;
}
@media (max-width: 47.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container h2 {
    font-size: 1.5rem;
    line-height: 1.75rem;
    padding: 0.5rem 0.25rem 0;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container h3 {
  margin: 1rem 0 !important;
  color: #000 !important;
  font-size: 1.3125rem !important;
}
@media (max-width: 47.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container h3 {
    font-size: 1.25rem !important;
    line-height: 1.5rem;
  }
}
@media (max-width: 35.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container h3 {
    margin: 0.5rem 0 !important;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container p {
  color: #000;
}
@media (max-width: 35.99875rem) {
  .npx-contact-exit-popup-block-wrapper .nxp-top-container p {
    margin: 0.5rem 0;
  }
}
.npx-contact-exit-popup-block-wrapper .nxp-top-container hr {
  width: 60%;
  margin-bottom: 0;
  margin-right: auto;
  margin-left: auto;
}
.npx-contact-exit-popup-block-wrapper .nxp-columns-container {
  padding-left: 2rem;
}
.npx-contact-exit-popup-block-wrapper .npx-left-column {
  padding-right: 2.5rem;
}
.npx-contact-exit-popup-block-wrapper .npx-left-column img {
  max-width: 200px;
  margin-right: 2.5rem;
  margin-left: 0.625rem;
}
.npx-contact-exit-popup-block-wrapper .toast-wrapper {
  display: none !important;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .form-submit,
.npx-contact-exit-popup-block-wrapper .npx-right-column form #edit-submit,
.npx-contact-exit-popup-block-wrapper .npx-right-column form #edit-ajax-submit {
  display: inline-block;
  padding: 0.75rem 1.875rem;
  margin: 0.625rem auto 0;
  font-weight: bold;
  font-size: 0.875rem;
  line-height: 1.5rem;
  color: #fff;
  background: #e454ff;
  border: none;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-box-shadow: 0;
          box-shadow: 0;
  -webkit-transition: all 500ms;
  -o-transition: all 500ms;
  transition: all 500ms;
  border-radius: 4px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .form-submit:hover,
.npx-contact-exit-popup-block-wrapper .npx-right-column form #edit-submit:hover,
.npx-contact-exit-popup-block-wrapper .npx-right-column form #edit-ajax-submit:hover {
  background-color: #c434df;
  text-decoration: none;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper .form-item {
  max-width: 200px;
  margin: 0.625rem 1.25rem 0.625rem 0;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper .form-text {
  color: #808080;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper .form-email {
  color: #808080;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper input {
  max-width: 200px;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-input-fields-wrapper input.error {
  border: 2px solid red !important;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .js-form-type-checkbox input {
  display: none;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .js-form-type-checkbox {
  margin: 0;
  -webkit-box-ordinal-group: 4;
  -webkit-order: 3;
      -ms-flex-order: 3;
          order: 3;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url("../img/checkbox-off.png");
  background-repeat: no-repeat;
  background-position: 0px 2px;
  display: block;
  margin: 0;
  line-height: 1.5rem;
  font-size: 0.875rem;
  font-weight: normal;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .js-form-type-checkbox input:checked + label {
  background-image: url("../img/checkbox-on-blue.png");
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-freshmail-accept + label a {
  color: #000;
  text-decoration: underline;
  cursor: pointer;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .npx-freshmail-accept.form-checkbox.error + label a {
  color: red;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .n-wrapped-btn {
  position: relative;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .n-wrapped-btn span {
  position: absolute;
  top: calc(50% - 7px);
  left: 10px;
  color: #fff;
  font-size: 1.75rem;
  font-weight: bold;
  pointer-events: none;
}
.npx-contact-exit-popup-block-wrapper .npx-right-column form .n-wrapped-btn input {
  padding-left: 5rem;
  max-width: 320px;
  height: auto;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-align: left;
  font-weight: normal;
  line-height: 0.875rem;
  font-size: 0.75rem;
}
.npx-contact-exit-popup-block-wrapper .npx-msg {
  display: none;
}
.npx-contact-exit-popup-block-wrapper .ajax-progress.ajax-progress-throbber {
  background: transparent;
  margin-top: 5rem;
}

.spb-popup-main-wrapper.spb_center {
  width: 800px !important;
  margin: 0 auto !important;
  -webkit-transform: translate(-50%, -45%);
       -o-transform: translate(-50%, -45%);
          transform: translate(-50%, -45%);
  background-color: #eceeef;
  border: #666 1px solid;
  border-radius: 8px;
  max-height: 100vh;
  overflow-y: auto;
}
@media (max-width: 47.99875rem) {
  .spb-popup-main-wrapper.spb_center {
    max-width: 98%;
  }
}

.spb_close {
  border: none;
  background: transparent;
  font-size: 1.875rem;
}

@media (max-width: 61.99875rem) {
  .block-bootstrap4grow-freshmailpopupexitstronazcytatami-modal.spb_overlay {
    display: none;
  }
}

.npx-dates-table-wrapper {
  margin: 0 0 2.5rem;
}

.npx-date-table-elem {
  padding: 0.9375rem 2.5rem 0.9375rem 4.6875rem;
  border: 1px solid #d0d8db;
  width: 100%;
}
@media (min-width: 36rem) {
  .npx-date-table-elem {
    padding: 0.9375rem 2.5rem 0.9375rem 2.5rem;
    width: auto;
  }
}
.npx-date-table-elem:nth-child(1) {
  background-image: url("../images/radio-on.png");
  background-repeat: no-repeat;
  background-position: 30px 20px;
  padding: 0.9375rem 2.5rem 0.9375rem 4.6875rem;
}
@media (max-width: 35.99875rem) {
  .npx-date-table-elem:nth-child(1) {
    border-bottom: 0;
  }
}
@media (min-width: 36rem) {
  .npx-date-table-elem:nth-child(1) {
    border-right: 0;
  }
}
.npx-date-table-elem::after {
  display: block;
  position: absolute;
  left: 50%;
  top: 0;
  margin-left: -1rem;
  margin-top: -1rem;
  background: transparent url("../images/termin-plus.png") no-repeat 0 0;
  width: 32px;
  height: 32px;
  content: "";
}
@media (min-width: 36rem) {
  .npx-date-table-elem::after {
    left: 0;
    top: 50%;
  }
}
.npx-date-table-elem:nth-child(1)::after {
  display: none;
}

.npx-date-title {
  height: 30px;
  font-size: 1.25rem;
  line-height: 1.875rem;
}

.npx-date-desc {
  height: 20px;
  font-size: inherit;
  line-height: 1.25rem;
  color: #a2a2a2;
}

#npx-training-form .npx-training-date-dates-header {
  line-height: 1.5;
  margin: 2rem 0 1rem 0;
}

.npx-training-date-not-guaranted {
  background: #cfd8dd;
  left: -1px;
  top: calc(100% + 1px);
  line-height: 1.25rem;
  font-size: 0.625rem;
  height: 20px;
}
.npx-training-date-guaranted {
  left: -1px;
  top: calc(100% + 1px);
  background: #fecc09;
  line-height: 1.25rem;
  font-size: 0.625rem;
  height: 20px;
}

.npx-dates-variant-wrapper .ajax-progress.ajax-progress-throbber {
  top: -20px;
  left: -40px;
}
.npx-dates-variant-wrapper .js-form-type-radio input {
  display: none;
}
.npx-dates-variant-wrapper .js-form-type-radio label {
  line-height: 2rem;
  background-color: #fff;
  background-image: url("../images/radio-off.png");
  background-repeat: no-repeat;
  display: block;
  cursor: pointer;
  -webkit-background-size: 25px 25px;
          background-size: 25px;
}
.npx-dates-variant-wrapper .js-form-type-radio .form-radio[type=radio]:checked + label {
  background-color: #fff;
  background-image: url("../images/radio-on.png");
  padding-left: 2.25rem;
}

.npx-counter-wrapper {
  margin-left: -0.25rem;
}

.npx-tabs {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-align-content: stretch;
      -ms-flex-line-pack: stretch;
          align-content: stretch;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 48rem) {
  .npx-tabs {
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}

@media (min-width: 75rem) {
  .npx-box-left {
    padding-right: 1rem;
  }
}
@media (max-width: 74.99875rem) {
  .npx-box-left {
    -webkit-box-ordinal-group: 3;
    -webkit-order: 2;
        -ms-flex-order: 2;
            order: 2;
  }
}

.npx-counter-info {
  color: red;
}

.npx-counter-icon {
  background: transparent url("../images/budzik.png") no-repeat center center;
  display: inline-block;
  width: 20px;
  height: 20px;
  -webkit-background-size: cover;
          background-size: cover;
  margin-right: 0.375rem;
  margin-left: 0.3125rem;
}

#npx-price-info-wrapper {
  font-size: 0.875rem;
}
#npx-price-info-wrapper .form-item-npx-discount-code {
  position: relative;
  display: inline-block;
}
#npx-price-info-wrapper .form-item-npx-discount-code input[type=text] {
  margin: 0;
  height: 32px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-width: 1px;
  width: 100%;
  max-width: 200px;
}
#npx-price-info-wrapper .form-item-npx-discount-code .field-suffix {
  background-color: #fff;
  border: 0;
  display: inline-block;
}
#npx-price-info-wrapper .form-item-npx-discount-code .field-suffix a {
  display: block;
  position: absolute;
  z-index: 10;
  height: 34px;
  background: transparent url("../images/przelicz.png") no-repeat center center;
  width: 20px;
  text-align: left;
  text-indent: -9990px;
  right: 10px;
  top: 0;
  outline: 0;
  border: 0;
  margin-right: 0.25rem;
}
#npx-price-info-wrapper #npx-expand-bottom-wrapper {
  width: 100%;
}
@media (min-width: 48rem) {
  #npx-price-info-wrapper #npx-expand-bottom-wrapper {
    width: auto;
    margin-right: 1rem;
  }
}
#npx-price-info-wrapper .list-group-item {
  border: none;
  display: list-item;
  margin-left: 1.25rem;
  padding: 0;
}
#npx-price-info-wrapper .item-list {
  padding-top: 3rem;
}
#npx-price-info-wrapper li {
  list-style-image: url("../images/li.png");
}
@media (min-width: 48rem) {
  #npx-price-info-wrapper .npx-social-colorbox-link {
    top: -15px;
  }
}
#npx-price-info-wrapper .npx-social-colorbox-link a::before {
  vertical-align: sub;
  height: 20px;
  width: 20px;
  margin: 0 0.3125rem 0 0;
  background: transparent url("../images/price-tag.png") no-repeat center center;
  -webkit-background-size: auto auto;
          background-size: auto;
  background-size: auto;
  -webkit-background-size: cover;
          background-size: cover;
  display: inline-block;
  content: "";
}

@media (min-width: 48rem) {
  .npx-box-left .npx-price-b {
    background-image: url("../images/dziobek.png");
    background-repeat: no-repeat;
    background-position: 0 0;
    margin-top: -0.9375rem;
  }
  .npx-box-left .npx-price-b-a {
    margin-top: 0.9375rem;
  }
}
@media (min-width: 75rem) {
  .npx-box-left .npx-price-b {
    margin-top: 0;
  }
  .npx-box-left .npx-price-b-a {
    margin-top: 0;
  }
}

@media (max-width: 35.99875rem) {
  .npx-price {
    min-height: 150px;
  }
}
.npx-price-a {
  padding: 1.875rem 2.5rem 0 0;
}
.npx-price-a-a {
  line-height: 1.25rem;
  font-size: 0.875rem;
}
.npx-price-a-b {
  font-size: 1.25rem;
}
.npx-price-b {
  width: 100%;
}
@media (min-width: 48rem) {
  .npx-price-b {
    width: 60%;
    padding: inherit;
    padding-top: 3.125rem;
  }
}
@media (min-width: 62rem) {
  .npx-price-b {
    padding-top: 3.125rem;
    width: auto;
  }
}
.npx-price-b-a {
  font-size: 1.25rem;
}
.npx-price-b-b {
  font-size: 1.125rem;
}
.npx-price-b-c {
  color: #a2a2a2;
}
@media (max-width: 47.99875rem) {
  .npx-price-b-c {
    font-size: 0.8125rem;
  }
}
@media (min-width: 75rem) {
  .npx-price-b-c {
    top: 5px;
  }
}

.npx-counter-wrapper {
  top: 7px;
  position: relative;
}
@media (min-width: 48rem) {
  .npx-counter-wrapper {
    top: -10px;
  }
}

.npx-calculation-box {
  padding: 1.875rem 1.875rem 0;
  margin: 0 -1.875rem;
  width: calc(100% + 60px);
  background-color: #f8faf9;
  background-image: url("../images/dziobek2.png");
  background-repeat: no-repeat;
  background-position: 0 0;
}
@media (min-width: 36rem) {
  .npx-calculation-box {
    background-image: url("../images/kreska.png");
    background-repeat: repeat-x;
    background-position: 0 0;
    background-color: transparent;
    padding-top: 0;
  }
}
.npx-calculation-box .list-group-item {
  background: transparent;
  padding-left: 0;
}
.npx-calculation-box input {
  max-width: 200px;
}

#npx-participants-amount-wrapper .description {
  font-size: 1em;
}
#npx-participants-amount-wrapper small.text-muted {
  max-width: calc(100% - 149px);
  float: right;
  color: #000 !important;
  font-size: 1rem;
}
@media (min-width: 36rem) {
  #npx-participants-amount-wrapper small.text-muted {
    float: none;
    max-width: 100%;
  }
}
#npx-participants-amount-wrapper span.ui-spinner {
  display: inline-block;
  position: relative;
  border: 1px solid #d0d8db;
  padding: 0 2.8125rem;
  border-radius: 0;
  margin: 0 0.625rem 0 0;
}
#npx-participants-amount-wrapper span.ui-spinner .form-control:focus {
  background-color: #fff;
  border-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#npx-participants-amount-wrapper span.ui-spinner input {
  border: 0;
  height: 44px;
  line-height: 2.8125rem;
  padding: 0 0.625rem;
  margin: 0;
  border-radius: 0;
  width: 45px;
  text-align: center;
}
@media (min-width: 36rem) {
  #npx-participants-amount-wrapper span.ui-spinner input {
    height: 44px;
  }
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button {
  border: 0;
  outline: 0;
  position: absolute;
  left: 0;
  top: 0;
  height: 45px;
  width: 45px;
  background-color: transparent;
  opacity: 0.85;
  padding: 0;
  margin: 0;
  right: auto;
  background-image: url("../images/spinner-min.png");
  background-position: center center;
  background-repeat: no-repeat;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button:hover {
  background-color: #ebeff2;
  cursor: pointer;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button.ui-corner-tr {
  background-image: url("../images/spinner-plus.png");
  left: auto;
  right: 0;
  border-left: 1px solid #ddd;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button.ui-corner-br {
  border-right: 1px solid #ddd;
}

#szkolenie-grupa-8 a.npx-form-tab {
  margin: 0.9375rem 0;
}
@media (min-width: 48rem) {
  #szkolenie-grupa-8 a.npx-form-tab {
    padding: 1.0625rem;
    margin: 1.25rem 0.9375rem;
  }
}
#szkolenie-grupa-8 .form-item-npx-training {
  display: none;
}

a.npx-form-tab {
  max-width: 340px;
  padding: 0;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
@media (min-width: 48rem) {
  a.npx-form-tab {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 45%;
        -ms-flex: 1 0 45%;
            flex: 1 0 45%;
    max-width: 45%;
  }
}
@media (min-width: 75rem) {
  a.npx-form-tab {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 29%;
        -ms-flex: 1 0 29%;
            flex: 1 0 29%;
    max-width: 29%;
  }
}
a.npx-form-tab:hover {
  -webkit-transform: scale(1.1);
       -o-transform: scale(1.1);
          transform: scale(1.1);
  z-index: 101;
}
a.npx-form-tab.npx-active-tab {
  -webkit-transform: scale(1.1);
       -o-transform: scale(1.1);
          transform: scale(1.1);
  z-index: 100;
}

.npx-form-outer-wrapper {
  -webkit-box-shadow: 0 0 50px 5px #f1f1f1;
  box-shadow: 0 0 50px 5px #f1f1f1;
  line-height: 1.5;
}

#npx-top-wrapper > div:not(#npx-tabs) {
  padding: 0 0.3rem;
}
@media (min-width: 36rem) {
  #npx-top-wrapper > div:not(#npx-tabs) {
    padding: 0 2.5rem;
  }
}

.npx-blocks-program-tab-wrapper {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  border: solid rgba(128, 128, 128, 0.74) 1px;
  border-radius: 4px;
}
@media (min-width: 48rem) {
  .npx-blocks-program-tab-wrapper {
    height: 100%;
  }
}

li a.active .npx-blocks-program-tab-wrapper, li a:hover .npx-blocks-program-tab-wrapper {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
  -webkit-box-shadow: 0 0 15px 0 #888;
          box-shadow: 0 0 15px 0 #888;
}

.npx-program-tabs-wrapper {
  border-radius: 4px;
}
.npx-program-tabs-wrapper .list-group-item {
  border: 0;
}
.npx-program-tabs-wrapper .nav-tabs {
  list-style: none;
  border-bottom: 0;
}
@media (min-width: 48rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 48%;
        -ms-flex: 1 0 48%;
            flex: 1 0 48%;
    max-width: 48%;
  }
}
@media (min-width: 62rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 31%;
        -ms-flex: 1 0 31%;
            flex: 1 0 31%;
    max-width: 31%;
  }
}
@media (min-width: 75rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
            flex: 1 0 25%;
    max-width: 25%;
  }
}
.npx-program-tabs-wrapper .nav-tabs li a {
  text-align: left;
  background: #f3f3f5;
  margin-bottom: 0.9375rem;
}
.npx-program-tabs-wrapper .nav-tabs a {
  text-decoration: none;
  color: #000;
}
.npx-program-tabs-wrapper .nav-tabs a:hover {
  text-decoration: none;
}
.npx-program-tabs-wrapper .nav-tabs a.active {
  text-transform: none;
}
.npx-program-tabs-wrapper .nav-tabs a.active .npx-blocks-program-tab-wrapper {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
  -webkit-box-shadow: 0 0 15px 0 #888;
          box-shadow: 0 0 15px 0 #888;
}

#npx-regular-box-wrapper {
  opacity: 0.65;
}

.npx-box-right:not(.npx-active-box) .npx-price, .npx-box-left:not(.npx-active-box) .npx-price {
  opacity: 0.65;
}
.npx-box-right:not(.npx-active-box) .npx-price-b-a, .npx-box-left:not(.npx-active-box) .npx-price-b-a {
  text-decoration: line-through;
}
@media (max-width: 74.99875rem) {
  .npx-box-right:not(.npx-active-box) .npx-price, .npx-box-left:not(.npx-active-box) .npx-price {
    display: none !important;
  }
}

#npx-regular-box-wrapper .npx-price-b-c {
  top: 0;
}

.npx-active-tab .npx-training-form-tab-wrapper, .npx-form-tab:hover .npx-training-form-tab-wrapper {
  -webkit-box-shadow: 0 0 15px 0 #54534f;
  box-shadow: 0 0 15px 0 #54534f;
  border-radius: 4px;
}

.npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word {
  width: 100%;
  padding: 0 1.25rem;
}
@media (min-width: 36rem) {
  .npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word {
    width: auto;
  }
}
.npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word-last {
  padding-left: 0.625rem;
}
.npx-training-form-type-info-wrapper .npx-spoiler-content {
  font-size: inherit;
}
.npx-training-form-type-info-wrapper .two-columns-template .col-sm ul li {
  list-style-image: url("../images/online-li-yellow.png");
}
.npx-training-form-type-info-wrapper .two-columns-template .col-sm:nth-child(1) ul li {
  list-style-image: url("../images/online-li-blue.png");
}
.npx-training-form-type-info-wrapper .n-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  border: 1px solid #034b7d;
  background-color: #fff;
  cursor: pointer;
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  border: 1px solid #034b7d;
  cursor: pointer;
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle.show-icon::before {
  content: "ROZWIŃ OPIS";
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle.hide-icon::before {
  content: "ZWIŃ OPIS";
}
.npx-training-form-type-info-wrapper .stationary {
  padding: 0.125rem 1rem;
  background: #ffc60c;
  border-radius: 20px;
  border: #ffc60c 2px solid;
  font-weight: 600;
}
.npx-training-form-type-info-wrapper .live-online {
  padding: 0.125rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  background: var(--secondary);
}

.tr-form-stationary {
  padding: 0.1875rem 1rem;
  background: #ffc60c;
  border-radius: 20px;
  font-weight: 800;
  color: #000;
  margin-left: 0.625rem;
  text-transform: uppercase;
  font-size: 0.625rem;
}
.tr-form-online {
  padding: 0.1875rem 1rem;
  border-radius: 20px;
  font-weight: 800;
  background: var(--secondary);
  color: #fff;
  margin-left: 0.625rem;
  text-transform: uppercase;
  font-size: 0.625rem;
}

.npx-variant .fieldset-legend {
  color: #000;
  margin: 2rem 0;
  display: block;
}
.npx-variant h4 {
  line-height: 1.5rem;
}

.npx-training-form-tab-wrapper {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  border: solid rgba(128, 128, 128, 0.44) 1px;
  border-radius: 4px;
  height: 100%;
}
.npx-training-form-tab-wrapper .n-tab-header-inner {
  background: rgba(0, 0, 0, 0.55);
}
.npx-training-form-tab-header-inner {
  border-radius: 4px 4px 0 0;
  min-height: 183px;
  background: rgba(0, 0, 0, 0.55);
}
.npx-training-form-tab-header {
  background-repeat: no-repeat;
  -webkit-background-size: cover;
          background-size: cover;
  border-radius: 4px 4px 0 0;
}
.npx-training-form-tab-header-hours {
  text-shadow: none;
  font-size: 0.75rem;
}
.npx-training-form-tab-header-type {
  font-size: 0.625rem;
}
.npx-training-form-tab-header-type .stationary {
  padding: 0.125rem 1rem;
  background: #ffc60c;
  border-radius: 20px;
  border: #ffc60c 2px solid;
}
.npx-training-form-tab-header-type .live-online {
  padding: 0.125rem 1rem;
  border-radius: 20px;
}
.npx-training-form-tab-header-type .webinar, .npx-training-form-tab-header-type .online {
  padding: 0.125rem 1rem;
  border-radius: 20px;
}
.npx-training-form-tab-header-title h3 {
  font-size: 1.3rem;
  text-shadow: -1px -1px 14px black, 1px -1px 14px black, -1px 1px 14px black, 1px 1px 14px black;
  text-transform: none;
  color: #fff;
  font-weight: 600;
  margin: 2rem 0 1.2rem 0;
}
.npx-training-form-tab-content {
  text-transform: none;
}
.npx-training-form-tab-content p {
  font-size: 0.9375rem;
}
.npx-training-form-tab-content ul {
  padding-left: 0.9375rem;
}
.npx-training-form-tab-content ul li {
  font-size: 0.9375rem;
  text-transform: none;
  font-weight: normal;
  text-align: left;
  list-style-image: url("../images/li_checkmark.png");
  line-height: 1.3rem;
}
.npx-training-form-tab-more {
  margin: auto 0.9375rem 0.625rem 0;
}

#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .tab-content {
  background: #f1fbfc;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  padding: 1.875rem 0;
}
#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .tab-content .tab-pane {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
}

.node--type-landing-page .npx-training-form-tab-header-title h3 {
  margin-top: 0;
}

.ckeditor-accordion-container > dl dt > a > .ckeditor-accordion-toggle::before, .ckeditor-accordion-container > dl dt > a > .ckeditor-accordion-toggle::after {
  background-color: #000;
}

.pdf-program-link img {
  width: 48px;
  height: auto;
}

.program-accordion ul {
  padding-left: 1.2rem;
}
.program-accordion li {
  margin: 0.5rem 0 0.5rem 1.1rem;
}
.program-accordion .pdf-program {
  z-index: 5;
  position: relative;
}
@media (min-width: 62rem) {
  .program-accordion .pdf-program {
    float: right;
  }
}
.program-accordion .pdf-program-link {
  margin-left: 0.625rem;
}
@media (min-width: 36rem) {
  .program-accordion .pdf-program-link {
    margin-left: 1.25rem;
  }
}
.program-accordion .pdf-program-link img {
  width: 38px;
  display: inline-block;
}
@media (min-width: 36rem) {
  .program-accordion .pdf-program-link img {
    width: 48px;
  }
}
.program-accordion .pdf-program-link a {
  margin: 0.9375rem;
}
@media (min-width: 62rem) {
  .program-accordion .pdf-program span {
    display: block;
    margin-bottom: 0.625rem;
  }
}
@media (min-width: 62rem) {
  .program-accordion h2.field-label-above {
    margin-right: 10.625rem;
  }
}
.program-accordion .ckeditor-accordion-container h4 {
  font-size: 1.5rem;
  font-weight: 700;
}
.program-accordion .ckeditor-accordion-container > dl dt > a, .program-accordion .ckeditor-accordion-container > dl dt > a:not(.button) {
  color: inherit;
  border-bottom: #dcdddf 1px solid;
  text-decoration: none;
}
.program-accordion .ckeditor-accordion-container > dl dt > a:hover, .program-accordion .ckeditor-accordion-container > dl dt > a:not(.button):hover {
  color: inherit;
  text-decoration: underline;
}
.program-accordion .ckeditor-accordion-toggler {
  background-color: transparent !important;
  color: inherit;
  font-size: 1.2rem;
}
.program-accordion dl dt > a {
  background-color: #ecedef;
  color: #000;
  border-bottom: #dcdddf 1px solid;
}
.program-accordion dl {
  border: #dcdddf 1px solid;
}

#szkolenie-grupa-6.sekcja.w100.w100limitContent .tab-content {
  background: #f1fbfc;
  padding: 1.875rem 0;
}

.npx-training-form-tab-header {
  position: relative;
}
.npx-training-form-tab-header-inner {
  z-index: 9;
  position: relative;
}
.npx-training-form-tab-header img {
  position: absolute;
  z-index: 2;
}

.npx-more-program-tabs-wrapper {
  background: #f1fbfc;
}
@media (min-width: 48rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 100%;
        -ms-flex: 1 0 100%;
            flex: 1 0 100%;
  }
}
@media (min-width: 62rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 100%;
        -ms-flex: 1 0 100%;
            flex: 1 0 100%;
  }
}
@media (min-width: 75rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    max-width: 49%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 49%;
        -ms-flex: 1 0 49%;
            flex: 1 0 49%;
  }
}

#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .npx-more-program-tabs-wrapper .item-list {
  margin-top: 0;
}

.npx-program-tabs-wrapper .nav-tabs li .npx-blocks-program-tab-wrapper > a.npx-no-autolink-with-link {
  margin-bottom: 0;
}
.npx-program-tabs-wrapper .nav-tabs li .npx-blocks-program-tab-wrapper > a.npx-no-autolink-with-link .n-tab-header {
  height: 100%;
}

.npx-more-program-tabs-wrapper {
  border-radius: 4px;
}
.npx-more-program-tabs-wrapper .list-group-item {
  border: 0;
}
.npx-more-program-tabs-wrapper .nav-tabs {
  list-style: none;
  border-bottom: 0;
}
@media (min-width: 48rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 48%;
        -ms-flex: 1 0 48%;
            flex: 1 0 48%;
    max-width: 48%;
  }
}
@media (min-width: 62rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 31%;
        -ms-flex: 1 0 31%;
            flex: 1 0 31%;
    max-width: 31%;
  }
}
@media (min-width: 75rem) {
  .npx-more-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
            flex: 1 0 25%;
    max-width: 25%;
  }
}
.npx-more-program-tabs-wrapper .nav-tabs li {
  background: transparent;
}
.npx-more-program-tabs-wrapper .nav-tabs li .npx-blocks-program-tab-wrapper {
  background: #fff;
}
.npx-more-program-tabs-wrapper .nav-tabs li a {
  text-align: left;
  background: #f3f3f5;
  margin-bottom: 0.9375rem;
}
.npx-more-program-tabs-wrapper .nav-tabs a {
  text-decoration: none;
  color: #000;
}
.npx-more-program-tabs-wrapper .nav-tabs a:hover {
  text-decoration: none;
}
.npx-more-program-tabs-wrapper .nav-tabs a.active {
  text-transform: none;
}
.npx-more-program-tabs-wrapper .nav-tabs a.active .npx-blocks-program-tab-wrapper {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
  -webkit-box-shadow: 0 0 15px 0 #888;
          box-shadow: 0 0 15px 0 #888;
}

.npx-more-tabs-txt-wrapper {
  background: #f1fbfc;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  padding: 1.875rem 0;
}
.npx-more-tabs-txt-wrapper .npx-more-tabs-txt {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
}

.npx-more-program-tabs-wrapper .nav-tabs li a.npx-no-autolink,
.npx-more-program-tabs-wrapper .nav-tabs li .npx-training-form-tab-more a {
  background-color: transparent;
}

.npx-more-program-tabs-wrapper .nav-tabs li a:not(.npx-no-autolink) {
  background-color: transparent;
  text-decoration: underline;
}

::-webkit-scrollbar {
  -webkit-appearance: none;
}

::-webkit-scrollbar:vertical {
  width: 12px;
}

::-webkit-scrollbar:horizontal {
  height: 12px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  border: 2px solid #ffffff;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #ffffff;
}

.training-terms-block {
  width: 800px;
}
@media (min-width: 62rem) {
  .training-terms-block {
    width: 100%;
  }
}
.training-terms-block-wrapper {
  overflow-x: scroll;
}
@media (min-width: 62rem) {
  .training-terms-block-wrapper {
    overflow-x: auto;
  }
}
.training-terms-block-td-1 {
  width: 16%;
}
.training-terms-block-td-2 {
  width: 20%;
}
.training-terms-block-td-3 {
  width: 12%;
}
.training-terms-block-td-4 {
  width: 6%;
}
.training-terms-block-td-5 {
  width: 12%;
}
.training-terms-block-h4 {
  font-size: 1rem;
}
.training-terms-block-with-sustable-table {
  margin: 0.3125rem 0 0.625rem 0;
}
.training-terms-block-th {
  padding-bottom: 0.625rem;
}
.training-terms-block-td-clickable::after {
  content: "";
  width: 13px;
  height: 7px;
  display: inline-block;
  background: transparent no-repeat url(../images/menu-arrow.png) 0 0;
  margin-left: 0.625rem;
  -o-transition: -o-transform 300ms ease;
  -webkit-transition: -webkit-transform 300ms ease;
  transition: -webkit-transform 300ms ease;
  transition: transform 300ms ease;
  transition: transform 300ms ease, -webkit-transform 300ms ease, -o-transform 300ms ease;
  -webkit-transform: scaleY(-1);
  -o-transform: scaleY(-1);
     transform: scaleY(-1);
}
.training-terms-block-td-clickable.open::after {
  -webkit-transform: scaleY(1);
  -o-transform: scaleY(1);
     transform: scaleY(1);
}
.training-terms-block-npx-form-button {
  padding: 0.625rem 0.9375rem;
}
.training-terms-block .ask-for-course-closed {
  width: 90%;
  max-width: 400px;
  padding: 0.625rem 0.9375rem;
}

.load-more-terms {
  border: 2px solid #0053B3;
  margin: -1.5rem auto 1.875rem;
  border-radius: 6px;
  width: 185px;
  background-color: #fff;
}
.load-more-terms-bg {
  height: 0;
  background: #d0d8db;
  margin-top: 4.375rem;
}
.load-more-terms-wrapper {
  position: absolute;
  width: calc(100vw - 40px);
}
@media (min-width: 62rem) {
  .load-more-terms-wrapper {
    position: relative;
    width: auto;
  }
}

.paragraph--type-arguments {
  margin-top: 3.75rem;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 36rem) {
  .paragraph--type-arguments {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
.paragraph--type-arguments .group-left {
  width: 100%;
  height: 100px;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .group-left {
    width: 45%;
  }
}
.paragraph--type-arguments .group-right {
  width: 100%;
  height: auto;
  background: transparent;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .group-right {
    width: 55%;
    height: 100px;
    background: #f4f7f5;
    border-radius: 100px 0 0 100px;
  }
}
.paragraph--type-arguments a.n-get-pdf {
  padding-right: 2.5rem;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments a.n-get-pdf {
    padding-right: 0;
  }
}
.paragraph--type-arguments .field--name-field-arguments-right {
  padding-left: 1.25rem;
  max-width: 400px;
  margin-left: auto;
  border-left: #000 1px solid;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .field--name-field-arguments-right {
    padding-left: 2.5rem;
  }
}
.paragraph--type-arguments .field--name-field-arguments-right p {
  font-size: 0.7rem;
  line-height: 1rem;
}
.paragraph--type-arguments .field--name-field-arguments-left {
  max-width: 300px;
  padding-left: 3.125rem;
  font-size: 1.2rem;
}
.paragraph--type-arguments .field--name-field-arguments-left::before {
  content: "";
  position: absolute;
  width: 100px;
  height: 100px;
  top: 0;
  left: 0;
  z-index: -1;
  background: #f4f7f5;
  border-radius: 100px;
}

.inner-menu-sticky #block-npxfloatingbeltblock {
  -webkit-box-shadow: none;
  box-shadow: none;
  border-bottom: 1px solid #ebedec;
}

#block-npxfloatingbeltblock {
  top: 0px;
  display: none;
  z-index: 9999;
  width: 100%;
  left: 0;
  right: 0;
}
@media (min-width: 68.8125rem) {
  #block-npxfloatingbeltblock {
    width: 100vw;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
  }
}
#block-npxfloatingbeltblock * {
  content-visibility: auto;
}

.npx-floating-block-wrapper {
  max-width: 1415px;
}
.npx-floating-block-button-wrapper {
  padding-right: 0.625rem;
}
.npx-floating-block-form-button-wrapper {
  padding-right: 0.625rem;
}
@media (min-width: 48rem) {
  .npx-floating-block-amount-wrapper {
    margin-right: 1rem;
  }
}
@media (min-width: 62rem) {
  .npx-floating-block-amount-wrapper {
    line-height: 4.5rem;
  }
}

#npx-floating-block-wrapper {
  max-width: 1415px;
}
#npx-floating-block-wrapper img {
  max-height: 66px;
}

h2.field-label-above p {
  font-size: inherit;
  font-weight: inherit;
  margin-bottom: inherit;
}

@media (min-width: 62rem) {
  .field--name-field-blog-posts {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
  .field--name-field-blog-posts .node--type-article.node--view-mode-grow3 {
    height: 100%;
    display: block;
    position: relative;
    padding-bottom: 3.5rem;
  }
  .field--name-field-blog-posts .field--name-node-link {
    margin-left: auto;
    margin-right: auto;
  }
  .field--name-field-blog-posts .field__item {
    max-width: 92%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 32%;
        -ms-flex: 1 0 32%;
            flex: 1 0 32%;
  }
}
.field--name-field-blog-posts .field__item {
  text-align: center;
}
.field--name-field-blog-posts h3 {
  margin: 0;
  font-size: 1.25rem;
}
.field--name-field-blog-posts img {
  -webkit-transition: -webkit-transform 0.2s;
  transition: -webkit-transform 0.2s;
  -o-transition: -o-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s, -o-transform 0.2s;
}
.field--name-field-blog-posts img:hover {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
}
.field--name-field-blog-posts .field--name-field-image {
  overflow: hidden;
}
.field--name-field-blog-posts .node--type-article {
  max-width: 450px;
  margin: 0 auto;
}
@media (max-width: 61.99875rem) {
  .field--name-field-blog-posts .node--type-article {
    margin-bottom: 2rem;
  }
}
.field--name-field-blog-posts .node--type-article h3 a {
  color: #000;
}

#block-valentinesexitpopupotherpagesblock, #block-valentinesexitpopuptrainingblock {
    display: none;
}

#spb-block-valentinesexitpopuptrainingblock .spb_top_center.spb-popup-main-wrapper, #spb-block-valentinesexitpopupotherpagesblock .spb_top_center.spb-popup-main-wrapper {
  max-width: 1000px;
  top: 50%;
  -webkit-transform: translateY(-50%);
       -o-transform: translateY(-50%);
          transform: translateY(-50%);
  border: none;
}
@media (max-width: 35.99875rem) {
  #spb-block-valentinesexitpopuptrainingblock .spb_top_center.spb-popup-main-wrapper, #spb-block-valentinesexitpopupotherpagesblock .spb_top_center.spb-popup-main-wrapper {
    max-width: 98%;
  }
}
@media (min-width: 36rem) {
  #spb-block-valentinesexitpopuptrainingblock .spb_top_center.spb-popup-main-wrapper, #spb-block-valentinesexitpopupotherpagesblock .spb_top_center.spb-popup-main-wrapper {
    width: 80% !important;
  }
}

.valentines-fb {
  height: 20%;
  width: 28%;
  right: 7%;
  bottom: 2%;
}

.valentines-link {
  height: 12%;
  width: 22%;
  left: 12%;
  bottom: 4%;
}

.npx-program-tabs-wrapper .nav-tabs li a.npx-no-autolink,
.npx-program-tabs-wrapper .nav-tabs li .npx-training-form-tab-more a {
  background-color: transparent;
}

.npx-program-tabs-wrapper .nav-tabs li a:not(.npx-no-autolink) {
  background-color: transparent;
  text-decoration: underline;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
