/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

.nav {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
  color: #0053B3;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  -o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.nav-link:hover, .nav-link:focus {
  color: #005283;
  text-decoration: none;
}
.nav-link.disabled {
  color: #6c757d;
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  border-bottom: 1px solid #dee2e6;
}
.nav-tabs .nav-link {
  margin-bottom: -0.0625rem;
  background: none;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  border-color: #e9ecef #e9ecef #dee2e6;
  isolation: isolate;
}
.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}
.nav-tabs .dropdown-menu {
  margin-top: -0.0625rem;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: 0.25rem;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #0d6efd;
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  -webkit-box-flex: 1;
  -webkit-flex: 1 1 auto;
      -ms-flex: 1 1 auto;
          flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  -webkit-flex-basis: 0;
      -ms-flex-preferred-size: 0;
          flex-basis: 0;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

#block-bootstrap4grow-grow3menu-2 {
  min-height: auto;
  display: inline-grid;
}

li.n-online-info-wrapper {
  float: none;
  max-width: 1415px;
  margin: 0 auto !important;
  -webkit-box-sizing: initial !important;
          box-sizing: initial !important;
  cursor: auto !important;
}

.n-online-info-wrapper .n-online-info {
  display: block;
  font-weight: bold;
  margin: 0.5rem 0;
  text-transform: uppercase;
  font-size: 0.8125rem;
}

.sf-accordion .n-online-info {
  font-size: 0.8125rem;
}

#block-grow3menu ul#superfish-grow3menu > li > ul > li.sf-depth-2.menuparent {
  -webkit-box-sizing: initial;
          box-sizing: initial;
}

.n-menu-red {
  color: red;
  text-transform: uppercase;
  font-weight: bold;
  padding-bottom: 0.5rem;
  display: block;
}

.block-superfishgrow3menu .sf-depth-1.menuparent {
  cursor: default;
}

ul.sf-menu span.n-menu-red {
  margin-left: -0.9375rem;
  width: calc(100vw - 15px);
  padding-bottom: 0.625rem;
  padding-top: 0.625rem;
}

ul#superfish-grow3menu-accordion {
  border-top: 1px solid #d0d8db;
  background: #fff;
}
ul#superfish-grow3menu-accordion .n-menu-search-wrapper-li {
  display: none;
}
ul#superfish-grow3menu-accordion a.sf-depth-1 {
  font-weight: 800;
  border-bottom: 1px solid #e3e9e9;
  text-transform: uppercase;
  padding: 1.25rem 0;
}
ul#superfish-grow3menu-accordion li.sf-expanded > a {
  border: 0;
}
ul#superfish-grow3menu-accordion li.sf-expanded.sf-depth-1 ul {
  border-top: 1px solid #e3e9e9;
}
ul#superfish-grow3menu-accordion a.sf-depth-2 {
  font-weight: 500;
}
ul#superfish-grow3menu-accordion .sf-clone-parent {
  display: none;
}
ul#superfish-grow3menu-accordion a.menuparent:after {
  content: "";
  width: 13px;
  height: 7px;
  display: inline-block;
  background: transparent no-repeat url("/themes/custom/bootstrap4grow/images/menu-arrow.png") 0 0;
  margin-left: 0.625rem;
  -webkit-transition: -webkit-transform 300ms ease;
  transition: -webkit-transform 300ms ease;
  -o-transition: -o-transform 300ms ease;
  transition: transform 300ms ease;
  transition: transform 300ms ease, -webkit-transform 300ms ease, -o-transform 300ms ease;
  -webkit-transform: scaleY(-1);
       -o-transform: scaleY(-1);
          transform: scaleY(-1);
}
ul#superfish-grow3menu-accordion a {
  padding: 0.625rem;
  line-height: 1.25rem;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
ul#superfish-grow3menu-accordion a.menuparent:hover:after {
  -webkit-transform: scaleY(1);
       -o-transform: scaleY(1);
          transform: scaleY(1);
}
ul#superfish-grow3menu-accordion a:not(.sf-depth-4) {
  color: #000;
}
ul#superfish-grow3menu-accordion.sf-expanded {
  display: block;
  left: 0 !important;
  width: 100%;
  position: absolute;
  top: 66px !important;
}
ul#superfish-grow3menu-accordion.sf-expanded li {
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
  max-width: 100%;
}

ul#superfish-grow3menu a.menuparent:hover::after {
  -webkit-transform: scaleY(1);
  -o-transform: scaleY(1);
     transform: scaleY(1);
}
ul#superfish-grow3menu li.sf-depth-1:not(:first-child) > ul li {
  width: auto;
}
ul#superfish-grow3menu li.sf-depth-1:first-child > ul li ul {
  background: #fff;
}
ul#superfish-grow3menu li.sf-depth-1:not(:first-child) > ul {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  padding-right: 5rem !important;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu li.sf-depth-1:not(:first-child) > ul {
    padding-right: calc(-83.4375rem + 100vw) !important;
  }
}
@media (min-width: 100rem) {
  ul#superfish-grow3menu li.sf-depth-1:not(:first-child) > ul {
    padding-right: calc((-88.4375rem + 100vw) / 1.25) !important;
  }
}
ul#superfish-grow3menu li > ul li.sf-depth-2.sf-no-children {
  margin-left: 2.5rem;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
ul#superfish-grow3menu li.sf-depth-1:first-child > ul li.sf-depth-2.sf-no-children {
  margin-top: 0;
}
ul#superfish-grow3menu li.sf-depth-1:first-child > ul li.sf-depth-2.sf-no-children:last-of-type {
  margin-top: 2.25rem;
}
ul#superfish-grow3menu li.sf-depth-1 > ul::after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  content: " ";
  display: block;
  background: rgba(0, 0, 0, 0.07);
  height: 1px;
}
ul#superfish-grow3menu > li:first-child > ul {
  min-height: 530px;
  overflow: hidden;
}
ul#superfish-grow3menu > li > ul {
  background: #fff;
  top: 66px;
  width: 100% !important;
  z-index: 100;
  border-bottom: 1px solid silver;
  padding-top: 1rem;
  padding-bottom: 2rem;
  -webkit-box-shadow: 0 12px 9px 0 rgba(0, 0, 0, 0.5);
          box-shadow: 0 12px 9px 0 rgba(0, 0, 0, 0.5);
}
ul#superfish-grow3menu li.sf-depth-2 {
  max-width: 400px;
}
ul#superfish-grow3menu li.sf-depth-2.menuparent:first-child::after {
  content: " ";
  position: absolute;
  width: 1px;
  height: 400px;
  top: 16px;
  -webkit-transform: translateX(400px);
       -o-transform: translateX(400px);
          transform: translateX(400px);
  background: rgba(0, 0, 0, 0.07);
  z-index: 9999;
}
@media (min-width: 81.25rem) {
  ul#superfish-grow3menu li.sf-depth-2.menuparent:nth-child(2)::after {
    content: " ";
    position: absolute;
    width: 1px;
    height: 400px;
    top: 16px;
    -webkit-transform: translateX(930px);
         -o-transform: translateX(930px);
            transform: translateX(930px);
    background: rgba(0, 0, 0, 0.07);
    z-index: 9999;
  }
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu li.sf-depth-2.menuparent:nth-child(2)::after {
    -webkit-transform: translateX(960px);
         -o-transform: translateX(960px);
            transform: translateX(960px);
  }
}
ul#superfish-grow3menu a.sf-depth-2.menuparent::after {
  content: "";
  width: 7px;
  height: 13px;
  display: inline-block;
  background: transparent no-repeat url("/themes/custom/bootstrap4grow/images/menu-arrow-right.png") 0 0;
  margin-left: 0.625rem;
  -webkit-transform: scale(0.8);
  -o-transform: scale(0.8);
     transform: scale(0.8);
}
ul#superfish-grow3menu a.sf-depth-2.menuparent {
  color: #000;
  pointer-events: none;
  font-weight: 600;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
ul#superfish-grow3menu > li:first-child > ul li.sf-depth-2.sf-no-children {
  float: none;
  padding-left: 1.875rem;
  margin-left: 0;
  margin-bottom: 0.625rem;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu > li:first-child > ul li.sf-depth-2.sf-no-children {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
@media (min-width: 88.75rem) {
  ul#superfish-grow3menu > li:first-child > ul li.sf-depth-2.sf-no-children {
    padding-left: 0;
    padding-right: 0;
  }
}
ul#superfish-grow3menu > li:first-child > ul li.sf-depth-2.sf-no-children.n-menu-rcol {
  position: absolute;
}
ul#superfish-grow3menu > li > ul > li.sf-depth-2 > ul {
  top: 16px;
  width: 100% !important;
  min-height: 500px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: none;
          box-shadow: none;
}
ul#superfish-grow3menu .n-menu-rcol {
  position: absolute;
  top: 0;
}
ul#superfish-grow3menu .moneybox {
  width: 214px;
  height: 50px;
  margin-top: 0;
  background: transparent url("/themes/custom/bootstrap4grow/images/swinka5final.png") no-repeat 0 center;
  -webkit-background-size: contain;
          background-size: contain;
  text-indent: -9990px;
  pointer-events: all;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu .moneybox {
    width: 312px;
    height: 73px;
    margin-top: 1rem;
  }
}
ul#superfish-grow3menu .menu-link-promocje {
  width: 214px;
  height: 50px;
  margin-top: 0;
  background: transparent url("/themes/custom/bootstrap4grow/images/ikona_promocje.png") no-repeat 0 center;
  -webkit-background-size: contain;
          background-size: contain;
  text-indent: -9990px;
  pointer-events: all;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu .menu-link-promocje {
    width: 312px;
    height: 73px;
    margin-top: 1rem;
  }
}
ul#superfish-grow3menu .kalendarz {
  margin-top: 1.875rem;
  width: 214px;
  height: 50px;
  background: transparent url("/themes/custom/bootstrap4grow/images/kalendarz.png") no-repeat 0 0;
  -webkit-background-size: contain;
          background-size: contain;
  text-indent: -9990px;
  pointer-events: all;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu .kalendarz {
    width: 312px;
    height: 73px;
  }
}
ul#superfish-grow3menu > li > ul a {
  padding: 0;
  line-height: 1.1;
  padding: 0.25rem 0;
  font-size: 0.9375rem;
}
ul#superfish-grow3menu li.sf-depth-3 a {
  max-width: 500px;
  padding: 0.5rem 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-decoration: none;
}
ul#superfish-grow3menu a.sf-depth-3 {
  padding-bottom: 0.5rem;
  padding-top: 0.5rem;
}
@media (min-width: 65.625rem) {
  ul#superfish-grow3menu.sf-menu span.n-menu-red {
    margin-left: 0;
    padding: 0;
  }
}
ul#superfish-grow3menu li.sf-depth-2.menuparent {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
  width: auto !important;
  float: none;
  font-size: 0.9375rem;
  line-height: 1.125rem;
  position: static;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu li.sf-depth-2.menuparent {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
@media (min-width: 88.75rem) {
  ul#superfish-grow3menu li.sf-depth-2.menuparent {
    padding-left: 0;
    padding-right: 0;
  }
}

@media (min-width: 65.625rem) {
  .n-menu-search-wrapper-li input {
    position: relative;
    color: #000;
    padding: 0.625rem;
    border: #ffab1a 1px solid;
    border-radius: 5px;
    margin-left: 1.75rem;
    margin-top: 0.5rem;
  }
}
@media (min-width: 87.5rem) {
  .n-menu-search-wrapper-li input {
    margin-left: 0.5rem;
  }
}
@media (min-width: 88.75rem) {
  .n-menu-search-wrapper-li input {
    margin-left: 0;
  }
}

@media (min-width: 62rem) {
  ul.sf-menu span.n-menu-red {
    margin-left: 0.5rem;
  }
}
.n-menu-search-results-wrapper {
  position: absolute;
  top: 1em;
  z-index: 497;
}

ul.sf-menu ul li.n-menu-search-wrapper-li {
  position: static;
  max-width: 400px;
  padding-top: 0.625rem;
  padding-bottom: 0.9375rem;
  float: none;
}

ul.sf-menu ul.n-menu-search-results {
  position: relative;
  top: 0;
  width: 100% !important;
}

.container.npx-gray-bg {
  background: #f4f4f4;
  padding: 1.5625rem 0.9375rem;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  color: #333;
}

#cboxOverlay {
  background: #006bd9;
  opacity: 0.85 !important;
}

#cboxClose {
  position: absolute;
  bottom: auto;
  top: 5px;
  right: 5px;
  background: url("../images/close.png") no-repeat 0 0;
  width: 30px;
  height: 30px;
  text-indent: -9999px;
}
#cboxClose:hover {
  background-position: 0;
}

#cboxWrapper {
  border-radius: 0;
}

#cboxContent {
  padding: 4.375rem 0.3125rem 0.3125rem;
  border-radius: 0;
}
@media (min-width: 36rem) {
  #cboxContent {
    padding: 4.375rem 1.875rem 1.875rem;
  }
}
@media (min-width: 62rem) {
  #cboxContent {
    padding: 4.375rem 3.75rem 1.875rem;
  }
}

.red-text, .npx-red-text {
  color: #B3002F;
  font-weight: bold;
}

.site-footer {
  background: -webkit-gradient(linear, left top, right top, color-stop(0, #00366c), to(#0060c1));
  background: -webkit-linear-gradient(left, #00366c 0, #0060c1 100%);
  background: -o-linear-gradient(left, #00366c 0, #0060c1 100%);
  background: linear-gradient(to right, #00366c 0, #0060c1 100%);
  padding: 2.8125rem 0;
}

.region-footer-first {
  padding: 2.1875rem 4.375rem;
}

#block-bootstrap4grow-stopkadanekontaktowe {
  max-width: 1068px;
  margin-bottom: 3.125rem;
  margin-top: 3.125rem;
}
#block-bootstrap4grow-stopkadanekontaktowe h3 {
  font-size: 1.25rem;
  margin: 0;
  color: #fff;
}
#block-bootstrap4grow-stopkadanekontaktowe .contextual-links a {
  color: #333;
}
#block-bootstrap4grow-stopkadanekontaktowe a {
  color: #fff;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-facebook, #block-bootstrap4grow-stopkadanekontaktowe a.n-linkedin {
  position: relative;
  padding-left: 1.25rem;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-phone, #block-bootstrap4grow-stopkadanekontaktowe a.n-email {
  position: relative;
  padding-left: 1.25rem;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-phone::before, #block-bootstrap4grow-stopkadanekontaktowe a.n-email::before {
  content: "";
  position: absolute;
  top: 5px;
  width: 12px;
  height: 12px;
  margin-left: -1.25rem;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-phone::before {
  background: url("../images/Icon_footer_phone.png") no-repeat;
  -webkit-background-size: cover;
          background-size: cover;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-email::before {
  background: url("../images/Icon_footer_email.png") no-repeat;
  -webkit-background-size: contain;
          background-size: contain;
  background-position-y: 70%;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-facebook::before {
  content: "";
  position: absolute;
  top: 4px;
  width: 12px;
  height: 12px;
  margin-left: -1.25rem;
  background: url("../images/Icon_footer_facebook.png") no-repeat;
  -webkit-background-size: contain;
          background-size: contain;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-linkedin::before {
  content: "";
  position: absolute;
  top: 4px;
  width: 12px;
  height: 12px;
  margin-left: -1.25rem;
  background: url("../images/Icon_footer_linkedin.png") no-repeat;
  -webkit-background-size: contain;
          background-size: contain;
}
#block-bootstrap4grow-stopkadanekontaktowe .n-col {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
@media (min-width: 62rem) {
  #block-bootstrap4grow-stopkadanekontaktowe .n-col {
    border-right: #6db4fa 2px solid;
  }
}
#block-bootstrap4grow-stopkadanekontaktowe .n-col:last-child {
  border: 0;
}

#block-bootstrap4grow-npxmailcontactblock {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  max-width: 100vw;
  background: #f3f3f5;
  padding: 2.5rem 0;
}
#block-bootstrap4grow-npxmailcontactblock .npx-mailcontact-right-info-n-img img {
  margin: 0 auto;
}

.npx-mailcontact-header {
  max-width: 1415px;
}
.npx-mailcontact-header h2 {
  font-size: 1.5rem;
}
@media (min-width: 62rem) {
  .npx-mailcontact-header h2 {
    font-size: 2rem;
  }
}
.npx-mailcontact-n-col-name {
  padding-right: 0;
  padding-left: 0;
}
@media (min-width: 36rem) {
  .npx-mailcontact-n-col-name {
    padding-right: 0.3125rem;
  }
}
.npx-mailcontact-n-col-name input {
  width: 100%;
  background: #fff url("../images/user_icon.svg") left center no-repeat;
  background-position-x: left;
  background-position-x: 8px;
}
.npx-mailcontact-n-col-email {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 36rem) {
  .npx-mailcontact-n-col-email {
    padding-left: 0.3125rem;
  }
}
.npx-mailcontact-n-col-email input {
  width: 100%;
  background: #fff url("../images/icon_email.png") left center no-repeat;
  background-position-x: left;
  background-position-x: 8px;
  padding-left: 1.875rem;
}
.npx-mailcontact-form-wrapper-over .toast-wrapper, .npx-mailcontact-form-wrapper-over .messages {
  display: none !important;
}
.npx-mailcontact-form-wrapper {
  max-width: 1415px;
}
.npx-mailcontact-form-wrapper .js-form-type-checkbox input {
  display: none;
}
.npx-mailcontact-form-wrapper .form-textarea-wrapper {
  width: 100%;
}
.npx-mailcontact-form-wrapper label {
  display: block;
}
.npx-mailcontact-form-wrapper .field-suffix {
  display: none;
}
.npx-mailcontact-form-wrapper .error + div .field-suffix {
  display: block;
}
.npx-mailcontact-form-wrapper input.form-text.error, .npx-mailcontact-form-wrapper input.form-email.error,
.npx-mailcontact-form-wrapper textarea.error, .npx-mailcontact-form-wrapper input.form-checkbox.error {
  outline: #ff0000 1px solid;
}
.npx-mailcontact-form-wrapper input.form-checkbox.error + label {
  color: #fc5353;
}
.npx-mailcontact-form-wrapper div.form-textarea-wrapper + .field-suffix,
.npx-mailcontact-form-wrapper input.form-text + .field-suffix,
.npx-mailcontact-form-wrapper input.form-email + .field-suffix {
  display: none;
}
.npx-mailcontact-form-wrapper input.form-text.error + .field-suffix,
.npx-mailcontact-form-wrapper input.form-email.error + .field-suffix {
  display: block;
}
.npx-mailcontact-form-wrapper .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url("../images/checkbox-off.png");
  background-repeat: no-repeat;
  background-position: 0px 2px;
  display: block;
  margin: 0;
  line-height: 1.25rem;
  font-size: 0.75rem;
}
.npx-mailcontact-form-wrapper .js-form-type-checkbox input:checked + label {
  background-image: url("../images/checkbox-on.png");
}
.npx-mailcontact-form-wrapper .required-info {
  font-size: 0.75rem;
  padding-left: calc(var(--bs-gutter-x) * 0.5);
}
.npx-mailcontact-form-wrapper .button.form-submit {
  margin: 0.625rem 0;
  padding: 0.75rem 1.875rem;
  width: auto;
}
@media (min-width: 36rem) {
  .npx-mailcontact-form-wrapper .button.form-submit {
    margin: 0.75rem 0 0 0.625rem;
  }
}
.npx-mailcontact-right-info-n-name {
  font-size: 1rem;
}
.npx-mailcontact-right-info-n-position {
  font-size: 0.875rem;
}
.npx-mailcontact-right-info-n-phone a {
  font-size: 1.375rem;
  color: #000;
}
.npx-mailcontact-right-info-n-img {
  margin-right: 0.625rem;
}
@media (min-width: 36rem) {
  .npx-mailcontact-right-info-n-img {
    margin-right: 1.25rem;
  }
}
.npx-mailcontact-right-info-n-img img {
  border: var(--secondary) 1px solid;
  border-radius: 50px;
  max-width: 80px;
  max-height: 80px;
}
.npx-mailcontact-right-info-n-row h3 {
  font-size: 1rem;
}
@media (min-width: 62rem) {
  .npx-mailcontact-right-info-n-row h3, .npx-mailcontact-right-info-n-row > p {
    text-align: left;
  }
}
.npx-mailcontact-right-info-n-row:first-child:after {
  margin-left: auto;
  margin-right: auto;
}
@media (min-width: 62rem) {
  .npx-mailcontact-right-info-n-row:first-child:after {
    margin-left: 6.25rem;
  }
}
.npx-mailcontact-left-info-n-row {
  margin-bottom: 3.5rem;
}
.npx-mailcontact-left-info-n-txt {
  font-size: 0.9375rem;
  line-height: 1.2;
}
.npx-mailcontact-left-info-n-icon {
  width: 85px;
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
}
.npx-mailcontact-middle-col .row {
  padding: 0 1.875rem;
}
.npx-mailcontact-middle-col .npx-mailcontact-middle-col-row-3 {
  padding: 0 calc(var(--bs-gutter-x) * 0.5 + 1.875rem);
}
.npx-mailcontact-middle-col .form-item-accept {
  margin: 0;
  margin-top: 0.625rem;
  padding-left: 0;
}
@media (min-width: 36rem) {
  .npx-mailcontact-middle-col .form-item-accept {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 0;
        -ms-flex: 1 0 0px;
            flex: 1 0 0;
  }
}
.npx-mailcontact-middle-col .form-item-message {
  width: 100%;
  margin-right: 0;
  padding: 0 calc(var(--bs-gutter-x) * 0.5);
}
.npx-mailcontact-middle-col .form-item-message .field-suffix {
  display: none;
}
.npx-mailcontact-middle-col textarea {
  width: 100%;
}
.npx-mailcontact-left-col {
  padding-left: 0;
}
.npx-mailcontact-right-col {
  margin-top: 2.5rem;
  text-align: center;
  padding-left: 1.875rem;
}
@media (min-width: 62rem) {
  .npx-mailcontact-right-col {
    border-left: #d4d8db 1px solid;
    padding-left: 0.9375rem;
  }
}
@media (min-width: 68.8125rem) {
  .npx-mailcontact-right-col {
    padding-left: 1.875rem;
  }
}
.npx-mailcontact-right-info-n-row:first-child:after {
  content: "";
  display: block;
  width: 215px;
  height: 1px;
  background: #ccc;
  margin-bottom: 1.25rem;
  margin-top: 1.25rem;
}
.npx-mailcontact-right-info {
  max-width: 400px;
}
@media (min-width: 62rem) {
  .npx-mailcontact-right-info {
    margin-top: -3.4375rem;
  }
}
.npx-mailcontact-left-info-h3 {
  font-size: 1.125rem;
  margin: 0 0 0.25rem 0;
}
.npx-mailcontact-right-info-h3 {
  font-size: 1.125rem;
  margin: 0 0 0.25rem 0;
}
.npx-mailcontact-thx-header {
  font-size: 1.75rem;
}

.npx-freshmail-block-footer-background {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  background: #ecedef;
}

.npx-freshmail-block-footer-wrapper {
  max-width: 1200px;
  color: #000;
}
.npx-freshmail-block-footer-wrapper .npx-left-column {
  padding-right: 2.5rem;
}
.npx-freshmail-block-footer-wrapper .npx-freshmail-accept + label a {
  color: #000;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 400;
}
.npx-freshmail-block-footer-wrapper input.error {
  border-color: red;
}
.npx-freshmail-block-footer-wrapper input.error + label a, .npx-freshmail-block-footer-wrapper input.error + label {
  color: red;
}
.npx-freshmail-block-footer-wrapper .npx-right-column .js-form-type-checkbox {
  margin-bottom: 0;
  margin-top: 0;
}
.npx-freshmail-block-footer-wrapper .npx-right-column .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url(../images/checkbox-off.png);
  background-repeat: no-repeat;
  background-position: 0 2px;
  display: block;
  margin: 0;
  line-height: 1.5rem;
  font-size: 0.875rem;
  font-weight: 400;
}
.npx-freshmail-block-footer-wrapper .npx-right-column .js-form-type-checkbox input {
  display: none;
}
.npx-freshmail-block-footer-wrapper .npx-right-column .js-form-type-checkbox input:checked + label {
  background-image: url(../images/checkbox-on-blue.png);
}
.npx-freshmail-block-footer-wrapper .npx-right-column form fieldset.npx-freshmail-list-id {
  margin-bottom: 0;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .npx-input-fields-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .npx-input-fields-wrapper label {
  font-size: 0.875rem;
  font-weight: 400;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .npx-input-fields-wrapper input {
  max-width: 200px;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .form-type-email, .npx-freshmail-block-footer-wrapper .npx-right-column form .form-type-textfield {
  max-width: 200px;
  margin: 0.625rem 1.25rem 0.625rem 0;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .form-submit, .npx-freshmail-block-footer-wrapper .npx-right-column form #edit-submit {
  margin: 0.625rem auto 0;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .n-wrapped-btn {
  position: relative;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .n-wrapped-btn input {
  padding-left: 5rem;
  max-width: 300px;
  height: auto;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-align: left;
  font-weight: 400;
  line-height: 0.875rem;
  font-size: 0.75rem;
  padding-right: 1.5rem;
  width: auto;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .n-wrapped-btn span {
  position: absolute;
  top: calc(50% - 18px);
  left: 10px;
  color: #fff;
  font-size: 1.75rem;
  font-weight: 700;
  pointer-events: none;
  z-index: 10;
}
.npx-freshmail-block-footer-wrapper .nxp-top-container {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
.npx-freshmail-block-footer-wrapper .nxp-top-container h4 {
  font-size: 1.8rem;
  color: #06c;
  margin: 0.25rem 0;
  font-weight: 700;
  text-align: center;
}
@media (min-width: 48rem) {
  .npx-freshmail-block-footer-wrapper .nxp-top-container h4 {
    text-align: left;
  }
}
.npx-freshmail-block-footer-wrapper .npx-freshmail-list-id .fieldset-legend {
  margin-bottom: 0.625rem;
  display: inline-block;
  font-size: 1rem;
}
.npx-freshmail-block-footer-wrapper .nxp-top-text-container {
  line-height: 1.4rem;
  color: #505050;
}
.npx-freshmail-block-footer-wrapper .nxp-top-text-container p {
  margin: 0;
  font-size: 1.1rem;
}
.npx-freshmail-block-footer-wrapper .npx-msg {
  display: none;
}

.npx-freshmail-block-footer-wrapper.npx-freshmail-form-sent .npx-msg {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  min-height: 300px;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 1.5rem;
  background: url("../img/Icon_newsletter_stopka_TYP.png") center 60px no-repeat;
  color: #909090;
}
.npx-freshmail-block-footer-wrapper.npx-freshmail-form-sent .npx-msg h3 {
  margin-bottom: 0;
  font-size: 1.8rem;
  font-weight: normal;
  font-style: italic;
  color: #909090;
}
.npx-freshmail-block-footer-wrapper.npx-freshmail-form-sent .npx-msg p {
  margin-top: 0;
  font-size: 1.2rem;
}
.npx-freshmail-block-footer-wrapper.npx-freshmail-form-sent .nxp-columns-container {
  display: none !important;
}

.readmore-js-link, .readless-js-link {
  margin: 0;
  padding: 0.1875rem 0;
  border-radius: 5px;
  background: #f3f3f5;
  color: #4c4c4c;
  text-align: center;
  text-decoration: none;
  margin-bottom: 0.5rem;
}

.readless-js-link {
  display: none;
}

.readmore-js-link {
  display: block;
}

.readmore-item {
  height: 100px;
  overflow: hidden;
}
.readmore-item.readmore-big {
  height: 250px;
}

.readmore-open .readless-js-link {
  display: block;
}
.readmore-open .readmore-js-link {
  display: none;
}
.readmore-open .readmore-item {
  height: auto;
}

.readmore-js:not(.readmore-open) .readmore-item {
  position: relative;
}
.readmore-js:not(.readmore-open) .readmore-item::after {
  content: "";
  position: absolute;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: -webkit-linear-gradient(rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.9) 95%);
  background-image: -webkit-linear-gradient(rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.9) 95%);
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(20%, rgba(255, 255, 255, 0)), color-stop(95%, rgba(255, 255, 255, 0.9)));
  background-image: linear-gradient(rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.9) 95%);
  background-image: -o-linear-gradient(rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.9) 95%);
  pointer-events: none;
}

.slick-slider ul.slick-dots {
  margin: 0 0 0;
  padding: 0;
  position: relative;
  right: auto;
  left: auto;
  bottom: auto;
  top: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}
.slick-slider ul.slick-dots li {
  margin: 0;
  padding: 0;
  list-style-type: none;
  list-style-image: none;
}
.slick-slider ul.slick-dots li button {
  display: block;
  border: none;
  width: 16px;
  height: 16px;
  padding: 0;
  margin: 0 0.25rem;
  background: #d1d9dc;
  border-radius: 50px;
  text-align: left;
  text-indent: -9990px;
}
.slick-slider ul.slick-dots li button:hover {
  color: #98a0a3;
}
.slick-slider ul.slick-dots li.slick-active button {
  background-color: #fecc09;
}
.slick-slider ul.slick-dots li.slick-active button:hover {
  background-color: #fecc09;
}

button.slick-arrow {
  margin: 0;
  padding: 0;
  position: relative;
  display: block;
  opacity: 1;
  width: 24px;
  height: 32px;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-image: url("../images/arrows.png");
  text-align: left;
  text-indent: -9990px;
  border: none;
  background-color: transparent;
}
button.slick-arrow.slick-prev {
  background-position: 0 0;
}
button.slick-arrow.slick-next {
  background-position: -24px 0;
}
button.slick-arrow.slick-prev:hover {
  background-position: 0 -32px;
  background-image: url("../images/arrows.png");
}
button.slick-arrow.slick-next:hover {
  background-position: -24px -32px;
  background-image: url("../images/arrows.png");
}

.mylivechat_inline.mylivechat-mobile-docked {
  max-width: calc(100% - 30px);
}

.mylivechat_buttonround img {
  position: absolute !important;
  width: 80px !important;
  height: 80px !important;
  left: 0 !important;
  top: 0 !important;
  border-radius: 40px;
}

.mylivechat_buttonround_tooltip {
  width: 200px !important;
  top: 20px !important;
  right: 110px !important;
  white-space: normal !important;
  padding: 0.5rem !important;
  line-height: 1rem !important;
  display: block !important;
}
@media (min-width: 36rem) {
  .mylivechat_buttonround_tooltip {
    width: 360px !important;
  }
}

.mylivechat_buttonround {
  width: 80px !important;
  height: 80px !important;
  top: -20px !important;
  left: auto !important;
  right: 5px !important;
  background: #fff !important;
}

.n-order-summary-price {
  font-size: 1.25rem;
}

.opinia .group-footer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  background-image: url("../images/opinia.png");
  background-position: right top;
  background-repeat: no-repeat;
  padding-top: 1.875rem;
}
.opinia .group-footer img {
  border-radius: 50%;
  -webkit-box-shadow: 1px 1px 6px 0 #a1a1a1;
          box-shadow: 1px 1px 6px 0 #a1a1a1;
}
.opinia .field--name-field-linkedin-link {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-end;
      -ms-flex-item-align: end;
          align-self: flex-end;
}
.opinia .field--name-field-linkedin-link a {
  position: relative;
  text-indent: -99999px;
  text-align: left;
  display: block;
  width: 46px;
  height: 11px;
  margin-top: 0.3125rem;
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
  -webkit-align-self: end;
      -ms-flex-item-align: end;
          align-self: end;
}
.opinia .field--name-field-linkedin-link a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/linkedin-logo.png");
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.opinia .n-signature-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-align-content: flex-end;
      -ms-flex-line-pack: end;
          align-content: flex-end;
  margin-right: 1.25rem;
}
.opinia .n-signature-wrapper p {
  margin: 0;
  text-align: right;
}
.opinia .field--name-field-image {
  margin-left: 0;
  padding-right: 0.375rem;
}
.opinia .field--name-field-zajawka p {
  font-size: 0.75rem;
  font-weight: bold;
  line-height: 1.1875rem;
}

.right-wrapper-column-inner span.h3 {
  margin: 5rem 0 2.9rem 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
@media (min-width: 62rem) {
  .right-wrapper-column-inner {
    max-width: 80%;
  }
}

@media (min-width: 62rem) {
  #edit-email-btn-container {
    max-width: 40%;
  }
}
#edit-email-btn-container .form-email {
  max-width: 100%;
}

.npx-voucher-wrapper {
  position: fixed;
  top: 100px;
  right: 0;
  display: none;
  z-index: 999;
}

a.npx-close-voucher-block, a.npx-close-voucher-second-block {
  top: 13px;
}

.npx-close-voucher-block, .npx-close-voucher-second-block {
  position: absolute;
  left: 0;
  top: 0;
  width: 32px;
  height: 32px;
  opacity: 0.7;
  padding: 0.625rem;
}
.npx-close-voucher-block:before, .npx-close-voucher-block:after, .npx-close-voucher-second-block:before, .npx-close-voucher-second-block:after {
  position: absolute;
  left: 15px;
  content: " ";
  height: 16px;
  width: 2px;
  background-color: #333;
  -webkit-box-shadow: 0 0 10px 2px #fff;
          box-shadow: 0 0 10px 2px #fff;
}
.npx-close-voucher-block:before, .npx-close-voucher-second-block:before {
  -webkit-transform: rotate(45deg);
       -o-transform: rotate(45deg);
          transform: rotate(45deg);
}
.npx-close-voucher-block:after, .npx-close-voucher-second-block:after {
  -webkit-transform: rotate(-45deg);
       -o-transform: rotate(-45deg);
          transform: rotate(-45deg);
}

.block-voucher-second-block {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: none;
  z-index: 999999;
}

.npx-voucher-second-wrapper {
  position: fixed;
  top: 50px;
  left: 50%;
  -webkit-transform: translateX(-50%);
       -o-transform: translateX(-50%);
          transform: translateX(-50%);
}
.npx-voucher-second-wrapper img {
  max-height: calc(100vh - 100px);
  width: auto;
}

html, body {
  overflow-x: hidden;
}

em {
  font-weight: 600;
}

body ol, body ul {
  padding-left: 1rem;
}

.text-red {
  color: #dc3545;
}

ul {
  list-style-image: url("../images/li_checkmark.png");
}
ul li {
  margin-bottom: 0.5rem;
}
ul ul {
  list-style-image: url("../images/li_full.png");
  margin-top: 0.5rem;
}

img[data-align=right] {
  float: right;
  padding: 0.3125rem 0.625rem;
}

img.align-left {
  float: left;
  margin: 1rem 2rem 0.5rem 0;
}

.toast-wrapper {
  display: none !important;
}

textarea {
  border-color: #d4d8db;
  border-width: 1px;
  border-radius: 0;
  background-color: #fff;
  padding: 0.75rem;
  border-style: solid;
  line-height: 1.75rem;
  margin-top: 0;
  margin-bottom: 0;
}

legend {
  font-weight: 700;
}

.js-form-item {
  margin-top: 1rem;
  margin-bottom: 1rem;
  position: relative;
}

.form-type-textfield input, input.form-email, .form-item-subscriber-name input {
  border-color: #d4d8db;
  border-width: 1px;
  border-radius: 0;
  background-color: #fff;
  padding: 0 0 0 1.5rem;
  border-style: solid;
  line-height: 3rem;
  height: 50px;
  margin-top: 0;
  margin-bottom: 0;
}

label {
  margin-bottom: 0.5rem;
}

input[type=radio], input[type=checkbox] {
  display: none;
}

input[type=radio]:checked + label, input[type=checkbox]:checked + label {
  background-image: url(../images/checkbox-on.png);
}

.js-form-type-checkbox {
  padding-left: 0;
}

.js-form-type-radio label, .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url(../images/checkbox-off.png);
  background-repeat: no-repeat;
  background-position: 0 center;
  display: block;
  margin: 0;
  line-height: 1.25rem;
  font-size: 0.875rem;
}

a.npx-form-button {
  text-decoration: none;
  text-transform: none !important;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
