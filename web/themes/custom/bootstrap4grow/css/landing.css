@charset "UTF-8";
/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

::-webkit-scrollbar {
  -webkit-appearance: none;
}

::-webkit-scrollbar:vertical {
  width: 12px;
}

::-webkit-scrollbar:horizontal {
  height: 12px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  border: 2px solid #ffffff;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #ffffff;
}

.training-terms-block {
  width: 800px;
}
@media (min-width: 62rem) {
  .training-terms-block {
    width: 100%;
  }
}
.training-terms-block-wrapper {
  overflow-x: scroll;
}
@media (min-width: 62rem) {
  .training-terms-block-wrapper {
    overflow-x: auto;
  }
}
.training-terms-block-td-1 {
  width: 16%;
}
.training-terms-block-td-2 {
  width: 20%;
}
.training-terms-block-td-3 {
  width: 12%;
}
.training-terms-block-td-4 {
  width: 6%;
}
.training-terms-block-td-5 {
  width: 12%;
}
.training-terms-block-h4 {
  font-size: 1rem;
}
.training-terms-block-with-sustable-table {
  margin: 0.3125rem 0 0.625rem 0;
}
.training-terms-block-th {
  padding-bottom: 0.625rem;
}
.training-terms-block-td-clickable::after {
  content: "";
  width: 13px;
  height: 7px;
  display: inline-block;
  background: transparent no-repeat url(../images/menu-arrow.png) 0 0;
  margin-left: 0.625rem;
  -o-transition: -o-transform 300ms ease;
  -webkit-transition: -webkit-transform 300ms ease;
  transition: -webkit-transform 300ms ease;
  transition: transform 300ms ease;
  transition: transform 300ms ease, -webkit-transform 300ms ease, -o-transform 300ms ease;
  -webkit-transform: scaleY(-1);
  -o-transform: scaleY(-1);
     transform: scaleY(-1);
}
.training-terms-block-td-clickable.open::after {
  -webkit-transform: scaleY(1);
  -o-transform: scaleY(1);
     transform: scaleY(1);
}
.training-terms-block-npx-form-button {
  padding: 0.625rem 0.9375rem;
}
.training-terms-block .ask-for-course-closed {
  width: 90%;
  max-width: 400px;
  padding: 0.625rem 0.9375rem;
}

.load-more-terms {
  border: 2px solid #0053B3;
  margin: -1.5rem auto 1.875rem;
  border-radius: 6px;
  width: 185px;
  background-color: #fff;
}
.load-more-terms-bg {
  height: 0;
  background: #d0d8db;
  margin-top: 4.375rem;
}
.load-more-terms-wrapper {
  position: absolute;
  width: calc(100vw - 40px);
}
@media (min-width: 62rem) {
  .load-more-terms-wrapper {
    position: relative;
    width: auto;
  }
}

#szkolenie-grupa-1 {
  background: #0056B3;
  background-position: center;
  -webkit-background-size: cover;
          background-size: cover;
}
#szkolenie-grupa-1 .field__label {
  display: none;
}
#szkolenie-grupa-1 a.npx-form-button-inline.npx-autolink {
  padding: 0;
  border: 0;
  background: transparent;
  color: #fecc09;
  font-weight: bold;
  text-transform: none;
  margin-top: 0.75rem;
  display: inline-block;
  font-size: inherit;
}
#szkolenie-grupa-1 .field--name-field-ts-opis {
  font-size: 1.125rem;
  line-height: 1.5rem;
  margin: 0;
  position: relative;
  z-index: 9;
  padding-top: 0.5rem;
}
#szkolenie-grupa-1 .field--name-field-ts-opis p {
  font-size: 1.125rem;
  line-height: 1.5rem;
}
#szkolenie-grupa-1 .inner {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
}
#szkolenie-grupa-1 h2 {
  margin-top: 0;
}
#szkolenie-grupa-1 h1 {
  line-height: 3.125rem;
  font-weight: bold;
  font-size: 2.5rem;
  margin-top: 2.75rem;
  z-index: 9;
  position: relative;
}
#szkolenie-grupa-1 a, #szkolenie-grupa-1 span.h1, #szkolenie-grupa-1 h1, #szkolenie-grupa-1 h2, #szkolenie-grupa-1 h3, #szkolenie-grupa-1 h4, #szkolenie-grupa-1 h5, #szkolenie-grupa-1 p, #szkolenie-grupa-1 li {
  color: #fff;
}
#szkolenie-grupa-1 a.npx-program-button {
  color: #191919;
}
#szkolenie-grupa-1 ul, #szkolenie-grupa-1 ol {
  list-style-image: url("/themes/custom/bootstrap4grow/images/check-white.png");
}
#szkolenie-grupa-1 .group-right .obraz img {
  padding-left: 0.625rem;
}
#szkolenie-grupa-1 .obraz {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}
#szkolenie-grupa-1 .obraz img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  color: transparent;
  min-height: 220px;
}
#szkolenie-grupa-1.full-width-image h1 {
  margin-top: 5.75rem;
  text-shadow: -1px -1px 14px #000, 1px -1px 14px #000, -1px 1px 14px #000, 1px 1px 14px #000;
}
@media (max-width: 61.99875rem) {
  #szkolenie-grupa-1.full-width-image h1 {
    margin-top: 3rem;
  }
}
@media (min-width: 75rem) {
  #szkolenie-grupa-1.half-width-image .inner {
    max-width: 100%;
    padding-right: 0;
    padding-left: 1.875rem;
  }
}
@media (min-width: 87.5rem) {
  #szkolenie-grupa-1.half-width-image .inner {
    padding-left: calc(50vw - 44.21875rem + 1.875rem);
  }
}
#szkolenie-grupa-1.half-width-image h1 {
  margin-top: 2rem;
}
#szkolenie-grupa-1.half-width-image span.h1 {
  margin-top: 3rem;
  text-shadow: -1px -1px 14px #000, 1px -1px 14px #000, -1px 1px 14px #000, 1px 1px 14px #000;
  font-size: 2rem;
  line-height: 2.25rem;
  display: block;
  font-weight: 700;
}
#szkolenie-grupa-1 .inner-absolute {
  bottom: 10px;
  -webkit-transform: translateX(-50%);
       -o-transform: translateX(-50%);
          transform: translateX(-50%);
  left: 50%;
  width: 100%;
  margin-top: 3rem;
  text-shadow: -1px -1px 14px #000, 1px -1px 14px #000, -1px 1px 14px #000, 1px 1px 14px #000;
  font-size: 1.5rem;
  line-height: 2rem;
  display: block;
  font-weight: 700;
  color: #fff;
}
@media (min-width: 48rem) {
  #szkolenie-grupa-1 .inner-absolute {
    font-size: 2.5rem;
    line-height: 3.5rem;
  }
}

@media (max-width: 61.99875rem) {
  .paragraph--type-tytul-szkolenia.ds-2col-fluid > .group-left {
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 100%;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
    max-width: 100%;
  }
}
@media (min-width: 75rem) {
  .paragraph--type-tytul-szkolenia.ds-2col-fluid > .group-left {
    padding-right: 1.5rem;
  }
}

.node--type-landing-page .paragraph--type-tytul-szkolenia.ds-2col-fluid > .group-left {
  padding-bottom: 2rem !important;
}
@media (min-width: 75rem) {
  .node--type-landing-page #szkolenie-grupa-1.half-width-image h1 {
    margin-top: 7.75rem;
  }
}

.npx-counter-wrapper {
  margin-left: -0.25rem;
}

.npx-tabs {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-align-content: stretch;
      -ms-flex-line-pack: stretch;
          align-content: stretch;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 48rem) {
  .npx-tabs {
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}

@media (min-width: 75rem) {
  .npx-box-left {
    padding-right: 1rem;
  }
}
@media (max-width: 74.99875rem) {
  .npx-box-left {
    -webkit-box-ordinal-group: 3;
    -webkit-order: 2;
        -ms-flex-order: 2;
            order: 2;
  }
}

.npx-counter-info {
  color: red;
}

.npx-counter-icon {
  background: transparent url("../images/budzik.png") no-repeat center center;
  display: inline-block;
  width: 20px;
  height: 20px;
  -webkit-background-size: cover;
          background-size: cover;
  margin-right: 0.375rem;
  margin-left: 0.3125rem;
}

#npx-price-info-wrapper {
  font-size: 0.875rem;
}
#npx-price-info-wrapper .form-item-npx-discount-code {
  position: relative;
  display: inline-block;
}
#npx-price-info-wrapper .form-item-npx-discount-code input[type=text] {
  margin: 0;
  height: 32px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-width: 1px;
  width: 100%;
  max-width: 200px;
}
#npx-price-info-wrapper .form-item-npx-discount-code .field-suffix {
  background-color: #fff;
  border: 0;
  display: inline-block;
}
#npx-price-info-wrapper .form-item-npx-discount-code .field-suffix a {
  display: block;
  position: absolute;
  z-index: 10;
  height: 34px;
  background: transparent url("../images/przelicz.png") no-repeat center center;
  width: 20px;
  text-align: left;
  text-indent: -9990px;
  right: 10px;
  top: 0;
  outline: 0;
  border: 0;
  margin-right: 0.25rem;
}
#npx-price-info-wrapper #npx-expand-bottom-wrapper {
  width: 100%;
}
@media (min-width: 48rem) {
  #npx-price-info-wrapper #npx-expand-bottom-wrapper {
    width: auto;
    margin-right: 1rem;
  }
}
#npx-price-info-wrapper .list-group-item {
  border: none;
  display: list-item;
  margin-left: 1.25rem;
  padding: 0;
}
#npx-price-info-wrapper .item-list {
  padding-top: 3rem;
}
#npx-price-info-wrapper li {
  list-style-image: url("../images/li.png");
}
@media (min-width: 48rem) {
  #npx-price-info-wrapper .npx-social-colorbox-link {
    top: -15px;
  }
}
#npx-price-info-wrapper .npx-social-colorbox-link a::before {
  vertical-align: sub;
  height: 20px;
  width: 20px;
  margin: 0 0.3125rem 0 0;
  background: transparent url("../images/price-tag.png") no-repeat center center;
  -webkit-background-size: auto auto;
          background-size: auto;
  background-size: auto;
  -webkit-background-size: cover;
          background-size: cover;
  display: inline-block;
  content: "";
}

@media (min-width: 48rem) {
  .npx-box-left .npx-price-b {
    background-image: url("../images/dziobek.png");
    background-repeat: no-repeat;
    background-position: 0 0;
    margin-top: -0.9375rem;
  }
  .npx-box-left .npx-price-b-a {
    margin-top: 0.9375rem;
  }
}
@media (min-width: 75rem) {
  .npx-box-left .npx-price-b {
    margin-top: 0;
  }
  .npx-box-left .npx-price-b-a {
    margin-top: 0;
  }
}

@media (max-width: 35.99875rem) {
  .npx-price {
    min-height: 150px;
  }
}
.npx-price-a {
  padding: 1.875rem 2.5rem 0 0;
}
.npx-price-a-a {
  line-height: 1.25rem;
  font-size: 0.875rem;
}
.npx-price-a-b {
  font-size: 1.25rem;
}
.npx-price-b {
  width: 100%;
}
@media (min-width: 48rem) {
  .npx-price-b {
    width: 60%;
    padding: inherit;
    padding-top: 3.125rem;
  }
}
@media (min-width: 62rem) {
  .npx-price-b {
    padding-top: 3.125rem;
    width: auto;
  }
}
.npx-price-b-a {
  font-size: 1.25rem;
}
.npx-price-b-b {
  font-size: 1.125rem;
}
.npx-price-b-c {
  color: #a2a2a2;
}
@media (max-width: 47.99875rem) {
  .npx-price-b-c {
    font-size: 0.8125rem;
  }
}
@media (min-width: 75rem) {
  .npx-price-b-c {
    top: 5px;
  }
}

.npx-counter-wrapper {
  top: 7px;
  position: relative;
}
@media (min-width: 48rem) {
  .npx-counter-wrapper {
    top: -10px;
  }
}

.npx-calculation-box {
  padding: 1.875rem 1.875rem 0;
  margin: 0 -1.875rem;
  width: calc(100% + 60px);
  background-color: #f8faf9;
  background-image: url("../images/dziobek2.png");
  background-repeat: no-repeat;
  background-position: 0 0;
}
@media (min-width: 36rem) {
  .npx-calculation-box {
    background-image: url("../images/kreska.png");
    background-repeat: repeat-x;
    background-position: 0 0;
    background-color: transparent;
    padding-top: 0;
  }
}
.npx-calculation-box .list-group-item {
  background: transparent;
  padding-left: 0;
}
.npx-calculation-box input {
  max-width: 200px;
}

#npx-participants-amount-wrapper .description {
  font-size: 1em;
}
#npx-participants-amount-wrapper small.text-muted {
  max-width: calc(100% - 149px);
  float: right;
  color: #000 !important;
  font-size: 1rem;
}
@media (min-width: 36rem) {
  #npx-participants-amount-wrapper small.text-muted {
    float: none;
    max-width: 100%;
  }
}
#npx-participants-amount-wrapper span.ui-spinner {
  display: inline-block;
  position: relative;
  border: 1px solid #d0d8db;
  padding: 0 2.8125rem;
  border-radius: 0;
  margin: 0 0.625rem 0 0;
}
#npx-participants-amount-wrapper span.ui-spinner .form-control:focus {
  background-color: #fff;
  border-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#npx-participants-amount-wrapper span.ui-spinner input {
  border: 0;
  height: 44px;
  line-height: 2.8125rem;
  padding: 0 0.625rem;
  margin: 0;
  border-radius: 0;
  width: 45px;
  text-align: center;
}
@media (min-width: 36rem) {
  #npx-participants-amount-wrapper span.ui-spinner input {
    height: 44px;
  }
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button {
  border: 0;
  outline: 0;
  position: absolute;
  left: 0;
  top: 0;
  height: 45px;
  width: 45px;
  background-color: transparent;
  opacity: 0.85;
  padding: 0;
  margin: 0;
  right: auto;
  background-image: url("../images/spinner-min.png");
  background-position: center center;
  background-repeat: no-repeat;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button:hover {
  background-color: #ebeff2;
  cursor: pointer;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button.ui-corner-tr {
  background-image: url("../images/spinner-plus.png");
  left: auto;
  right: 0;
  border-left: 1px solid #ddd;
}
#npx-participants-amount-wrapper span.ui-spinner a.ui-spinner-button.ui-corner-br {
  border-right: 1px solid #ddd;
}

#szkolenie-grupa-8 a.npx-form-tab {
  margin: 0.9375rem 0;
}
@media (min-width: 48rem) {
  #szkolenie-grupa-8 a.npx-form-tab {
    padding: 1.0625rem;
    margin: 1.25rem 0.9375rem;
  }
}
#szkolenie-grupa-8 .form-item-npx-training {
  display: none;
}

a.npx-form-tab {
  max-width: 340px;
  padding: 0;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
@media (min-width: 48rem) {
  a.npx-form-tab {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 45%;
        -ms-flex: 1 0 45%;
            flex: 1 0 45%;
    max-width: 45%;
  }
}
@media (min-width: 75rem) {
  a.npx-form-tab {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 29%;
        -ms-flex: 1 0 29%;
            flex: 1 0 29%;
    max-width: 29%;
  }
}
a.npx-form-tab:hover {
  -webkit-transform: scale(1.1);
       -o-transform: scale(1.1);
          transform: scale(1.1);
  z-index: 101;
}
a.npx-form-tab.npx-active-tab {
  -webkit-transform: scale(1.1);
       -o-transform: scale(1.1);
          transform: scale(1.1);
  z-index: 100;
}

.npx-form-outer-wrapper {
  -webkit-box-shadow: 0 0 50px 5px #f1f1f1;
  box-shadow: 0 0 50px 5px #f1f1f1;
  line-height: 1.5;
}

#npx-top-wrapper > div:not(#npx-tabs) {
  padding: 0 0.3rem;
}
@media (min-width: 36rem) {
  #npx-top-wrapper > div:not(#npx-tabs) {
    padding: 0 2.5rem;
  }
}

.npx-blocks-program-tab-wrapper {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  border: solid rgba(128, 128, 128, 0.74) 1px;
  border-radius: 4px;
}
@media (min-width: 48rem) {
  .npx-blocks-program-tab-wrapper {
    height: 100%;
  }
}

li a.active .npx-blocks-program-tab-wrapper, li a:hover .npx-blocks-program-tab-wrapper {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
  -webkit-box-shadow: 0 0 15px 0 #888;
          box-shadow: 0 0 15px 0 #888;
}

.npx-program-tabs-wrapper {
  border-radius: 4px;
}
.npx-program-tabs-wrapper .list-group-item {
  border: 0;
}
.npx-program-tabs-wrapper .nav-tabs {
  list-style: none;
  border-bottom: 0;
}
@media (min-width: 48rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 48%;
        -ms-flex: 1 0 48%;
            flex: 1 0 48%;
    max-width: 48%;
  }
}
@media (min-width: 62rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 31%;
        -ms-flex: 1 0 31%;
            flex: 1 0 31%;
    max-width: 31%;
  }
}
@media (min-width: 75rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
            flex: 1 0 25%;
    max-width: 25%;
  }
}
.npx-program-tabs-wrapper .nav-tabs li a {
  text-align: left;
  background: #f3f3f5;
  margin-bottom: 0.9375rem;
}
.npx-program-tabs-wrapper .nav-tabs a {
  text-decoration: none;
  color: #000;
}
.npx-program-tabs-wrapper .nav-tabs a:hover {
  text-decoration: none;
}
.npx-program-tabs-wrapper .nav-tabs a.active {
  text-transform: none;
}
.npx-program-tabs-wrapper .nav-tabs a.active .npx-blocks-program-tab-wrapper {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
  -webkit-box-shadow: 0 0 15px 0 #888;
          box-shadow: 0 0 15px 0 #888;
}

#npx-regular-box-wrapper {
  opacity: 0.65;
}

.npx-box-right:not(.npx-active-box) .npx-price, .npx-box-left:not(.npx-active-box) .npx-price {
  opacity: 0.65;
}
.npx-box-right:not(.npx-active-box) .npx-price-b-a, .npx-box-left:not(.npx-active-box) .npx-price-b-a {
  text-decoration: line-through;
}
@media (max-width: 74.99875rem) {
  .npx-box-right:not(.npx-active-box) .npx-price, .npx-box-left:not(.npx-active-box) .npx-price {
    display: none !important;
  }
}

#npx-regular-box-wrapper .npx-price-b-c {
  top: 0;
}

.npx-active-tab .npx-training-form-tab-wrapper, .npx-form-tab:hover .npx-training-form-tab-wrapper {
  -webkit-box-shadow: 0 0 15px 0 #54534f;
  box-shadow: 0 0 15px 0 #54534f;
  border-radius: 4px;
}

.npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word {
  width: 100%;
  padding: 0 1.25rem;
}
@media (min-width: 36rem) {
  .npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word {
    width: auto;
  }
}
.npx-training-form-type-info-wrapper .n-type-header-inner .n-type-word-last {
  padding-left: 0.625rem;
}
.npx-training-form-type-info-wrapper .npx-spoiler-content {
  font-size: inherit;
}
.npx-training-form-type-info-wrapper .two-columns-template .col-sm ul li {
  list-style-image: url("../images/online-li-yellow.png");
}
.npx-training-form-type-info-wrapper .two-columns-template .col-sm:nth-child(1) ul li {
  list-style-image: url("../images/online-li-blue.png");
}
.npx-training-form-type-info-wrapper .n-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  border: 1px solid #034b7d;
  background-color: #fff;
  cursor: pointer;
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle {
  color: #034b7d;
  line-height: 1.875rem;
  font-size: 0.625rem;
  z-index: 20;
  border: 1px solid #034b7d;
  cursor: pointer;
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle.show-icon::before {
  content: "ROZWIŃ OPIS";
}
.npx-training-form-type-info-wrapper .npx-spoiler-toggle.hide-icon::before {
  content: "ZWIŃ OPIS";
}
.npx-training-form-type-info-wrapper .stationary {
  padding: 0.125rem 1rem;
  background: #ffc60c;
  border-radius: 20px;
  border: #ffc60c 2px solid;
  font-weight: 600;
}
.npx-training-form-type-info-wrapper .live-online {
  padding: 0.125rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  background: var(--secondary);
}

.tr-form-stationary {
  padding: 0.1875rem 1rem;
  background: #ffc60c;
  border-radius: 20px;
  font-weight: 800;
  color: #000;
  margin-left: 0.625rem;
  text-transform: uppercase;
  font-size: 0.625rem;
}
.tr-form-online {
  padding: 0.1875rem 1rem;
  border-radius: 20px;
  font-weight: 800;
  background: var(--secondary);
  color: #fff;
  margin-left: 0.625rem;
  text-transform: uppercase;
  font-size: 0.625rem;
}

.npx-variant .fieldset-legend {
  color: #000;
  margin: 2rem 0;
  display: block;
}
.npx-variant h4 {
  line-height: 1.5rem;
}

.npx-training-form-tab-wrapper {
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  border: solid rgba(128, 128, 128, 0.44) 1px;
  border-radius: 4px;
  height: 100%;
}
.npx-training-form-tab-wrapper .n-tab-header-inner {
  background: rgba(0, 0, 0, 0.55);
}
.npx-training-form-tab-header-inner {
  border-radius: 4px 4px 0 0;
  min-height: 183px;
  background: rgba(0, 0, 0, 0.55);
}
.npx-training-form-tab-header {
  background-repeat: no-repeat;
  -webkit-background-size: cover;
          background-size: cover;
  border-radius: 4px 4px 0 0;
}
.npx-training-form-tab-header-hours {
  text-shadow: none;
  font-size: 0.75rem;
}
.npx-training-form-tab-header-type {
  font-size: 0.625rem;
}
.npx-training-form-tab-header-type .stationary {
  padding: 0.125rem 1rem;
  background: #ffc60c;
  border-radius: 20px;
  border: #ffc60c 2px solid;
}
.npx-training-form-tab-header-type .live-online {
  padding: 0.125rem 1rem;
  border-radius: 20px;
}
.npx-training-form-tab-header-type .webinar, .npx-training-form-tab-header-type .online {
  padding: 0.125rem 1rem;
  border-radius: 20px;
}
.npx-training-form-tab-header-title h3 {
  font-size: 1.3rem;
  text-shadow: -1px -1px 14px black, 1px -1px 14px black, -1px 1px 14px black, 1px 1px 14px black;
  text-transform: none;
  color: #fff;
  font-weight: 600;
  margin: 2rem 0 1.2rem 0;
}
.npx-training-form-tab-content {
  text-transform: none;
}
.npx-training-form-tab-content p {
  font-size: 0.9375rem;
}
.npx-training-form-tab-content ul {
  padding-left: 0.9375rem;
}
.npx-training-form-tab-content ul li {
  font-size: 0.9375rem;
  text-transform: none;
  font-weight: normal;
  text-align: left;
  list-style-image: url("../images/li_checkmark.png");
  line-height: 1.3rem;
}
.npx-training-form-tab-more {
  margin: auto 0.9375rem 0.625rem 0;
}

#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .tab-content {
  background: #f1fbfc;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  padding: 1.875rem 0;
}
#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .tab-content .tab-pane {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
}

.node--type-landing-page .npx-training-form-tab-header-title h3 {
  margin-top: 0;
}

.field--name-field-question {
  position: relative;
  display: inline-block;
  cursor: pointer;
  padding-left: 2rem;
}
.field--name-field-question.active:before {
  -webkit-transform: rotate(-45deg);
       -o-transform: rotate(-45deg);
          transform: rotate(-45deg);
  top: 28px;
}
.field--name-field-question:before {
  position: absolute;
  top: 22px;
  cursor: pointer;
  content: "";
  display: inline-block;
  width: 11px;
  height: 11px;
  border-right: 2px solid #343a40;
  border-top: 2px solid #343a40;
  -webkit-transform: rotate(135deg);
  -o-transform: rotate(135deg);
     transform: rotate(135deg);
  margin-right: 0.5em;
  margin-left: 1em;
  max-width: 12px;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 auto;
  -webkit-flex: 1 0 auto;
          flex: 1 0 auto;
  -ms-flex-item-align: center;
  -webkit-align-self: center;
          align-self: center;
  left: -12px;
  -webkit-transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 600ms cubic-bezier(0.4, 0, 0.2, 1);
}
.field--name-field-question h3, .field--name-field-question p, .field--name-field-question h4 {
  margin: 0.625rem 0;
}

.field--name-field-answer h3, .field--name-field-answer h4 {
  margin: 0 0 0.625rem 0;
}

#szkolenie-grupa-6 {
  max-width: 100vw;
  padding: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}
#szkolenie-grupa-6 .field--name-dynamic-block-fieldnode-ds-training-program-block .item-list {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 5.625rem auto;
}

.field--name-dynamic-block-fieldnode-ds-training-program-block .field-label-above:first-of-type:not(:last-of-type):after {
  display: none;
}

.ckeditor-accordion-container > dl dt > a > .ckeditor-accordion-toggle::before, .ckeditor-accordion-container > dl dt > a > .ckeditor-accordion-toggle::after {
  background-color: #000;
}

.pdf-program-link img {
  width: 48px;
  height: auto;
}

.program-accordion ul {
  padding-left: 1.2rem;
}
.program-accordion li {
  margin: 0.5rem 0 0.5rem 1.1rem;
}
.program-accordion .pdf-program {
  z-index: 5;
  position: relative;
}
@media (min-width: 62rem) {
  .program-accordion .pdf-program {
    float: right;
  }
}
.program-accordion .pdf-program-link {
  margin-left: 0.625rem;
}
@media (min-width: 36rem) {
  .program-accordion .pdf-program-link {
    margin-left: 1.25rem;
  }
}
.program-accordion .pdf-program-link img {
  width: 38px;
  display: inline-block;
}
@media (min-width: 36rem) {
  .program-accordion .pdf-program-link img {
    width: 48px;
  }
}
.program-accordion .pdf-program-link a {
  margin: 0.9375rem;
}
@media (min-width: 62rem) {
  .program-accordion .pdf-program span {
    display: block;
    margin-bottom: 0.625rem;
  }
}
@media (min-width: 62rem) {
  .program-accordion h2.field-label-above {
    margin-right: 10.625rem;
  }
}
.program-accordion .ckeditor-accordion-container h4 {
  font-size: 1.5rem;
  font-weight: 700;
}
.program-accordion .ckeditor-accordion-container > dl dt > a, .program-accordion .ckeditor-accordion-container > dl dt > a:not(.button) {
  color: inherit;
  border-bottom: #dcdddf 1px solid;
  text-decoration: none;
}
.program-accordion .ckeditor-accordion-container > dl dt > a:hover, .program-accordion .ckeditor-accordion-container > dl dt > a:not(.button):hover {
  color: inherit;
  text-decoration: underline;
}
.program-accordion .ckeditor-accordion-toggler {
  background-color: transparent !important;
  color: inherit;
  font-size: 1.2rem;
}
.program-accordion dl dt > a {
  background-color: #ecedef;
  color: #000;
  border-bottom: #dcdddf 1px solid;
}
.program-accordion dl {
  border: #dcdddf 1px solid;
}

#szkolenie-grupa-6.sekcja.w100.w100limitContent .tab-content {
  background: #f1fbfc;
  padding: 1.875rem 0;
}

.npx-training-form-tab-header {
  position: relative;
}
.npx-training-form-tab-header-inner {
  z-index: 9;
  position: relative;
}
.npx-training-form-tab-header img {
  position: absolute;
  z-index: 2;
}

.paragraph--type-arguments {
  margin-top: 3.75rem;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media (min-width: 36rem) {
  .paragraph--type-arguments {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}
.paragraph--type-arguments .group-left {
  width: 100%;
  height: 100px;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .group-left {
    width: 45%;
  }
}
.paragraph--type-arguments .group-right {
  width: 100%;
  height: auto;
  background: transparent;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .group-right {
    width: 55%;
    height: 100px;
    background: #f4f7f5;
    border-radius: 100px 0 0 100px;
  }
}
.paragraph--type-arguments a.n-get-pdf {
  padding-right: 2.5rem;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments a.n-get-pdf {
    padding-right: 0;
  }
}
.paragraph--type-arguments .field--name-field-arguments-right {
  padding-left: 1.25rem;
  max-width: 400px;
  margin-left: auto;
  border-left: #000 1px solid;
}
@media (min-width: 62rem) {
  .paragraph--type-arguments .field--name-field-arguments-right {
    padding-left: 2.5rem;
  }
}
.paragraph--type-arguments .field--name-field-arguments-right p {
  font-size: 0.7rem;
  line-height: 1rem;
}
.paragraph--type-arguments .field--name-field-arguments-left {
  max-width: 300px;
  padding-left: 3.125rem;
  font-size: 1.2rem;
}
.paragraph--type-arguments .field--name-field-arguments-left::before {
  content: "";
  position: absolute;
  width: 100px;
  height: 100px;
  top: 0;
  left: 0;
  z-index: -1;
  background: #f4f7f5;
  border-radius: 100px;
}

.field-oni-juz-byli_item_even {
  margin-top: 1.25rem;
  margin-left: auto;
  margin-right: 0;
}

.field--name-field-oni-juz-byli_item {
  max-width: 660px;
  clear: both;
}
.field--name-field-oni-juz-byli_item .group-middle {
  width: 100%;
}
@media (min-width: 36rem) {
  .field--name-field-oni-juz-byli_item .group-middle {
    width: calc(100% - 110px);
  }
}

.opinia .group-footer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  background-image: url("../images/opinia.png");
  background-position: right top;
  background-repeat: no-repeat;
  padding-top: 1.875rem;
}
.opinia .group-footer img {
  border-radius: 50%;
  -webkit-box-shadow: 1px 1px 6px 0 #a1a1a1;
          box-shadow: 1px 1px 6px 0 #a1a1a1;
}
.opinia .field--name-field-linkedin-link {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-end;
      -ms-flex-item-align: end;
          align-self: flex-end;
}
.opinia .field--name-field-linkedin-link a {
  position: relative;
  text-indent: -99999px;
  text-align: left;
  display: block;
  width: 46px;
  height: 11px;
  margin-top: 0.3125rem;
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
  -webkit-align-self: end;
      -ms-flex-item-align: end;
          align-self: end;
}
.opinia .field--name-field-linkedin-link a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/linkedin-logo.png");
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.opinia .n-signature-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-align-content: flex-end;
      -ms-flex-line-pack: end;
          align-content: flex-end;
  margin-right: 1.25rem;
}
.opinia .n-signature-wrapper p {
  margin: 0;
  text-align: right;
}
.opinia .field--name-field-image {
  margin-left: 0;
  padding-right: 0.375rem;
}
.opinia .field--name-field-zajawka p {
  font-size: 0.75rem;
  font-weight: bold;
  line-height: 1.1875rem;
}

@media (min-width: 62rem) {
  .field--name-field-loga-firm {
    max-height: 106px;
    overflow: hidden;
  }
}
.field--name-field-loga-firm .field__item.col-auto {
  -webkit-transform: scale(0.7);
       -o-transform: scale(0.7);
          transform: scale(0.7);
}
@media (min-width: 36rem) {
  .field--name-field-loga-firm .field__item.col-auto {
    -webkit-transform: none;
         -o-transform: none;
            transform: none;
  }
}
.field--name-field-loga-firm img {
  opacity: 0.5;
  height: 40px;
  width: auto;
  margin: 0.1875rem auto;
}

.field--name-field-logo-1-ref {
  padding-bottom: 0.25rem;
}

.field--name-field-logo-2-ref, .field--name-field-logo-1-ref {
  min-height: 60px;
}

h2.field-label-above p {
  font-size: inherit;
  font-weight: inherit;
  margin-bottom: inherit;
}

@media (min-width: 62rem) {
  .field--name-field-blog-posts {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
  .field--name-field-blog-posts .node--type-article.node--view-mode-grow3 {
    height: 100%;
    display: block;
    position: relative;
    padding-bottom: 3.5rem;
  }
  .field--name-field-blog-posts .field--name-node-link {
    margin-left: auto;
    margin-right: auto;
  }
  .field--name-field-blog-posts .field__item {
    max-width: 92%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 32%;
        -ms-flex: 1 0 32%;
            flex: 1 0 32%;
  }
}
.field--name-field-blog-posts .field__item {
  text-align: center;
}
.field--name-field-blog-posts h3 {
  margin: 0;
  font-size: 1.25rem;
}
.field--name-field-blog-posts img {
  -webkit-transition: -webkit-transform 0.2s;
  transition: -webkit-transform 0.2s;
  -o-transition: -o-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s, -o-transform 0.2s;
}
.field--name-field-blog-posts img:hover {
  -webkit-transform: scale(1.05);
       -o-transform: scale(1.05);
          transform: scale(1.05);
}
.field--name-field-blog-posts .field--name-field-image {
  overflow: hidden;
}
.field--name-field-blog-posts .node--type-article {
  max-width: 450px;
  margin: 0 auto;
}
@media (max-width: 61.99875rem) {
  .field--name-field-blog-posts .node--type-article {
    margin-bottom: 2rem;
  }
}
.field--name-field-blog-posts .node--type-article h3 a {
  color: #000;
}

.narrow {
  max-width: 1200px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
}
.narrow h2 {
  margin-left: 0;
  margin-right: 0;
}
@media (min-width: 81.25rem) {
  .narrow h2 {
    margin-left: -3vw;
    margin-right: -3vw;
  }
}
@media (min-width: 100rem) {
  .narrow h2 {
    margin-left: -6.625rem;
    margin-right: -6.625rem;
  }
}

.w100 {
  max-width: 100vw;
  padding: 0;
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

.n-breadcrumb {
  display: inline-block;
}

.inner {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
}

div.node--type-landing-page .field--name-body {
  padding-top: 1.5rem;
}

body h2 {
  margin-top: 0;
  padding-top: 2rem;
}

body h2.field-label-above::after {
  display: block;
  left: 50%;
  margin-left: -1.25rem;
  position: absolute;
  content: " ";
  width: 40px;
  height: 2px;
  background: #fecc09;
  bottom: -15px;
}
@media (min-width: 62rem) {
  body h2.field-label-above::after {
    left: 0;
    margin-left: 0;
  }
}

@media (min-width: 48rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 100%;
        -ms-flex: 1 0 100%;
            flex: 1 0 100%;
  }
}
@media (min-width: 62rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 100%;
        -ms-flex: 1 0 100%;
            flex: 1 0 100%;
  }
}
@media (min-width: 75rem) {
  .npx-program-tabs-wrapper .nav-tabs > li {
    max-width: 49%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 49%;
        -ms-flex: 1 0 49%;
            flex: 1 0 49%;
  }
}

.npx-training-form-tab-header {
  -webkit-background-size: 100% 200px;
          background-size: 100% 200px;
}

@media (min-width: 48rem) {
  .npx-training-form-tab-header, .npx-training-form-tab-content {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 50%;
        -ms-flex: 1 0 50%;
            flex: 1 0 50%;
  }
}

.npx-training-form-tab-header-inner {
  height: 100%;
}

.npx-training-form-tab-header-top {
  height: 200px;
}

.npx-training-form-tab-header-info-inner.h5 {
  font-weight: bold;
  font-size: 0.9375rem;
}
.npx-training-form-tab-header-info-inner span {
  color: #ffc60c;
}

@media (min-width: 48rem) {
  .npx-training-form-tab-header-inner {
    border-top-right-radius: 0;
  }
}

#szkolenie-grupa-6 {
  background: #f1fbfc;
}

.npx-program-tabs-wrapper .nav-tabs li .npx-blocks-program-tab-wrapper > a.npx-no-autolink-with-link {
  margin-bottom: 0;
}
.npx-program-tabs-wrapper .nav-tabs li .npx-blocks-program-tab-wrapper > a.npx-no-autolink-with-link .n-tab-header {
  height: 100%;
}

.npx-program-tabs-wrapper .nav-tabs li .npx-training-form-tab-content a:not(.npx-no-autolink) {
  background: transparent;
  text-decoration: underline;
  margin-bottom: 0;
}

.load-more-terms-wrapper {
  position: relative;
}

@media (max-width: 74.99875rem) {
  #szkolenie-grupa-1 .field--name-field-ts-opis {
    display: inline-block;
  }
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
