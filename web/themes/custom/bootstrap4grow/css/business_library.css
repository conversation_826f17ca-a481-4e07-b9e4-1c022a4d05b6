/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

/* VARIABLES */
/* TYPOGRAPHY */
/* Google Fonts */
:root {
  --secondary: #0066cc;
}

.nav {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
  color: #0053B3;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  -o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.nav-link:hover, .nav-link:focus {
  color: #005283;
  text-decoration: none;
}
.nav-link.disabled {
  color: #6c757d;
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  border-bottom: 1px solid #dee2e6;
}
.nav-tabs .nav-link {
  margin-bottom: -0.0625rem;
  background: none;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  border-color: #e9ecef #e9ecef #dee2e6;
  isolation: isolate;
}
.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}
.nav-tabs .dropdown-menu {
  margin-top: -0.0625rem;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: 0.25rem;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #0d6efd;
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  -webkit-box-flex: 1;
  -webkit-flex: 1 1 auto;
      -ms-flex: 1 1 auto;
          flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  -webkit-flex-basis: 0;
      -ms-flex-preferred-size: 0;
          flex-basis: 0;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

#block-bootstrap4grow-grow3menu-2 {
  min-height: auto;
  display: inline-grid;
}

li.n-online-info-wrapper {
  float: none;
  max-width: 1415px;
  margin: 0 auto !important;
  -webkit-box-sizing: initial !important;
          box-sizing: initial !important;
  cursor: auto !important;
}

.n-online-info-wrapper .n-online-info {
  display: block;
  font-weight: bold;
  margin: 0.5rem 0;
  text-transform: uppercase;
  font-size: 0.8125rem;
}

.sf-accordion .n-online-info {
  font-size: 0.8125rem;
}

#block-grow3menu ul#superfish-grow3menu > li > ul > li.sf-depth-2.menuparent {
  -webkit-box-sizing: initial;
          box-sizing: initial;
}

.n-menu-red {
  color: red;
  text-transform: uppercase;
  font-weight: bold;
  padding-bottom: 0.5rem;
  display: block;
}

.block-superfishgrow3menu .sf-depth-1.menuparent {
  cursor: default;
}

ul.sf-menu span.n-menu-red {
  margin-left: -0.9375rem;
  width: calc(100vw - 15px);
  padding-bottom: 0.625rem;
  padding-top: 0.625rem;
}

ul#superfish-grow3menu-accordion {
  border-top: 1px solid #d0d8db;
  background: #fff;
}
ul#superfish-grow3menu-accordion .n-menu-search-wrapper-li {
  display: none;
}
ul#superfish-grow3menu-accordion a.sf-depth-1 {
  font-weight: 800;
  border-bottom: 1px solid #e3e9e9;
  text-transform: uppercase;
  padding: 1.25rem 0;
}
ul#superfish-grow3menu-accordion li.sf-expanded > a {
  border: 0;
}
ul#superfish-grow3menu-accordion li.sf-expanded.sf-depth-1 ul {
  border-top: 1px solid #e3e9e9;
}
ul#superfish-grow3menu-accordion a.sf-depth-2 {
  font-weight: 500;
}
ul#superfish-grow3menu-accordion .sf-clone-parent {
  display: none;
}
ul#superfish-grow3menu-accordion a.menuparent:after {
  content: "";
  width: 13px;
  height: 7px;
  display: inline-block;
  background: transparent no-repeat url("/themes/custom/bootstrap4grow/images/menu-arrow.png") 0 0;
  margin-left: 0.625rem;
  -webkit-transition: -webkit-transform 300ms ease;
  transition: -webkit-transform 300ms ease;
  -o-transition: -o-transform 300ms ease;
  transition: transform 300ms ease;
  transition: transform 300ms ease, -webkit-transform 300ms ease, -o-transform 300ms ease;
  -webkit-transform: scaleY(-1);
       -o-transform: scaleY(-1);
          transform: scaleY(-1);
}
ul#superfish-grow3menu-accordion a {
  padding: 0.625rem;
  line-height: 1.25rem;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
ul#superfish-grow3menu-accordion a.menuparent:hover:after {
  -webkit-transform: scaleY(1);
       -o-transform: scaleY(1);
          transform: scaleY(1);
}
ul#superfish-grow3menu-accordion a:not(.sf-depth-4) {
  color: #000;
}
ul#superfish-grow3menu-accordion.sf-expanded {
  display: block;
  left: 0 !important;
  width: 100%;
  position: absolute;
  top: 66px !important;
}
ul#superfish-grow3menu-accordion.sf-expanded li {
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
  max-width: 100%;
}

ul#superfish-grow3menu a.menuparent:hover::after {
  -webkit-transform: scaleY(1);
  -o-transform: scaleY(1);
     transform: scaleY(1);
}
ul#superfish-grow3menu li.sf-depth-1:not(:first-child) > ul li {
  width: auto;
}
ul#superfish-grow3menu li.sf-depth-1:first-child > ul li ul {
  background: #fff;
}
ul#superfish-grow3menu li.sf-depth-1:not(:first-child) > ul {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  padding-right: 5rem !important;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu li.sf-depth-1:not(:first-child) > ul {
    padding-right: calc(-83.4375rem + 100vw) !important;
  }
}
@media (min-width: 100rem) {
  ul#superfish-grow3menu li.sf-depth-1:not(:first-child) > ul {
    padding-right: calc((-88.4375rem + 100vw) / 1.25) !important;
  }
}
ul#superfish-grow3menu li > ul li.sf-depth-2.sf-no-children {
  margin-left: 2.5rem;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
ul#superfish-grow3menu li.sf-depth-1:first-child > ul li.sf-depth-2.sf-no-children {
  margin-top: 0;
}
ul#superfish-grow3menu li.sf-depth-1:first-child > ul li.sf-depth-2.sf-no-children:last-of-type {
  margin-top: 2.25rem;
}
ul#superfish-grow3menu li.sf-depth-1 > ul::after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  content: " ";
  display: block;
  background: rgba(0, 0, 0, 0.07);
  height: 1px;
}
ul#superfish-grow3menu > li:first-child > ul {
  min-height: 530px;
  overflow: hidden;
}
ul#superfish-grow3menu > li > ul {
  background: #fff;
  top: 66px;
  width: 100% !important;
  z-index: 100;
  border-bottom: 1px solid silver;
  padding-top: 1rem;
  padding-bottom: 2rem;
  -webkit-box-shadow: 0 12px 9px 0 rgba(0, 0, 0, 0.5);
          box-shadow: 0 12px 9px 0 rgba(0, 0, 0, 0.5);
}
ul#superfish-grow3menu li.sf-depth-2 {
  max-width: 400px;
}
ul#superfish-grow3menu li.sf-depth-2.menuparent:first-child::after {
  content: " ";
  position: absolute;
  width: 1px;
  height: 400px;
  top: 16px;
  -webkit-transform: translateX(400px);
       -o-transform: translateX(400px);
          transform: translateX(400px);
  background: rgba(0, 0, 0, 0.07);
  z-index: 9999;
}
@media (min-width: 81.25rem) {
  ul#superfish-grow3menu li.sf-depth-2.menuparent:nth-child(2)::after {
    content: " ";
    position: absolute;
    width: 1px;
    height: 400px;
    top: 16px;
    -webkit-transform: translateX(930px);
         -o-transform: translateX(930px);
            transform: translateX(930px);
    background: rgba(0, 0, 0, 0.07);
    z-index: 9999;
  }
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu li.sf-depth-2.menuparent:nth-child(2)::after {
    -webkit-transform: translateX(960px);
         -o-transform: translateX(960px);
            transform: translateX(960px);
  }
}
ul#superfish-grow3menu a.sf-depth-2.menuparent::after {
  content: "";
  width: 7px;
  height: 13px;
  display: inline-block;
  background: transparent no-repeat url("/themes/custom/bootstrap4grow/images/menu-arrow-right.png") 0 0;
  margin-left: 0.625rem;
  -webkit-transform: scale(0.8);
  -o-transform: scale(0.8);
     transform: scale(0.8);
}
ul#superfish-grow3menu a.sf-depth-2.menuparent {
  color: #000;
  pointer-events: none;
  font-weight: 600;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
ul#superfish-grow3menu > li:first-child > ul li.sf-depth-2.sf-no-children {
  float: none;
  padding-left: 1.875rem;
  margin-left: 0;
  margin-bottom: 0.625rem;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu > li:first-child > ul li.sf-depth-2.sf-no-children {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
@media (min-width: 88.75rem) {
  ul#superfish-grow3menu > li:first-child > ul li.sf-depth-2.sf-no-children {
    padding-left: 0;
    padding-right: 0;
  }
}
ul#superfish-grow3menu > li:first-child > ul li.sf-depth-2.sf-no-children.n-menu-rcol {
  position: absolute;
}
ul#superfish-grow3menu > li > ul > li.sf-depth-2 > ul {
  top: 16px;
  width: 100% !important;
  min-height: 500px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: none;
          box-shadow: none;
}
ul#superfish-grow3menu .n-menu-rcol {
  position: absolute;
  top: 0;
}
ul#superfish-grow3menu .moneybox {
  width: 214px;
  height: 50px;
  margin-top: 0;
  background: transparent url("/themes/custom/bootstrap4grow/images/swinka5final.png") no-repeat 0 center;
  -webkit-background-size: contain;
          background-size: contain;
  text-indent: -9990px;
  pointer-events: all;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu .moneybox {
    width: 312px;
    height: 73px;
    margin-top: 1rem;
  }
}
ul#superfish-grow3menu .menu-link-promocje {
  width: 214px;
  height: 50px;
  margin-top: 0;
  background: transparent url("/themes/custom/bootstrap4grow/images/ikona_promocje.png") no-repeat 0 center;
  -webkit-background-size: contain;
          background-size: contain;
  text-indent: -9990px;
  pointer-events: all;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu .menu-link-promocje {
    width: 312px;
    height: 73px;
    margin-top: 1rem;
  }
}
ul#superfish-grow3menu .kalendarz {
  margin-top: 1.875rem;
  width: 214px;
  height: 50px;
  background: transparent url("/themes/custom/bootstrap4grow/images/kalendarz.png") no-repeat 0 0;
  -webkit-background-size: contain;
          background-size: contain;
  text-indent: -9990px;
  pointer-events: all;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu .kalendarz {
    width: 312px;
    height: 73px;
  }
}
ul#superfish-grow3menu > li > ul a {
  padding: 0;
  line-height: 1.1;
  padding: 0.25rem 0;
  font-size: 0.9375rem;
}
ul#superfish-grow3menu li.sf-depth-3 a {
  max-width: 500px;
  padding: 0.5rem 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-decoration: none;
}
ul#superfish-grow3menu a.sf-depth-3 {
  padding-bottom: 0.5rem;
  padding-top: 0.5rem;
}
@media (min-width: 65.625rem) {
  ul#superfish-grow3menu.sf-menu span.n-menu-red {
    margin-left: 0;
    padding: 0;
  }
}
ul#superfish-grow3menu li.sf-depth-2.menuparent {
  max-width: 1415px;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin: 0 auto;
  width: auto !important;
  float: none;
  font-size: 0.9375rem;
  line-height: 1.125rem;
  position: static;
}
@media (min-width: 87.5rem) {
  ul#superfish-grow3menu li.sf-depth-2.menuparent {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
@media (min-width: 88.75rem) {
  ul#superfish-grow3menu li.sf-depth-2.menuparent {
    padding-left: 0;
    padding-right: 0;
  }
}

@media (min-width: 65.625rem) {
  .n-menu-search-wrapper-li input {
    position: relative;
    color: #000;
    padding: 0.625rem;
    border: #ffab1a 1px solid;
    border-radius: 5px;
    margin-left: 1.75rem;
    margin-top: 0.5rem;
  }
}
@media (min-width: 87.5rem) {
  .n-menu-search-wrapper-li input {
    margin-left: 0.5rem;
  }
}
@media (min-width: 88.75rem) {
  .n-menu-search-wrapper-li input {
    margin-left: 0;
  }
}

@media (min-width: 62rem) {
  ul.sf-menu span.n-menu-red {
    margin-left: 0.5rem;
  }
}
.n-menu-search-results-wrapper {
  position: absolute;
  top: 1em;
  z-index: 497;
}

ul.sf-menu ul li.n-menu-search-wrapper-li {
  position: static;
  max-width: 400px;
  padding-top: 0.625rem;
  padding-bottom: 0.9375rem;
  float: none;
}

ul.sf-menu ul.n-menu-search-results {
  position: relative;
  top: 0;
  width: 100% !important;
}

.container.npx-gray-bg {
  background: #f4f4f4;
  padding: 1.5625rem 0.9375rem;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  color: #333;
}

#cboxOverlay {
  background: #006bd9;
  opacity: 0.85 !important;
}

#cboxClose {
  position: absolute;
  bottom: auto;
  top: 5px;
  right: 5px;
  background: url("../images/close.png") no-repeat 0 0;
  width: 30px;
  height: 30px;
  text-indent: -9999px;
}

#cboxWrapper {
  border-radius: 0;
}

#cboxContent {
  padding: 4.375rem 0.3125rem 0.3125rem;
  border-radius: 0;
}
@media (min-width: 36rem) {
  #cboxContent {
    padding: 4.375rem 1.875rem 1.875rem;
  }
}
@media (min-width: 62rem) {
  #cboxContent {
    padding: 4.375rem 3.75rem 1.875rem;
  }
}

.red-text, .npx-red-text {
  color: #B3002F;
  font-weight: bold;
}

.site-footer {
  background: -webkit-gradient(linear, left top, right top, color-stop(0, #00366c), to(#0060c1));
  background: -webkit-linear-gradient(left, #00366c 0, #0060c1 100%);
  background: -o-linear-gradient(left, #00366c 0, #0060c1 100%);
  background: linear-gradient(to right, #00366c 0, #0060c1 100%);
  padding: 2.8125rem 0;
}

.region-footer-first {
  padding: 2.1875rem 4.375rem;
}

#block-bootstrap4grow-stopkadanekontaktowe {
  max-width: 1068px;
  margin-bottom: 3.125rem;
  margin-top: 3.125rem;
}
#block-bootstrap4grow-stopkadanekontaktowe h3 {
  font-size: 1.25rem;
  margin: 0;
  color: #fff;
}
#block-bootstrap4grow-stopkadanekontaktowe .contextual-links a {
  color: #333;
}
#block-bootstrap4grow-stopkadanekontaktowe a {
  color: #fff;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-facebook, #block-bootstrap4grow-stopkadanekontaktowe a.n-linkedin {
  position: relative;
  padding-left: 1.25rem;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-phone, #block-bootstrap4grow-stopkadanekontaktowe a.n-email {
  position: relative;
  padding-left: 1.25rem;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-phone::before, #block-bootstrap4grow-stopkadanekontaktowe a.n-email::before {
  content: "";
  position: absolute;
  top: 5px;
  width: 12px;
  height: 12px;
  margin-left: -1.25rem;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-phone::before {
  background: url("../images/Icon_footer_phone.png") no-repeat;
  -webkit-background-size: cover;
          background-size: cover;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-email::before {
  background: url("../images/Icon_footer_email.png") no-repeat;
  -webkit-background-size: contain;
          background-size: contain;
  background-position-y: 70%;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-facebook::before {
  content: "";
  position: absolute;
  top: 4px;
  width: 12px;
  height: 12px;
  margin-left: -1.25rem;
  background: url("../images/Icon_footer_facebook.png") no-repeat;
  -webkit-background-size: contain;
          background-size: contain;
}
#block-bootstrap4grow-stopkadanekontaktowe a.n-linkedin::before {
  content: "";
  position: absolute;
  top: 4px;
  width: 12px;
  height: 12px;
  margin-left: -1.25rem;
  background: url("../images/Icon_footer_linkedin.png") no-repeat;
  -webkit-background-size: contain;
          background-size: contain;
}
#block-bootstrap4grow-stopkadanekontaktowe .n-col {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
@media (min-width: 62rem) {
  #block-bootstrap4grow-stopkadanekontaktowe .n-col {
    border-right: #6db4fa 2px solid;
  }
}
#block-bootstrap4grow-stopkadanekontaktowe .n-col:last-child {
  border: 0;
}

#block-bootstrap4grow-npxmailcontactblock {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  max-width: 100vw;
  background: #f3f3f5;
  padding: 2.5rem 0;
}
#block-bootstrap4grow-npxmailcontactblock .npx-mailcontact-right-info-n-img img {
  margin: 0 auto;
}

.npx-mailcontact-header {
  max-width: 1415px;
}
.npx-mailcontact-header h2 {
  font-size: 1.5rem;
}
@media (min-width: 62rem) {
  .npx-mailcontact-header h2 {
    font-size: 2rem;
  }
}
.npx-mailcontact-n-col-name {
  padding-right: 0;
  padding-left: 0;
}
@media (min-width: 36rem) {
  .npx-mailcontact-n-col-name {
    padding-right: 0.3125rem;
  }
}
.npx-mailcontact-n-col-name input {
  width: 100%;
  background: #fff url("../images/user_icon.svg") left center no-repeat;
  background-position-x: left;
  background-position-x: 8px;
}
.npx-mailcontact-n-col-email {
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 36rem) {
  .npx-mailcontact-n-col-email {
    padding-left: 0.3125rem;
  }
}
.npx-mailcontact-n-col-email input {
  width: 100%;
  background: #fff url("../images/icon_email.png") left center no-repeat;
  background-position-x: left;
  background-position-x: 8px;
  padding-left: 1.875rem;
}
.npx-mailcontact-form-wrapper-over .toast-wrapper, .npx-mailcontact-form-wrapper-over .messages {
  display: none !important;
}
.npx-mailcontact-form-wrapper {
  max-width: 1415px;
}
.npx-mailcontact-form-wrapper .js-form-type-checkbox input {
  display: none;
}
.npx-mailcontact-form-wrapper .form-textarea-wrapper {
  width: 100%;
}
.npx-mailcontact-form-wrapper label {
  display: block;
}
.npx-mailcontact-form-wrapper .field-suffix {
  display: none;
}
.npx-mailcontact-form-wrapper .error + div .field-suffix {
  display: block;
}
.npx-mailcontact-form-wrapper input.form-text.error, .npx-mailcontact-form-wrapper input.form-email.error,
.npx-mailcontact-form-wrapper textarea.error, .npx-mailcontact-form-wrapper input.form-checkbox.error {
  outline: #ff0000 1px solid;
}
.npx-mailcontact-form-wrapper input.form-checkbox.error + label {
  color: #fc5353;
}
.npx-mailcontact-form-wrapper div.form-textarea-wrapper + .field-suffix,
.npx-mailcontact-form-wrapper input.form-text + .field-suffix,
.npx-mailcontact-form-wrapper input.form-email + .field-suffix {
  display: none;
}
.npx-mailcontact-form-wrapper input.form-text.error + .field-suffix,
.npx-mailcontact-form-wrapper input.form-email.error + .field-suffix {
  display: block;
}
.npx-mailcontact-form-wrapper .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url("../images/checkbox-off.png");
  background-repeat: no-repeat;
  background-position: 0px 2px;
  display: block;
  margin: 0;
  line-height: 1.25rem;
  font-size: 0.75rem;
}
.npx-mailcontact-form-wrapper .js-form-type-checkbox input:checked + label {
  background-image: url("../images/checkbox-on.png");
}
.npx-mailcontact-form-wrapper .required-info {
  font-size: 0.75rem;
  padding-left: calc(var(--bs-gutter-x) * 0.5);
}
.npx-mailcontact-form-wrapper .button.form-submit {
  margin: 0.625rem 0;
  padding: 0.75rem 1.875rem;
  width: auto;
}
@media (min-width: 36rem) {
  .npx-mailcontact-form-wrapper .button.form-submit {
    margin: 0.75rem 0 0 0.625rem;
  }
}
.npx-mailcontact-right-info-n-name {
  font-size: 1rem;
}
.npx-mailcontact-right-info-n-position {
  font-size: 0.875rem;
}
.npx-mailcontact-right-info-n-phone a {
  font-size: 1.375rem;
  color: #000;
}
.npx-mailcontact-right-info-n-img {
  margin-right: 0.625rem;
}
@media (min-width: 36rem) {
  .npx-mailcontact-right-info-n-img {
    margin-right: 1.25rem;
  }
}
.npx-mailcontact-right-info-n-img img {
  border: var(--secondary) 1px solid;
  border-radius: 50px;
  max-width: 80px;
  max-height: 80px;
}
.npx-mailcontact-right-info-n-row h3 {
  font-size: 1rem;
}
@media (min-width: 62rem) {
  .npx-mailcontact-right-info-n-row h3, .npx-mailcontact-right-info-n-row > p {
    text-align: left;
  }
}
.npx-mailcontact-right-info-n-row:first-child:after {
  margin-left: auto;
  margin-right: auto;
}
@media (min-width: 62rem) {
  .npx-mailcontact-right-info-n-row:first-child:after {
    margin-left: 6.25rem;
  }
}
.npx-mailcontact-left-info-n-row {
  margin-bottom: 3.5rem;
}
.npx-mailcontact-left-info-n-txt {
  font-size: 0.9375rem;
  line-height: 1.2;
}
.npx-mailcontact-left-info-n-icon {
  width: 85px;
  -webkit-flex-shrink: 0;
      -ms-flex-negative: 0;
          flex-shrink: 0;
}
.npx-mailcontact-middle-col .row {
  padding: 0 1.875rem;
}
.npx-mailcontact-middle-col .npx-mailcontact-middle-col-row-3 {
  padding: 0 calc(var(--bs-gutter-x) * 0.5 + 1.875rem);
}
.npx-mailcontact-middle-col .form-item-accept {
  margin: 0;
  margin-top: 0.625rem;
  padding-left: 0;
}
@media (min-width: 36rem) {
  .npx-mailcontact-middle-col .form-item-accept {
    -webkit-box-flex: 1;
    -webkit-flex: 1 0 0;
        -ms-flex: 1 0 0px;
            flex: 1 0 0;
  }
}
.npx-mailcontact-middle-col .form-item-message {
  width: 100%;
  margin-right: 0;
  padding: 0 calc(var(--bs-gutter-x) * 0.5);
}
.npx-mailcontact-middle-col .form-item-message .field-suffix {
  display: none;
}
.npx-mailcontact-middle-col textarea {
  width: 100%;
}
.npx-mailcontact-left-col {
  padding-left: 0;
}
.npx-mailcontact-right-col {
  margin-top: 2.5rem;
  text-align: center;
  padding-left: 1.875rem;
}
@media (min-width: 62rem) {
  .npx-mailcontact-right-col {
    border-left: #d4d8db 1px solid;
    padding-left: 0.9375rem;
  }
}
@media (min-width: 68.8125rem) {
  .npx-mailcontact-right-col {
    padding-left: 1.875rem;
  }
}
.npx-mailcontact-right-info-n-row:first-child:after {
  content: "";
  display: block;
  width: 215px;
  height: 1px;
  background: #ccc;
  margin-bottom: 1.25rem;
  margin-top: 1.25rem;
}
.npx-mailcontact-right-info {
  max-width: 400px;
}
@media (min-width: 62rem) {
  .npx-mailcontact-right-info {
    margin-top: -3.4375rem;
  }
}
.npx-mailcontact-left-info-h3 {
  font-size: 1.125rem;
  margin: 0 0 0.25rem 0;
}
.npx-mailcontact-right-info-h3 {
  font-size: 1.125rem;
  margin: 0 0 0.25rem 0;
}
.npx-mailcontact-thx-header {
  font-size: 1.75rem;
}

.npx-freshmail-block-footer-background {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  background: #ecedef;
}

.npx-freshmail-block-footer-wrapper {
  max-width: 1200px;
  color: #000;
}
.npx-freshmail-block-footer-wrapper .npx-left-column {
  padding-right: 2.5rem;
}
.npx-freshmail-block-footer-wrapper .npx-freshmail-accept + label a {
  color: #000;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 400;
}
.npx-freshmail-block-footer-wrapper input.error {
  border-color: red;
}
.npx-freshmail-block-footer-wrapper input.error + label a, .npx-freshmail-block-footer-wrapper input.error + label {
  color: red;
}
.npx-freshmail-block-footer-wrapper .npx-right-column .js-form-type-checkbox {
  margin-bottom: 0;
  margin-top: 0;
}
.npx-freshmail-block-footer-wrapper .npx-right-column .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url(../images/checkbox-off.png);
  background-repeat: no-repeat;
  background-position: 0 2px;
  display: block;
  margin: 0;
  line-height: 1.5rem;
  font-size: 0.875rem;
  font-weight: 400;
}
.npx-freshmail-block-footer-wrapper .npx-right-column .js-form-type-checkbox input {
  display: none;
}
.npx-freshmail-block-footer-wrapper .npx-right-column .js-form-type-checkbox input:checked + label {
  background-image: url(../images/checkbox-on-blue.png);
}
.npx-freshmail-block-footer-wrapper .npx-right-column form fieldset.npx-freshmail-list-id {
  margin-bottom: 0;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .npx-input-fields-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .npx-input-fields-wrapper label {
  font-size: 0.875rem;
  font-weight: 400;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .npx-input-fields-wrapper input {
  max-width: 200px;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .form-type-email, .npx-freshmail-block-footer-wrapper .npx-right-column form .form-type-textfield {
  max-width: 200px;
  margin: 0.625rem 1.25rem 0.625rem 0;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .form-submit, .npx-freshmail-block-footer-wrapper .npx-right-column form #edit-submit {
  margin: 0.625rem auto 0;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .n-wrapped-btn {
  position: relative;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .n-wrapped-btn input {
  padding-left: 5rem;
  max-width: 300px;
  height: auto;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-align: left;
  font-weight: 400;
  line-height: 0.875rem;
  font-size: 0.75rem;
  padding-right: 1.5rem;
  width: auto;
}
.npx-freshmail-block-footer-wrapper .npx-right-column form .n-wrapped-btn span {
  position: absolute;
  top: calc(50% - 18px);
  left: 10px;
  color: #fff;
  font-size: 1.75rem;
  font-weight: 700;
  pointer-events: none;
  z-index: 10;
}
.npx-freshmail-block-footer-wrapper .nxp-top-container {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
.npx-freshmail-block-footer-wrapper .nxp-top-container h4 {
  font-size: 1.8rem;
  color: #06c;
  margin: 0.25rem 0;
  font-weight: 700;
  text-align: center;
}
@media (min-width: 48rem) {
  .npx-freshmail-block-footer-wrapper .nxp-top-container h4 {
    text-align: left;
  }
}
.npx-freshmail-block-footer-wrapper .npx-freshmail-list-id .fieldset-legend {
  margin-bottom: 0.625rem;
  display: inline-block;
  font-size: 1rem;
}
.npx-freshmail-block-footer-wrapper .nxp-top-text-container {
  line-height: 1.4rem;
  color: #505050;
}
.npx-freshmail-block-footer-wrapper .nxp-top-text-container p {
  margin: 0;
  font-size: 1.1rem;
}
.npx-freshmail-block-footer-wrapper .npx-msg {
  display: none;
}

.npx-freshmail-block-footer-wrapper.npx-freshmail-form-sent .npx-msg {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
  min-height: 300px;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 1.5rem;
  background: url("../img/Icon_newsletter_stopka_TYP.png") center 60px no-repeat;
  color: #909090;
}
.npx-freshmail-block-footer-wrapper.npx-freshmail-form-sent .npx-msg h3 {
  margin-bottom: 0;
  font-size: 1.8rem;
  font-weight: normal;
  font-style: italic;
  color: #909090;
}
.npx-freshmail-block-footer-wrapper.npx-freshmail-form-sent .npx-msg p {
  margin-top: 0;
  font-size: 1.2rem;
}
.npx-freshmail-block-footer-wrapper.npx-freshmail-form-sent .nxp-columns-container {
  display: none !important;
}

.readmore-js-link, .readless-js-link {
  margin: 0;
  padding: 0.1875rem 0;
  border-radius: 5px;
  background: #f3f3f5;
  color: #4c4c4c;
  text-align: center;
  text-decoration: none;
  margin-bottom: 0.5rem;
}

.readless-js-link {
  display: none;
}

.readmore-js-link {
  display: block;
}

.readmore-item {
  height: 100px;
  overflow: hidden;
}
.readmore-item.readmore-big {
  height: 250px;
}

.readmore-open .readless-js-link {
  display: block;
}
.readmore-open .readmore-js-link {
  display: none;
}
.readmore-open .readmore-item {
  height: auto;
}

.readmore-js:not(.readmore-open) .readmore-item {
  position: relative;
}
.readmore-js:not(.readmore-open) .readmore-item::after {
  content: "";
  position: absolute;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: -webkit-linear-gradient(rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.9) 95%);
  background-image: -webkit-linear-gradient(rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.9) 95%);
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(20%, rgba(255, 255, 255, 0)), color-stop(95%, rgba(255, 255, 255, 0.9)));
  background-image: linear-gradient(rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.9) 95%);
  background-image: -o-linear-gradient(rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.9) 95%);
  pointer-events: none;
}

.slick-slider ul.slick-dots {
  margin: 0 0 0;
  padding: 0;
  position: relative;
  right: auto;
  left: auto;
  bottom: auto;
  top: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}
.slick-slider ul.slick-dots li {
  margin: 0;
  padding: 0;
  list-style-type: none;
  list-style-image: none;
}
.slick-slider ul.slick-dots li button {
  display: block;
  border: none;
  width: 16px;
  height: 16px;
  padding: 0;
  margin: 0 0.25rem;
  background: #d1d9dc;
  border-radius: 50px;
  text-align: left;
  text-indent: -9990px;
}
.slick-slider ul.slick-dots li button:hover {
  color: #98a0a3;
}
.slick-slider ul.slick-dots li.slick-active button {
  background-color: #fecc09;
}
.slick-slider ul.slick-dots li.slick-active button:hover {
  background-color: #fecc09;
}

button.slick-arrow {
  margin: 0;
  padding: 0;
  position: relative;
  display: block;
  opacity: 1;
  width: 24px;
  height: 32px;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-image: url("../images/arrows.png");
  text-align: left;
  text-indent: -9990px;
  border: none;
  background-color: transparent;
}
button.slick-arrow.slick-prev {
  background-position: 0 0;
}
button.slick-arrow.slick-next {
  background-position: -24px 0;
}
button.slick-arrow.slick-prev:hover {
  background-position: 0 -32px;
  background-image: url("../images/arrows.png");
}
button.slick-arrow.slick-next:hover {
  background-position: -24px -32px;
  background-image: url("../images/arrows.png");
}

.mylivechat_inline.mylivechat-mobile-docked {
  max-width: calc(100% - 30px);
}

.mylivechat_buttonround img {
  position: absolute !important;
  width: 80px !important;
  height: 80px !important;
  left: 0 !important;
  top: 0 !important;
  border-radius: 40px;
}

.mylivechat_buttonround_tooltip {
  width: 200px !important;
  top: 20px !important;
  right: 110px !important;
  white-space: normal !important;
  padding: 0.5rem !important;
  line-height: 1rem !important;
  display: block !important;
}
@media (min-width: 36rem) {
  .mylivechat_buttonround_tooltip {
    width: 360px !important;
  }
}

.mylivechat_buttonround {
  width: 80px !important;
  height: 80px !important;
  top: -20px !important;
  left: auto !important;
  right: 5px !important;
  background: #fff !important;
}

.n-order-summary-price {
  font-size: 1.25rem;
}

.opinia .group-footer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  background-image: url("../images/opinia.png");
  background-position: right top;
  background-repeat: no-repeat;
  padding-top: 1.875rem;
}
.opinia .group-footer img {
  border-radius: 50%;
  -webkit-box-shadow: 1px 1px 6px 0 #a1a1a1;
          box-shadow: 1px 1px 6px 0 #a1a1a1;
}
.opinia .field--name-field-linkedin-link {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: flex-end;
      -ms-flex-item-align: end;
          align-self: flex-end;
}
.opinia .field--name-field-linkedin-link a {
  position: relative;
  text-indent: -99999px;
  text-align: left;
  display: block;
  width: 46px;
  height: 11px;
  margin-top: 0.3125rem;
  -webkit-background-size: contain;
          background-size: contain;
  background-repeat: no-repeat;
  -webkit-align-self: end;
      -ms-flex-item-align: end;
          align-self: end;
}
.opinia .field--name-field-linkedin-link a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/linkedin-logo.png");
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.opinia .n-signature-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-align-content: flex-end;
      -ms-flex-line-pack: end;
          align-content: flex-end;
  margin-right: 1.25rem;
}
.opinia .n-signature-wrapper p {
  margin: 0;
  text-align: right;
}
.opinia .field--name-field-image {
  margin-left: 0;
  padding-right: 0.375rem;
}
.opinia .field--name-field-zajawka p {
  font-size: 0.75rem;
  font-weight: bold;
  line-height: 1.1875rem;
}

.right-wrapper-column-inner span.h3 {
  margin: 5rem 0 2.9rem 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
@media (min-width: 62rem) {
  .right-wrapper-column-inner {
    max-width: 80%;
  }
}

@media (min-width: 62rem) {
  #edit-email-btn-container {
    max-width: 40%;
  }
}
#edit-email-btn-container .form-email {
  max-width: 100%;
}

.npx-voucher-wrapper {
  position: fixed;
  top: 100px;
  right: 0;
  display: none;
  z-index: 999;
}

a.npx-close-voucher-block, a.npx-close-voucher-second-block {
  top: 13px;
}

.npx-close-voucher-block, .npx-close-voucher-second-block {
  position: absolute;
  left: 0;
  top: 0;
  width: 32px;
  height: 32px;
  opacity: 0.7;
  padding: 0.625rem;
}
.npx-close-voucher-block:before, .npx-close-voucher-block:after, .npx-close-voucher-second-block:before, .npx-close-voucher-second-block:after {
  position: absolute;
  left: 15px;
  content: " ";
  height: 16px;
  width: 2px;
  background-color: #333;
  -webkit-box-shadow: 0 0 10px 2px #fff;
          box-shadow: 0 0 10px 2px #fff;
}
.npx-close-voucher-block:before, .npx-close-voucher-second-block:before {
  -webkit-transform: rotate(45deg);
       -o-transform: rotate(45deg);
          transform: rotate(45deg);
}
.npx-close-voucher-block:after, .npx-close-voucher-second-block:after {
  -webkit-transform: rotate(-45deg);
       -o-transform: rotate(-45deg);
          transform: rotate(-45deg);
}

.block-voucher-second-block {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: none;
  z-index: 999999;
}

.npx-voucher-second-wrapper {
  position: fixed;
  top: 50px;
  left: 50%;
  -webkit-transform: translateX(-50%);
       -o-transform: translateX(-50%);
          transform: translateX(-50%);
}
.npx-voucher-second-wrapper img {
  max-height: calc(100vh - 100px);
  width: auto;
}

html, body {
  overflow-x: hidden;
}

em {
  font-weight: 600;
}

body ol, body ul {
  padding-left: 1rem;
}

.text-red {
  color: #dc3545;
}

ul {
  list-style-image: url("../images/li_checkmark.png");
}
ul li {
  margin-bottom: 0.5rem;
}
ul ul {
  list-style-image: url("../images/li_full.png");
  margin-top: 0.5rem;
}

img[data-align=right] {
  float: right;
  padding: 0.3125rem 0.625rem;
}

img.align-left {
  float: left;
  margin: 1rem 2rem 0.5rem 0;
}

.toast-wrapper {
  display: none !important;
}

textarea {
  border-color: #d4d8db;
  border-width: 1px;
  border-radius: 0;
  background-color: #fff;
  padding: 0.75rem;
  border-style: solid;
  line-height: 1.75rem;
  margin-top: 0;
  margin-bottom: 0;
}

legend {
  font-weight: 700;
}

.js-form-item {
  margin-top: 1rem;
  margin-bottom: 1rem;
  position: relative;
}

.form-type-textfield input, input.form-email, .form-item-subscriber-name input {
  border-color: #d4d8db;
  border-width: 1px;
  border-radius: 0;
  background-color: #fff;
  padding: 0 0 0 1.5rem;
  border-style: solid;
  line-height: 3rem;
  height: 50px;
  margin-top: 0;
  margin-bottom: 0;
}

label {
  margin-bottom: 0.5rem;
}

input[type=radio], input[type=checkbox] {
  display: none;
}

input[type=radio]:checked + label, input[type=checkbox]:checked + label {
  background-image: url(../images/checkbox-on.png);
}

.js-form-type-checkbox {
  padding-left: 0;
}

.js-form-type-radio label, .js-form-type-checkbox label {
  padding: 0 0 0 1.875rem;
  background-color: transparent;
  background-image: url(../images/checkbox-off.png);
  background-repeat: no-repeat;
  background-position: 0 center;
  display: block;
  margin: 0;
  line-height: 1.25rem;
  font-size: 0.875rem;
}

a.npx-form-button {
  text-decoration: none;
  text-transform: none !important;
}

.page-view-biblioteka-biznesu .view-content {
  max-width: 900px;
}
.page-view-biblioteka-biznesu .ds-2col-fluid > .group-left {
  width: 100%;
  float: none;
}
@media (min-width: 48rem) {
  .page-view-biblioteka-biznesu .ds-2col-fluid > .group-left {
    width: 25%;
  }
}
.page-view-biblioteka-biznesu .ds-2col-fluid > .group-right {
  width: 100%;
  float: none;
}
@media (min-width: 48rem) {
  .page-view-biblioteka-biznesu .ds-2col-fluid > .group-right {
    width: 75%;
    margin-left: 1rem;
  }
}
.page-view-biblioteka-biznesu h2 {
  font-size: 120%;
  line-height: 130%;
  margin: 1rem 0;
  font-weight: 700;
  text-align: center;
}
@media (min-width: 48rem) {
  .page-view-biblioteka-biznesu h2 {
    text-align: left;
  }
}
.page-view-biblioteka-biznesu a {
  color: #4c4c4c;
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
