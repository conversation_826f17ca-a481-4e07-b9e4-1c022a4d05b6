<?php

/**
 * @file
 * Functions to support theming in the SASS Starterkit subtheme.
 */

use Dr<PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Render\Markup;
use Drupal\file\Entity\File;
use Drupal\image\Entity\ImageStyle;
use Dr<PERSON>al\Core\Url;
use Dr<PERSON>al\node\NodeInterface;
use Drupal\paragraphs\ParagraphInterface;
use Drupal\taxonomy\TermInterface;

function bootstrap4grow_preprocess_field(&$variables) {
  if ($variables['field_name'] == 'field_npxtraining_seo') {
    $variables['headline'] = substr(strip_tags($variables['items'][0]['content']['#text']), 0, 797) . '..';
  } else if ($variables['field_name'] == 'field_answer') {
    $variables['par_id'] = $variables['element']['#object']->id();
  } else if ($variables['field_name'] == 'field_galeria') {
    $variables['styled_images'] = [];
    foreach ($variables['items'] as &$item) {
      $paragraph = $item['content']['#paragraph'];
      if ($paragraph instanceof Drupal\paragraphs\Entity\Paragraph && $paragraph->hasField('field_obraz')) {
        foreach ($paragraph->get('field_obraz') as $image_field) {
          if ($image_field->entity) {
            $image = $image_field->entity;
            $image_uri = $image->getFileUri();
            $style = Drupal\image\Entity\ImageStyle::load('height_250');
            if ($style) {
              $image_path = \Drupal::service('file_system')->realpath($image_uri);
              list($width, $height) = getimagesize($image_path);
              $dimensions = ['width' => $width, 'height' => $height];
              $style->transformDimensions($dimensions, $image_uri);
              $variables['styled_images'][] = [
                'url' => $style->buildUrl($image_uri),
                'alt' => $image_field->alt,
                'original_url' => \Drupal::service('file_url_generator')->generateAbsoluteString($image_uri),
                'title' => $image_field->title,
                'width' => $dimensions['width'],
                'height' => $dimensions['height'],
              ];
            }
          }
        }
      }
    }
  }
}

/**
 * Implements hook_preprocess_field__field_name().
 * @param array $variables
 * Function adds a link to the benefits page to the paragraph field.
 * If the paragraph is a part of the training page, the link is added.
 */ 
function bootstrap4grow_preprocess_field__paragraph__field_opis_trenera__cecha_par(&$variables){
    $node = \Drupal::routeMatch()->getParameter('node');
    if ($node instanceof NodeInterface && $node->getType() === 'npxtraining') {
        if ($node->id() == 7) {
            $variables['benefit_link'] = TRUE;
            $field_value = $variables['items'][0]['content']['#text'];
            $modified_value = preg_replace('/(<h3[^>]*>.*?<\/h3>)/i', '$1<a href="/benefity" target="_blank" class="benefits-linker">Więcej >></a>', $field_value);
            $variables['items'][0]['content']['#text'] = $modified_value;
        } else {
            $variables['benefit_link'] = FALSE;
        }
    } else {
        $variables['benefit_link'] = FALSE;
    }
}
function bootstrap4grow_preprocess_field__default__node__node_title__opinia___custom(&$variables) {
  $node = $variables['element']['#object'];
  $tag = $node->field_kategoria->entity;
  $not_allowed_tag_ids = [5042, 5041, 4812, 496];

  if ($tag && !in_array($tag->id(), $not_allowed_tag_ids)) {

    $npxtraining_node_ids = \Drupal::entityQuery('node')
      ->accessCheck(FALSE)
      ->condition('type', 'npxtraining')
      ->condition('field_kategoria', $tag->id())
      ->condition('status', 1)
      ->range(0, 1)
      ->execute();
    $variables['items'][0]['content']['#name'] = $node->get('field_name')->value;
    $variables['items'][0]['content']['#surname'] = $node->get('field_surname')->value;
    $variables['items'][0]['content']['#intro'] = trim(preg_replace('/\s+/', ' ', strip_tags(str_replace('&nbsp;', ' ', $node->get('field_zajawka')->value)))) . ' ';
    if (!empty($npxtraining_node_ids)) {
      $npxtraining_node = \Drupal\node\Entity\Node::load(reset(array: $npxtraining_node_ids));
    //  $variables['items'][0]['content']['#training_node_url'] = $npxtraining_node->toUrl()->toString();
      $variables['items'][0]['content']['#related_training'] = TRUE;
      $variables['items'][0]['content']['#tytul_formalny'] = $npxtraining_node->get('field_npxtraining_tytul_formalny')->value;
    }
    else {
      $variables['items'][0]['content']['#related_training'] = FALSE;
    }
  }
}
function bootstrap4grow_preprocess_field__default__field_image__article(&$variables) {
  $item = $variables['items'][0];
  if (isset($item['content']['#item'])) {
    $image_file = $item['content']['#item']->entity;
    if ($image_file) {
      $style = ImageStyle::load('blog_page_image');
      if ($style) {
        $image_url = $style->buildUrl($image_file->getFileUri());
        // Parse the URL and remove the query string
        $url_parts = parse_url($image_url);
        $variables['image_url'] = $url_parts['scheme'] . '://' . $url_parts['host'] . $url_parts['path'];
      }
    }
  }
}

/**
 * Implements hook_form_system_theme_settings_alter() for settings form.
 *
 * Replace Barrio setting options with subtheme ones.
 */
function bootstrap4grow_form_system_theme_settings_alter(&$form, FormStateInterface $form_state) {
  $form['components']['navbar']['bootstrap_barrio_navbar_top_background']['#options'] = array(
    'bg-primary' => t('Primary'),
    'bg-secondary' => t('Secondary'),
    'bg-light' => t('Light'),
    'bg-dark' => t('Dark'),
    'bg-white' => t('White'),
    'bg-transparent' => t('Transparent'),
  );
  $form['components']['navbar']['bootstrap_barrio_navbar_background']['#options'] = array(
    'bg-primary' => t('Primary'),
    'bg-secondary' => t('Secondary'),
    'bg-light' => t('Light'),
    'bg-dark' => t('Dark'),
    'bg-white' => t('White'),
    'bg-transparent' => t('Transparent'),
  );
}
/**
 * Template_preprocess_image().
 */
function bootstrap4grow_preprocess_image(&$variables) {
  $current_url = Url::fromRoute('<current>');
  $disable_urls = ['/zespol-warszawa', '/sale-szkoleniowe-warszawa-wynajem-sal-szkoleniowych', '/biblioteka-biznesu'];

  $current_route_match = \Drupal::routeMatch();
  $node = $current_route_match->getParameter('node');
  if ($node instanceof NodeInterface && ($node->getType() === 'article' || $node->getType() === 'media' || $node->getType() === 'npxquiz' || $node->getType() === 'page')) {
    return;
  }

  $term = $current_route_match->getParameter('taxonomy_term');
  if ($term instanceof TermInterface && $term->bundle() === 'training_category') {
    return;
  }

  if (in_array($current_url->toString(), $disable_urls)) {
    return;
  }
  if(strpos($variables['attributes']['src'], 'video_thumbnails') > -1) {
    return;
  }
  $variables['attributes']['data-src'] = $variables['attributes']['src'];
  unset($variables['attributes']['src']);
  if(!isset($variables['attributes']['class'])) {
    $variables['attributes']['class'] = [];
  }
  $variables['attributes']['class'] = array_merge($variables['attributes']['class'], ['b-lazy']);
}

/**
 * Implements hook_page_attachments_alter().
 */
function bootstrap4grow_page_attachments_alter(array &$attachments) {
  // Conditionally remove an asset.
  $current_path = \Drupal::service('path.matcher')->isFrontPage();
  $library = $attachments['#attached']['library'];
  $node = \Drupal::routeMatch()->getParameter('node');
 
  if ($node and !\Drupal::currentUser()->isAuthenticated() and $node->bundle()=='npxtraining') {
    unset($library[array_search('ckeditor_spoiler/ckeditor_spoiler', $library)]);
    unset($library[array_search('ng_lightbox/ng_lightbox', $library)]);
    unset($library[array_search('npx_abtest/test_belt_on_bottom', $library)]);
    unset($library[array_search('bootstrap_barrio/global-styling', $library)]);
  }  
  $library = array_flip($library);
  
  if ($current_path){
    $library['bootstrap4grow/frontpage'] = 1;
  }
  else {
    if ($node) {
      switch($node->bundle()) {
        case 'npxtraining':
          $library['bootstrap4grow/npxtraining'] = 1;
          break;
        case 'page':
        case 'blog_cat_page':
        case 'opinia':
          $library['bootstrap4grow/page'] = 1;
          break;
        case 'landing_page':
          $library['bootstrap4grow/landing'] = 1;
          break;
        case 'article':
          $library['bootstrap4grow/article'] = 1;
          break;
        case 'coaching':
          $library['bootstrap4grow/coaching'] = 1;
          break;
        case 'npxquiz':
          $library['bootstrap4grow/npxquiz'] = 1;
          break;
        case 'book':
          $library['bootstrap4grow/book'] = 1;
          break;
        case 'webform':
          $library['bootstrap4grow/webform'] = 1;
          break;
      }
    }
    else {
      $view_id = \Drupal::routeMatch()->getParameter('view_id');
      if($view_id) {
        switch($view_id) {
          case 'biblioteka_biznesu':
            $library['bootstrap4grow/business_library'] = 1;
            break;
          case 'opinie':
            $library['bootstrap4grow/reviews'] = 1;
            break;
          case 'referencje':
            $library['bootstrap4grow/references'] = 1;
            break;
        }
      }
      else {
        $current_path = \Drupal::service('path.current')->getPath();
        if (str_contains($current_path, 'ankieta')) {
          $library['bootstrap4grow/npxsurvey'] = 1;
        }
      }
    }
  }
  $attachments['#attached']['library'] = array_keys($library);
}

function bootstrap4grow_preprocess_html(&$variables) {
  $criticalCss = file_get_contents('themes/custom/bootstrap4grow/css/criticalmain.css');
  $variables['page']['#attached']['html_head'][] = [
    [
      '#tag' => 'style',
      '#value' => Markup::create($criticalCss),
      '#weight' => -99,
    ],
    'critical-css',
  ];
  $variables['is_ajax'] = \Drupal::request()->isXmlHttpRequest();

  $current_path = \Drupal::service('path.matcher')->isFrontPage();
  if ($current_path) {    
    $criticalCss = file_get_contents('themes/custom/bootstrap4grow/css/critical-frontpage.css');
    $variables['page']['#attached']['html_head'][] = [
      [
        '#tag' => 'style',
        '#value' => Markup::create($criticalCss),
        '#weight' => -100,
      ],
      'critical-css-front',
    ];
  }
  else {
    $node = \Drupal::routeMatch()->getParameter('node');
    if ($node && $node->bundle()=='npxtraining') {
      $criticalCss = file_get_contents('themes/custom/bootstrap4grow/css/critical-npxtraining.css');
      $variables['page']['#attached']['html_head'][] = [
        [
          '#tag' => 'style',
          '#value' => Markup::create($criticalCss),
          '#weight' => -100,
        ],
        'critical-css-training',
      ];      
    }
  }
}

function bootstrap4grow_preprocess_field__default__node__field_npxtraining_metodologia__npxtraining(&$variables) {
  $node = $variables['element']['#object'];
  if (!is_null($node->get('field_seminarium')) and $node->get('field_seminarium')->value) {
      $variables['seminarium'] = 'seminarium';
  } else {
    $variables['seminarium'] = 'szkolenie';
  }
}
function bootstrap4grow_preprocess_field_group_html_element(&$variables) {
  $node = \Drupal::routeMatch()->getParameter('node');
  if ($variables['element']['#group_name'] == 'group_g1_topslider' || ($variables['element']['#group_name'] == 'group_div' && $node instanceof \Drupal\node\NodeInterface && $node->bundle() == 'landing_page')) {
    if(!is_null($variables['element']['group_inner']['field_top_tytul']['#items'])) {
      if(count($variables['element']['group_inner']['field_top_tytul']['#items']->referencedEntities()[0]->get('field_ts_obraz')->referencedEntities()) > 0) {
        $renderer = \Drupal::service('renderer');
        if ($variables['element']['group_inner']['field_top_tytul']['#items']->referencedEntities()[0]->getType() == 'tytul_szkolenia_ii') {
          $variables['top_par_type'] = 'top-title-fullwidth-image';
          $file_url = $variables['element']['group_inner']['field_top_tytul']['#items']->referencedEntities()[0]->get('field_ts_obraz')->referencedEntities()[0]->getFileUri();
          $img_alt = $variables['element']['group_inner']['field_top_tytul']['#items']->referencedEntities()[0]->get('field_ts_obraz')->getValue()[0]['alt'];
          $img_title = $variables['element']['group_inner']['field_top_tytul']['#items']->referencedEntities()[0]->get('field_ts_obraz')->getValue()[0]['title'];
          $style = ImageStyle::load('top_image_full_width');
          $url = $style->buildUrl($file_url);
          $variables['top_title_h1'] = $variables['element']['group_inner']['field_top_tytul']['#items']->referencedEntities()[0]->get('field_ts_tytul')->view();
          $variables['top_title'] = strip_tags($renderer->render($variables['top_title_h1']));
      //    $variables['image'] = $variables['element']['group_inner']['field_top_tytul']['#items']->referencedEntities()[0]->get('field_ts_obraz')->view();
          $variables['image_url'] = $url;
          $variables['image_alt'] = $img_alt;
          $variables['image_title'] = $img_title;
          $variables['top_description'] = $variables['element']['group_inner']['field_top_tytul']['#items']->referencedEntities()[0]->get('field_ts_opis')->view();
        } else if ($variables['element']['group_inner']['field_top_tytul']['#items']->referencedEntities()[0]->getType() == 'tytul_szkolenia') {
          $variables['top_par_type'] = 'top-title-right-image';
          $file_url = $variables['element']['group_inner']['field_top_tytul']['#items']->referencedEntities()[0]->get('field_ts_obraz')->referencedEntities()[0]->getFileUri();
          $img_alt = $variables['element']['group_inner']['field_top_tytul']['#items']->referencedEntities()[0]->get('field_ts_obraz')->getValue()[0]['alt'];
          $img_title = $variables['element']['group_inner']['field_top_tytul']['#items']->referencedEntities()[0]->get('field_ts_obraz')->getValue()[0]['title'];
          $style = ImageStyle::load('half_width_new');
          $url = $style->buildUrl($file_url);
          $variables['top_title_h1'] = $variables['element']['group_inner']['field_top_tytul']['#items']->referencedEntities()[0]->get('field_ts_tytul')->view();
          $variables['top_title'] = strip_tags($renderer->render($variables['top_title_h1']));
          $variables['image_url'] = $url;
          $variables['image_alt'] = $img_alt;
          $variables['image_title'] = $img_title;
          $variables['top_description'] = $variables['element']['group_inner']['field_top_tytul']['#items']->referencedEntities()[0]->get('field_ts_opis')->view();
        }
      }
    }
  }
  $renderer = \Drupal::service('renderer');

  if ($variables['element']['#group_name'] == 'group_g19' && $node instanceof NodeInterface && $node->bundle() == 'npxtraining') {
    if (!$node->get('field_video_embed')->isEmpty() && !$node->get('field_video_text')->isEmpty()) {
      // Render fields explicitly without labels.
      $video_embed = $node->get('field_video_embed')->view(['label' => 'hidden']);
      $video_text = $node->get('field_video_text')->view(['label' => 'hidden']);

      $variables['video_embed_rendered'] = Markup::create(trim($renderer->render($video_embed)));
      $variables['video_text_rendered'] = Markup::create(trim($renderer->render($video_text)));
    }
    else {
      $variables['video_embed_rendered'] = '';
      $variables['video_text_rendered'] = '';
    }
  }
  else {
    $variables['video_embed_rendered'] = '';
    $variables['video_text_rendered'] = '';
  }
}


/**
 * Implements hook_preprocess_field_group_html_element__paragraph__trener_do_szkolenia_par__group_wideo().
 *
 * This function processes the video fields in the 'trener_do_szkolenia_par' paragraph type.
 */
function bootstrap4grow_preprocess_field_group_html_element__paragraph__trener_do_szkolenia_par__group_wideo(array &$variables) {
  // 1) Find the Paragraph the field-group is wrapping.
  $paragraph = NULL;
  if (isset($variables['element']['field_wideo_trenera']['#object'])
      && $variables['element']['field_wideo_trenera']['#object'] instanceof ParagraphInterface) {
    $paragraph = $variables['element']['field_wideo_trenera']['#object'];
  }
  elseif (isset($variables['element']['field_trainer_video_text']['#object'])
      && $variables['element']['field_trainer_video_text']['#object'] instanceof ParagraphInterface) {
    $paragraph = $variables['element']['field_trainer_video_text']['#object'];
  }
  // 2) If we got it, render both sub-fields; otherwise leave them blank.

  if ($paragraph) {
    $view_builder = \Drupal::entityTypeManager()->getViewBuilder('paragraph');

    // 1) Video embed (uses your default embed formatter):
    if (!$paragraph->get('field_wideo_trenera')->isEmpty()) {
      $embed_render = $view_builder->viewField($paragraph->get('field_wideo_trenera'), 'default');
      $variables['video_embed_rendered'] = Markup::create(\Drupal::service('renderer')->render($embed_render));
    }
    else {
      $variables['video_embed_rendered'] = '';
    }

    // 2) Trainer text — use the field‐view API so we can hide the label here:
    if (!$paragraph->get('field_trainer_video_text')->isEmpty()) {
      // This honors your “hidden” label directive, regardless of UI display settings.
      $text_build = $paragraph
        ->get('field_trainer_video_text')
        ->view([
          'label' => 'hidden',
          'type'  => 'text_default',  // or whatever formatter you need
        ]);
      $variables['video_text_rendered'] = Markup::create(\Drupal::service('renderer')->render($text_build));
    }
    else {
      $variables['video_text_rendered'] = '';
    }
  }
  else {
    $variables['video_embed_rendered'] = '';
    $variables['video_text_rendered'] = '';
  }

}
