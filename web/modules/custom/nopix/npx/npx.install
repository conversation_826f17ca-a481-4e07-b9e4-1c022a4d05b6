<?php

use Drupal\field\Entity\FieldConfig;
use Drupal\field\Entity\FieldStorageConfig;
use Drupal\Core\Config\FileStorage;

/**
 * Implements hook_update_N() on Module npx Update # 8001.
 * Install field_npxtraining_date_form
 */
function npx_update_8001(&$sandbox) {
  $config_path = \Drupal::service('extension.path.resolver')->getPath('module', 'npx') . '/config/install';
  $storage = new FileStorage($config_path);
  
  $data = $storage->read('field.storage.node.field_npxtraining_date_form');
  if (!FieldStorageConfig::loadByName($data['entity_type'], $data['field_name'])) {
    FieldStorageConfig::create($data)->save();
  }
  
  $data = $storage->read('field.field.node.npxtraining_date.field_npxtraining_date_form');
  if (!FieldConfig::loadByName($data['entity_type'], $data['bundle'], $data['field_name'])) {
    FieldConfig::create($data)->save();
  }
}
