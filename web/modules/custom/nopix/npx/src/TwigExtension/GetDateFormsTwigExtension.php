<?php

namespace Drupal\npx\TwigExtension;

use \Twig\Extension\AbstractExtension;
use \Twig\TwigFunction;
use Dr<PERSON>al\node\NodeInterface;

/**
 * Class GetDateFormsTwigExtension.
 */
class GetDateFormsTwigExtension extends AbstractExtension {
  
  /**
   * {@inheritdoc}
   */
  public function getTokenParsers() {
    return [];
  }
  
  /**
   * {@inheritdoc}
   */
  public function getNodeVisitors() {
    return [];
  }
  
  /**
   * {@inheritdoc}
   */
  public function getFilters() {
    return [];
  }
  
  /**
   * {@inheritdoc}
   */
  public function getTests() {
    return [];
  }
  
  /**
   * {@inheritdoc}
   */
  public function getFunctions() {
    return [
      new TwigFunction('npx_is_date_stationary', [$this, 'npx_is_date_stationary']),
      new TwigFunction('npx_is_date_online', [$this, 'npx_is_date_online']),
    ];
  }
  
  /**
   * {@inheritdoc}
   */
  public function getOperators() {
    return [];
  }
  
  /**
   * {@inheritdoc}
   */
  public function getName() {
    return 'npx.twig.getdateforms';
  }
  
  public function npx_is_date_stationary(NodeInterface $date): bool {
    return $this->npx_date_has_label($date, 'stationary');
  }
    
  public function npx_is_date_online(NodeInterface $date): bool {
    return $this->npx_date_has_label($date, 'online');
  }
  
  protected function npx_date_has_label(NodeInterface $date, string $label): bool {
    foreach($date->get('field_npxtraining_date_form') as $val) {
      if($val->value == $label) {
        return true;
      }
    }
    return false;
  }
}
