<?php

namespace Drupal\npx_reports\Form;

use <PERSON>upal\Core\Form\FormBase;
use Drupal\Core\Form\FormStateInterface;
use <PERSON>upal\Core\Link;
use Drupal\npx_reports\NpxReportsHelper;
use Drupal\npx_reports\Report5Csv;

/**
 * Class Report5Form.
 *
 * @package Drupal\npx_reports\Form
 */
class Report6Form extends FormBase {

  /** Date format for date comparison */
  const DATE_FORMAT = 'Y-m-d';

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'report6_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $form['#attached'] = [
      'library' => [
        'npx_reports/npx_reports.form'
      ]
    ];

    $this->setFiltersFromUrl($form_state);

    $form['filters_wrapper'] = [
      '#type' => 'details',
      '#title' => $this->t('Filtry'),
      '#open' => true,
      '#attributes' => [
        'class' => ['form--inline'],
      ],
    ];
    $form['filters_wrapper']['date_wrapper'] = [
      '#type' => 'container',
      '#title' => $this->t('Rok - rrrr'),
      '#title_display' => 'before',
      '#attributes' => [
        'class' => ['form--inline'],
      ],
    ];

    $current_year = intval(date('Y'));
    $year_options = array_combine(range(2015, $current_year + 1), range(2015, $current_year + 1));

    if(!$form_state->hasValue('date_year')) {
      $form_state->setValue('date_year', $current_year);
    }

    $form['filters_wrapper']['date_wrapper']['first_month'] = [
      '#type' => 'select',
      '#title' => $this->t('Zacznij od'),
      '#title_display' => 'before',
      '#options' => [
        1 => 'I',
        2 => 'II',
        3 => 'III',
        4 => 'IV',
        5 => 'V',
        6 => 'VI',
        7 => 'VII',
        8 => 'VIII',
        9 => 'IX',
        10 =>'X',
        11 => 'XI',
        12 => 'XII',
      ],
      '#default_value' => $form_state->getValue('first_month', 1),
    ];

    $form['filters_wrapper']['date_wrapper']['date_year'] = [
      '#type' => 'select',
      '#title' => $this->t('Rok'),
      '#title_display' => 'before',
      '#options' => $year_options,
      '#default_value' => $form_state->getValue('date_year', $current_year - 1),
    ];

    $form['filters_wrapper']['training_wrapper'] = [
      '#type' => 'container',
      '#attributes' => [
        'class' => ['container-inline'],
      ],
    ];
    $form['filters_wrapper']['training_wrapper']['training_not'] = [
      '#type' => 'select',
      '#title' => $this->t('Wyklucz'),
      '#default_value' => '',
      '#options' => $this->getTrainingOptions(),
      '#default_value' => $form_state->getValue('training_not'),
      '#multiple' => true,
      '#attributes' => [
        'class' => ['chosen-select'],
        'data-placeholder' => 'Wyklucz tematy',
      ],
    ];


    $form['filters_wrapper']['send'] = [
      '#type' => 'submit',
      '#value' => $this->t('Generuj raport'),
    ];

    $dates = NpxReportsHelper::getDatesByYear($form_state->getValue('date_year'));

    $data = [];
    $sum_data = [
      'months' => [],
      'targets' => [],
      'ctargets' => [],
    ];

    $finance = [];
    $sum_finance = [
      'months' => [],
      'targets' => [],
      'ctargets' => [],
    ];
    $income = [];
    $sum_income = [
      'months' => [],
    ];
    $sum_income_prediction = [
      'months' => [],
    ];

    $trainers = [];
    $norm_total = 0;

    $config = $this->configFactory()->get('npx_reports.report6');

    $dates_filtered = [];

    foreach($dates as $date) {
      if($this->checkExcludeFilter($date->id(), $form_state->getValue('training_not'))) {
        continue;
      }

      if(!$date->isPublished()) {
        continue;
      }
      $days = NpxReportsHelper::calculateDaysMultiple($date->field_npxtraining_date_start, $date->field_npxtraining_date_end);
      $trainer_id = $date->field_npxtraining_date_trainer->entity->id();

      if(!isset($dates_filtered[$trainer_id][$date->field_npxtraining_date_start->value])) {
        $dates_filtered[$trainer_id][$date->field_npxtraining_date_start->value][$date->id()] = $days;
      } else {
        $tmp_days = reset($dates_filtered[$trainer_id][$date->field_npxtraining_date_start->value]);
        if($days > $tmp_days) {
          $dates_filtered[$trainer_id][$date->field_npxtraining_date_start->value] = [];
          $dates_filtered[$trainer_id][$date->field_npxtraining_date_start->value][$date->id()] = $days;
        }
      }
    }

    foreach($dates as $date) {
      if($this->checkExcludeFilter($date->id(), $form_state->getValue('training_not'))) {
        continue;
      }

      if(!$date->isPublished()) {
        continue;
      }

      $datetime = \DateTime::createFromFormat(self::DATE_FORMAT, $date->field_npxtraining_date_start->value);
      $month = $datetime->format('n');
      $trainer_id = $date->field_npxtraining_date_trainer->entity->id();

      if(!isset($dates_filtered[$trainer_id][$date->field_npxtraining_date_start->value][$date->id()])) {
        continue;
      }

      $days = NpxReportsHelper::calculateDaysMultiple($date->field_npxtraining_date_start, $date->field_npxtraining_date_end);
      $hours = $days * 8;

      if($date->hasField('field_npxtraining_hours') && $date->field_npxtraining_hours->value) {
        $hours = $date->field_npxtraining_hours->value;
      }

      if(!isset($trainers[$trainer_id])) {
        $trainers[$trainer_id] = $date->field_npxtraining_date_trainer->entity->title->value;
        $norm_total += $config->get('trainer_' . $trainer_id, 0);
      }

      if(isset($data[$trainer_id][$month])) {
        $data[$trainer_id][$month] += $hours;
      }
      else {
        $data[$trainer_id][$month] = $hours;
      }

      if(isset($sum_data['months'][$month])) {
        $sum_data['months'][$month] += $hours;
      } else {
        $sum_data['months'][$month] = $hours;
      }
    }

    $closed_dates = NpxReportsHelper::getClosedDatesByYear($form_state->getValue('date_year'));

    foreach($closed_dates as $date) {
      $datetime = \DateTime::createFromFormat(self::DATE_FORMAT, $date->field_npxtraining_date_start->value);
      $month = $datetime->format('n');
      $trainer_id = $date->field_npxtraining_date_trainer->entity->id();

      $days = NpxReportsHelper::calculateDaysMultiple($date->field_npxtraining_date_start, $date->field_npxtraining_date_end);
      $hours = $days * 8;

      if($date->hasField('field_npxtraining_hours') && $date->field_npxtraining_hours->value) {
        $hours = $date->field_npxtraining_hours->value;
      }

      if(!isset($trainers[$trainer_id])) {
        $trainers[$trainer_id] = $date->field_npxtraining_date_trainer->entity->title->value;
        $norm_total += $form_state->getValue('trainer_' . $trainer_id, 0);
      }

      if(isset($data[$trainer_id][$month])) {
        $data[$trainer_id][$month] += $hours;
      }
      else {
        $data[$trainer_id][$month] = $hours;
      }

      if(isset($sum_data['months'][$month])) {
        $sum_data['months'][$month] += $hours;
      } else {
        $sum_data['months'][$month] = $hours;
      }

      if(isset($finance[$trainer_id][$month])) {
        $finance[$trainer_id][$month] += $date->get('field_npxtraining_closed_price')->value;
        if(is_numeric($date->get('field_npxtraining_date_addcost')->value)) {
          $finance[$trainer_id][$month] -= $date->get('field_npxtraining_date_addcost')->value;
        }
        if(is_numeric($date->get('field_npxtraining_date_constcost')->value)) {
          $finance[$trainer_id][$month] -= $date->get('field_npxtraining_date_constcost')->value * (int)$date->get('field_npxtraining_closed_amount')->value;
        }
        if(is_numeric($date->get('field_npxtraining_date_trcost')->value)) {
          $finance[$trainer_id][$month] -= $date->get('field_npxtraining_date_trcost')->value;
        }
      } else {
        $finance[$trainer_id][$month] = $date->get('field_npxtraining_closed_price')->value;
        if(is_numeric($date->get('field_npxtraining_date_addcost')->value)) {
          $finance[$trainer_id][$month] -= $date->get('field_npxtraining_date_addcost')->value;
        }
        if(is_numeric($date->get('field_npxtraining_date_constcost')->value)) {
          $finance[$trainer_id][$month] -= $date->get('field_npxtraining_date_constcost')->value * (int)$date->get('field_npxtraining_closed_amount')->value;
        }
        if(is_numeric($date->get('field_npxtraining_date_trcost')->value)) {
          $finance[$trainer_id][$month] -= $date->get('field_npxtraining_date_trcost')->value;
        }
      }
      if(isset($sum_finance['months'][$month])) {
        $sum_finance['months'][$month] += $date->get('field_npxtraining_closed_price')->value;
        if(is_numeric($date->get('field_npxtraining_date_addcost')->value)) {
          $sum_finance['months'][$month] -= $date->get('field_npxtraining_date_addcost')->value;
        }
        if(is_numeric($date->get('field_npxtraining_date_constcost')->value)) {
          $sum_finance['months'][$month] -= $date->get('field_npxtraining_date_constcost')->value * (int)$date->get('field_npxtraining_closed_amount')->value;
        }
        if(is_numeric($date->get('field_npxtraining_date_trcost')->value)) {
          $sum_finance['months'][$month] -= $date->get('field_npxtraining_date_trcost')->value;
        }
      } else {
        $sum_finance['months'][$month] = $date->get('field_npxtraining_closed_price')->value;
        if(is_numeric($date->get('field_npxtraining_date_addcost')->value)) {
          $sum_finance['months'][$month] -= $date->get('field_npxtraining_date_addcost')->value;
        }
        if(is_numeric($date->get('field_npxtraining_date_constcost')->value)) {
          $sum_finance['months'][$month] -= $date->get('field_npxtraining_date_constcost')->value * (int)$date->get('field_npxtraining_closed_amount')->value;
        }
        if(is_numeric($date->get('field_npxtraining_date_trcost')->value)) {
          $sum_finance['months'][$month] -= $date->get('field_npxtraining_date_trcost')->value;
        }
      }
      if(isset($income[$trainer_id][$month])) {
        $income[$trainer_id][$month] += $date->get('field_npxtraining_closed_price')->value;
        if(is_numeric($date->get('field_npxtraining_date_addcost')->value)) {
          $income[$trainer_id][$month] -= $date->get('field_npxtraining_date_addcost')->value;
          $income[$trainer_id][$month] -= $date->get('field_npxtraining_date_constcost')->value * (int)$date->get('field_npxtraining_closed_amount')->value;
          $income[$trainer_id][$month] -= $date->get('field_npxtraining_date_trcost')->value;
        }
      } else {
        $income[$trainer_id][$month] = $date->get('field_npxtraining_closed_price')->value;
        if(is_numeric($date->get('field_npxtraining_date_addcost')->value)) {
          $income[$trainer_id][$month] -= $date->get('field_npxtraining_date_addcost')->value;
          $income[$trainer_id][$month] -= $date->get('field_npxtraining_date_constcost')->value * (int)$date->get('field_npxtraining_closed_amount')->value;
          $income[$trainer_id][$month] -= $date->get('field_npxtraining_date_trcost')->value;
        }
      }
      if(isset($sum_income['months'][$month])) {
        $sum_income['months'][$month] += $date->get('field_npxtraining_closed_price')->value;
        if(is_numeric($date->get('field_npxtraining_date_addcost')->value)) {
          $sum_income['months'][$month] -= $date->get('field_npxtraining_date_addcost')->value;
          $sum_income['months'][$month] -= $date->get('field_npxtraining_date_constcost')->value * (int)$date->get('field_npxtraining_closed_amount')->value;
          $sum_income['months'][$month] -= $date->get('field_npxtraining_date_trcost')->value;
        }
      } else {
        $sum_income['months'][$month] = $date->get('field_npxtraining_closed_price')->value;
        if(is_numeric($date->get('field_npxtraining_date_addcost')->value)) {
          $sum_income['months'][$month] -= $date->get('field_npxtraining_date_addcost')->value;
          $sum_income['months'][$month] -= $date->get('field_npxtraining_date_constcost')->value * (int)$date->get('field_npxtraining_closed_amount')->value;
          $sum_income['months'][$month] -= $date->get('field_npxtraining_date_trcost')->value;
        }
      }
      $sum_income_prediction['months'][$month] = $sum_income['months'][$month];
    }

    $submissions = NpxReportsHelper::getSubmissionsByYear($form_state->getValue('date_year'));

    foreach($submissions as $submission) {
      $training_date = NpxReportsHelper::getTrainingDate($submission);
      if($submission->field_npxsubmission_send_notify->value == false) {
        if($training_date) {
          $month = $datetime->format('n');
          if(isset($sum_income_prediction['months'][$month])) {
            $sum_income_prediction['months'][$month] += (int)$submission->field_npxsubmission_calc_td->value;
            if(is_numeric($training_date->field_npxtraining_date_addcost->value)) {
              $sum_income_prediction['months'][$month] -= (int)$training_date->field_npxtraining_date_addcost->value;
            }
            if(is_numeric($training_date->field_npxtraining_date_constcost->value)) {
              $sum_income_prediction['months'][$month] -= (int)$submission->field_npxsubmission_amount->value * (int)$training_date->field_npxtraining_date_constcost->value;
            }
            if(is_numeric($training_date->field_npxtraining_date_trcost->value)) {
              $sum_income_prediction['months'][$month] -= (int)$training_date->field_npxtraining_date_trcost->value;
            }
          } else {
            $sum_income_prediction['months'][$month] = (int)$submission->field_npxsubmission_calc_td->value;
            if(is_numeric($training_date->field_npxtraining_date_addcost->value)) {
              $sum_income_prediction['months'][$month] -= (int)$training_date->field_npxtraining_date_addcost->value;
            }
            if(is_numeric($training_date->field_npxtraining_date_constcost->value)) {
              $sum_income_prediction['months'][$month] -= (int)$submission->field_npxsubmission_amount->value * (int)$training_date->field_npxtraining_date_constcost->value;
            }
            if(is_numeric($training_date->field_npxtraining_date_trcost->value)) {
              $sum_income_prediction['months'][$month] -= (int)$training_date->field_npxtraining_date_trcost->value;
            }
          }

        }
        continue;
      }
      if($training_date) {
        $trainer_id = $training_date->field_npxtraining_date_trainer->entity->id();
        $datetime = \DateTime::createFromFormat(self::DATE_FORMAT, $training_date->field_npxtraining_date_start->value);
        $month = $datetime->format('n');
        if(isset($finance[$trainer_id][$month])) {
          $finance[$trainer_id][$month] += (int)$submission->field_npxsubmission_calc_td->value;
          if(is_numeric($training_date->field_npxtraining_date_constcost->value)) {
            $finance[$trainer_id][$month] -= (int)$submission->field_npxsubmission_amount->value * (int)$training_date->field_npxtraining_date_constcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_addcost->value)) {
            $finance[$trainer_id][$month] -= (int)$training_date->field_npxtraining_date_addcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_trcost->value)) {
            $finance[$trainer_id][$month] -= (int)$training_date->field_npxtraining_date_trcost->value;
          }
        } else {
          $finance[$trainer_id][$month] = (int)$submission->field_npxsubmission_calc_td->value;
          if(is_numeric($training_date->field_npxtraining_date_constcost->value)) {
            $finance[$trainer_id][$month] -= (int)$submission->field_npxsubmission_amount->value * (int)$training_date->field_npxtraining_date_constcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_addcost->value)) {
            $finance[$trainer_id][$month] -= (int)$training_date->field_npxtraining_date_addcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_trcost->value)) {
            $finance[$trainer_id][$month] -= (int)$training_date->field_npxtraining_date_trcost->value;
          }
        }
        if(isset($sum_finance['months'][$month])) {
          $sum_finance['months'][$month] += (int)$submission->field_npxsubmission_calc_td->value;
          if(is_numeric($training_date->field_npxtraining_date_constcost->value)) {
            $sum_finance['months'][$month] -= (int)$submission->field_npxsubmission_amount->value * (int)$training_date->field_npxtraining_date_constcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_addcost->value)) {
            $sum_finance['months'][$month] -= (int)$training_date->field_npxtraining_date_addcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_trcost->value)) {
            $sum_finance['months'][$month] -= (int)$training_date->field_npxtraining_date_trcost->value;
          }
        } else {
          $sum_finance['months'][$month] = (int)$submission->field_npxsubmission_calc_td->value;
          if(is_numeric($training_date->field_npxtraining_date_constcost->value)) {
            $sum_finance['months'][$month] -= (int)$submission->field_npxsubmission_amount->value * (int)$training_date->field_npxtraining_date_constcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_addcost->value)) {
            $sum_finance['months'][$month] -= (int)$training_date->field_npxtraining_date_addcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_trcost->value)) {
            $sum_finance['months'][$month] -= (int)$training_date->field_npxtraining_date_trcost->value;
          }
        }
        if(isset($income[$trainer_id][$month])) {
          $income[$trainer_id][$month] += (int)$submission->field_npxsubmission_calc_td->value;
          if(is_numeric($training_date->field_npxtraining_date_addcost->value)) {
            $income[$trainer_id][$month] -= (int)$training_date->field_npxtraining_date_addcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_constcost->value)) {
            $income[$trainer_id][$month] -= (int)$submission->field_npxsubmission_amount->value * (int)$training_date->field_npxtraining_date_constcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_trcost->value)) {
            $income[$trainer_id][$month] -= (int)$training_date->field_npxtraining_date_trcost->value;
          }
        } else {
          $income[$trainer_id][$month] = (int)$submission->field_npxsubmission_calc_td->value;
          if(is_numeric($training_date->field_npxtraining_date_addcost->value)) {
            $income[$trainer_id][$month] -= (int)$training_date->field_npxtraining_date_addcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_constcost->value)) {
            $income[$trainer_id][$month] -= (int)$submission->field_npxsubmission_amount->value * (int)$training_date->field_npxtraining_date_constcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_trcost->value)) {
            $income[$trainer_id][$month] -= (int)$training_date->field_npxtraining_date_trcost->value;
          }
        }
        if(isset($sum_income['months'][$month])) {
          $sum_income['months'][$month] += (int)$submission->field_npxsubmission_calc_td->value;
          if(is_numeric($training_date->field_npxtraining_date_addcost->value)) {
            $sum_income['months'][$month] -= (int)$training_date->field_npxtraining_date_addcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_constcost->value)) {
            $sum_income['months'][$month] -= (int)$submission->field_npxsubmission_amount->value * (int)$training_date->field_npxtraining_date_constcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_trcost->value)) {
            $sum_income['months'][$month] -= (int)$training_date->field_npxtraining_date_trcost->value;
          }
        } else {
          $sum_income['months'][$month] = (int)$submission->field_npxsubmission_calc_td->value;
          if(is_numeric($training_date->field_npxtraining_date_addcost->value)) {
            $sum_income['months'][$month] -= (int)$training_date->field_npxtraining_date_addcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_constcost->value)) {
            $sum_income['months'][$month] -= (int)$submission->field_npxsubmission_amount->value * (int)$training_date->field_npxtraining_date_constcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_trcost->value)) {
            $sum_income['months'][$month] -= (int)$training_date->field_npxtraining_date_trcost->value;
          }
        }
        if(isset($sum_income_prediction['months'][$month])) {
          $sum_income_prediction['months'][$month] += (int)$submission->field_npxsubmission_calc_td->value;
          if(is_numeric($training_date->field_npxtraining_date_addcost->value)) {
            $sum_income_prediction['months'][$month] -= (int)$training_date->field_npxtraining_date_addcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_constcost->value)) {
            $sum_income_prediction['months'][$month] -= (int)$submission->field_npxsubmission_amount->value * (int)$training_date->field_npxtraining_date_constcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_trcost->value)) {
            $sum_income_prediction['months'][$month] -= (int)$training_date->field_npxtraining_date_trcost->value;
          }
        } else {
          $sum_income_prediction['months'][$month] = (int)$submission->field_npxsubmission_calc_td->value;
          if(is_numeric($training_date->field_npxtraining_date_addcost->value)) {
            $sum_income_prediction['months'][$month] -= (int)$training_date->field_npxtraining_date_addcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_constcost->value)) {
            $sum_income_prediction['months'][$month] -= (int)$submission->field_npxsubmission_amount->value * (int)$training_date->field_npxtraining_date_constcost->value;
          }
          if(is_numeric($training_date->field_npxtraining_date_trcost->value)) {
            $sum_income_prediction['months'][$month] -= (int)$training_date->field_npxtraining_date_trcost->value;
          }
        }
      }
    }

    $months_header = [
      'I',
      'II',
      'III',
      'IV',
      'V',
      'VI',
      'VII',
      'VIII',
      'IX',
      'X',
      'XI',
      'XII',
    ];

    $form['months_wrapper'] = [
      '#type' => 'details',
      '#title' => 'Miesięczne cele finansowe',
      '#open' => true,
      '#attributes' => ['class' => ['sticky-form-element']],
    ];

    $form['months_wrapper']['table_months_settings'] = [
      '#type' => 'table',
      '#header' => $months_header,
    ];

    foreach($months_header as $i => $roman) {
      $form['months_wrapper']['table_months_settings'][0]['month_' . $i] = [
        '#type' => 'textfield',
        '#title' => $roman,
        '#title_display' => 'invisible',
        '#default_value' => $config->get('month_' . $i),
        '#size' => 6,
      ];
    }
    $csv = new Report5Csv();

    $header = [
      'Miesiąc',
      ['data' => 'I'],
      ['data' => 'II'],
      ['data' => 'III'],
      ['data' => 'IV'],
      ['data' => 'V'],
      ['data' => 'VI'],
      ['data' => 'VII'],
      ['data' => 'VIII'],
      ['data' => 'IX'],
      ['data' => 'X'],
      ['data' => 'XI'],
      ['data' => 'XII'],
      ['data' => 'Rok'],
    ];

    $first_month = (int)$form_state->getValue('first_month', 1);
    $header = $this->getMonthsHeader($first_month);

    foreach($data as $tid => $months) {
      $form['trainer_' . $tid] = [
        '#type' => 'textfield',
        '#title' => $trainers[$tid],
        '#default_value' => $config->get('trainer_' . $tid, ''),
        '#size' => 6,
      ];
      $form['table_' . $tid] = [
        '#type' => 'table',
        '#header' => $header,
        '#caption' => $trainers[$tid],
        '#suffix' => '<br/><hr/><br/>',
      ];
      $csv->addNameRow($trainers[$tid]);
      $days_total = 0;
      $form['table_' . $tid][0]['c0'] = ['#markup' => 'Liczba godzin'];
      $csv_row = ['Liczba godzin'];
      for($i = $first_month; $i < 12 + $first_month; $i++) {
        $am = 0;
        $i_index = $i;
        if($i > 12) {
          $i_index = $i - 12;
        }
        if(isset($months[$i_index])) {
          $am = $months[$i_index];
        }
        $form['table_' . $tid][0]['c'.$i_index] = ['#markup' => $am];
        $csv_row[] = $am;
        $days_total += $am;
      }
      $form['table_' . $tid][0]['c13'] = ['#markup' => $days_total];
      $csv_row[] = $days_total;
      $csv->addRow($csv_row);

      $target_total = 0;
      $form['table_' . $tid][1]['c0'] = ['#markup' => '% realizacji celu'];
      $csv_row = ['% realizacji celu'];
      for($i = $first_month; $i < 12 + $first_month; $i++) {
        $am = 0;
        $norm = $config->get('trainer_' . $tid, '');
        $i_index = $i;
        if($i > 12) {
          $i_index = $i - 12;
        }
        if(isset($months[$i_index])) {
          $am = $months[$i_index];
        }
        $target = NpxReportsHelper::calculate_percentage($am, $norm);
        $form['table_' . $tid][1]['c'.$i_index] = ['#markup' => $this->formatTargetRow($target)];
        $csv_row[] = $target;
        $target_total += $target;
      }
      $form['table_' . $tid][1]['c13'] = ['#markup' => $this->formatTargetRow(round($target_total / 12, 2))];
      $csv_row[] = round($target_total / 12, 2);
      $csv->addRow($csv_row);

      $form['table_' . $tid][2]['c0'] = ['#markup' => '% skumulowany realizacji celu'];
      $csv_row = ['% skumulowany realizacji celu'];
      $ctarget_total = 0;
      $month_step = 1;
      for($i = $first_month; $i < 12 + $first_month; $i++) {
        $norm = $config->get('trainer_' . $tid, '');
        $am_total = 0;
        $i_index = $i;
        if($i > 12) {
          $i_index = $i - 12;
        }

        for($j = $first_month; $j <= $i; $j++) {
          $am = 0;
          $j_index = $j;
          if($j > 12) {
            $j_index = $j - 12;
          }
          if(isset($months[$j_index])) {
            $am = $months[$j_index];
          }
          $am_total += $am;
        }
        $target = NpxReportsHelper::calculate_percentage($am_total, $norm * $month_step);
        $form['table_' . $tid][2]['c'.$i_index] = ['#markup' => $this->formatTargetRow($target)];
        $csv_row[] = $target;
        if($i == 12 + $first_month - 1) {
          $ctarget_total = $target;
        }
        $month_step++;
      }
      $form['table_' . $tid][2]['c13'] = ['#markup' => $this->formatTargetRow($ctarget_total)];
      $csv_row[] = $ctarget_total;
      $csv->addRow($csv_row);
      $csv->addEmptyRow();

      //FINANCE
      $target_total = 0;
      $form['table_' . $tid][3]['c0'] = ['#markup' => '% realizacji celu finansowego'];
      $csv_row = ['% realizacji celu finansowego'];
      for($i = $first_month; $i < 12 + $first_month; $i++) {
        $am = 0;
        $trn = $config->get('trainer_' . $tid, 0) / $norm_total;
        $i_index = $i;
        if($i > 12) {
          $i_index = $i - 12;
        }
        $norm = $config->get('month_' . ($i_index - 1), 0) * $trn;
        if(isset($finance[$tid][$i_index])) {
          $am = $finance[$tid][$i_index];
        }
        $target = NpxReportsHelper::calculate_percentage($am, $norm);
        $form['table_' . $tid][3]['c'.$i_index] = ['#markup' => $this->formatTargetRow($target)];
        $csv_row[] = $target;
        $target_total += $target;
      }
      $form['table_' . $tid][3]['c13'] = ['#markup' => $this->formatTargetRow(round($target_total / 12, 2))];
      $csv_row[] = round($target_total / 12, 2);
      $csv->addRow($csv_row);

      $form['table_' . $tid][4]['c0'] = ['#markup' => '% skumulowany realizacji celu finansowego'];
      $csv_row = ['% skumulowany realizacji celu finansowego'];
      $ctarget_total = 0;
      for($i = $first_month; $i < 12 + $first_month; $i++) {
        $norm_cumulated = 0;
        $am_total = 0;
        $trn = $config->get('trainer_' . $tid, 0) / $norm_total;
        $i_index = $i;
        if($i > 12) {
          $i_index = $i - 12;
        }

        for($j = $first_month; $j <= $i; $j++) {
          $am = 0;
          $j_index = $j;
          if($j > 12) {
            $j_index = $j - 12;
          }

          if(isset($finance[$tid][$j_index])) {
            $am = $finance[$tid][$j_index];
          }
          $am_total += $am;
          $norm_cumulated += $config->get('month_' . ($j_index - 1), 0) * $trn;
        }
        $target = NpxReportsHelper::calculate_percentage($am_total, $norm_cumulated);
        $form['table_' . $tid][4]['c'.$i_index] = ['#markup' => $this->formatTargetRow($target)];
        $csv_row[] = $target;
        if($i == 12 + $first_month - 1) {
          $ctarget_total = $target;
        }
      }
      $form['table_' . $tid][4]['c13'] = ['#markup' => $this->formatTargetRow($ctarget_total)];
      $csv_row[] = $ctarget_total;
      $csv->addRow($csv_row);
      $csv->addEmptyRow();

      // INCOME
      $form['table_' . $tid][5]['c0'] = ['#markup' => 'Przychód'];
      $csv_row = ['Przychód'];
      $income_total = 0;
      for($i = $first_month; $i < 12 + $first_month; $i++) {
        $am = 0;
        $i_index = $i;
        if($i > 12) {
          $i_index = $i - 12;
        }

        if(isset($income[$tid][$i_index])) {
          $am = $income[$tid][$i_index];
        }
        $form['table_' . $tid][5]['c'.$i_index] = ['#markup' => number_format($am, 0, ',', ' ')];
        $csv_row[] = $am;
        $income_total += $am;
      }
      $form['table_' . $tid][5]['c13'] = ['#markup' => number_format($income_total, 0, ',', ' ')];
      $csv_row[] = $income_total;
      $csv->addRow($csv_row);
      $csv->addEmptyRow();

      // INCOME CUMULATED
//       $form['table_' . $tid][6]['c0'] = ['#markup' => 'Przychód średniomiesięczny skumulowany'];
//       $csv_row = ['Przychód średniomiesięczny skumulowany'];
//       $income_total = 0;
//       $month_step = 1;
//       for($i = $first_month; $i < 12 + $first_month; $i++) {
//         $am_total = 0;
//         $i_index = $i;
//         if($i > 12) {
//           $i_index = $i - 12;
//         }

//         for($j = $first_month; $j <= $i; $j++) {
//           $am = 0;
//           $j_index = $j;
//           if($j > 12) {
//             $j_index = $j - 12;
//           }

//           if(isset($income[$tid][$j_index])) {
//             $am = $income[$tid][$j_index];
//           }
//           $am_total += $am;
//         }

//         $form['table_' . $tid][6]['c'.$i_index] = ['#markup' => number_format($am_total/$month_step, 0, ',', ' ')];
//         $csv_row[] = $am_total/$month_step;
//         $income_total += $income[$tid][$i_index];
//         $month_step++;
//       }
//       $form['table_' . $tid][6]['c13'] = ['#markup' => number_format($income_total/12, 0, ',', ' ')];
//       $csv_row[] = $income_total/12;
//       $csv->addRow($csv_row);
//       $csv->addEmptyRow();
    }

    $form['table_summary'] = [
      '#type' => 'table',
      '#header' => $header,
      '#caption' => 'Wszyscy trenerzy',
      '#suffix' => '<br/><hr/><br/>',
    ];
    $csv->addEmptyRow();
    $csv->addNameRow('Wszyscy trenerzy');
    $days_total = 0;
    $csv_row = ['Liczba godzin'];
    $form['table_summary'][0]['c0'] = ['#markup' => 'Liczba godzin'];
    for($i = $first_month; $i < 12 + $first_month; $i++) {
      $am = 0;
      $i_index = $i;
      if($i > 12) {
        $i_index = $i - 12;
      }
      if(isset($sum_data['months'][$i_index])) {
        $am = $sum_data['months'][$i_index];
      }
      $form['table_summary'][0]['c'.$i_index] = ['#markup' => $am];
      $csv_row[] = $am;
      $days_total += $am;
    }
    $form['table_summary'][0]['c13'] = ['#markup' => $days_total];
    $csv_row[] = $days_total;
    $csv->addRow($csv_row);

    $target_total = 0;
    $form['table_summary'][1]['c0'] = ['#markup' => '% realizacji celu'];
    $csv_row = ['% realizacji celu'];
    for($i = $first_month; $i < 12 + $first_month; $i++) {
      $am = 0;
      $norm = $norm_total;
      $i_index = $i;
      if($i > 12) {
        $i_index = $i - 12;
      }
      if(isset($sum_data['months'][$i_index])) {
        $am = $sum_data['months'][$i_index];
      }
      $target = NpxReportsHelper::calculate_percentage($am, $norm);
      $form['table_summary'][1]['c'.$i_index] = ['#markup' => $this->formatTargetRow($target)];
      $csv_row[] = $target;
      $target_total += $target;
    }
    $form['table_summary'][1]['c13'] = ['#markup' => $this->formatTargetRow(round($target_total / 12, 2))];
    $csv_row[] = round($target_total / 12, 2);
    $csv->addRow($csv_row);

    $form['table_summary'][2]['c0'] = ['#markup' => '% skumulowany realizacji celu'];
    $csv_row = ['% skumulowany realizacji celu'];
    $ctarget_total = 0;
    $month_step = 1;
    for($i = $first_month; $i < 12 + $first_month; $i++) {
      $norm = $norm_total;
      $am_total = 0;
      $i_index = $i;
      if($i > 12) {
        $i_index = $i - 12;
      }
      for($j = $first_month; $j <= $i; $j++) {
        $am = 0;
        $j_index = $j;
        if($j > 12) {
          $j_index = $j - 12;
        }
        if(isset($sum_data['months'][$j_index])) {
          $am = $sum_data['months'][$j_index];
        }
        $am_total += $am;
      }
      $target = NpxReportsHelper::calculate_percentage($am_total, $norm * $month_step);
      $form['table_summary'][2]['c'.$i_index] = ['#markup' => $this->formatTargetRow($target)];
      $csv_row[] = $target;
      if($i == 12 + $first_month - 1) {
        $ctarget_total = $target;
      }
      $month_step++;
    }
    $form['table_summary'][2]['c13'] = ['#markup' => $this->formatTargetRow($ctarget_total)];
    $csv_row[] = $ctarget_total;
    $csv->addRow($csv_row);

    //FINANCE
    $target_total = 0;
    $form['table_summary'][3]['c0'] = ['#markup' => '% realizacji celu finansowego'];
    $csv_row = ['% realizacji celu finansowego'];
    for($i = $first_month; $i < 12 + $first_month; $i++) {
      $am = 0;
      $i_index = $i;
      if($i > 12) {
        $i_index = $i - 12;
      }
      $norm = $config->get('month_' . ($i_index -1), 0);
      if(isset($sum_finance['months'][$i_index])) {
        $am = $sum_finance['months'][$i_index];
      }
      $target = NpxReportsHelper::calculate_percentage($am, $norm);
      $form['table_summary'][3]['c'.$i_index] = ['#markup' => $this->formatTargetRow($target)];
      $csv_row[] = $target;
      $target_total += $target;
    }
    $form['table_summary'][3]['c13'] = ['#markup' => $this->formatTargetRow(round($target_total / 12, 2))];
    $csv_row[] = round($target_total / 12, 2);
    $csv->addRow($csv_row);

    $form['table_summary'][4]['c0'] = ['#markup' => '% skumulowany realizacji celu finansowego'];
    $csv_row = ['% skumulowany realizacji celu finansowego'];
    $ctarget_total = 0;
    for($i = $first_month; $i < 12 + $first_month; $i++) {
      $norm_cumulated = 0;
      $am_total = 0;
      $i_index = $i;
      if($i > 12) {
        $i_index = $i - 12;
      }

      for($j = 1; $j <= $i; $j++) {
        $am = 0;
        $j_index = $j;
        if($j > 12) {
          $j_index = $j - 12;
        }

        if(isset($sum_finance['months'][$j_index])) {
          $am = $sum_finance['months'][$j_index];
        }
        $am_total += $am;
        $norm_cumulated += $config->get('month_' . ($j_index - 1), 0);
      }
      $target = NpxReportsHelper::calculate_percentage($am_total, $norm_cumulated);
      $form['table_summary'][4]['c'.$i_index] = ['#markup' => $this->formatTargetRow($target)];
      $csv_row[] = $target;
      if($i == 12 + $first_month - 1) {
        $ctarget_total = $target;
      }
    }
    $form['table_summary'][4]['c13'] = ['#markup' => $this->formatTargetRow($ctarget_total)];
    $csv_row[] = $ctarget_total;
    $csv->addRow($csv_row);

    // PREDICTION
    $income_total_prediction = 0;
    $csv_row = ['Prognoza'];
    $form['table_summary'][5]['c0'] = ['#markup' => 'Prognoza'];
    for($i = $first_month; $i < 12 + $first_month; $i++) {
      $am = 0;
      $i_index = $i;
      if($i > 12) {
        $i_index = $i - 12;
      }
      if(isset($sum_income_prediction['months'][$i_index])) {
        $am = $sum_income_prediction['months'][$i_index];
      }
      $form['table_summary'][5]['c'.$i_index] = ['#markup' => number_format($am, 0, ',', ' ')];
      $csv_row[] = $am;
      $income_total_prediction += $am;
    }
    $form['table_summary'][5]['c13'] = ['#markup' => number_format($income_total_prediction, 0, ',', ' ')];
    $csv_row[] = $income_total_prediction;
    $csv->addRow($csv_row);

    // INCOME
    $income_total = 0;
    $csv_row = ['Przychód'];
    $form['table_summary'][6]['c0'] = ['#markup' => 'Przychód'];
    for($i = $first_month; $i < 12 + $first_month; $i++) {
      $am = 0;
      $i_index = $i;
      if($i > 12) {
        $i_index = $i - 12;
      }
      if(isset($sum_income['months'][$i_index])) {
        $am = $sum_income['months'][$i_index];
      }
      $form['table_summary'][6]['c'.$i_index] = ['#markup' => number_format($am, 0, ',', ' ')];
      $csv_row[] = $am;
      $income_total += $am;
    }
//     $form['table_summary'][5]['c13'] = ['#markup' => $income_total];
    $form['table_summary'][6]['c13'] = ['#markup' => number_format($income_total, 0, ',', ' ')];
    $csv_row[] = $income_total;
    $csv->addRow($csv_row);

    // INCOME CUMULATED
//     $form['table_summary'][6]['c0'] = ['#markup' => 'Przychód średniomiesięczny skumulowany'];
//     $csv_row = ['Przychód średniomiesięczny skumulowany'];
//     $income_total = 0;
//     $month_step = 1;
//     for($i = $first_month; $i < 12 + $first_month; $i++) {
//       $am_total = 0;
//       $i_index = $i;
//       if($i > 12) {
//         $i_index = $i - 12;
//       }

//       for($j = $first_month; $j <= $i; $j++) {
//         $am = 0;
//         $j_index = $j;
//         if($j > 12) {
//           $j_index = $j - 12;
//         }

//         if(isset($sum_income['months'][$j_index])) {
//           $am = $sum_income['months'][$j_index];
//         }
//         $am_total += $am;
//       }

//       $form['table_summary'][6]['c'.$i_index] = ['#markup' => number_format($am_total/$month_step, 0, ',', ' ')];
//       $csv_row[] = $am_total/$month_step;
//       $income_total += $sum_income['months'][$i_index];
//       $month_step++;
//     }
//     $form['table_summary'][6]['c13'] = ['#markup' => number_format($income_total/12, 0, ',', ' ')];
//     $csv_row[] = $income_total/12;
//     $csv->addRow($csv_row);
//     $csv->addEmptyRow();

    $form['export_wrapper'] = [
      '#type' => 'details',
      '#title' => 'Export',
      '#open' => true,
    ];
    $form['export_wrapper']['export_link'] = [
      '#markup' => '<a href="" id="export-link" download="raport5.csv">Pobierz plik</a>',
    ];
    $form['export_wrapper']['export_data'] = [
      '#type' => 'textarea',
      '#title' => 'Dane do wklejenia do excela',
      '#description' => 'Ctrl + C',
      '#value' => $csv->getCsvString(),
      '#id' => 'csv-export-data',
      '#attributes' => ['readonly' => 'readonly'],
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
//     $form_state->setRebuild();

    $config = $this->configFactory()->getEditable('npx_reports.report6');

    foreach ($form_state->getValues() as $key => $val) {
//       $this->messenger()->addMessage($key . '=>' . $val);
      if(substr($key, 0, 8) == 'trainer_' && is_numeric($val)) {
        $config->set($key, $val);
      } elseif($key == 'table_months_settings' && !empty($val)) {
//         kint($val);
        foreach($val[0] as $k => $v) {
          if(is_numeric($v)) {
            $config->set($k, $v);
          }
        }
      }
    }

    $config->save();

    $filter_query = [
      'date_year' => $form_state->getValue('date_year'),
      'training_not' => $form_state->getValue('training_not'),
      'first_month' => $form_state->getValue('first_month'),
    ];

    $form_state->setRedirect('npx_reports.report6_form', $filter_query);
  }

  protected function getTrainingOptions() {
    $query = \Drupal::entityQuery('node')
    ->condition('type','npxtraining')
    ->sort('title')
    ->accessCheck(FALSE);

    $ids = $query->execute();

    $manager = \Drupal::getContainer()->get('entity_type.manager')->getStorage('node');
    $entities = $manager->loadMultiple($ids);

    $options = [ 'all' => '- wszystkie -' ];

    foreach ($entities as $e) {
      $options[$e->id()] = $e->title->value;
    }

    return $options;
  }

  protected function checkExcludeFilter($date_id, $training_id) {
    if(!is_array($training_id) || empty($training_id)) {
      return false;
    }
    /** @var $query \Drupal\Core\Entity\Query\QueryInterface */
    $query = \Drupal::service('entity_type.manager')->getStorage('node')->getQuery()->accessCheck(FALSE);
    $query->condition('type', 'npxtraining')
    ->condition('nid', $training_id, 'IN')
    ->condition('field_npxtraining_dates', $date_id)
    ->count();
    $count = $query->execute();

    return $count > 0;
  }

  protected function formatTargetRow($val) {
    if((int)$val >= 100) {
      return sprintf("<span class='target-achieved'>%d</span>",$val);
    } else {
      return sprintf("<span class='target-not-achieved'>%d</span>",$val);
    }
  }

  protected function setFiltersFromUrl(FormStateInterface $form_state) {
    $date_year = $this->getRequest()->get('date_year');
    $training_not = $this->getRequest()->get('training_not');
    $first_month = $this->getRequest()->get('first_month');
    if($date_year) {
      $form_state->setValue('date_year', $date_year);
    }
    if($training_not) {
      $form_state->setValue('training_not', $training_not);
    }
    if($first_month) {
      $form_state->setValue('first_month', $first_month);
    }
  }

  protected function getMonthsHeader($start = 1) {
    $base = [
      1 => 'I',
      2 => 'II',
      3 => 'III',
      4 => 'IV',
      5 => 'V',
      6 => 'VI',
      7 => 'VII',
      8 => 'VIII',
      9 => 'IX',
      10 =>'X',
      11 => 'XI',
      12 => 'XII',
    ];

    $months = ['Miesiąc'];

    for($i = $start; $i < 12 + $start; $i++) {
      if($i <= 12) {
        $months[] = ['data' => $base[$i]];
      } else {
        $months[] = ['data' => $base[$i - 12]];
      }
    }

    $months[] = 'Rok';

    return $months;
  }
}
