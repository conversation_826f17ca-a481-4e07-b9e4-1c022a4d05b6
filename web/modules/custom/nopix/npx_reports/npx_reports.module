<?php

/**
 * @file
 * Contains npx_reports.module..
 * <AUTHOR>
 */

/**
 * Perform alterations before an entity form is included in the IEF widget.
 *
 * @param $entity_form
 *   Nested array of form elements that comprise the entity form.
 * @param $form_state
 *   The form state of the parent form.
 */
function npx_reports_inline_entity_form_entity_form_alter(&$entity_form, &$form_state) {
  if ($entity_form['#entity_type'] == 'node' && $entity_form['#bundle'] == 'npxtraining_date') {
    if(!\Drupal::currentUser()->hasPermission('npx reports access')){
      $entity_form['field_npxtraining_date_constcost']['#access'] = false;
      $entity_form['field_npxtraining_date_trcost']['#access'] = false;
      $entity_form['field_npxtraining_date_addcost']['#access'] = false;
    }
    $entity_form['field_npxtraining_date_constcost']['#prefix'] = '<strong class="button--danger">W przypadku szkoleń poza naszą salą zmień/uzupełnij poniżej warto<PERSON>ci:</strong>';
  }
}

/**
 * Implements HOOK_form_FORM_ID_alter().
 */
function npx_reports_form_node_npxtraining_closed_form_alter(&$form, \Drupal\Core\Form\FormStateInterface $form_state, $form_id) {
  if(!\Drupal::currentUser()->hasPermission('npx reports access')){
    $form['field_npxtraining_date_constcost']['#access'] = false;
    $form['field_npxtraining_date_trcost']['#access'] = false;
    $form['field_npxtraining_date_addcost']['#access'] = false;
    $form['field_npxtraining_closed_cstmr']['#access'] = false;
    $form['field_npxtraining_closed_amount']['#access'] = false;
    $form['field_npxtraining_closed_price']['#access'] = false;
  }
  $form['field_npxtraining_closed_cstmr']['#prefix'] = '<strong class="button--danger">W przypadku szkoleń poza naszą salą lub freelancera zmień/uzupełnij poniżej wartości:</strong>';
}
/**
 * Implements HOOK_form_FORM_ID_alter().
 */
function npx_reports_form_node_npxtraining_closed_edit_form_alter(&$form, \Drupal\Core\Form\FormStateInterface $form_state, $form_id) {
  if(!\Drupal::currentUser()->hasPermission('npx reports access')){
    $form['field_npxtraining_date_constcost']['#access'] = false;
    $form['field_npxtraining_date_trcost']['#access'] = false;
    $form['field_npxtraining_date_addcost']['#access'] = false;
    $form['field_npxtraining_closed_cstmr']['#access'] = false;
    $form['field_npxtraining_closed_amount']['#access'] = false;
    $form['field_npxtraining_closed_price']['#access'] = false;
  }
  $form['field_npxtraining_closed_cstmr']['#prefix'] = '<strong class="button--danger">W przypadku szkoleń poza naszą salą lub freelancera zmień/uzupełnij poniżej wartości:</strong>';
}
