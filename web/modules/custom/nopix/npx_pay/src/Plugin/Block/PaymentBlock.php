<?php

namespace Drupal\npx_pay\Plugin\Block;

use <PERSON><PERSON>al\Core\Block\BlockBase;
use <PERSON><PERSON>al\Core\Plugin\ContainerFactoryPluginInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Config\ConfigFactory;
use Symfony\Component\HttpFoundation\Request;
use Paynow\Client;
use Paynow\Environment;
use Paynow\Exception\PaynowException;
use Paynow\Service\Payment;

/**
 * Provides a 'PaymentBlock' block.
 *
 * @Block(
 *  id = "npx_pay_payment_block",
 *  admin_label = @Translation("Npx Payment block"),
 * )
 */
class PaymentBlock extends BlockBase implements ContainerFactoryPluginInterface {

  /**
   * Drupal\Core\Entity\EntityTypeManagerInterface definition.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;
  
  /**
   * Symfony\Component\HttpFoundation\Request definition.
   *
   * @var \Symfony\Component\HttpFoundation\Request
   */
  protected $currentRequest;
  
  /**
   * Configuration Factory.
   *
   * @var \Drupal\Core\Config\ConfigFactory
   */
  protected $configFactory;
  
  /**
   * Constructs a new PaymentBlock object.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param string $plugin_definition
   *   The plugin implementation definition.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    EntityTypeManagerInterface $entity_type_manager,
    Request $current_request,
    ConfigFactory $config_factory
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->entityTypeManager = $entity_type_manager;
    $this->currentRequest = $current_request;
    $this->configFactory = $config_factory;
  }
  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('entity_type.manager'),
      $container->get('request_stack')->getCurrentRequest(),
      $container->get('config.factory')
    );
  }
  /**
   * {@inheritdoc}
   */
  public function build() {
    //FIXME cache max-age bug, test for performance
    \Drupal::service('page_cache_kill_switch')->trigger();
    
    $build = [];

    $sid = $this->currentRequest->get('npxsid');
    
    $submission = $this->entityTypeManager->getStorage('npxsubmission')->load($sid);

    if($submission) {
      $node = $this->entityTypeManager->getStorage('node')->load($submission->get('field_npxsubmission_nid')->entity->id());
      if($node->field_npxtraining_pair_only->value == true) {
        return $build;
      }
      
      $summary_title = $submission->get('field_npxsubmission_nid')->entity->getTitle();
      $summary_price = (int)$submission->get('field_npxsubmission_calc_td')->value;
      $vat = 0;
      if (!$submission->get('field_npxsubmission_private')->value) {
        $vat = $summary_price * 0.23;
      }
      $summary_price += $vat;
      $summary_price = round($summary_price, 2);
      
      $amount = $summary_price * 100;
      $order_id = $submission->id();
      $description = $submission->get('field_npxsubmission_nid')->entity->getTitle();
      $first_name = $submission->get('field_npxsubmission_firstname')->value;
      $last_name = $submission->get('field_npxsubmission_name')->value;
      $email = $submission->get('field_npxsubmission_email')->value;
      $payment_url = '';
      if (!$submission->get('field_npxsubmission_private')->value) {
        $payment_url = $this->getPaymentUrl($amount, $order_id, $description, $first_name, $last_name, $email);
      }    
      $build['payment_form'] = [
        '#theme' => 'npx_pay',
        '#summary_title' => $summary_title,
	'#summary_price' => $summary_price,
        '#is_client_private' => $submission->get('field_npxsubmission_private')->value,
        '#payment_url' => $payment_url,
        '#cache' => [ 'max-age' => 0 ],
      ];
    }
    
    return $build;
  }
  
  /**
   * {@inheritdoc}
   */
  public function getCacheMaxAge() {
    return 0;
  }
  
  protected function getPaymentUrl($amount, $order_id, $description, $first_name, $last_name, $email): ?string {
    $config = $this->configFactory->get('npx_pay.payconfig');
    $api_key = $config->get('api_key');
    $signature_key = $config->get('signature_key');
    
    $client = new Client($api_key, $signature_key, Environment::PRODUCTION);
    
    $orderReference = $order_id;
    $idempotencyKey = uniqid($orderReference . '_');
    
    $paymentData = [
      'amount' => (int)$amount,
      'currency' => 'PLN',
      'externalId' => $orderReference,
      'description' => $description,
      'buyer' => [
        'email' => $email,
        'firstName' => $first_name,
        'lastName' => $last_name,
      ]
    ];
    
    try {
      $payment = new Payment($client);
      
      /** @var \Paynow\Response\Payment\Authorize $result */
      $result = $payment->authorize($paymentData, $idempotencyKey);
      return $result->getRedirectUrl();
    } catch (PaynowException $exception) {
      // catch errors
      return null;
    }
  }
}
