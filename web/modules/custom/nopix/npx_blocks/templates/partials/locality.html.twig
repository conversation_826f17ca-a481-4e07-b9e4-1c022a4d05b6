{% if row.entity.field_npxtraining_date_room.value is not empty %}
  {% set roomInfo = row.entity.field_npxtraining_date_room.entity.field_room_page_desc.value %}
{% else %}
  {% set roomInfo = row.entity.field_npxtraining_date_room.entity.field_room_page_desc.value %}
{% endif %}

{% if npx_is_date_online(term.entity) %}
  <span property="location" typeof="VirtualLocation">
    <a target="_blank" property="url" href="/szkolenia-online">Online</a>
  </span>
{% endif %}
{%  if npx_is_date_stationary(term.entity) and npx_is_date_online(term.entity) %}
  <span>lub</span>
{% endif %}
{%  if npx_is_date_stationary(term.entity) %}
  <span property="location" typeof="Place">
    <span property="address" typeof="PostalAddress">
      <meta property="addressLocality" content="Warszawa" />
      <meta property="streetAddress" content="{{ roomInfo|striptags }}" />
      <a property="url" class="npx-term-table-cbox-map-link" data-colorbox-inline="#npx-form-colorbox-inline-map-table-{{ term.entity.id }}" href="#">Warszawa</a>
    </span>
    <meta property="name" content="4GROW" />
  </span>
{% endif %}
