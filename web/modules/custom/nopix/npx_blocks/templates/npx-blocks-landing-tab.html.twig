{#*
/**
 * Available variables:
 * - hours
 * - title
 * - content
 * - image
 * - image_alt
 * - image_title
 * - webinar
 * - online
 * - stationary_only
 * - closed
 * - labels
 * - price
 * - dates
 * - city
 * - room
 * - date_start
 * - date_end
 * - has_links
 * - rating
 * - rating_count

**/
#}

<div class="npx-blocks-program-tab-wrapper d-flex flex-column flex-md-row" {% if rating and rating_count and not closed and not has_links %} vocab="https://schema.org/" typeof="Product"{% endif %}>
  <div class="n-tab-header npx-training-form-tab-header">
    {% if image != '' %}
      <img {% if rating and rating_count and not closed and not has_links %} property="image"{% endif %} class="b-lazy" data-src="{{image}}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" alt="{{ image_alt }}" title="{{ image_title}}"/>
    {% endif %}
    {% if rating and rating_count and not closed and not has_links %}
      <div property="aggregateRating" typeof="AggregateRating" class="d-none">
        <span property="ratingValue">{{ rating }}</span>
        <span property="bestRating">6</span>
        <span property="ratingCount">{{ rating_count }}</span>
      </div>
    {% endif %}
    <div class="npx-training-form-tab-header-inner text-white p-2">
      <div class="npx-training-form-tab-header-top">
        <div class="npx-training-form-tab-header-top-inner d-flex justify-content-between">
          <div class="npx-training-form-tab-header-hours fw-bold">{{ hours }}</div>
          <div class="npx-training-form-tab-header-type text-start fw-bold">
  {#        
            {% if webinar %}
              <span class="webinar border-secondary border-2 bg-secondary text-uppercase">Webinar</span>
            {% elseif online %}
              <span class="online border-secondary border-2 bg-secondary text-uppercase">Online</span>
            {% else %}
              {% if not stationary_only %}
                <span class="live-online border-secondary border-2 bg-secondary text-uppercase">Online</span>
              {% endif %}
              <span class="stationary text-black text-uppercase">Stacjonarne</span>
            {% endif %}
  #}
            {% for key, label in labels %}
              {% if key == "online" %}
                <span class="live-online border-secondary border-2 bg-secondary text-uppercase">{{ label }}</span>
              {% elseif key == "stationary" %}
                <span class="stationary text-black text-uppercase">{{ label }}</span>
              {% else %}
                {# <span class="{{ key }} text-uppercase">{{ label }}</span> #}
              {% endif %}
            {% endfor %}
          </div>
        </div>
        <div class="npx-training-form-tab-header-title">
          <h3{% if rating and rating_count and not closed and not has_links %} property="name"{% endif %}>{{ title }}</h3>
        </div>
      </div>
      <div class="npx-training-form-tab-header-info">
        {% if closed %} 
          <div class="npx-training-form-tab-header-info-dates npx-training-form-tab-header-info-inner h5">Szkolenie na zamówienie</div>
          <div class="npx-training-form-tab-header-info-city npx-training-form-tab-header-info-inner h5"><span>Lokalizacja:</span> cała Polska</span></div>
        {% else %}
          {% if dates %}<div class="npx-training-form-tab-header-info-dates npx-training-form-tab-header-info-inner h5"><span>Termin:</span> {{ dates }}</div>{% endif %}
          {% if price %}<div class="npx-training-form-tab-header-info-price npx-training-form-tab-header-info-inner h5"><span>Cena:</span> {{ price }} zł netto</div>{% endif %}
          {% if city %}<div class="npx-training-form-tab-header-info-city npx-training-form-tab-header-info-inner h5"><span>Lokalizacja:</span> {{ city }}</span></div>{% endif %}
        {% endif %}
      </div>
    </div>
  </div>
  <div class="npx-training-form-tab-content p-2 pb-0 fw-normal">
    {{ content|raw }}
  </div>
</div>
{% if city and not closed %}
  <div class="d-none" typeof="Event" vocab="https://schema.org/">
    <meta property="name" content="{{ title }}" />
    <meta property="description" content="{{ content|striptags }}" />
    <meta property="startDate" content="{{ date_start }}" />
    <meta property="endDate" content="{{ date_end }}" />
    <span property="location" typeof="Place">
      <span property="address" typeof="PostalAddress">
        <meta property="addressLocality" content="Warszawa" />
        <meta property="streetAddress" content="{{ room|striptags }}" />
      </span>
      <meta property="name" content="4GROW" />
    </span>
  </div>
{% endif %}