{#*
/**
 * Available variables:
 * - hours
 * - title
 * - content
 * - image
 * - image_alt
 * - image_title
 * - webinar
 * - online
 * - stationary_only
 * - closed
 * - labels
 * - price
 * - dates
 * - city
 * - room
 * - date_start
 * - date_end
 * - has_links
 * - rating
 * - rating_count
 * - has_available_dates
 * - sample_dates

**/
#}

<div class="npx-blocks-program-tab-wrapper d-flex flex-column flex-md-row" {% if rating and rating_count and not closed and not has_links %} vocab="https://schema.org/" typeof="Product"{% endif %}>
  <div class="n-tab-header npx-training-form-tab-header">
    {% if image != '' %}
      <img {% if rating and rating_count and not closed and not has_links %} property="image"{% endif %} class="b-lazy" data-src="{{image}}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" alt="{{ image_alt }}" title="{{ image_title}}"/>
    {% endif %}
    {% if rating and rating_count and not closed and not has_links %}
      <div property="aggregateRating" typeof="AggregateRating" class="d-none">
        <span property="ratingValue">{{ rating }}</span>
        <span property="bestRating">6</span>
        <span property="ratingCount">{{ rating_count }}</span>
      </div>
    {% endif %}
    <div class="npx-training-form-tab-header-inner text-white p-2">
      <div class="npx-training-form-tab-header-top">
        <div class="npx-training-form-tab-header-top-inner d-flex justify-content-between">
          <div class="npx-training-form-tab-header-hours fw-bold">{{ hours }}</div>
          <div class="npx-training-form-tab-header-type text-start fw-bold">
            {% for key, label in labels %}
              {% if key == "webinar" %}
                <span class="webinar text-white text-uppercase px-2 py-1 rounded" style="background-color: #FFA500;">{{ label }}</span>
              {% elseif key == "online" %}
                <span class="online text-white text-uppercase px-2 py-1 rounded" style="background-color: #FFA500;">{{ label }}</span>
              {% elseif key == "online_live" %}
                <span class="online-live text-white text-uppercase px-2 py-1 rounded" style="background-color: #FFA500;">{{ label }}</span>
              {% elseif key == "stationary" %}
                <span class="stationary text-black text-uppercase px-2 py-1 rounded" style="background-color: #FFA500;">{{ label }}</span>
              {% endif %}
            {% endfor %}
          </div>
        </div>
        <div class="npx-training-form-tab-header-title">
          <h3{% if rating and rating_count and not closed and not has_links %} property="name"{% endif %}>{{ title }}</h3>
        </div>
      </div>
      <div class="npx-training-form-tab-header-info{% if not closed %} d-flex align-items-end{% endif %}">
        {% if closed %}
          <div class="npx-training-form-tab-header-info-dates npx-training-form-tab-header-info-inner h5">Szkolenie na zamówienie</div>
          <div class="npx-training-form-tab-header-info-city npx-training-form-tab-header-info-inner h5"><span>Lokalizacja:</span> Dowolna</span></div>
        {% elseif not has_available_dates %}
          {# No available dates - show custom pricing and info #}
          <div class="npx-training-form-tab-header-info-left flex-fill pe-3" style="width: 100%;">
            <div class="small text-warning"><strong>Terminy od:</strong></div>
            <div class="text-white"><strong>wkrótce podamy</strong></div>
            {% if price %}<div class="small text-warning mt-2"><strong>Cena od:</strong></div>{% endif %}
            {% if price %}<div class="text-white"><strong>{{ price }} zł netto</strong></div>{% endif %}
            <div class="small text-warning mt-2"><strong>Lokalizacja:</strong></div>
            <div class="text-white"><strong>Warszawa</strong></div>
          </div>
          <div class="npx-training-form-tab-header-info-right flex-fill ps-3" style="width: 100%;">
            <div class="small text-white"><strong>Dostępne także na zamówienie:</strong></div>
            <div class="text-white"><strong>Dowolna</strong></div>
          </div>
        {% else %}
          {# Has available dates - show normal info #}
          <div class="npx-training-form-tab-header-info-left flex-fill pe-3" style="width: 100%;">
            {% if dates %}<div class="small text-warning"><strong>Termin:</strong></div>{% endif %}
            {% if dates %}<div class="text-white"><strong>{{ dates }}</strong></div>{% endif %}
            {% if price %}<div class="small text-warning mt-2"><strong>Cena od:</strong></div>{% endif %}
            {% if price %}<div class="text-white"><strong>{{ price }} zł netto</strong></div>{% endif %}
            {% if city %}<div class="small text-warning mt-2"><strong>Lokalizacja:</strong></div>{% endif %}
            {% if city %}<div class="text-white"><strong>{{ city }}</strong></div>{% endif %}
          </div>
          <div class="npx-training-form-tab-header-info-right flex-fill ps-3" style="width: 100%;">
            <div class="small text-white mt-3"><strong>Dostępne także na zamówienie:</strong></div>
            <div class="text-white"><strong>Dowolna</strong></div>
          </div>
        {% endif %}
      </div>
    </div>
  </div>

  {# Content section - right side #}
  <div class="npx-training-form-tab-content p-3 fw-normal flex-grow-1">
    {{ content|striptags('<p><br><strong><em><ul><li><ol><div><span>')|raw }}
  </div>
</div>
{% if city and not closed %}
  <div class="d-none" typeof="Event" vocab="https://schema.org/">
    <meta property="name" content="{{ title }}" />
    <meta property="description" content="{{ content|striptags }}" />
    <meta property="startDate" content="{{ date_start }}" />
    <meta property="endDate" content="{{ date_end }}" />
    <span property="location" typeof="Place">
      <span property="address" typeof="PostalAddress">
        <meta property="addressLocality" content="Warszawa" />
        <meta property="streetAddress" content="{{ room|striptags }}" />
      </span>
      <meta property="name" content="4GROW" />
    </span>
  </div>
{% endif %}
