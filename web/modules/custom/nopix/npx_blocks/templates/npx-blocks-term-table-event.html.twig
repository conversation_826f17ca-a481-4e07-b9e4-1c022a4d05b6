{{ attach_library('bootstrap4grow/bootstrap.collapse') }}

{% set showCourseClosed = true %}
{% set index = 0 %}
{% set displayRelated = true %}

{% if termDates is not empty %}
  {% if node.field_term_table_title.value is empty %}
    <h2 class="field-label-above">Te<PERSON><PERSON><PERSON> sz<PERSON>ń</h2>
  {% else %}
    <h2 class="field-label-above">{{ node.field_term_table_title.value }}</h2>
  {% endif %}
  <div class="text-before-term-table">{{ node.field_text_before_terms.value|raw }}</div>
  <div class="training-terms-block-wrapper pb-4 pb-lg-0">
    <table id="training-terms-block" class="training-terms-block">
      <thead>
      <tr>
        <th class="training-terms-block-th px-2" scope="col">TERMINY</th>
        <th class="training-terms-block-th px-2" scope="col">WARIANT SZKOLENIA</th>
        <th class="training-terms-block-th px-2" scope="col">LOKALIZACJA</th>
        {% if seminar %}
          <th class="training-terms-block-th px-2" scope="col">CENA</th>
        {% endif %}
        <th class="training-terms-block-th px-2" scope="col"></th>
      </tr>
      </thead>
      <tbody>

      {% for rows in termDates %}
        {% for row in rows %}
          {% if index == 2 %}
            {% include '@npx_blocks/partials/course-closed.html.twig' %}
            {% set showCourseClosed = false %}
            {% set index = index + 1 %}
          {% endif %}
            <tr {% if npx_is_date_stationary(row['term'].entity) %}typeof="Event" vocab="https://schema.org/"{% endif %} class="{{ index >= 5 ? 'hidden' : '' }}">
              <td class="training-terms-block-td training-terms-block-td-1 px-2 py-0">
                <span {% if npx_is_date_stationary(row['term'].entity) %}property="startDate"{% endif %}>{{ row['term'].entity.field_npxtraining_date_start.value }}</span>
                <span>-</span>
                <span {% if npx_is_date_stationary(row['term'].entity) %}property="endDate"{% endif %}>{{ row['term'].entity.field_npxtraining_date_end.value }}</span>
                {% if npx_is_date_stationary(row['term'].entity) and row['node'].field_npxtraining_block_img.entity.uri.value is not null %}
                  <meta property="image" content="{{ url('<front>')|render ~ file_url(row['node'].field_npxtraining_block_img.entity.uri.value) }}" />
                {% endif %}
                {% if npx_is_date_stationary(row['term'].entity) %}
                  <meta property="eventAttendanceMode" content="https://schema.org/MixedEventAttendanceMode">
                  <meta property="eventStatus" content="https://schema.org/EventScheduled">
                  <span property="organizer" typeof="Organization">
                    <meta property="name" content="4GROW" />
                    <meta property="url" content="https://4grow.pl" />
                  </span>
                  <span property="performer" typeof="Organization">
                    <meta property="name" content="4GROW" />
                    <meta property="url" content="https://4grow.pl" />
                  </span>
                {% endif %}
              </td>
              <td class="with-subtable training-terms-block-td px-2 py-0 training-terms-block-td-2 w-50">
                <table class="training-terms-block-with-sustable-table">
                  <tr>
                    <td {% if npx_is_date_stationary(row['term'].entity) %}property="name"{% endif %} class="training-terms-block-td-clickable clickable"  data-bs-toggle="collapse" data-bs-target="#bs-{{ row['term'].entity.id }}-accordion" aria-expanded="true" aria-controls="#bs-{{ row['term'].entity.id }}--accordion">
                      <h4 class="training-terms-block-h4 d-inline m-0">
                      {{ row['node'].field_npxtraining_tytul_formalny.value }}</h4>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <div {% if npx_is_date_stationary(row['term'].entity) %}property="description"{% endif %} id="bs-{{ row['term'].entity.id }}-accordion" class="collapse">{% if row['node'].field_zajawka_termin.value %}{{ row['node'].field_zajawka_termin.value }}{% else %}{{ row['node'].field_npxtraining_tytul_formalny.value }}{% endif %}</div>
                    </td>
                  </tr>
                </table>
              </td>
              <td class="training-terms-block-td  px-2 py-0 training-terms-block-td-3">
                {% include '@npx_blocks/partials/locality.html.twig' with { 'term': row['term'] } %}
                {% include '@npx_blocks/partials/map.html.twig' with { 'term': row['term'] } %}
              </td>
              {% if seminar %}
                <td class="training-terms-block-td px-2 py-0 training-terms-block-td-4">
                  {{ row['price'] }}{% if row['price'] != '-' %} zł{% endif %}
                </td>
              {% endif %}
              <td class="training-terms-block-td px-2 text-center py-0 training-terms-block-td-5">
                  {% if seminar %}
                    <a {% if npx_is_date_stationary(row['term'].entity) %}property="url"{% endif %} href="/node/{{row['node'].id}}#npx-training-form"  class="npx-program-button training-terms-block-npx-form-button">Oblicz rabat</a>
                  {% else %}
                    <a {% if npx_is_date_stationary(row['term'].entity) %}property="url"{% endif %} href="#npx-training-form" data-training-id="{{row['node'].id}}" data-training-date="{{row['term'].entity.id}}" class="training-terms-block-npx-form-button npx-program-button text-uppercase">Zarezerwuj</a>
                  {% endif %}
              </td>
            </tr>
            {% set index = index + 1 %}
          {% endfor %}
        {% endfor %}

      </tbody>
    </table>

    {% if allTerms > 4 %}
      <div class="load-more-terms-wrapper">
        <div class="load-more-terms-bg w-100"></div>
        <a href="#" class="load-more-terms d-block text-uppercase text-black p-2 text-center text-decoration-none">Więcej terminów</a>
      </div>
    {% endif %}
  </div>

  <div class="text-after-term-table">{{ node.field_text_after_terms.value|raw }}</div>
{% endif %}
