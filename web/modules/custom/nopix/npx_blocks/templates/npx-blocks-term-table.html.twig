{% set showCourseClosed = true %}
{% set index = 0 %}
{% set displayRelated = true %}

{% if termDates is not empty %}
  {% if node.field_term_table_title.value is empty %}
    <h2 class="field-label-above">Te<PERSON><PERSON><PERSON> s<PERSON>koleń</h2>
  {% else %}
    <h2 class="field-label-above">{{ node.field_term_table_title.value }}</h2>
  {% endif %}
  <div class="text-before-term-table">{{ node.field_text_before_terms.value|raw }}</div>
  <div class="training-terms-block-wrapper pb-4 pb-lg-0">
    <table id="training-terms-block" class="training-terms-block">
      <thead>
        <tr>
          <th scope="col" class="px-2">TERMINY</th>
          <th scope="col" class="px-2">WARIANT SZKOLENIA</th>
          <th scope="col" class="px-2">LOKALIZACJA</th>
          {% if seminar %} 
            <th scope="col" class="px-2">CENA</th>
          {% endif %}
          <th scope="col" class="px-2"></th>
        </tr>
      </thead>
      <tbody>

        {% for rows in termDates %}
          {% for row in rows %}
            {% if index == 2 %}
              {% include '@npx_blocks/partials/course-closed.html.twig' %}
              {% set showCourseClosed = false %}
              {% set index = index + 1 %}
            {% endif %}
            <tr typeof="Course" vocab="https://schema.org/" class="{{ index >= 5 ? 'hidden' : '' }}">
              <td class="training-terms-block-td-1 training-terms-block-td px-2">
                <span rel="hasCourseInstance" typeof="CourseInstance">
                  <span property="startDate">{{ row['term'].entity.field_npxtraining_date_start.value }}</span>
                  <span>-</span>
                  <span property="endDate">{{ row['term'].entity.field_npxtraining_date_end.value }}</span>
                  {% include '@npx_blocks/partials/coursemode.html.twig' %}
                  {% if row['term'].entity.field_npxtraining_hours.value is not null and row['term'].entity.field_npxtraining_hours.value > 16 %}
                    <meta property="courseWorkload" content="P3D"/>
                  {% elseif row['term'].entity.field_npxtraining_hours.value is not null and row['term'].entity.field_npxtraining_hours.value > 8 %}
                    <meta property="courseWorkload" content="P2D"/>
                  {% else %}
                    <meta property="courseWorkload" content="P1D"/>
                  {% endif %} 
                </span>
                {% include '@npx_blocks/partials/instance-locality.html.twig' %}
              </td>
              <td class="training-terms-block-td-2 training-terms-block-td with-subtable px-2">
                {% include '@npx_blocks/partials/organizer.html.twig' %}
                {% if row['node'].field_npxtraining_block_img.entity.uri.value %}
                  <meta property="image" content="{{ url('<front>')|render ~ file_url(row['node'].field_npxtraining_block_img.entity.uri.value) }}" />
                {% endif %}
                <table>
                  <tr>
                    <td property="name" class="clickable" data-toggle="collapse" data-target="#{{ row['term'].entity.id }}-accordion">
                      <h4 class="training-terms-block-h4">{{ row['node'].field_npxtraining_tytul_formalny.value }}</h4>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <div property="description" id="{{ row['term'].entity.id }}-accordion" class="collapse">{% if row['node'].field_zajawka_termin.value %}{{ row['node'].field_zajawka_termin.value }}{% else %}{{ row['node'].field_npxtraining_tytul_formalny.value }}{% endif %}</div>
                    </td>
                  </tr>
                </table>
              </td>
              <td class="training-terms-block-td-3 training-terms-block-td px-2">
                {% include '@npx_blocks/partials/locality.html.twig' with { 'term': row['term'] } %}
                {% include '@npx_blocks/partials/map.html.twig' with { 'term': row['term'] } %}
              </td>
              {% if seminar %}
                <td class="training-terms-block-td-4 training-terms-block-td px-2">
                  {{ row['price'] }}{% if row['price'] != '-' %} zł{% endif %}
                </td>
              {% endif %}
              <td class="training-terms-block-td-5 training-terms-block-td text-center px-2">
                {% if seminar %}
                  <a property="url" href="/node/{{row['node'].id}}#npx-training-form" data-training-id="{{row['node'].id}}" class="npx-program-button training-terms-block-npx-form-button">Oblicz rabat</a>
                {% else %}
                  <a property="url" href="#npx-training-form" data-training-id="{{row['node'].id}}" data-training-date="{{row['node'].id}}" class="npx-program-button training-terms-block-npx-form-button">Zarezerwuj</a>
                {% endif %}
              </td>
            </tr>
            {% set index = index + 1 %}
          {% endfor %}
        {% endfor %}

      </tbody>
    </table>

    {% if allTerms > 4 %}
      <div class="load-more-terms-wrapper">
        <div class="load-more-terms-bg w-100"></div>
        <a href="#" class="load-more-terms d-block text-uppercase text-black p-2 text-center text-decoration-none">Więcej terminów</a>
      </div>
    {% endif %}
  </div>

  <div class="text-after-term-table">{{ node.field_text_after_terms.value|raw }}</div>
{% endif %}
