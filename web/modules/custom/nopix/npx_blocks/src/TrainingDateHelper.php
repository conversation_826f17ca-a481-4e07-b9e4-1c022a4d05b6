<?php

namespace Drupal\npx_blocks;

class TrainingDateHelper {
  /**
   * Default date format
   * @var date format dtring
   */
  const DEFAULT_DATE_FORMAT = 'Y-m-d';
  
  /**
   * Check if date is available
   * @param node $date
   * @param boolean $ignore_availability
   * 
   * @return boolean
   */
  static public function checkDate($date, $ignore_availability=false) {
    if(!$date->isPublished()) {
      return false;
    }
    if($date->field_npxtraining_date_available->value == 'tak'
        || $date->field_npxtraining_date_available->value == 'ostatnie'
        || $ignore_availability) {
          $now = date(self::DEFAULT_DATE_FORMAT);
          $tmp_date = substr($date->field_npxtraining_date_start->value, 0, 10);
          $cmp = strcmp($now, $tmp_date);
          if($cmp < 0) {
            return true;
          }
        }
        return false;
  }
  
  /**
   * Prepare date display
   * 
   * @param string $date_start
   * @param string $date_end
   * @param boolean $full_month_name
   * @param boolean $with_year
   * @param boolean $short_year
   * 
   * @return string
   */
  static public function prepareDateDisplay($date_start, $date_end, $full_month_name=false, $with_year=false, $short_year=false) {
    $tmp_start = date_parse(substr($date_start, 0, 10));
    $tmp_end = date_parse(substr($date_end, 0, 10));
    if($full_month_name) {
      $months_arr = array(
          '1' => 'stycznia',
          '2' => 'lutego',
          '3' => 'marca',
          '4' => 'kwietnia',
          '5' => 'maja',
          '6' => 'czerwca',
          '7' => 'lipca',
          '8' => 'sierpnia',
          '9' => 'września',
          '10' => 'października',
          '11' => 'listopada',
          '12' => 'grudnia',
      );
      if($tmp_start['month'] != $tmp_end['month']) {
        if($with_year) {
          return $tmp_start['day'].' '.$months_arr[$tmp_start['month']].' - '.$tmp_end['day'].' '.$months_arr[$tmp_end['month']].' '.$tmp_start['year'];
        } else {
          return $tmp_start['day'].' '.$months_arr[$tmp_start['month']].' - '.$tmp_end['day'].' '.$months_arr[$tmp_end['month']];
        }
      } else {
        if($tmp_start['day'] != $tmp_end['day']) {
          if($with_year) {
            return $tmp_start['day'].'-'.$tmp_end['day'].' '.$months_arr[$tmp_start['month']].' '.$tmp_start['year'];
          } else {
            return $tmp_start['day'].'-'.$tmp_end['day'].' '.$months_arr[$tmp_start['month']];
          }
        } else {
          if($with_year) {
            return $tmp_start['day'].' '.$months_arr[$tmp_start['month']].' '.$tmp_start['year'];
          } else {
            return $tmp_start['day'].' '.$months_arr[$tmp_start['month']];
          }
        }
      }
    } else if($with_year && $short_year) {
      if($tmp_start['month'] != $tmp_end['month']) {
        return $tmp_start['day'].'.'.sprintf('%02d', $tmp_start['month']).' - '.$tmp_end['day'].'.'.sprintf('%02d', $tmp_end['month']).'.'.substr($tmp_start['year'], -2);
      } else {
        if($tmp_start['day'] != $tmp_end['day']) {
          return $tmp_start['day'].'-'.$tmp_end['day'].'.'.sprintf('%02d', $tmp_start['month']).'.'.substr($tmp_start['year'], -2);
        } else {
          return $tmp_start['day'].'.'.sprintf('%02d', $tmp_start['month']).'.'.substr($tmp_start['year'], -2);
        }
      }
    } else if($with_year) {
      if($tmp_start['month'] != $tmp_end['month']) {
        return $tmp_start['day'].'.'.sprintf('%02d', $tmp_start['month']).' - '.$tmp_end['day'].'.'.sprintf('%02d', $tmp_end['month']).'.'.$tmp_start['year'];
      } else {
        if($tmp_start['day'] != $tmp_end['day']) {
          return $tmp_start['day'].'-'.$tmp_end['day'].'.'.sprintf('%02d', $tmp_start['month']).'.'.$tmp_start['year'];
        } else {
          return $tmp_start['day'].'.'.sprintf('%02d', $tmp_start['month']).'.'.$tmp_start['year'];
        }
      }
    } else {
      if($tmp_start['month'] != $tmp_end['month']) {
        return $tmp_start['day'].'.'.sprintf('%02d', $tmp_start['month']).' - '.$tmp_end['day'].'.'.sprintf('%02d', $tmp_end['month']);
      } else {
        if($tmp_start['day'] != $tmp_end['day']) {
          return $tmp_start['day'].'-'.$tmp_end['day'].'.'.sprintf('%02d', $tmp_start['month']);
        } else {
          return $tmp_start['day'].'.'.sprintf('%02d', $tmp_start['month']);
        }
      }
    }
  }
  
  static public function prepareDateDisplayNode($node, $full_month_name=false, $with_year=false, $short_year=false) {
    $count = count($node->field_npxtraining_date_start);
    $out = '';
    
    for($i = 0; $i < $count; $i++) {
      $out .= self::prepareDateDisplay($node->field_npxtraining_date_start[$i]->value, $node->field_npxtraining_date_end[$i]->value, $full_month_name, $with_year, $short_year);
      
      if($i < ($count - 1)) {
        $out .= ' + ';
      }
      
    }
    return $out;
    
  }
  
  /**
   * Find closest date
   * 
   * @param array $dates
   * 
   * @return string
   */
  static public function findClosestDate($dates) {
    $now = date(self::DEFAULT_DATE_FORMAT);
    asort($dates);
    $closest = NULL;
    foreach($dates as $date) {
      $tdate = substr($date, 0, 10);
      $cmp = strcmp($now, $tdate);
      if($cmp < 0) {
        return $date;
      } else if($cmp > 0) {
        $closest = $date;
      }
    }
    return $closest;
  }
  
  /**
   * Find closest dates
   * 
   * @param array $dates
   * @param int $nr
   * 
   * @return array
   */
  static public function findClosestDates($dates, $nr=3) {
    $closest_arr = [];
    
    for($i = 0; $i < $nr; $i++) {
      $date = self::findClosestDate($dates);
      if(!$date) {
        break;
      }
      $closest_arr[] = $date;
      $index = array_search($date, $dates);
      if ($index !== false) {
        unset($dates[$index]);
      }
    }
    
    return $closest_arr;
  }
  
  static public function calculateDays($start, $end) {
    $date_start = new \DateTime($start);
    $date_end = new \DateTime($end);
    
    $date_diff = $date_start->diff($date_end);
    
    return $date_diff->format('%d') + 1;
  }
  
  static public function calculateDaysNode($node) {
    $count = count($node->field_npxtraining_date_start);
    $out = '';
    
    for($i = 0; $i < $count; $i++) {
      $out .= self::calculateDays($node->field_npxtraining_date_start[$i]->value, $node->field_npxtraining_date_end[$i]->value);
      
      if($i < ($count - 1)) {
        $out .= ' + ';
      }
      
    }
    return $out;
  }
  
  static public function calculateHoursNode($node) {
    $count = count($node->field_npxtraining_date_start);
    $days = 0;
    
    for($i = 0; $i < $count; $i++) {
      $days += self::calculateDays($node->field_npxtraining_date_start[$i]->value, $node->field_npxtraining_date_end[$i]->value);
    }
    return $days * 8;
  }
  
  static public function generateSchemaEvent($title, $desc, $start, $end, $price, $lowPrice, $person, $url = false) {
    $script = '<script type="application/ld+json">{';
    $script .= '"@context": "http://schema.org",';
    $script .= '"@type": "Event",';
    $script .= '"location": {';
    $script .= '"@type": "Place",';
    $script .= '"address": {';
    $script .= '"@type": "PostalAddress",';
    $script .= '"addressLocality": "Warszawa",';
    $script .= '"streetAddress": "ul. Panieńska 9 lokal 25",';
    $script .= '"postalCode" : "03-704"';
    $script .= '},';
    $script .= '"name": "Adres szkolenia"';
    $script .= '},';
    $script .= '"name": "' . $title . '",';
    $script .= '"description": "' . $desc . '",';
    $script .= '"startDate": "' . $start . '",';
    $script .= '"endDate": "' . $end . '",';
  
    if($url) {
      $script .= '"url": "' . $url . '",';
    }
    
    $script .= '"offers": {';
    $script .= '"@type": "AggregateOffer",';
    $script .= '"price": "' . $price . '",';
    $script .= '"highPrice": "' . $price . '",';
    $script .= '"lowPrice": "' . $lowPrice . '",';
    $script .= '"priceCurrency": "PLN"';
    $script .= '},';
    $script .= '"performer" : {';
    $script .= '"@type": "Person",';
    $script .= '"name": "Trener ' . $person . '"';
    $script .= '}';
    $script .= '}</script>';
    
    return $script;
  }
}
