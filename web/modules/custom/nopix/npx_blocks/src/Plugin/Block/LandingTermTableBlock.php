<?php

namespace Drupal\npx_blocks\Plugin\Block;

use <PERSON><PERSON><PERSON>\node\NodeInterface;
use <PERSON><PERSON><PERSON>\Core\Block\BlockBase;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON><PERSON>\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON><PERSON><PERSON>\Core\Routing\CurrentRouteMatch;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\npx_training_form\NpxCalculatorHelper;
use Drupal\npx_training_form\NpxDiscountHelper;

/**
 * Provides a 'LandingTermTableBlock' block.
 *
 * @Block(
 *  id = "npx landing_term_table_block",
 *  admin_label = @Translation("Npx Landing term table block"),
 * )
 */
class LandingTermTableBlock extends BlockBase implements ContainerFactoryPluginInterface
{

  /**
   * \Drupal\Core\Entity\EntityTypeManager definition.
   *
   * @var \Drupal\Core\Entity\EntityTypeManager
   */
  protected $entityTypeManager;

  /**
   * Drupal\Core\Routing\CurrentRouteMatch definition.
   *
   * @var \Drupal\Core\Routing\CurrentRouteMatch
   */
  protected $currentRouteMatch;

  /**
   * Constructs a new BreadcrumbBlock object.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param string $plugin_definition
   *   The plugin implementation definition.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    EntityTypeManagerInterface $entity_type_manager,
    CurrentRouteMatch $current_route_match
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->currentRouteMatch = $current_route_match;
    $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition)
  {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('entity_type.manager'),
      $container->get('current_route_match')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function build()
  {
    $node = $this->currentRouteMatch->getParameter('node');
    if (!$node instanceof NodeInterface) {
      return;
    }

    $additional_nodes = $node->get('field_related_trainings')->referencedEntities();
    $node_seminar = $node->get('field_related_seminar')->entity;
    $node = $node->get('field_related_training')->entity;

    $termDates = [];
    //     foreach ($node->get('field_npxtraining_dates') as $term_date) {
//       if(!$term_date->entity->isPublished()) {
//         continue;
//       }
//       $termTime = $term_date->entity->get('field_npxtraining_date_start')->date->getTimestamp();

    //       if ($termTime > time()) {
//         $termDates[][] = ['node' => $node, 'term' => $term_date];
//       }
//     }

    $fake_discount = 0;
    $fake_discount_upval = 0;
    if (NpxDiscountHelper::isParamFakeDiscountSet()) {
      $fake_discount = NpxDiscountHelper::getParamFakeDiscountVal();
      $fake_discount_upval = NpxDiscountHelper::getParamFakeDiscountUpVal();
    }
    $amount = 1;

    $base_price_seminar = $node_seminar->field_npxtraining_price->value;

    $code_info = [];

    $disable_regular = false;
    if (!is_null($node_seminar)) {
      foreach ($node_seminar->get('field_npx_related_training') as $variant) {
        $index = 0;
        foreach ($variant->entity->get('field_npxtraining_dates') as $term_date) {
          if (!$term_date->entity->isPublished()) {
            continue;
          }

          $termTime = $term_date->entity->get('field_npxtraining_date_start')->date->getTimestamp();

          if ($termTime > time()) {
            $base_price = $variant->entity->field_npxtraining_price->value;
            $discount_info = NpxDiscountHelper::getDiscountInfo($term_date->entity->id());
            $calc_info = NpxCalculatorHelper::getCalculation($base_price + $fake_discount + $fake_discount_upval, $amount, $discount_info, $code_info, $disable_regular);

            if ($calc_info['info']['has_active_first'] && $calc_info['info']['has_active_last']) {
              $price = min($calc_info['regular']['total_with_discount'], $calc_info['first']['total_with_discount'], $calc_info['last']['total_with_discount']);
            } else if ($calc_info['info']['has_active_first']) {
              $price = min($calc_info['regular']['total_with_discount'], $calc_info['first']['total_with_discount']);
            } else if ($calc_info['info']['has_active_last']) {
              $price = min($calc_info['regular']['total_with_discount'], $calc_info['last']['total_with_discount']);
            } else {
              $price = $calc_info['regular']['total_with_discount'];
            }

            $termDates[$index][] = ['node' => $variant->entity, 'term' => $term_date, 'price' => $price];

            $index++;
          }
        }
      }
    }
    if (!is_null($node)) {
      foreach ($node->get('field_npx_related_training') as $variant) {
        $index = 0;
        foreach ($variant->entity->get('field_npxtraining_dates') as $term_date) {
          if (!$term_date->entity->isPublished()) {
            continue;
          }

          $termTime = $term_date->entity->get('field_npxtraining_date_start')->date->getTimestamp();

          if ($termTime > time()) {
            $base_price = $variant->entity->field_npxtraining_price->value;
            $discount_info = NpxDiscountHelper::getDiscountInfo($term_date->entity->id());
            $calc_info = NpxCalculatorHelper::getCalculation($base_price + $fake_discount + $fake_discount_upval, $amount, $discount_info, $code_info, $disable_regular);

            if ($calc_info['info']['has_active_first'] && $calc_info['info']['has_active_last']) {
              $price = min($calc_info['regular']['total_with_discount'], $calc_info['first']['total_with_discount'], $calc_info['last']['total_with_discount']);
            } else if ($calc_info['info']['has_active_first']) {
              $price = min($calc_info['regular']['total_with_discount'], $calc_info['first']['total_with_discount']);
            } else if ($calc_info['info']['has_active_last']) {
              $price = min($calc_info['regular']['total_with_discount'], $calc_info['last']['total_with_discount']);
            } else {
              $price = $calc_info['regular']['total_with_discount'];
            }
            $termDates[$index][] = ['node' => $variant->entity, 'term' => $term_date, 'price' => $price];

            $index++;
          }
        }
      }
    }

    foreach ($additional_nodes as $additional_node) {
      $index = 0;
      foreach ($additional_node->get('field_npxtraining_dates') as $term_date) {
        if (!$term_date->entity->isPublished()) {
          continue;
        }

        $termTime = $term_date->entity->get('field_npxtraining_date_start')->date->getTimestamp();

        if ($termTime > time()) {
          if (!$additional_node->get('field_npxtraining_closed')->value) {
            $base_price = $additional_node->field_npxtraining_price->value;
            $discount_info = NpxDiscountHelper::getDiscountInfo($term_date->entity->id());
            $calc_info = NpxCalculatorHelper::getCalculation($base_price + $fake_discount + $fake_discount_upval, $amount, $discount_info, $code_info, $disable_regular);

            if ($calc_info['info']['has_active_first'] && $calc_info['info']['has_active_last']) {
              $price = min($calc_info['regular']['total_with_discount'], $calc_info['first']['total_with_discount'], $calc_info['last']['total_with_discount']);
            } else if ($calc_info['info']['has_active_first']) {
              $price = min($calc_info['regular']['total_with_discount'], $calc_info['first']['total_with_discount']);
            } else if ($calc_info['info']['has_active_last']) {
              $price = min($calc_info['regular']['total_with_discount'], $calc_info['last']['total_with_discount']);
            } else {
              $price = $calc_info['regular']['total_with_discount'];
            }
          } else {
            $price = '-';
          }
          $termDates[$index][] = ['node' => $additional_node, 'term' => $term_date, 'price' => $price];

          $index++;
        }
      }
    }

    $allTerms = 0;
    $flattenedArray = [];

    // Flatten the array
    foreach ($termDates as $rows) {
      $allTerms += count($rows);
      $flattenedArray = array_merge($flattenedArray, $rows);
    }

    // Sort the flattened array
    usort($flattenedArray, function ($a, $b) {
      $a_date = strtotime($a['term']->entity->field_npxtraining_date_start->value);
      $b_date = strtotime($b['term']->entity->field_npxtraining_date_start->value);

      if ($a_date == $b_date) {
        return 0;
      }
      return ($a_date < $b_date) ? -1 : 1;
    });

    // Rebuild the original structure
    $sortedTermDates = [];
    foreach ($flattenedArray as $item) {
      $sortedTermDates[$item['parent']][] = $item;
    }

    $termDates = $sortedTermDates;
    if (!is_null($node)) {
      $rdfaType = $node->get('field_rdfa_type')->getString();

      // Ask if these fields should be also check in variant??
      $stationary = (int) $node->get('field_npxtraining_stationary')->getString();
      $online = (int) $node->get('field_npxtraining_online')->getString();

      $middleItem = ceil(count($termDates) / 2);
      $theme = 'npx_blocks_term_table_event';

      /*
            if ($rdfaType == 'event') {
              $theme = 'npx_blocks_term_table_event';
            }
            else if ($rdfaType == 'course') {
              $theme = 'npx_blocks_term_table_course';
            }
            else {
              $theme = 'npx_blocks_term_table';
            }
          } else {
            $theme = 'npx_blocks_term_table';
            $online = null;
            $stationary = null;
            $middleItem = null;
          }
      */
      $node = $this->currentRouteMatch->getParameter('node');

      return [
        '#theme' => $theme,
        '#node' => $node,
        '#stationary' => $stationary,
        '#online' => $online,
        '#middleItem' => $middleItem,
        '#termDates' => $termDates,
        '#allTerms' => $allTerms,
        '#seminar' => true,
        '#attached' => [
          'library' => [
            'npx_blocks/npx_blocks.trainingtermtableblock'
          ]
        ]
      ];
    }

  }
}