<?php

namespace Drupal\npx_blocks\Plugin\Block;

use <PERSON><PERSON><PERSON>\Core\Block\BlockBase;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON><PERSON>al\Core\Render\Markup;
use <PERSON><PERSON>al\Core\Routing\CurrentRouteMatch;
use <PERSON><PERSON>al\image\Entity\ImageStyle;
use <PERSON><PERSON><PERSON>\node\NodeInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\npx_training_form\NpxCalculatorHelper;
use Drupal\npx_training_form\NpxDiscountHelper;

/**
 * Provides a 'TrainingProgramBlock' block.
 *
 * @Block(
 *  id = "npx_training_program_block",
 *  admin_label = @Translation("Training program block"),
 * )
 */
class TrainingProgramBlock extends BlockBase implements ContainerFactoryPluginInterface {
  
  const DATE_FORMAT = 'Y-m-d';
  
  /**
   * \Drupal\Core\Entity\EntityTypeManager definition.
   *
   * @var \Drupal\Core\Entity\EntityTypeManager
   */
  protected $entityTypeManager;

  /**
   * Drupal\Core\Routing\CurrentRouteMatch definition.
   *
   * @var \Drupal\Core\Routing\CurrentRouteMatch
   */
  protected $currentRouteMatch;

  /**
   * Constructs a new BreadcrumbBlock object.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param string $plugin_definition
   *   The plugin implementation definition.
   */
  public function __construct(
      array $configuration,
      $plugin_id,
      $plugin_definition,
      EntityTypeManagerInterface $entity_type_manager,
      CurrentRouteMatch $current_route_match
      ) {
        parent::__construct($configuration, $plugin_id, $plugin_definition);
        $this->currentRouteMatch = $current_route_match;
        $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
        $configuration,
        $plugin_id,
        $plugin_definition,
        $container->get('entity_type.manager'),
        $container->get('current_route_match')
        );
  }

  /**
   * {@inheritdoc}
   */
  public function build() {
    $build = [];
    
    if(!$node = $this->currentRouteMatch->getParameter('node')) {
      return $build;
    }

    if(!$node instanceof NodeInterface) {
      return $build;
    }

    $section_title = 'Program szkolenia';

    if(count($node->field_npx_related_training)) {
//       $section_title = 'Wybierz wariant szkolenia i sprawdź program';
      $section_title = 'Przeczytaj program lub sprawdź inne warianty szkolenia';
    }
    
    if($node->get('field_tytul_sekcji_g6')->value != '') {
      $section_title = $node->get('field_tytul_sekcji_g6')->value;
    }

    $build['title'] = [
      '#markup' => '<div class="item-list">' . $section_title . '</div>',
      '#weight' => -100,
    ];
    
    if(isset($node->field_npx_related_training)) {
      $build['npx_tabs_wrapper'] = [
        '#type' => 'container',
        '#id' => 'npx-tabs',
        '#attributes' => [
          'class' => ['npx-program-tabs-wrapper'],
        ],
      ];

//       $first_tab_class = '';
//       if(count($node->field_npx_related_training) == 0) {
//         $first_tab_class = 'hidden';
//       }

//       $image_source = '';
//       if($node->get('field_npxtraining_block_img')->entity) {
//         $image_uri = $node->get('field_npxtraining_block_img')->entity->getFileUri();
//         $style = ImageStyle::load('training_program_tab_image');
//         if($style) {
//           $image_source = $style->buildUrl($image_uri);//file_create_url($image_uri);
//         } else {
//           $image_source =  file_url_transform_relative(file_create_url($image_uri));
//         }
//       }
//       $tab_build = [
//         '#theme' => 'npx_blocks_program_tab',
//         '#hours' => $this->getHours($node),
//         '#title' => $node->get('field_npxtraining_tytul_formalny')->value,
//         '#content' => $node->get('field_program_szkolenia_wstep')->value,
//         '#image' => $image_source,
//         '#webinar' => $node->get('field_npxtraining_webinar')->value,
//         '#online' => $node->get('field_npxtraining_online')->value,
//         '#stationary_only' => $node->get('field_npxtraining_stationary')->value,
//       ];

//       $tab_rendered = \Drupal::service('renderer')->render($tab_build);

      $tab_nav_items = [
//         [
//           'value' => ['#markup' => Markup::create('<a class="npx-no-autolink active ' . $first_tab_class . '" data-bs-toggle="tab" href="#program-tab-' . $node->id() . '" data-npx-nid="' . $node->id() . '">' . $tab_rendered . '</a>')],
//           '#wrapper_attributes' => [ 'class' => ['active', 'nav-item'] ],
//         ],
      ];

      $build['npx_tabs_wrapper']['npx_tabs_content'] = [
        '#type' => 'container',
        '#attributes' => [ 'class' => ['tab-content'] ],
        '#weight' => 20,
      ];
//       $build['npx_tabs_wrapper']['npx_tabs_content']['tab_' . $node->id()] = [
//         '#type' => 'container',
//         '#id' => 'program-tab-' . $node->id(),
//         '#attributes' => [
//           'id' => ['program-tab-' . $node->id()],
//           'class' => [
//             'tab-pane',
//             'fade',
//             'in',
//             'active',
//             'show',
//           ],
//         ]
//       ];
//       $build['npx_tabs_wrapper']['npx_tabs_content']['tab_' . $node->id()]['content'] = self::buildTabContent($node);

      $first_class = 'active';
      $first_tab_class = 'show';
      
      foreach($node->field_npx_related_training as $idx => $rel) {
        $idx++;
        if($rel->entity && $rel->entity->isPublished()) {
          $related = $this->entityTypeManager->getStorage('node')->load($rel->entity->id());

          $image_source = '';
          if($related->get('field_npxtraining_block_img')->entity) {
            $image_uri = $related->get('field_npxtraining_block_img')->entity->getFileUri();
            $style = ImageStyle::load('training_program_tab_image_after_img_from_bg');
            if($style) {
              $image_source = $style->buildUrl($image_uri);//file_create_url($image_uri);
            } else {
              $image_source =  \Drupal::service('file_url_generator')->generateString($image_uri);
            }

            $image_alt = $related->get('field_npxtraining_block_img')->first()->get('alt')->getValue();
            $image_title = $related->get('field_npxtraining_block_img')->first()->get('title')->getValue();
          }

          $alias = \Drupal::service('path_alias.manager')->getAliasByPath('/node/' . $related->id());

          $tab_build = [
            '#theme' => 'npx_blocks_program_tab',
            '#hours' => $this->getHours($related),
            '#title' => $related->get('field_npxtraining_tytul_formalny')->value,
            '#content' => $related->get('field_program_szkolenia_wstep')->value,
            '#image' => $image_source,
            '#image_alt' => $image_alt,
            '#image_title' => $image_title,
            '#webinar' => $related->get('field_npxtraining_webinar')->value,
            '#online' => $related->get('field_npxtraining_online')->value,
            '#stationary_only' => $related->get('field_npxtraining_stationary')->value,
            '#closed' => $related->get('field_npxtraining_closed')->value,
            '#labels' => $this->getDateLabels($related),
            '#price' => $this->getPrice($related),
	          '#index' => $idx,
            '#alias' => $alias,
            '#rating' => $related->get('field_aggregate_rating')->value,
            '#rating_count' => $related->get('field_aggregate_rating_count')->value,
          ];

          $title = $related->get('field_top_tytul')->referencedEntities()[0]->get('field_ts_tytul')->value;

          $tab_rendered = \Drupal::service('renderer')->render($tab_build);

          if(count($tab_nav_items) == 0) {
            $markup = Markup::create('<a class="npx-no-autolink" href="#program-tab-' . $related->id() . '" aria-label="' . $title . '" data-npx-nid="' . $related->id() . '">' . $tab_rendered . '</a>');
          } else {
            $markup = Markup::create('<a class="npx-no-autolink ' . $first_class . '" href="' . $alias . '" aria-label="' . $title . '" data-npx-nid="' . $related->id() . '">' . $tab_rendered . '</a>');
          }
          $tab_nav_items[] = [
//             '#markup' => Markup::create('<a class="npx-no-autolink ' . $first_class . '" data-bs-toggle="tab" href="#program-tab-' . $related->id() . '" data-npx-nid="' . $related->id() . '">' . $tab_rendered . '</a>'),
            '#markup' => $markup,
            '#wrapper_attributes' => [
              'class' => ['nav-item', $first_class],
            ],
          ];
          
          if($first_class == '') {
            
          }

          if(count($tab_nav_items) > 1) {
            //Disable tabs other than first
            continue;
          }
          $build['npx_tabs_wrapper']['npx_tabs_content']['tab_' . $related->id()] = [
            '#type' => 'container',
            '#id' => 'program-tab-' . $related->id(),
            '#attributes' => [
              'id' => ['program-tab-' . $related->id()],
              'class' => [
                'tab-pane',
                'fade',
                'in',
                $first_class,
                $first_tab_class,
              ],
            ]
          ];
          if($first_class != '') {
            $first_class = '';
            $first_tab_class = '';
          }
          $build['npx_tabs_wrapper']['npx_tabs_content']['tab_' . $related->id()]['content'] = self::buildTabContent($related);
        }
      }

      $build['npx_tabs_wrapper']['npx_tabs'] = [
        '#theme' => 'item_list',
        '#list_type' => 'ul',
        '#items' => $tab_nav_items,
        '#attributes' => [
          'class' => ['nav nav-tabs d-flex flex-column justify-content-center flex-md-row flex-wrap']
        ],
        '#weight' => 10,
      ];
      if(count($node->field_npx_related_training) < 2) {
        $build['npx_tabs_wrapper']['npx_tabs']['#wrapper_attributes'] = [ 'class' => ['hidden']];
      }
    } else {
      $build['content_' . $node->id()] = self::buildTabContent($node);
    }

    // MORE RELATED TRAINING
    if($node->get('field_npx_more_rel_tr_txt')->value != '') {
      $build['npx_more_tabs_txt'] = [
        '#markup' => '<div class="npx-more-tabs-txt">' . $node->get('field_npx_more_rel_tr_txt')->value . '</div>',
        '#prefix' => '<div class="npx-more-tabs-txt-wrapper">',
        '#suffix' => '</div>',
      ];
    }
    
    if(isset($node->field_npx_more_related_training)) {
      $build['npx_more_tabs_wrapper'] = [
        '#type' => 'container',
        '#id' => 'npx-tabs',
        '#attributes' => [
          'class' => ['npx-more-program-tabs-wrapper'],
        ],
      ];
        
      $tab_nav_items = [];
        
      $build['npx_more_tabs_wrapper']['npx_tabs_content'] = [
        '#type' => 'container',
        '#attributes' => [ 'class' => ['tab-content'] ],
        '#weight' => 20,
      ];
        
      $first_class = '';
      $first_tab_class = '';
      
      foreach($node->field_npx_more_related_training as $idx => $rel) {
        $idx++;
        if($rel->entity) {
          $related = $this->entityTypeManager->getStorage('node')->load($rel->entity->id());
          
          $image_source = '';
          if($related->get('field_npxtraining_block_img')->entity) {
            $image_uri = $related->get('field_npxtraining_block_img')->entity->getFileUri();
            $style = ImageStyle::load('training_program_tab_image_after_img_from_bg');
            if($style) {
              $image_source = $style->buildUrl($image_uri);//file_create_url($image_uri);
            } else {
              $image_source =  \Drupal::service('file_url_generator')->generateString($image_uri);
            }
            
            $image_alt = $related->get('field_npxtraining_block_img')->first()->get('alt')->getValue();
            $image_title = $related->get('field_npxtraining_block_img')->first()->get('title')->getValue();
          }
          
          $alias = \Drupal::service('path_alias.manager')->getAliasByPath('/node/' . $related->id());
          
          $tab_build = [
            '#theme' => 'npx_blocks_program_more_tab',
            '#hours' => $this->getHours($related),
            '#title' => $related->get('field_npxtraining_tytul_formalny')->value,
            '#content' => $related->get('field_program_szkolenia_wstep')->value,
            '#image' => $image_source,
            '#image_alt' => $image_alt,
            '#image_title' => $image_title,
            '#webinar' => $related->get('field_npxtraining_webinar')->value,
            '#online' => $related->get('field_npxtraining_online')->value,
            '#stationary_only' => $related->get('field_npxtraining_stationary')->value,
            '#closed' => $related->get('field_npxtraining_closed')->value,
            '#labels' => $this->getDateLabels($related),
            '#price' => $this->getPrice($related),
            '#index' => $idx,
            '#alias' => $alias,
            '#rating' => $related->get('field_aggregate_rating')->value,
            '#rating_count' => $related->get('field_aggregate_rating_count')->value,
          ];
          
          $title = $related->get('field_top_tytul')->referencedEntities()[0]->get('field_ts_tytul')->value;
            
          $tab_rendered = \Drupal::service('renderer')->render($tab_build);
          
          $markup = Markup::create('<a class="npx-no-autolink ' . $first_class . '" href="' . $alias . '" aria-label="' . $title . '" data-npx-nid="' . $related->id() . '">' . $tab_rendered . '</a>');
          $tab_nav_items[] = [
            '#markup' => $markup,
            '#wrapper_attributes' => [
              'class' => ['nav-item', $first_class],
            ],
          ];
          
        }
      }
        
      $build['npx_more_tabs_wrapper']['npx_tabs'] = [
        '#theme' => 'item_list',
        '#list_type' => 'ul',
        '#items' => $tab_nav_items,
        '#attributes' => [
          'class' => ['nav nav-tabs d-flex flex-column justify-content-center flex-md-row flex-wrap']
        ],
        '#weight' => 10,
      ];
    }
    
    $build['#attached'] = [
      'library' => [
        'npx_blocks/npx_blocks.trainingprogramblock',
      ]
    ];

    $build['#cache']['max-age'] = 0;

    return $build;
  }

  public function buildTabContent($node) {
    $build = [];

    if(!$node instanceof NodeInterface) {
      return $build;
    }

    $build['programm_wrapper'] = [
      '#type' => 'container',
      '#attributes' => [ 'class' => ['container', 'program-accordion'] ]
    ];

    $fileField = $node->field_program_szkolenia->getValue();

    if (count($fileField) == 1) {
      $fid = $fileField[0]['target_id'];
      /** @var \Drupal\file\FileInterface $file */
      $file = $this->entityTypeManager->getStorage('file')->load($fid);
      $fileUrl = \Drupal::service('file_url_generator')->generateAbsoluteString($file->getFileUri());
      $fileIconUrl = \Drupal::request()->getSchemeAndHttpHost() . '/' . \Drupal::moduleHandler()->getModule('npx_blocks')->getPath() . '/icons/pdf-pol.png';

      $build['programm_wrapper']['file'] = [
        '#markup' => '<p class="pdf-program text-end text-lg-center"><span>Pobierz program:</span> <a target="_blank" class="pdf-program-link" href="' . $fileUrl . '"><img class="d-inline-block" alt="plik" width="48" height="48" src="' . $fileIconUrl . '" /></a></p>'
      ];
    }

    if (count($fileField) == 2) {
      $content = '<p class="pdf-program text-end text-lg-center"><span>Pobierz program:</span> ';
      $index = 0;
      foreach ($fileField as $fileFieldItem) {
        $fid = $fileFieldItem['target_id'];
        /** @var \Drupal\file\FileInterface $file */
        $file = $this->entityTypeManager->getStorage('file')->load($fid);
        $fileUrl = \Drupal::service('file_url_generator')->generateAbsoluteString($file->getFileUri());
        $modulePath = \Drupal::request()->getSchemeAndHttpHost() . '/' . \Drupal::moduleHandler()->getModule('npx_blocks')->getPath();

        if ($index == 0) {
          $fileIconUrl = $modulePath . '/icons/pdf-pol.png';
        }

        if ($index == 1) {
          $fileIconUrl = $modulePath . '/icons/pdf-eng.png';
        }

        $content .= '<a target="_blank" class="pdf-program-link" href="' . $fileUrl . '"><img class="b-lazy d-inline-block" alt="pdf" width="48" height="48" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="' . $fileIconUrl . '" /></a>';
        $index++;
      }

      $content .= '</p>';
      $build['programm_wrapper']['file']['#markup'] = $content;
    }

    $build['programm_wrapper']['content'] = [
      '#markup' => $node->field_npxtraining_program->value,
    ];

    return $build;
  }

  public function getHours($node) {
    $out = '';
    $dcount = count($node->field_npxtraining_dates);
    if($dcount > 0) {
      $date = $node->field_npxtraining_dates[$dcount - 1];
    } else {
      return $out;
    }

    if($date->entity) {
      $fulldate = $this->entityTypeManager->getStorage('node')->load($date->entity->id());

      if($fulldate->get('field_npxtraining_date_hours')->value) {
        $out = $fulldate->get('field_npxtraining_date_hours')->value;
      } else {
        $count = count($fulldate->field_npxtraining_date_start);
        $days = 0;

        for($i = 0; $i < $count; $i++) {
          $days += self::calculateDays($fulldate->field_npxtraining_date_start[$i]->value, $fulldate->field_npxtraining_date_end[$i]->value);
        }
        $days_txt = $days > 1 ? ' dni | ' : ' dzień | ';
        $out =  $days . $days_txt . ($days * 8) . 'h';
      }
    }

    return $out;
  }
  
  public function getDateLabels(NodeInterface $node): array {
    $labels = [];
    
    $ids = [];
    foreach($node->get('field_npxtraining_dates') as $val) {
      if($val->entity) {
        $ids[] = $val->entity->id();
      }
    }
    $dates = $this->entityTypeManager->getStorage('node')->loadMultiple($ids);
    
    foreach($dates as $date_node) {
      if($this->checkDate($date_node)) {
        foreach($date_node->get('field_npxtraining_date_form') as $val) {
          $label = npx_field_npxtraining_date_form_values()[$val->value];
          $labels[$val->value] = $label;
        }
      }
    }
    
    return $labels;
  }
  
  public function checkDate(NodeInterface $date_node) {
    $now = new \DateTime(date(self::DATE_FORMAT));
    $date = \DateTime::createFromFormat(self::DATE_FORMAT, substr($date_node->field_npxtraining_date_start->value, 0, 10));
    $date->setTime(0,0,0);
    
    return $date > $now;
  }

  public function getPrice($node) {
    $base_price = $node->field_npxtraining_price->value;

    $price = $base_price;
    $dcount = count($node->field_npxtraining_dates);
    if ($dcount > 0) {
      $date = $node->field_npxtraining_dates[$dcount - 1];
    } else {
      return $out;
    }

    if($date->entity && $date->entity->isPublished()) {
      $fulldate = $this->entityTypeManager->getStorage('node')->load($date->entity->id());
      $termTime = $fulldate->get('field_npxtraining_date_start')->date->getTimestamp();
      if ($termTime > time()) {

        $fake_discount = 0;
        $fake_discount_upval = 0;
        if(NpxDiscountHelper::isParamFakeDiscountSet()) {
          $fake_discount = NpxDiscountHelper::getParamFakeDiscountVal();
          $fake_discount_upval = NpxDiscountHelper::getParamFakeDiscountUpVal();
        }
        $amount = 1;

        $code_info = [];

        $disable_regular = false;
        $discount_info = NpxDiscountHelper::getDiscountInfo($fulldate->id());
        $calc_info = NpxCalculatorHelper::getCalculation($base_price + $fake_discount + $fake_discount_upval, $amount, $discount_info, $code_info, $disable_regular);

        if ($calc_info['info']['has_active_first'] && $calc_info['info']['has_active_last']) {
          $price = min($calc_info['regular']['total_with_discount'], $calc_info['first']['total_with_discount'], $calc_info['last']['total_with_discount']);
        } else if ($calc_info['info']['has_active_first']) {
          $price = min($calc_info['regular']['total_with_discount'], $calc_info['first']['total_with_discount']);
        }  else if ($calc_info['info']['has_active_last']) {
          $price = min($calc_info['regular']['total_with_discount'], $calc_info['last']['total_with_discount']);
        } else {
          $price = $calc_info['regular']['total_with_discount'];
	      }
      }
    }
    return $price;
  }

  static public function calculateDays($start, $end) {
    $date_start = new \DateTime($start);
    $date_end = new \DateTime($end);

    $date_diff = $date_start->diff($date_end);

    return $date_diff->format('%d') + 1;
  }
}
