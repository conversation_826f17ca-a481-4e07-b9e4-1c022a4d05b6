<?php

namespace Drupal\npx_blocks\Plugin\Block;

use <PERSON><PERSON><PERSON>\node\NodeInterface;
use <PERSON><PERSON>al\Core\Block\BlockBase;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON><PERSON>\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON><PERSON><PERSON>\Core\Routing\CurrentRouteMatch;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides a 'TrainingTermTableBlock' block.
 *
 * @Block(
 *  id = "npx_training_term_table_block",
 *  admin_label = @Translation("Npx Training term table block"),
 * )
 */
class TrainingTermTableBlock extends BlockBase implements ContainerFactoryPluginInterface {

  /**
   * \Drupal\Core\Entity\EntityTypeManager definition.
   *
   * @var \Drupal\Core\Entity\EntityTypeManager
   */
  protected $entityTypeManager;

  /**
   * Drupal\Core\Routing\CurrentRouteMatch definition.
   *
   * @var \Drupal\Core\Routing\CurrentRouteMatch
   */
  protected $currentRouteMatch;

  /**
   * Constructs a new BreadcrumbBlock object.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param string $plugin_definition
   *   The plugin implementation definition.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    EntityTypeManagerInterface $entity_type_manager,
    CurrentRouteMatch $current_route_match
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->currentRouteMatch = $current_route_match;
    $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('entity_type.manager'),
      $container->get('current_route_match')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function build() {
    $node = $this->currentRouteMatch->getParameter('node');
    if (!$node instanceof NodeInterface) {
      return;
    }

    $termDates = [];
//     foreach ($node->get('field_npxtraining_dates') as $term_date) {
//       if(!$term_date->entity->isPublished()) {
//         continue;
//       }
//       $termTime = $term_date->entity->get('field_npxtraining_date_start')->date->getTimestamp();

//       if ($termTime > time()) {
//         $termDates[][] = ['node' => $node, 'term' => $term_date];
//       }
//     }

    foreach ($node->get('field_npx_related_training') as $variant) {
      $index = 0;
      foreach ($variant->entity->get('field_npxtraining_dates') as $term_date) {
        if(!$term_date->entity->isPublished()) {
          continue;
        }
        $termTime = $term_date->entity->get('field_npxtraining_date_start')->date->getTimestamp();

        if ($termTime > time()) {
          $termDates[$index][] = ['node' => $variant->entity, 'term' => $term_date];

          $index++;
        }
      }
    }

    $allTerms = 0;
    $flattenedArray = [];
    
    // Flatten the array
    foreach ($termDates as $rows) {
      $allTerms += count($rows);
      $flattenedArray = array_merge($flattenedArray, $rows);
    }
    
    // Sort the flattened array
    usort($flattenedArray, function($a, $b) {
      $a_date = strtotime($a['term']->entity->field_npxtraining_date_start->value);
      $b_date = strtotime($b['term']->entity->field_npxtraining_date_start->value);
      
      if ($a_date == $b_date) {
        return 0;
      }
      return ($a_date < $b_date) ? -1 : 1;
    });
    
    // Rebuild the original structure
    $sortedTermDates = [];
    foreach ($flattenedArray as $item) {
      $sortedTermDates[$item['parent']][] = $item;
    }
    
    $termDates = $sortedTermDates;
    $rdfaType = $node->get('field_rdfa_type')->getString();

    // Ask if these fields should be also check in variant??
    $stationary = (int) $node->get('field_npxtraining_stationary')->getString();
    $online = (int) $node->get('field_npxtraining_online')->getString();

    $middleItem = ceil(count($termDates) / 2);

    $theme = 'npx_blocks_term_table_event';

 /*   if ($rdfaType == 'event') {
      $theme = 'npx_blocks_term_table_event';
    }
    else if ($rdfaType == 'course') {
      $theme = 'npx_blocks_term_table_course';
    }
    else {
      $theme = 'npx_blocks_term_table';
    }*/

    return [
      '#theme' => $theme,
      '#node' => $node,
      '#stationary' => $stationary,
      '#online' => $online,
      '#middleItem' => $middleItem,
      '#termDates' => $termDates,
      '#allTerms' => $allTerms,
      '#attached' => [
        'library' => [
          'npx_blocks/npx_blocks.trainingtermtableblock'
        ]
      ]
    ];
  }

}
