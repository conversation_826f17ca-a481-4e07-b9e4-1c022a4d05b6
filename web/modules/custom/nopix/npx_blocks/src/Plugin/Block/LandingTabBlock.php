<?php

namespace Drupal\npx_blocks\Plugin\Block;

use <PERSON><PERSON>al\Core\Block\BlockBase;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON><PERSON>al\Core\Render\Markup;
use <PERSON><PERSON>al\Core\Routing\CurrentRouteMatch;
use <PERSON><PERSON>al\image\Entity\ImageStyle;
use <PERSON><PERSON><PERSON>\node\NodeInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\npx_training_form\NpxCalculatorHelper;
use Drupal\npx_training_form\NpxDiscountHelper;
use Drupal\npx_blocks\TrainingDateHelper;
use Drupal\Core\Url;

/**
 * Provides a 'LandingTabBlock' block.
 *
 * @Block(
 *  id = "npx_landing_tab_block",
 *  admin_label = @Translation("Landing page tab block"),
 * )
 */
class LandingTabBlock extends BlockBase implements ContainerFactoryPluginInterface {
  
  const DATE_FORMAT = 'Y-m-d';
  
  /**
   * \Drupal\Core\Entity\EntityTypeManager definition.
   *
   * @var \Drupal\Core\Entity\EntityTypeManager
   */
  protected $entityTypeManager;

  /**
   * Drupal\Core\Routing\CurrentRouteMatch definition.
   *
   * @var \Drupal\Core\Routing\CurrentRouteMatch
   */
  protected $currentRouteMatch;

  /**
   * Constructs a new BreadcrumbBlock object.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param string $plugin_definition
   *   The plugin implementation definition.
   */
  public function __construct(
      array $configuration,
      $plugin_id,
      $plugin_definition,
      EntityTypeManagerInterface $entity_type_manager,
      CurrentRouteMatch $current_route_match
      ) {
        parent::__construct($configuration, $plugin_id, $plugin_definition);
        $this->currentRouteMatch = $current_route_match;
        $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
        $configuration,
        $plugin_id,
        $plugin_definition,
        $container->get('entity_type.manager'),
        $container->get('current_route_match')
        );
  }

  /**
   * {@inheritdoc}
   */
  public function build() {
    $build = [];
    
    if(!$node = $this->currentRouteMatch->getParameter('node')) {
      return $build;
    }

    if(!$node instanceof NodeInterface) {
      return $build;
    }
    $landing_node = $node;
    $section_title = 'Szkolenia i seminaria';

    $node_seminar = $node->get('field_related_seminar')->entity;
    $additional_nodes = $node->get('field_related_trainings')->referencedEntities();
    $node = $node->get('field_related_training')->entity;

    if(count($node->field_npx_related_training)) {
      $section_title = 'Wybierz wariant szkolenia';
    }

    $build['title'] = [
      '#markup' => '<div class="item-list"><h2 class="field-label-above">' . $section_title . '</h2></div>',
      '#weight' => -100,
    ];
    
    if(isset($node->field_npx_related_training) or isset($node_seminar->field_npx_related_training)) {
      $build['npx_tabs_wrapper'] = [
        '#type' => 'container',
        '#id' => 'npx-tabs',
        '#attributes' => [
          'class' => ['npx-program-tabs-wrapper', 'pb-7'],
        ],
      ];

      $tab_nav_items = [
//         [
//           'value' => ['#markup' => Markup::create('<a class="npx-no-autolink active ' . $first_tab_class . '" data-bs-toggle="tab" href="#program-tab-' . $node->id() . '" data-npx-nid="' . $node->id() . '">' . $tab_rendered . '</a>')],
//           '#wrapper_attributes' => [ 'class' => ['active', 'nav-item'] ],
//         ],
      ];

  /*    $build['npx_tabs_wrapper']['npx_tabs_content'] = [
        '#type' => 'container',
        '#attributes' => [ 'class' => ['tab-content'] ],
        '#weight' => 20,
  ];*/
//       $build['npx_tabs_wrapper']['npx_tabs_content']['tab_' . $node->id()] = [
//         '#type' => 'container',
//         '#id' => 'program-tab-' . $node->id(),
//         '#attributes' => [
//           'id' => ['program-tab-' . $node->id()],
//           'class' => [
//             'tab-pane',
//             'fade',
//             'in',
//             'active',
//             'show',
//           ],
//         ]
//       ];
//       $build['npx_tabs_wrapper']['npx_tabs_content']['tab_' . $node->id()]['content'] = self::buildTabContent($node);

      $first_class = 'active';
      $first_tab_class = 'show';
      
      foreach($node->field_npx_related_training as $rel) {
        if($rel->entity) {
          $related = $this->entityTypeManager->getStorage('node')->load($rel->entity->id());

          $image_source = '';
          if($related->get('field_npxtraining_block_img')->entity) {
            $image_uri = $related->get('field_npxtraining_block_img')->entity->getFileUri();
            $style = ImageStyle::load('landing_program_tab_image');
            if($style) {
              $image_source = $style->buildUrl($image_uri);//file_create_url($image_uri);
            } else {
              $image_source =  \Drupal::service('file_url_generator')->generateString($image_uri);
            }
            $image_alt = $related->get('field_npxtraining_block_img')->first()->get('alt')->getValue();
            $image_title = $related->get('field_npxtraining_block_img')->first()->get('title')->getValue();
	        }
          if (strpos($related->get('field_program_szkolenia_wstep')->value, '<a') !== false) {
            $add_class = 'npx-no-autolink-with-link';
            $has_links = 1;
          }
          else {
            $add_class = '';
            $has_links = 0;
          }
          $closestDate = $this->getClosestFutureDate($related);
          $tab_build = [
            '#theme' => 'npx_blocks_landing_tab',
            '#hours' => $this->getHours($closestDate),
            '#title' => $related->get('field_npxtraining_tytul_formalny')->value,
            '#content' => $related->get('field_program_szkolenia_wstep')->value,
            '#image' => $image_source,
            '#image_alt' => $image_alt,
            '#image_title' => $image_title,
            '#webinar' => $related->get('field_npxtraining_webinar')->value,
	          '#online' => $related->get('field_npxtraining_online')->value,
            '#closed' => $related->get('field_npxtraining_closed')->value,
            '#stationary_only' => $related->get('field_npxtraining_stationary')->value,
	          '#labels' => $this->getDateLabels($closestDate),
            '#price' => $this->getPrice($related),
            '#dates' => $this->getDatesDisplay($closestDate),
            '#city' => $this->getCity($closestDate),
            '#room' => $closestDate->field_npxtraining_date_room->entity->field_room_page_desc->value,
            '#date_start' => $closestDate->field_npxtraining_date_start[0]->value,
            '#date_end' => $closestDate->field_npxtraining_date_end[0]->value,
            '#has_links' => $has_links,
            '#rating' => $related->get('field_aggregate_rating')->value,
            '#rating_count' => $related->get('field_aggregate_rating_count')->value,
          ];

          $tab_rendered = \Drupal::service('renderer')->render($tab_build);

          $node_url = Url::fromRoute('entity.node.canonical', ['node' => $rel->entity->id()], ['absolute' => FALSE])->toString();
          $title = $rel->entity->get('field_top_tytul')->referencedEntities()[0]->get('field_ts_tytul')->value;

          $tab_nav_items[] = [
            '#markup' => Markup::create('<a title="' . $title . '" aria-label="' . $title . '" class="npx-no-autolink ' . $first_class . ' ' . $add_class . '" data-bs-toggle="tab" href="' . $node_url . '">' . $tab_rendered . '</a>'),
            '#wrapper_attributes' => [
              'class' => ['nav-item', $first_class],
            ],
          ];

          $build['npx_tabs_wrapper']['npx_tabs_content']['tab_' . $related->id()] = [
            '#type' => 'container',
            '#id' => 'program-tab-' . $related->id(),
            '#attributes' => [
              'id' => ['program-tab-' . $related->id()],
              'class' => [
                'tab-pane',
                'fade',
                'in',
                $first_class,
                $first_tab_class,
              ],
            ]
          ];
          if($first_class != '') {
            $first_class = '';
            $first_tab_class = '';
          }
        }
      }

      foreach($node_seminar->field_npx_related_training as $rel) {
        if($rel->entity) {
          $related = $this->entityTypeManager->getStorage('node')->load($rel->entity->id());

          $image_source = '';
          if($related->get('field_npxtraining_block_img')->entity) {
            $image_uri = $related->get('field_npxtraining_block_img')->entity->getFileUri();
            $style = ImageStyle::load('landing_program_tab_image');
            if($style) {
              $image_source = $style->buildUrl($image_uri);//file_create_url($image_uri);
            } else {
              $image_source =  \Drupal::service('file_url_generator')->generateString($image_uri);
            }
            $image_alt = $related->get('field_npxtraining_block_img')->first()->get('alt')->getValue();
            $image_title = $related->get('field_npxtraining_block_img')->first()->get('title')->getValue();
          }
          if (strpos($related->get('field_program_szkolenia_wstep')->value, '<a') !== false) {
            $add_class = 'npx-no-autolink-with-link';
            $has_links = 1;
          }
          else {
            $add_class = '';
            $has_links = 0;
          }
          $closestDate = $this->getClosestFutureDate($related);
          dump($closestDate);
          $tab_build = [
            '#theme' => 'npx_blocks_landing_tab',
            '#hours' => $this->getHours($closestDate),
            '#title' => $related->get('field_npxtraining_tytul_formalny')->value,
            '#content' => $related->get('field_program_szkolenia_wstep')->value,
            '#image' => $image_source,
            '#image_alt' => $image_alt,
            '#image_title' => $image_title,
            '#webinar' => $related->get('field_npxtraining_webinar')->value,
            '#online' => $related->get('field_npxtraining_online')->value,
            '#closed' => $related->get('field_npxtraining_closed')->value,
            '#stationary_only' => $related->get('field_npxtraining_stationary')->value,
	          '#labels' => $this->getDateLabels($closestDate),
            '#price' => $this->getPrice($related),
            '#dates' => $this->getDatesDisplay($closestDate),
            '#city' => $this->getCity($closestDate),
            '#room' => $closestDate->field_npxtraining_date_room->entity->field_room_page_desc->value,
            '#date_start' => $closestDate->field_npxtraining_date_start[0]->value,
            '#date_end' => $closestDate->field_npxtraining_date_end[0]->value,
            '#has_links' => $has_links,
            '#rating' => $related->get('field_aggregate_rating')->value,
            '#rating_count' => $related->get('field_aggregate_rating_count')->value,
          ];
    
          $tab_rendered = \Drupal::service('renderer')->render($tab_build);

          $node_url = Url::fromRoute('entity.node.canonical', ['node' => $rel->entity->id()], ['absolute' => FALSE])->toString();
          $title = $rel->entity->get('field_top_tytul')->referencedEntities()[0]->get('field_ts_tytul')->value;
          
          $tab_nav_items[] = [
            '#markup' => Markup::create('<a title="' . $title . '" aria-label="' . $title . '" class="npx-no-autolink ' . $first_class . ' ' . $add_class . '" data-bs-toggle="tab" href="' . $node_url . '">' . $tab_rendered . '</a>'),
            '#wrapper_attributes' => [
              'class' => ['nav-item', $first_class],
            ],
          ];

          $build['npx_tabs_wrapper']['npx_tabs_content']['tab_' . $related->id()] = [
            '#type' => 'container',
            '#id' => 'program-tab-' . $related->id(),
            '#attributes' => [
              'id' => ['program-tab-' . $related->id()],
              'class' => [
                'tab-pane',
                'fade',
                'in',
                $first_class,
                $first_tab_class,
              ],
            ]
          ];
          if($first_class != '') {
            $first_class = '';
            $first_tab_class = '';
          }
        }
      }
      
      foreach ($additional_nodes as $related) {
        $image_source = '';
        
        if($related->get('field_npxtraining_block_img')->entity) {
          $image_uri = $related->get('field_npxtraining_block_img')->entity->getFileUri();
          $style = ImageStyle::load('landing_program_tab_image');
          if($style) {
            $image_source = $style->buildUrl($image_uri);//file_create_url($image_uri);
          } else {
            $image_source =  \Drupal::service('file_url_generator')->generateString($image_uri);
          }
          $image_alt = $related->get('field_npxtraining_block_img')->first()->get('alt')->getValue();
          $image_title = $related->get('field_npxtraining_block_img')->first()->get('title')->getValue();
        }
        if (strpos($related->get('field_program_szkolenia_wstep')->value, '<a') !== false) {
          $add_class = 'npx-no-autolink-with-link';
          $has_links = 1;
        }
        else {
          $add_class = '';
          $has_links = 0;
        }
        $closestDate = $this->getClosestFutureDate($related);

        $tab_build = [
          '#theme' => 'npx_blocks_landing_tab',
          '#hours' => $this->getHours($closestDate),
          '#title' => $related->get('field_npxtraining_tytul_formalny')->value,
          '#content' => $related->get('field_program_szkolenia_wstep')->value,
          '#image' => $image_source,
          '#image_alt' => $image_alt,
          '#image_title' => $image_title,
          '#webinar' => $related->get('field_npxtraining_webinar')->value,
          '#online' => $related->get('field_npxtraining_online')->value,
          '#closed' => $related->get('field_npxtraining_closed')->value,
          '#stationary_only' => $related->get('field_npxtraining_stationary')->value,
          '#labels' => $this->getDateLabels($closestDate),
          '#price' => $this->getPrice($related),
          '#dates' => $this->getDatesDisplay($closestDate),
          '#city' => $this->getCity($closestDate),
          '#room' => $closestDate->field_npxtraining_date_room->entity->field_room_page_desc->value,
          '#date_start' => $closestDate->field_npxtraining_date_start[0]->value,
          '#date_end' => $closestDate->field_npxtraining_date_end[0]->value,
          '#has_links' => $has_links,
          '#rating' => $related->get('field_aggregate_rating')->value,
          '#rating_count' => $related->get('field_aggregate_rating_count')->value,
        ];
  
        $tab_rendered = \Drupal::service('renderer')->render($tab_build);
        $node_url = Url::fromRoute('entity.node.canonical', ['node' => $related->id()], ['absolute' => FALSE])->toString();
        $title = $related->get('field_top_tytul')->referencedEntities()[0]->get('field_ts_tytul')->value;
        
        $tab_nav_items[] = [
          '#markup' => Markup::create('<a title="' . $title . '" aria-label="' . $title . '" class="npx-no-autolink ' . $first_class . ' ' . $add_class . '" data-bs-toggle="tab" href="' . $node_url . '">' . $tab_rendered . '</a>'),
          '#wrapper_attributes' => [
            'class' => ['nav-item', $first_class],
          ],
        ];
        $build['npx_tabs_wrapper']['npx_tabs_content']['tab_' . $related->id()] = [
          '#type' => 'container',
          '#id' => 'program-tab-' . $related->id(),
          '#attributes' => [
            'id' => ['program-tab-' . $related->id()],
            'class' => [
              'tab-pane',
              'fade',
              'in',
              $first_class,
              $first_tab_class,
            ],
          ]
        ];
        if($first_class != '') {
          $first_class = '';
          $first_tab_class = '';
          }
        }


      $build['npx_tabs_wrapper']['npx_tabs'] = [
        '#theme' => 'item_list',
        '#list_type' => 'ul',
        '#items' => $tab_nav_items,
        '#attributes' => [
          'class' => ['nav nav-tabs d-flex flex-column justify-content-center flex-md-row flex-wrap']
        ],
        '#weight' => 10,
      ];
      if(count($node->field_npx_related_training) == 0) {
        $build['npx_tabs_wrapper']['npx_tabs']['#wrapper_attributes'] = [ 'class' => ['hidden']];
      }
    }

    $build['#attached'] = [
      'library' => [
        'npx_blocks/npx_blocks.landingprogramblock',
      ]
    ];

    $build['#cache']['max-age'] = 0;

    return $build;
  }

  public function getHours($closestDate) {
    $out = '';
  
    if($closestDate) {
      if($closestDate->get('field_npxtraining_date_hours')->value) {
        $out = $closestDate->get('field_npxtraining_date_hours')->value;
      } else {
        $count = count($closestDate->field_npxtraining_date_start);
        $days = 0;
  
        for($i = 0; $i < $count; $i++) {
          $days += self::calculateDays($closestDate->field_npxtraining_date_start[$i]->value, $closestDate->field_npxtraining_date_end[$i]->value);
        }
        $days_txt = $days > 1 ? ' dni | ' : ' dzień | ';
        $out =  $days . $days_txt . ($days * 8) . 'h';
      }
    }
  
    return $out;
  }

  public function getDatesDisplay($closestDate) {
    $out = null;
    if ($closestDate) {
      $count = count($closestDate->field_npxtraining_date_start);
      $out = TrainingDateHelper::prepareDateDisplay($closestDate->field_npxtraining_date_start[0]->value, $closestDate->field_npxtraining_date_end[$count-1]->value); 
    }

    return $out;
  }
public function getCity($closestDate) {
  $city = null;
  if ($closestDate && isset($closestDate->field_npxtraining_date_city->entity)) {
    $city = $closestDate->field_npxtraining_date_city->entity->name->value;
  }
  return $city;
}

  public function getPrice($node) {
    $base_price = $node->field_npxtraining_price->value;

    $price = $base_price;
    $dcount = count($node->field_npxtraining_dates);
    if ($dcount > 0) {
      $date = $node->field_npxtraining_dates[$dcount - 1];
    } else {
      return $out;
    }

    if($date->entity && $date->entity->isPublished()) {
      $fulldate = $this->entityTypeManager->getStorage('node')->load($date->entity->id());
      $termTime = $fulldate->get('field_npxtraining_date_start')->date->getTimestamp();
      if ($termTime > time()) {

        $fake_discount = 0;
        $fake_discount_upval = 0;
        if(NpxDiscountHelper::isParamFakeDiscountSet()) {
          $fake_discount = NpxDiscountHelper::getParamFakeDiscountVal();
          $fake_discount_upval = NpxDiscountHelper::getParamFakeDiscountUpVal();
        }
        $amount = 1;

        $code_info = [];

        $disable_regular = false;
        $discount_info = NpxDiscountHelper::getDiscountInfo($fulldate->id());
        $calc_info = NpxCalculatorHelper::getCalculation($base_price + $fake_discount + $fake_discount_upval, $amount, $discount_info, $code_info, $disable_regular);

        if ($calc_info['info']['has_active_first'] && $calc_info['info']['has_active_last']) {
          $price = min($calc_info['regular']['total_with_discount'], $calc_info['first']['total_with_discount'], $calc_info['last']['total_with_discount']);
        } else if ($calc_info['info']['has_active_first']) {
          $price = min($calc_info['regular']['total_with_discount'], $calc_info['first']['total_with_discount']);
        }  else if ($calc_info['info']['has_active_last']) {
          $price = min($calc_info['regular']['total_with_discount'], $calc_info['last']['total_with_discount']);
        } else {
          $price = $calc_info['regular']['total_with_discount'];
	      }
      }
    }
    return $price;
  }
  public function getDateLabels($closestDate): array
  {
    $labels = [];

    if ($closestDate && $this->checkDate($closestDate)) {
      foreach ($closestDate->get('field_npxtraining_date_form') as $val) {
        $label = npx_field_npxtraining_date_form_values()[$val->value];
        $labels[$val->value] = $label;
      }
    }

    return $labels;
  }

  public function checkDate(NodeInterface $date_node) {
    $now = new \DateTime(date(self::DATE_FORMAT));
    $date = \DateTime::createFromFormat(self::DATE_FORMAT, substr($date_node->field_npxtraining_date_start->value, 0, 10));
    $date->setTime(0,0,0);
    
    return $date > $now;
  }
  public function getClosestFutureDate($node) {
    $closestDate = null;
    $closestDiff = PHP_INT_MAX;
  
    foreach ($node->field_npxtraining_dates as $date) {
      if($date->entity && $date->entity->isPublished()) {
        $fulldate = $this->entityTypeManager->getStorage('node')->load($date->entity->id());
        $termTime = $fulldate->get('field_npxtraining_date_start')->date->getTimestamp();
        if ($termTime > time()) {
          $diff = $termTime - time();
          if ($diff < $closestDiff) {
            $closestDiff = $diff;
            $closestDate = $fulldate;
          }
        }
      }
    }
  
    return $closestDate;
  }


  static public function calculateDays($start, $end) {
    $date_start = new \DateTime($start);
    $date_end = new \DateTime($end);

    $date_diff = $date_start->diff($date_end);

    return $date_diff->format('%d') + 1;
  }
  
}
