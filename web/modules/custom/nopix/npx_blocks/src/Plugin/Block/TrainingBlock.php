<?php

/**
 * @file
 * Contains \Drupal\npx_blocks\Plugin\Block\TrainingBlock.
 */

namespace Drupal\npx_blocks\Plugin\Block;

use <PERSON><PERSON>al\Core\Url;
use <PERSON>upal\Core\Block\BlockBase;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Plugin\ContainerFactoryPluginInterface;
use Drupal\Core\Routing\RouteMatchInterface;
use Drupal\npx_blocks\TrainingDateHelper;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
// use Drupal\Core\Link;
// use Drupal\npx_training_form\Form\TrainingForm;

/**
 * Provides a 'TrainingBlock' block.
 *
 * @Block(
 *  id = "npx_training_block",
 *  admin_label = @Translation("Npx Training block"),
 * )
 */
class TrainingBlock extends BlockBase implements ContainerFactoryPluginInterface {

  /**
   * Drupal\Core\Entity\EntityTypeManager definition.
   *
   * @var \Drupal\Core\Entity\EntityTypeManager
   */
  protected $entityTypeManager;

  /**
   * The current request.
   *
   * @var \Symfony\Component\HttpFoundation\Request
   */
  protected $request;
  
  /**
   * The current route match.
   *
   * @var \Drupal\Core\Routing\RouteMatchInterface
   */
  protected $routeMatch;
  
  /**
   * Construct.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param string $plugin_definition
   *   The plugin implementation definition.
   */
  public function __construct(
        array $configuration,
        $plugin_id,
        $plugin_definition,
        EntityTypeManagerInterface $entity_type_manager,
        Request $request,
        RouteMatchInterface $route_match
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->entityTypeManager = $entity_type_manager;
    $this->request = $request;
    $this->routeMatch = $route_match;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('entity_type.manager'),
      $container->get('request_stack')->getCurrentRequest(),
      $container->get('current_route_match')
    );
  }


  /**
   * {@inheritdoc}
   */
  public function blockForm($form, FormStateInterface $form_state) {
    $form['npx_block_details'] = array(
      '#type' => 'text_format',
      '#title' => $this->t('Szczegóły organizacyjne'),
      '#description' => $this->t('Tekst wyświetlany w colorboksie.'),
      '#default_value' => isset($this->configuration['npx_block_details']['value']) ? $this->configuration['npx_block_details']['value'] : '',
      '#format' => isset($this->configuration['npx_block_details']['format']) ? $this->configuration['npx_block_details']['format'] : null,
      '#weight' => '90',
    );
    $form['npx_block_footer'] = array(
      '#type' => 'text_format',
      '#title' => $this->t('Stopka bloku'),
      '#description' => $this->t('Dane osoby kontaktowej.'),
      '#default_value' => isset($this->configuration['npx_block_footer']['value']) ? $this->configuration['npx_block_footer']['value'] : '',
      '#format' => isset($this->configuration['npx_block_footer']['format']) ? $this->configuration['npx_block_footer']['format'] : null,
      '#weight' => '100',
    );

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function blockSubmit($form, FormStateInterface $form_state) {
    $this->configuration['npx_block_details'] = $form_state->getValue('npx_block_details');
    $this->configuration['npx_block_footer'] = $form_state->getValue('npx_block_footer');
  }

  /**
   * {@inheritdoc}
   */
  public function build() {
    $build = [];
    if (!$node = $this->routeMatch->getParameter('node')) {
      //dpm($node);
      return $build;
    }
//     $base_price = $node->field_npxtraining_price->value;
//     $total_discount = $base_price + (($base_price - ($base_price * TrainingForm::DISCOUNT_USER)) * 9);
//     $discount_price = $total_discount / 10;
//     $discount_price -= $discount_price * TrainingForm::DISCOUNT_PAYMENT;
//     $discount_price = round($discount_price);
    $block_info = $node->field_npxtraining_block_info->value;

    $date_nodes = [];
    $date_year_nodes = [];
    $dates = [];
    
    foreach ($node->field_npxtraining_dates as $val) {
      if($val->entity) {
        $date_node = $this->entityTypeManager->getStorage('node')->load($val->entity->id());
        if (TrainingDateHelper::checkDate($date_node, true)) {
          $tmp_date = substr($date_node->field_npxtraining_date_start->value, 0, 10);
          $date_nodes[$tmp_date] = $date_node;
          $dates[] = $tmp_date;
        }
        if (TrainingDateHelper::checkDate($date_node, true)) {
          $tmp_year = substr($date_node->field_npxtraining_date_start->value, 0, 4);
          $date_year_nodes[$tmp_year][] = $date_node;
        }
      }
    }
    //$out = '';
    //$closest_date = TrainingDateHelper::findClosestDate($dates);
    
    
    if(isset($node->field_npx_related_training)) {
      foreach ($node->field_npx_related_training as $rel) {
        if($rel->entity) {
          $related = $this->entityTypeManager->getStorage('node')->load($rel->entity->id());
          
          foreach ($related->field_npxtraining_dates as $val) {
            if($val->entity) {
              $date_node = $this->entityTypeManager->getStorage('node')->load($val->entity->id());
              if (TrainingDateHelper::checkDate($date_node, true)) {
                $tmp_date = substr($date_node->field_npxtraining_date_start->value, 0, 10) . 'rel' . $val->entity->id();
                $date_node->npxParent = $rel->entity->id();
                $date_nodes[$tmp_date] = $date_node;
                $dates[] = $tmp_date;
                $tmp_year = substr($date_node->field_npxtraining_date_start->value, 0, 4);
                $date_year_nodes[$tmp_year][] = $date_node;
              }
            }
          }
          
        }
      }
    }
    $closest_date = TrainingDateHelper::findClosestDate($dates);
    
    
    $header_inline_style = '';
    if ($node->field_npxtraining_block_img->entity) {
      $header_inline_style = sprintf('background-image: url("%s");', $node->field_npxtraining_block_img->entity->url());
    }
    
    $build['npx_all_open'] = [
      '#markup' => '<div class="npx-training-block-all">'
    ];
    
    //Header
    $build['npx_training_block_header_wrapper'] = [
      '#type' => 'container',
      '#attributes' => [
        'class' => ['npx-training-block-header-wrapper'],
        'style' => [$header_inline_style]
      ],
      '#cache' => [
        'max-age' => 0,
      ]
    ];
    $wrp_class = $node->field_npxtraining_closed->value ? 'npx-training-block-dates-closed-wrapper' : 'npx-training-block-dates-wrapper';
    $build['npx_training_block_dates_wrapper'] = [
      '#type' => 'container',
      '#attributes' => [
        'class' => [$wrp_class]
      ],
      '#cache' => [
        'max-age' => 0,
      ]
    ];
    
    ksort($date_nodes);
    
    foreach ($date_nodes as $date_index => $date_elem) {
//       $base_price = $node->field_npxtraining_price->value;
      $block_info = $node->field_npxtraining_block_info->value;
      
      if(isset($date_elem->npxParent)) {
        $related = $this->entityTypeManager->getStorage('node')->load($date_elem->npxParent);
        $block_info = $related->field_npxtraining_block_info->value;
//         $base_price = $related->field_npxtraining_price->value;
      }
      
//       if($date_elem->field_npxtraining_date_price->value) {
//         $base_price = $date_elem->field_npxtraining_date_price->value;
//       }
      
//       $total_discount = $base_price + (($base_price - ($base_price * TrainingForm::DISCOUNT_USER)) * 9);
//       $discount_price = $total_discount / 10;
//       $discount_price -= $discount_price * TrainingForm::DISCOUNT_PAYMENT;
//       $discount_price = round($discount_price);
      
      $city = $date_elem->field_npxtraining_date_city->entity->name->value;
      $closest_date_formatted = TrainingDateHelper::prepareDateDisplayNode($date_elem);
      
      

      //Disable when closed flag is set
      if (!$node->field_npxtraining_closed->value && $date_elem !== null) {
        //Dates
        $build['npx_training_block_dates_wrapper'][$date_index] = [
          '#type' => 'container',
          '#attributes' => [
            'class' => ['npx-training-block-date-elem', 'npx-slidedate-elem']
          ],
          '#cache' => [
            'max-age' => 0,
          ]
        ];

        if ($date_index == $closest_date) {
          $build['npx_training_block_dates_wrapper'][$date_index]['#attributes']['class'][] = 'active';
        }
        
        $days_diff = TrainingDateHelper::calculateDaysNode($date_elem);
        $days_txt = 'dni';
        if ($days_diff == 1) {
          $days_txt = 'dzień';
        }
        $hours = TrainingDateHelper::calculateHoursNode($date_elem);
        
        $build['npx_training_block_dates_wrapper'][$date_index]['npx_dates_left'] = [
          '#markup' => sprintf('<div class="npx-left"><div class="npx-days">%s %s szkolenia, %sh | %s</div><div class="npx-closest">%s</div></div>', $days_diff, $days_txt, $hours, $city, $closest_date_formatted),
          '#cache' => [
            'max-age' => 0,
          ]
        ];
        
        $guarant_info = '';
        if($date_elem->field_npxtraining_date_guarant->value) {
          $guarant_info = 'termin gwarantowany';
        } else {
          $guarant_info = 'zagwarantuj termin swoim zapisem';
        }
        $build['npx_training_block_dates_wrapper'][$date_index]['npx_dates_left']['guarant_info'] = [
            '#markup' => '<div class="npx-guarant-info">' . $guarant_info . '</div>',
            '#cache' => [
                'max-age' => 0,
            ]
        ];
        
        //Kalendarz popover
        $build['npx_training_block_dates_wrapper'][$date_index]['npx_dates_left']['calendar-popover'] = [
          //'#markup' => '<div class="npx-details-wrapper"><a href="#" class="npx-details" data-colorbox-inline="#npx-colorbox-inline-dates-list">Kalendarz colorbox</a></div>',
          '#markup' => '<div class="npx-details-wrapper kalendarz-popover"><a class="npx-details">Wszystkie terminy</a></div>',
          '#cache' => [
              'max-age' => 0,
          ]
        ];
        $build['npx_training_block_dates_wrapper'][$date_index]['npx_training_block_line'] = [
          '#type' => 'container',
          '#attributes' => [
              'class' => ['npx-training-block-line']
          ],
          '#cache' => [
              'max-age' => 0,
          ]
        ];
        
      //Disable when closed flag is set
        if ($node->field_npxtraining_promotion->value) {
          $build['npx_training_block_dates_wrapper'][$date_index]['npx_training_promotion']['npx_promotion'] = [ 
            '#markup' => sprintf('<div class="npx-promotion"><div class="field">%s</div></div>', $node->field_npxtraining_promotion->value),
          ];
          $build['npx_training_block_dates_wrapper'][$date_index]['npx_training_block_line']['npx_price'] = [ 
//             '#markup' => sprintf('<div class="npx-price"> <span>%s zł</span> do <span>%s zł</span></div>', $discount_price, $base_price),
            '#markup' => '<div class="npx-price"></div>',
          ];
        } else {
          if (!$node->field_npxtraining_closed->value) {
            $build['npx_training_block_dates_wrapper'][$date_index]['npx_training_block_line']['npx_price'] = [ 
//               '#markup' => sprintf('<div class="npx-price"> <span>%s zł</span> do <span>%s zł</span></div>', $discount_price, $base_price),
              '#markup' => '<div class="npx-price"></div>',
            ];
          } else {
            $build['npx_training_block_dates_wrapper'][$date_index]['npx_training_block_line']['npx_price'] = [ 
              '#markup' => '<div class="npx-price">Szkolenie realizowane na zamówienie</div>',
            ];
          }
        }
        $build['npx_training_block_dates_wrapper'][$date_index]['npx_training_block_line']['npx_block_extra'] = [
          '#markup' => sprintf('<div class="npx-block-extra"><img src="/themes/ansoak/css/i/ekstra.svg" alt="ekstra"/>Extra</div>', $block_info),
        ];
        //Training details
        
        $benefity = '<ul>
          <li><strong>Trener wspiera Cię telefonicznie bez limitu godzin</strong><BR/>
          Bo przecież po szkoleniu zaczynamy widzieć więcej, więc powstają kolejne pytania. No i pomocna dłoń przydaje się, by połączyć z Twoim życiem ogrom wiedzy i technik wyniesionych z naszego szkolenia.
          </li>
          <li><strong><span class="npx-days-to-change">90</span> minut treningu indywidualnego</strong><BR/>
          Chcemy, aby był taki czas, gdy trener jest tylko dla Ciebie, Uczestniku, i nikogo więcej - byś mógł nie przejmując się niczyją obecnością zająć się najtrudniejszymi dla Ciebie sprawami, byś mógł przećwiczyć z trenerem techniki sam na sam i dostać spersonalizowany feedback, by cała uwaga trenera była tylko dla Ciebie. Dlatego w ciągu roku od szkolenia dajemy Ci możliwość wykorzystania 90 min. indywidualnego treningu - sam na sam z trenerem, twarzą w twarz na spotkaniu u nas w biurze!
          </li>
          <li><strong>Roczny follow-up = ciekawe materiały utrwalające wiedzę</strong><BR/>
          Nowo zdobytą wiedzę najefektywniej zapamiętujemy, gdy w pierwszych miesiącach regularnie ją odświeżamy na różne sposoby oraz gdy wykorzystujemy ją do rozwiązywania nowych problemów. Dlatego przez rok będziemy przysyłać Ci mailowo, Uczestniku, najciekawsze i najważniejsze treści ze szkolenia pod różnorodną postacią:
            <ul>
              <li>Study case – opisujemy w nich przykłady użycia technik omawianych na szkoleniu w sytuacjach biznesowych.</li>
              <li>Materiały z teorią – zasady, techniki, narzędzia omawiane na szkoleniu wraz z przykładową zawartością komunikatów.</li>
              <li>Quizy – sprawdzają znajomość zagadnień poruszanych na szkoleniu oraz wykorzystania tej wiedzy do rozwiązywania problemów.</li> 
              <li>Pięciosekundówki – jedno, dwuzdaniowe najważniejsze kwintesencje, wnioski i reguły dotyczące zagadnień poruszanych na szkoleniu.</li>
            </ul>
          </li>
        </ul>';

        $build['npx_training_block_dates_wrapper'][$date_index]['npx_benefits_info'] = [
          '#markup' => '<div class="hidden"><div id="npx-benefits-info">'.$benefity.'</div></div>',
          '#cache' => [
              'max-age' => 0,
            ]
        ];
        $build['npx_training_block_dates_wrapper'][$date_index]['npx_training_block_line']['npx_block_info'] = [
          '#markup' => sprintf('<div class="npx-block-info">%s<div class="questionmark benefity-popover"><a><img src="/themes/ansoak/css/i/question-mark.svg" alt="pytanie"></a></div></div>', self::prepareDaysInfo($block_info, $days_diff)),
        ];
        $build['npx_training_block_dates_wrapper'][$date_index]['npx_form_button_wrapper'] = [
            '#type' => 'container',
            '#attributes' => [
                'class' => ['npx-training-block-button-wrapper']
            ],
            '#cache' => [
                'max-age' => 0,
            ]
        ];
        
        if($date_elem->field_npxtraining_date_available->value != 'nie') {
          if(isset($date_elem->npxParent)) {
            $form_url = Url::fromRoute('npx_training_form.training_form', ['training_id' => $date_elem->npxParent, 'training_date_id' => $date_elem->id()]);
          } else {
            $form_url = Url::fromRoute('npx_training_form.training_form', ['training_id' => $node->id(), 'training_date_id' => $date_elem->id()]);
          }
          $build['npx_training_block_dates_wrapper'][$date_index]['npx_form_button_wrapper']['npx_form_button'] = [
            '#type' => 'link',
            '#url' => $form_url,
//             '#title' => t('Oblicz cenę / zapisz uczestnika'),
            '#title' => t('Zapisz się na szkolenie'),
            '#attributes' => [
              'class' => ['npx-form-button']
            ],
            '#cache' => [
              'max-age' => 0,
            ]
          ];
        } else {
          $build['npx_training_block_dates_wrapper'][$date_index]['npx_form_button_wrapper']['npx_form_button'] = [
            '#markup' => '<div class="npx-availability-info">Brak wolnych miejsc.</div>',
          ];
        }
      }
      if (!$node->field_npxtraining_closed->value) {
        // $build['npx_training_block_dates_wrapper'][$date_index]['npx_training_block_details_button'] = [
        //     '#markup' => '<div class="npx-details-wrapper"><a href="#" class="npx-details" data-colorbox-inline="#npx-colorbox-inline-details">Szczegóły organizacyjne</a></div>',
        //     '#cache' => [
        //         'max-age' => 0,
        //     ]
        // ];
        $build['npx_training_block_dates_wrapper'][$date_index]['npx_training_block_details_button'] = [
          '#markup' => '<div class="npx-details-wrapper szczegoly-popover"><a class="npx-details">Szczegóły organizacyjne</a></div>',
          '#cache' => [
              'max-age' => 0,
          ]
        ];
      }
    
    }//end foreach
    
    //Footer
    $build['npx_training_block_footer'] = [
      '#markup' => '<div class="npx-training-block-footer">' . $this->configuration['npx_block_footer']['value'] . '</div>',
      '#cache' => [
        'max-age' => 0,
      ]
    ];
    
    $build['npx_training_block_post_footer'] = [
        '#type' => 'container',
        '#attributes' => [
            'class' => ['npx-training-block-post-footer']
        ],
        '#cache' => [
            'max-age' => 0,
        ]
    ];
    
    $build['npx_all_close'] = [
        '#markup' => '</div>'
    ];
    
    //Disable when closed flag is set
    if (!$node->field_npxtraining_closed->value) {
      $this->buildTrainingDatesList($build, $date_year_nodes, $node);
    }
    
    //Training details
    $build['npx_training_details'] = [
        '#markup' => '<div class="hidden"><div id="npx-colorbox-inline-details">' . $this->configuration['npx_block_details']['value'] . '</div></div>',
        '#cache' => [
            'max-age' => 0,
        ]
    ];

    
    
    $build['#attached'] = [
      'library' => [
        'npx_blocks/npx_blocks.trainingblock'
      ]
    ];
    
    return $build;
  }

  public function buildTrainingDatesList(&$build, &$date_nodes, $node) {
    $ignore_availability = true;
    $out = '<div class="hidden calendar-wrapper"><div id="npx-colorbox-inline-dates-list">';
    ksort($date_nodes);
    
    $out .= '<div class="npx-dates-list-wrapper">';
    
    $d_index = 0;
    
    foreach($date_nodes as $year => $dates) {
      usort($dates, function($a, $b) {
        return strcmp($a->field_npxtraining_date_start[0]->value, $b->field_npxtraining_date_start[0]->value);
      });
      $out .= sprintf('<h3><a name="terminy"></a>Terminy w %s roku:</h3>', $year);
      $out .= '<ul>';
      foreach($dates as $date) {
//         $out .= '<li>';
        $multi_events = '';
        $out .= $this->prepareLiForCalendar($d_index, $date);
        $out .= '<a class="npx-change-date" href="#" data-npx-index="' . $d_index . '">';
        
        $date_display = '';
        $city = $date->field_npxtraining_date_city->entity->name->value;
        
        $days_diff = TrainingDateHelper::calculateDaysNode($date);
        $days_txt = 'dni';
        if ($days_diff == 1) {
          $days_txt = 'dzień';
        }
        
        $hours = TrainingDateHelper::calculateHoursNode($date);
        
        foreach ($date->field_npxtraining_date_start as $key => $multi_dates) {
          $date_display .= TrainingDateHelper::prepareDateDisplay($multi_dates->value, $date->field_npxtraining_date_end[$key]->value, false);
          $date_display .= sprintf(' (%s %s, %sh) %s , ', $days_diff, $days_txt, $hours, $city);
          if($key > 0) {
            $multi_events .= $this->prepareLiForCalendar($d_index, $date, $key);
            $multi_events .= '</li>';
          }
        }
        $date_display = substr($date_display, 0, -2);
        $discount = '';
        if ($date->field_npxtraining_date_dis_per->value) {
          $discount = sprintf(', rabat %s%%', $date->field_npxtraining_date_dis_per->value);
        }
        if ($date->field_npxtraining_date_dis_fxd->value) {
          $discount = sprintf(', rabat %szł', $date->field_npxtraining_date_dis_fxd->value);
        }
        $out .= sprintf('<strong>%s%s</strong>', $date_display, $discount);
        if($date->field_npxtraining_date_available->value == 'tak'
            || $date->field_npxtraining_date_available->value == 'ostatnie'
            || $ignore_availability) {
          // if($date->field_npxtraining_date_guarant->value) {
          //   $out .= '<strong> - TERMIN GWARANTOWANY</strong>';
          // } else {
          //   $out .= sprintf(' - termin zostanie objęty gwarancją przy zapisie %s uczestnika', $node->field_npxtraining_min_guaranted->value);
          // }
          if($date->field_npxtraining_date_available->value == 'ostatnie') {
            $out .= '<strong> - OSTATNIE WOLNE MIEJSCE</strong>';
          }
        } else {
          $out .= '<strong> - BRAK WOLNYCH MIEJSC</strong>';
        }
//         $form_url = Url::fromRoute('npx_training_form.training_form', ['training_id' => $node->id(), 'training_date_id' => $date->id()]);
//         $out .= render(Link::fromTextAndUrl('Zapisz uczestnika', $form_url)->toRenderable());
        $out .= '</a>';
        $out .= '</li>';
        $out .= $multi_events;
        $d_index++;
      }
      $out .= '</ul>';
    }
    $out .= '</div>';
    $out .= '<div class="monthly" id="npx-monthly-calendar"></div>';
    $out .= '<div class="calender-legend">
    <div class="termin gwarantowany">Termin gwarantowany</div>
    <div class="termin niegwarantowany">Termin zagwarantujemy wraz z zapisem pierwszego Uczestnika<p class="dopisek">wyjątek: szkolenie AC/DC certyfikacja dla asesorów - gwarancja od 4 uczestnika</p></div>
    <h4>W 4GROW nie odwołujemy szkoleń!</h4>
    </div>';
    $out .= '</div></div>';
    $build['npx_dates_list'] = [
      '#markup' => $out
    ]; 
  }
  
  public function prepareLiForCalendar($index, $node, $date_index = 0) {
    $id = $index;
    $name = '';
    $start = substr($node->field_npxtraining_date_start[$date_index]->value,0,10);
    $end = substr($node->field_npxtraining_date_end[$date_index]->value,0,10);
    
    $quarant_class = '';
    if($node->field_npxtraining_date_guarant->value) {
      $quarant_class = 'npx-quarant';
    }
    
    
    if ($date_index > 0) {
      $elem = '<li class="npx-event-elem hidden" ';
    } else {
      $elem = '<li class="npx-event-elem" ';
    }
    $elem .= 'data-npx-id="' . $id . '" ';
    $elem .= 'data-npx-name="' . $name . '" ';
    $elem .= 'data-npx-start="' . $start . '" ';
    $elem .= 'data-npx-end="' . $end . '" ';
    $elem .= 'data-npx-quarant="' . $quarant_class . '" ';
    
    if(isset($node->npxParent)) {
      $elem .= 'data-npx-hide="true" ';
    }
    
    $elem .= '>';
    
    return $elem;
  }
  
  public static function prepareDaysInfo($text, $nr) {
    $min = $nr == 1 ? '60' : '90';
    return str_replace('##DAYS##', $min, $text);
  }
}
