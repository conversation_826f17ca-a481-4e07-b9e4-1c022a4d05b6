/**
 * @file
 * Attaches the behaviors for theCalendarBlock.
 */

(function ($, Drupal, drupalSettings) {

  'use strict';

  /**
   * @type {Drupal~behavior}
   *
   * @prop {Drupal~behaviorAttach} attach
   *   Adds behaviors to the npx_blocks CoachingBlock.
   */
  Drupal.behaviors.npxBlocksCalendarBlock = {
    attach: function (context) {
    },
    npxOnResize: function () {
      var $cal = $('#fullcalendar');
      var width = $(window).width();
      if(width < 768) {
        $cal.fullCalendar('option', { weekends: false });
      } else {
        $cal.fullCalendar('option', { weekends: true });
      }
    }
  };
})(jQuery, Drupal, drupalSettings);
