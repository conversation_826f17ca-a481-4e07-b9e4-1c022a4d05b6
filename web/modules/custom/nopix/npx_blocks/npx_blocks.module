<?php

use Drupal\node\NodeInterface;

function npx_blocks_page_attachments_alter(array &$page) {
  if ($node = \Drupal::routeMatch()->getParameter('node')) {
    if($node instanceof NodeInterface && $node->getType() == 'coaching') {
      if(!$node->field_npxtraining_block_img->entity) {
        return;
      }
      $background_url = $node->field_npxtraining_block_img->entity->createFileUrl();
      $background = 'url("' . $background_url . '") no-repeat scroll center center/cover';
      $page['#attached']['html_head'][] = [
        [
          '#tag' => 'style',
          '#value' => 'body.page-node-type-coaching #block-npxcoachingblock .content::after { background: ' . $background . '; }',
        ],
        'npx_blocks'
      ];
    }
  }
}

/**
 * Implements hook_theme().
 */
function npx_blocks_theme() {
  return [
    'npx_blocks_program_tab' => [
      'variables' => [
        'hours' => null,
        'title' => null,
        'content' => null,
        'image' => null,
        'image_alt' => null,
        'image_title' => null,
        'webinar' => null,
        'online' => null,
        'stationary_only' => null,
        'closed' => null,
        'price' => null,
        'labels' => null,
       	'index' => null,
        'alias' => null,
        'rating' => null,
        'rating_count' => null,
      ]
    ],
    'npx_blocks_program_more_tab' => [
      'variables' => [
        'hours' => null,
        'title' => null,
        'content' => null,
        'image' => null,
        'image_alt' => null,
        'image_title' => null,
        'webinar' => null,
        'online' => null,
        'stationary_only' => null,
        'closed' => null,
        'price' => null,
        'labels' => null,
       	'index' => null,
        'alias' => null,
        'rating' => null,
        'rating_count' => null,
      ]
    ],
    'npx_blocks_landing_tab' => [
      'variables' => [
        'hours' => null,
        'title' => null,
        'content' => null,
        'image' => null,
        'image_alt' => null,
        'image_title' => null,
        'webinar' => null,
        'online' => null,
      	'stationary_only' => null,
        'closed' => null,
        'labels' => null,
        'price' => null,
        'dates' => null,
        'city' => null,
        'room' => null,
        'date_start' => null,
        'date_end' => null,
        'has_links'=> null,
        'rating' => null,
        'rating_count' => null,
      ]
    ],
    'npx_blocks_term_table' => [
      'variables' => [
        'node' => null,
        'stationary' => null,
        'online' => null,
        'allTerms' => null,
        'middleItem' => null,
        'seminar' => null,
        'termDates' => null
      ]
    ],
    'npx_blocks_term_table_event' => [
      'variables' => [
        'node' => null,
        'stationary' => null,
        'online' => null,
        'allTerms' => null,
        'middleItem' => null,
        'seminar' => null,
        'termDates' => null
      ]
    ],
    'npx_blocks_term_table_course' => [
      'variables' => [
        'node' => null,
        'stationary' => null,
        'online' => null,
        'allTerms' => null,
        'middleItem' => null,
        'seminar' => null,
        'termDates' => null
      ]
    ],
    'npx_blocks_inner_menu' => [
      'variables' => [
        'items' => null,
      ]
    ],
  ];
}
/**
 * Implements hook_mail().
 */
function npx_blocks_mail($key, &$message, $params) {
  switch ($key) {
    case 'unpublished_node_notification':
      $message['subject'] = $params['subject'];
      $message['body'][] = $params['message'];
      break;
  }
}