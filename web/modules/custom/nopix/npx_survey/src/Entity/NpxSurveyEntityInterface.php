<?php

namespace Drupal\npx_survey\Entity;

use <PERSON><PERSON>al\Core\Entity\ContentEntityInterface;
use <PERSON><PERSON>al\Core\Entity\EntityChangedInterface;
use <PERSON><PERSON><PERSON>\user\EntityOwnerInterface;

/**
 * Provides an interface for defining Npx survey entity entities.
 *
 * @ingroup npx_survey
 */
interface NpxSurveyEntityInterface extends ContentEntityInterface, EntityChangedInterface, EntityOwnerInterface {

  // Add get/set methods for your configuration properties here.

  /**
   * Gets the Npx survey entity name.
   *
   * @return string
   *   Name of the Npx survey entity.
   */
  public function getName();

  /**
   * Sets the Npx survey entity name.
   *
   * @param string $name
   *   The Npx survey entity name.
   *
   * @return \Drupal\npx_survey\Entity\NpxSurveyEntityInterface
   *   The called Npx survey entity entity.
   */
  public function setName($name);

  /**
   * Gets the Npx survey entity creation timestamp.
   *
   * @return int
   *   Creation timestamp of the Npx survey entity.
   */
  public function getCreatedTime();

  /**
   * Sets the Npx survey entity creation timestamp.
   *
   * @param int $timestamp
   *   The Npx survey entity creation timestamp.
   *
   * @return \Drupal\npx_survey\Entity\NpxSurveyEntityInterface
   *   The called Npx survey entity entity.
   */
  public function setCreatedTime($timestamp);

  /**
   * Returns the Npx survey entity published status indicator.
   *
   * Unpublished Npx survey entity are only visible to restricted users.
   *
   * @return bool
   *   TRUE if the Npx survey entity is published.
   */
  public function isPublished();

  /**
   * Sets the published status of a Npx survey entity.
   *
   * @param bool $published
   *   TRUE to set this Npx survey entity to published, FALSE to set it to unpublished.
   *
   * @return \Drupal\npx_survey\Entity\NpxSurveyEntityInterface
   *   The called Npx survey entity entity.
   */
  public function setPublished($published);

}
