<?php

namespace Drupal\npx_freshmail;
use <PERSON><PERSON>al\Core\Config\ConfigFactoryInterface;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use FreshMail\RestApi as FmRestApi;

/**
 * Class NpxFreshmailManager.
 */
class NpxFreshmailManager {
  const TAXONOMY_VID = 'npx_freshmail_lists';
  const FIELD_LIST_ID = 'field_npx_freshmail_list_id';
  const FIELD_NAME = 'field_npx_freshmail_name_id';

  /**
   * Drupal\Core\Config\ConfigFactoryInterface definition.
   *
   * @var \Drupal\Core\Config\ConfigFactoryInterface
   */
  protected $configFactory;

  /**
   * Drupal\Core\Entity\EntityTypeManagerInterface definition.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * FreshMail\RestApi definition.
   * @var \FreshMail\RestApi
   */
  protected $fmRest;

  /**
   * Constructs a new NpxFreshmailManager object.
   */
  public function __construct(ConfigFactoryInterface $config_factory, EntityTypeManagerInterface $entity_type_manager) {
    $this->configFactory = $config_factory;
    $this->entityTypeManager = $entity_type_manager;

    $config = $this->getFreshmailConfig();

    $this->fmRest = new FmRestApi();
    $this->fmRest->setApiKey($config->get('freshmail_api_key'));
    $this->fmRest->setApiSecret($config->get('freshmail_api_secret_key'));
  }

  public function addSubscriber(NpxFreshmailSubscriber $subscriber, NpxFreshmailList $list=null) : bool {
    $config = $this->getFreshmailConfig();

    $data = [
      'email' => $subscriber->getEmail(),
      'list' => $list ? $list->getListId() : $config->get('freshmail_default_list_id'),
      'state' => 1
    ];

    $name_field_id = $list ? $list->getNameFieldId() : $config->get('freshmail_default_name_field_id');
    $subscriber_name = $subscriber->getName();

    if($name_field_id && $subscriber_name) {
      $data['custom_fields'] = [
        'imie' => $subscriber_name,
        'zwrot' => 'Cześć ' . $subscriber_name,
      ];
    }

    $area = $subscriber->getArea();
    if (!empty($area)) {
      $data['custom_fields']['obszar_tematyczny'] = $area;
    }

    $title = $subscriber->getTitle();
    if (!empty($title)) {
      $data['custom_fields']['tytul'] = $title;
    }

    $kind = $subscriber->getKind();
    if (!empty($kind)) {
      $data['custom_fields']['otwarte_zamkniete'] = $kind;
    }

    $startDate = $subscriber->getStartDate();
    if (!empty($startDate)) {
      $data['custom_fields']['data_rozpoczecia_szkolenia'] = $startDate;
    }

    $endDate = $subscriber->getEndDate();
    if (!empty($endDate)) {
      $data['custom_fields']['data_zakonczenia_szkolenia'] = $endDate;
    }

    $surname = $subscriber->getSurname();
    if (!empty($surname)) {
      $data['custom_fields']['nazwisko'] = $surname;
    }

    $company = $subscriber->getCompany();
    if (!empty($company)) {
      $data['custom_fields']['firma'] = $company;
    }

    $source = $subscriber->getSource();
    if (!empty($source)) {
      $data['custom_fields']['zrodlo'] = $source;
    }

    try {
      $this->fmRest->doRequest('subscriber/add', $data);

      return true;
    } catch (\Exception $e) {
      return false;
    }
  }

  public function addMultipleSubscribers(array $subscribers, NpxFreshmailList $list=null) {
    /** @var \Drupal\npx_freshmail\NpxFreshmailSubscriber $subscriber */
    foreach($subscribers as $subscriber) {
      $this->addSubscriber($subscriber, $list);
    }
  }

  public function getFreshmailLists() : array {
    $lists = [];

    $terms = $this->getTaxonomyTerms();

    /** @var \Drupal\taxonomy\Entity\Term $term */
    foreach($terms as $term) {
      if($term->isPublished()) {
        $name = $term->getName();
        $list_id = $term->get(self::FIELD_LIST_ID)->value;
        $name_field = $term->get(self::FIELD_NAME)->value;
        $tid = $term->id();

        $lists[] = new NpxFreshmailList($name, $list_id, $name_field, $tid);
      }
    }

    return $lists;
  }

  public function getFreshmailListsByTid(array $tids) : array {
    $lists = [];

    $terms = $this->getTaxonomyTermsByTid($tids);

    /** @var \Drupal\taxonomy\Entity\Term $term */
    foreach($terms as $term) {
      if($term->isPublished()) {
        $name = $term->getName();
        $list_id = $term->get(self::FIELD_LIST_ID)->value;
        $name_field = $term->get(self::FIELD_NAME)->value;
        $tid = $term->id();

        $lists[] = new NpxFreshmailList($name, $list_id, $name_field, $tid);
      }
    }

    return $lists;

  }

  public function getTotalSubscribersCount() : int {
    $count = 0;

    try {
      $res = $this->fmRest->doRequest('subscribers_list/lists');

      if(isset($res['lists'])) {
        foreach($res['lists'] as $list) {
          $count += (int)$list['subscribers_number'];
        }
      }

    } catch (\Exception $e) {
      return -1;
    }


    return $count;
  }

  public function getFreshmailListByListID(string $freshmail_list_id) : ?NpxFreshmailList {
    $entities = $this->entityTypeManager->getStorage('taxonomy_term')->loadByProperties([self::FIELD_LIST_ID => $freshmail_list_id]);

    $term = reset($entities);

    if($term) {
      $list = new NpxFreshmailList(
        $term->get('name')->value,
        $term->get(self::FIELD_LIST_ID)->value,
        $term->get(self::FIELD_NAME)->value,
        $term->id());

      return $list;
    } else {
      return null;
    }
  }

  protected function getFreshmailConfig() {
    return $this->configFactory->get('npx_freshmail.npxfreshmailsettings');
  }

  protected function getTaxonomyTerms() : array {
    /* \Drupal\taxonomy\TermStorage $term_storage */
    $term_storage = $this->entityTypeManager->getStorage('taxonomy_term');

    return $term_storage->loadTree(self::TAXONOMY_VID, 0, null, true);
  }

  protected function getTaxonomyTermsByTid(array $tids) {
    $terms = [];
    $terms = $this->entityTypeManager->getStorage('taxonomy_term')->loadMultiple($tids);

    return $terms;
  }
}
