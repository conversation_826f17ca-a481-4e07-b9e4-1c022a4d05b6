<?php

/**
 * @file
 * Contains \Drupal\npx_training_form\Form\TrainingForm.
 */

namespace Drupal\npx_training_form\Form;

use Drupal\Core\Form\FormBase;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use <PERSON>upal\Core\Extension\ModuleHandlerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use <PERSON>upal\node\NodeInterface;
use Drupal\Component\Utility\UrlHelper;
use Drupal\Core\Ajax\AjaxResponse;
use Drupal\Core\Ajax\ReplaceCommand;
// use Drupal\Core\Ajax\InvokeCommand;
use Drupal\Core\Render\Markup;
use Drupal\npx_training_form\NpxTrainingFormHelper;
use Drupal\npx_training_form\NpxCouponHelper;
use Drupal\npx_training_form\NpxDiscountHelper;
use Drupal\npx_training_form\NpxCalculatorHelper;
use Drupal\npx\NpxAmountHelper;
use Drupal\npx\NpxTraining;
use Drupal\npx\NpxTrainingDate;
use Drupal\npx\NpxDiscount;
use Drupal\npx\NpxCounter;
use Drupal\npx\NpxLangHelper;
use Drupal\npx\NpxWord;
use Drupal\npx\NpxDateUtils;
use Drupal\Core\Block\BlockManagerInterface;
use Drupal\npx\NpxLinkDiscount;
// use Drupal\image\Entity\ImageStyle;

/**
 * Class TrainingForm.
 *
 * @package Drupal\npx_training_form\Form
 */
class TrainingForm extends FormBase {

  /** discount for 2+ users */
  const DISCOUNT_USER = 0.2;

  /** discount code value */
  const DISCOUNT_CODE_VALUE = 100;

  /** payment options */
  const PAYMENT_OPTION_NORMAL = 'normal';

  /** Date format for date comparison */
  const DATE_FORMAT = 'Y-m-d';

  const DATE_FORMAT_DISPLAY = 'd-m-Y';

  const DATE_FORMAT_DISPLAY_SHORT = 'd.m';

  /** Limit dates in form */
  const FORM_DATES_LIMIT = 3;

  /** Private participant brutto = netto x multiplier */
  const PRIVATE_PRICE_MULTIPLIER = 1.05;

  /**
   * Drupal\Core\Entity\EntityTypeManagerInterface definition.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The module handler service.
   *
   * @var \Drupal\Core\Extension\ModuleHandlerInterface
   */
  protected $moduleHandler;

  /**
   * Block manager service.
   *
   * @var \Drupal\Core\Block\BlockManagerInterface
   */
  protected $blockManager;

  /**
   * Constructs a new CalendarForm object.
   */
  public function __construct(
      EntityTypeManagerInterface $entity_type_manager,
      ModuleHandlerInterface $module_handler,
      BlockManagerInterface $block_manager
      ) {
        $this->entityTypeManager = $entity_type_manager;
        $this->moduleHandler = $module_handler;
        $this->blockManager = $block_manager;
  }

  public static function create(ContainerInterface $container) {
    return new static(
        $container->get('entity_type.manager'),
        $container->get('module_handler'),
        $container->get('plugin.manager.block')
        );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'npx_training_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $training_nids = $this->getTrainingNids();

    if(empty($training_nids)) {
      $this->messenger()->addWarning('Brak szkoleń!');
    }

    if($page_node = $this->getRouteMatch()->getParameter('node')) {
      if($page_node instanceof NodeInterface) {
        $nid_from_url = $page_node->id();

        $page_npxobj = new NpxTraining($nid_from_url);
        if($page_npxobj->isClosed()) {
          return $form;
        }
      }
    }

    $form['#attached'] = [
      'library' => [
        'npx_training_form/npx_training_form.trainingform'
      ]
    ];

    if(!$page_node instanceof NodeInterface) {
      return $form;
    }


    $form['npx_anchor'] = [
      '#type' => 'html_tag',
      '#tag' => 'a',
      '#value' => '',
      '#attributes' => [
        'name' => 'formularz-zapisu',
        //'class' => ['visually-hidden'],
      ],
      '#weight' => -100,
    ];

//     if(empty($page_node->get('field_tytul_sekcji_g8')->value)) {
//       $form['title'] = [
//         '#markup' => '<h2 class="field-label-above">Wybierz termin i zarezerwuj miejsce</h2>',
//         '#weight' => -100,
//       ];
//     }

    $section_title = 'Wybierz termin szkolenia i zarezerwuj miejsce';

    // Check both old field and new paragraph system for related trainings
    $has_related_trainings = false;

    if ($page_node->hasField('field_npx_related_training') && !$page_node->get('field_npx_related_training')->isEmpty()) {
      $has_related_trainings = true;
    }

    // Check for training tiles in paragraphs
    if ($page_node->hasField('field_tiles_paragraphs') && !$page_node->get('field_tiles_paragraphs')->isEmpty()) {
      foreach ($page_node->get('field_tiles_paragraphs') as $paragraph_item) {
        $paragraph = $paragraph_item->entity;
        if ($paragraph && $paragraph->bundle() == 'tile_training') {
          $has_related_trainings = true;
          break;
        }
      }
    }

    if ($has_related_trainings) {
      $section_title = 'Wybierz wariant szkolenia i zarezerwuj miejsce';
    }
    if ($page_node->hasField('field_tytul_sekcji_g8') && !empty($page_node->get('field_tytul_sekcji_g8')->value)) {
      $form['title'] = [
        '#markup' => $page_node->get('field_tytul_sekcji_g8')->value,
        '#weight' => - 100,
      ];
    }
    else {
      $form['title'] = [
        '#markup' => '<h2 class="field-label-above">' . $section_title . '</h2>',
        '#weight' => -100,
      ];
    }

//     $date_nid_from_url = $this->getTrainingDateIdFromUrl();
    $set_default_option = false;
    $training_options = [];
    $training_discount = null;

    $node_storage = $this->entityTypeManager->getStorage('node');

    if(!empty($training_nids)) {
      $categories = $this->getTrainingCategories();
      $nodes = $node_storage->loadMultiple($training_nids);
      foreach($nodes as $node) {
        $cat_title = $categories[$node->field_npxtraining_category->target_id];
        if(!isset($node->field_npxtraining_dates[0])) {
          continue;
        }
        $training_options[$cat_title][$node->id()] = $node->field_npxtraining_mailtitle->value;
//         if ($nid_from_url == $node->id()) {
//           $set_default_option = true;
//         }
      }
    }

    $form['npx_top_wrapper'] = [
      '#type' => 'container',
      '#id' => 'npx-top-wrapper',
      '#prefix' => '<div class="npx-form-outer-wrapper p-0">',
    ];

    $form['npx_top_wrapper']['npx_dates_variant_wrapper']  = [
      '#type' => 'container',
      '#id' => 'npx-dates-variant-wrapper',
      '#attributes' => [ 'class' => ['row', 'npx-dates-variant-wrapper'] ],
    ];

    $first_rel_id = null;
    $first_date_option = null;

    if($page_node) {
      $form_state->setValue('npx_show_laptop_info', $page_node->field_npxtraining_laptop_info->value);
      $form_state->setValue('npx_pair_only', $page_node->field_npxtraining_pair_only->value);

      if(isset($page_node->field_npx_related_training)) {
        $variant_options = [];
//         $variant_options[$page_node->id()] = $page_node->get('field_npxtraining_tytul_formalny')->value;
        foreach($page_node->field_npx_related_training as $rel) {
          if($rel->entity) {
            if(!$first_rel_id) {
              $first_rel_id = $rel->entity->id();
            }
            $related = $node_storage->load($rel->entity->id());
            $variant_options[$rel->entity->id()] = $related->get('field_npxtraining_tytul_formalny')->value;
//             $tab_build = [
//                     '#theme' => 'npx_training_form_tab',
//                     '#hours' => $this->getHours($related),
//                     '#title' => $related->get('field_npxtraining_tytul_formalny')->value,
//                     '#image' => $image_source,
//                     '#webinar' => $related->get('field_npxtraining_webinar')->value,
//                     '#online' => $related->get('field_npxtraining_online')->value,
//                     '#stationary_only' => $related->get('field_npxtraining_stationary')->value,
//             ];
          }
        }
        $form['npx_top_wrapper']['npx_dates_variant_wrapper']['npx_variant'] = [
          '#type' => 'radios',
          '#title' => '<h4>Wariant szkolenia:</h4>',
          '#options' => $variant_options,
//           '#default_value' => array_key_first($variant_options),
          '#default_value' => $form_state->getValue('npx_variant', array_key_first($variant_options)),
          '#weight' => -40,
          '#attributes' => [ 'class' => ['col-xl-6', 'npx-variant'] ],
        ];
      }

    }

    $config = $this->configFactory()->get('npx_training_form.settings');

    $form['npx_top_wrapper']['npx_info_1'] = [
      '#markup' => $config->get('form_info_1.value'),
      '#weight' => -50,
      '#access' => false,
    ];

    $form['npx_top_wrapper']['npx_training'] = [
      '#type' => 'select',
      '#title' => 'Tytuł szkolenia',
      '#title_display' => 'invisible',
      '#options' => $training_options,
      '#default_value' => $form_state->getValue('npx_training', $first_rel_id),
      '#required' => true,
      '#weight' => -40,
      '#prefix' => '<div class="container-inline form-wrapper">',
      '#suffix' => '</div>',
      '#ajax' => [
        'callback' => [$this, 'ajaxTrainingDate'],
        'event' => 'change',
        'method' => 'replace',
        'effect' => 'fade',
        'wrapper' => 'npx-training-date-wrapper',
        'progress' => [
          'type' => 'throbber',
          'message' => 'Sprawdzam kalendarz',
        ],
      ],
    ];

    if ($set_default_option) {
      $form['npx_top_wrapper']['npx_training']['#default_value'] = $nid_from_url;
    }

    $form['npx_top_wrapper']['npx_dates_variant_wrapper']['npx_training_date_wrapper'] = [
      '#type' => 'container',
      '#id' => 'npx-training-date-wrapper',
      '#weight' => -35,
      '#attributes' => [ 'class' => ['col-xl-6'] ],
    ];


    $hide_benefits_mask = [];
    $training_has_online_dates = false;
    $training_is_webinar = false;
    $training_is_online = false;
    $training_is_seminar = false;

    $set_default_date_option = false;
    $base_price = $page_node->field_npxtraining_price->value;
    $has_dates = false;
    if ($form_state->hasValue('npx_training') || $set_default_option || $first_rel_id) {
      if($form_state->hasValue('npx_training')) {
        $training = $node_storage->load($form_state->getValue('npx_training'));
      } else {
//         $training = $node_storage->load($nid_from_url);
//         $has_dates = count($this->getTrainingDateNids($training)) > 0;
//         if(!$has_dates && $first_rel_id) {
//           $training = $node_storage->load($first_rel_id);
//         }
        $training = $node_storage->load($first_rel_id);
//         $form_state->setValue('npx_training', $first_rel_id);
      }
      $base_price = $training->field_npxtraining_price->value;

      $training_is_webinar = $training->get('field_npxtraining_webinar')->value;
      $training_is_online = $training->get('field_npxtraining_online')->value;
      $training_is_seminar = $training->get('field_seminarium')->value;
      $training_has_online_dates = !(bool)$training->get('field_npxtraining_stationary')->value;

      $field_hide_benefits = $training->get('field_hide_benefits')->getValue();
      foreach($field_hide_benefits as $fhb) {
        $hide_benefits_mask[] = $fhb['value'];
      }

      $form_state->setValue('npx_show_laptop_info', $training->field_npxtraining_laptop_info->value);

      $base_price = $training->field_npxtraining_price->value;
      $training_date_nids = $this->getTrainingDateNids($training);
      $training_dates = $node_storage->loadMultiple($training_date_nids);
      $training_dates_count = 0;

      $date_year_nodes = [];

      $training_date_options = [];
      foreach($training_dates as $training_date) {
        if ($this->checkTrainingDate($training_date)) {
          if($training_dates_count >= self::FORM_DATES_LIMIT) {
            break;
          }
          $training_dates_count++;
          $tmp_year = substr($training_date->field_npxtraining_date_start->value, 0, 4);
          $date_year_nodes[$tmp_year][] = $training_date;

          $num_dates = count($training_date->field_npxtraining_date_end);
          if ($num_dates > 1) {
            $training_date_title = NpxTrainingFormHelper::prepareDateDisplay($training_date->field_npxtraining_date_start[0]->value, $training_date->field_npxtraining_date_end[0]->value);
            for($i = 1; $i < $num_dates; $i++) {
              $training_date_title .= ', ' . NpxTrainingFormHelper::prepareDateDisplay($training_date->field_npxtraining_date_start[$i]->value, $training_date->field_npxtraining_date_end[$i]->value);
            }
          } else {
            $training_date_title = NpxTrainingFormHelper::prepareDateDisplay($training_date->field_npxtraining_date_start->value, $training_date->field_npxtraining_date_end->value);
          }
//           $city_name = $training_date->field_npxtraining_date_city->entity->name->value;

//           $days_diff = NpxTrainingFormHelper::calculateDaysNode($training_date);
//           $days_txt = 'dni';
//           if ($days_diff == 1) {
//             $days_txt = 'dzień';
//           }

          $hours = NpxTrainingFormHelper::calculateHoursNode($training_date);

          $npxDateObj = new NpxTrainingDate((int)$training_date->id());

          $day_hours_override = $npxDateObj->getHoursOverride();

//           $is_date_online = $npxDateObj->isDateOnline();

//           if($is_date_online) {
// //             $training_has_online_dates = true;
//             $training_date_options[$training_date->id()] = '<div class="npx-option-date npx-option-date-online">';
//           } else {
//           }

          $training_date_options[$training_date->id()] = '<span class="npx-option-date text-black fw-bold">';
          $training_date_options[$training_date->id()] .= $training_date_title;
          $training_date_options[$training_date->id()] .= '</span>';
          $training_date_options[$training_date->id()] .= '<span class="npx-option-day-hour px-1">';
          if($day_hours_override) {
            $training_date_options[$training_date->id()] .= $day_hours_override;
          } else {
//             $training_date_options[$training_date->id()] .= $days_diff . ' ' . $days_txt . ', ' . $hours . 'h';
            $training_date_options[$training_date->id()] .= 'godz 9:00-17:00, ' . $hours . 'h,';
          }
          $training_date_options[$training_date->id()] .= '</span>';
          if ($training_date->field_npxtraining_date_guarant->value == true) {
            $training_date_options[$training_date->id()] .= '<span class="">termin gwarantowany</span>';
          } else {
            $min_guarant = $training->field_npxtraining_min_guaranted->value;
            if($min_guarant != 0 && !$training_is_seminar) {
              if($min_guarant == 1) {
                $training_date_options[$training_date->id()] .= '<span class="">gwarancja od ' . $min_guarant . ' uczestnika</span>';
              } else {
                $training_date_options[$training_date->id()] .= '<span class="">gwarancja od ' . $min_guarant . ' uczestników</span>';
              }
            } else {
              if(!$training_is_seminar) {
                $training_date_options[$training_date->id()] .= '<span class="">zagwarantuj termin</span>';
              }
            }
          }

          foreach($training_date->get('field_npxtraining_date_form') as $tform_val) {
//             $tform_label = $training_date->get('field_npxtraining_date_form')->getSetting('allowed_values')[$tform_val->value];
            $tform_label = npx_field_npxtraining_date_form_values()[$tform_val->value];
            $training_date_options[$training_date->id()] .= '<span class="tr-form-' . $tform_val->value . '">' . $tform_label . '</span>';
          }

//           if($training_is_webinar) {
//             $training_date_options[$training_date->id()] .= '<div class="npx-guaranted">Szkolenie online webinar</div>';
//           } elseif($training_is_online) {
//             $training_date_options[$training_date->id()] .= '<div class="npx-guaranted">Szkolenie online</div>';
//           } else {
//             if($is_date_online) {
//               $training_date_options[$training_date->id()] .= '<div class="npx-guaranted">Szkolenie live online</div>';
//             } else {
//               $training_date_options[$training_date->id()] .= '<div class="npx-guaranted">Szkolenie stacjonarne</div>';
//             }
//           }

          if ($form_state->hasValue('npx_training_date') && $form_state->getValue('npx_training_date') === $training_date->id()) {
            if (isset($training_date->field_npxtraining_date_price->value)) {
              $base_price = $training_date->field_npxtraining_date_price->value;
            }
          }
        }
      }

      if($training_dates_count > 0) {
        $form['npx_top_wrapper']['npx_dates_variant_wrapper']['npx_training_date_wrapper']['npx_dates_header'] = [
          '#type' => 'html_tag',
          '#tag' => 'h4',
          '#value' => 'Najbliższe terminy:',
          '#attributes' => [ 'class' => ['npx-training-date-dates-header', 'text-black', 'fw-bold'] ],
        ];

        if($training_has_online_dates && !$training_is_webinar) {
          $form['npx_top_wrapper']['npx_dates_variant_wrapper']['npx_training_date_wrapper']['npx_dates_header']['#value'] = 'Terminy i forma szkolenia:';
        }

        $first_date_option = key($training_date_options);
        $form['npx_top_wrapper']['npx_dates_variant_wrapper']['npx_training_date_wrapper']['npx_training_date'] = [
          '#type' => 'radios',
          '#title' => 'Termin szkolenia',
          '#title_display' => 'invisible',
          '#options' => $training_date_options,
          '#default_value' => $form_state->getValue('npx_training_date', $first_date_option),
          '#required' => true,
          '#prefix' => '<div class="container px-0">',
          '#suffix' => '</div>',
          '#attributes' => [
            'class' => ['edit-npx-training-date'],
          ],
          '#ajax' => [
            'callback' => [$this, 'ajaxCalculateDeadline'],
            'event' => 'change',
            'effect' => 'fade',
            'progress' => [
              'type' => 'throbber',
              'message' => 'Sprawdzam termin',
            ],
          ],
        ];
        if(!$form_state->getValue('npx_training_date', false)) {
          $form_state->setValue('npx_training_date', $first_date_option);
        }

        if($training_is_online) {
          $form['npx_top_wrapper']['npx_dates_variant_wrapper']['npx_training_date_wrapper']['npx_dates_header']['#attributes']['class'][] = 'hidden';
          $form['npx_top_wrapper']['npx_dates_variant_wrapper']['npx_training_date_wrapper']['npx_training_date']['#attributes']['class'][] = 'hidden';
        }
      } else {
        $form_state->setValue('npx_training_date', null);
        $set_default_date_option = false;
      }
      $npxTrainingObj = new NpxTraining((int)$training->id());
      if(!$npxTrainingObj->hasTrainingDate((int)$form_state->getValue('npx_training_date', 0))) {
        $form_state->setValue('npx_training_date', null);
        $set_default_date_option = false;
      }
    }

    $last_user_available = false;

    $display_calculator = false;

    $form['npx_top_wrapper']['npx_date_info_wrapper'] = [
      '#type' => 'container',
      '#id' => 'npx-date-info-wrapper',
      '#weight' => 0
    ];

    $training_date_labels = [];

    if ($form_state->hasValue('npx_training_date') || $set_default_date_option) {
      if ($set_default_date_option && !$form_state->hasValue('npx_training_date')) {
//         $training_date =  $node_storage->load($date_nid_from_url);
      } else {
        $training_date =  $node_storage->load($form_state->getValue('npx_training_date'));
      }
      $display_calculator = true;

      if ($training_date->field_npxtraining_date_available->value == 'ostatnie') {
        $last_user_available = true;
      }

      if (isset($training_date->field_npxtraining_date_price->value)) {
        $base_price = $training_date->field_npxtraining_date_price->value;
      }

      if ($training_date->field_npxtraining_date_available->value == 'nie') {
        $form['npx_top_wrapper']['npx_date_info_wrapper']['npx_no_plbottomleft_info'] = [
          '#markup' => '<div class="npx-no-places-info npx-border-green-inner red-text">Brak wolnych miejsc - wypełnij formularz, jeśli chcesz zapisać się na listę rezerwową lub zapisz się na inny termin (możesz napisać w polu Uwagi, że dodatkowo interesuje Cię lista rezerwowa wcześniejszego terminu).</div>',
          '#prefix' => '<div class="npx-no-places-info-wrapper npx-border-green">',
          '#suffix' => '</div>',
          '#weight' => -33
        ];
      }
      if ($form_state->getValue('npx_show_laptop_info') == true) {
	      $form['npx_top_wrapper']['npx_date_info_wrapper']['npx_laptop_info'] = [
          '#markup' => '<div class="npx-laptop-info npx-border-gray-inner">Na szkoleniu MSPowerPoint pracujemy z komputerem. Jeśli potrzebujesz naszej pomocy w wypożyczeniu laptopa, napisz to w uwagach. Do zamówienia zostanie doliczony koszt 100zł netto (123 z VAT).</div>',
          '#prefix' => '<div class="npx-laptop-info-wrapper npx-border-gray">',
          '#suffix' => '</div>',
          '#weight' => -33
        ];
      }

      if($form_state->getValue('npx_private_participant', false)) {
        $base_price *= self::PRIVATE_PRICE_MULTIPLIER;
      }

      $npxDateObj = new NpxTrainingDate((int)$training_date->id());
      $training_date_labels = $npxDateObj->getDateLabels();
      $roomInfo = $npxDateObj->getRoomPageDescription();
      $roomUri = $npxDateObj->getRoomMapLink();
      $npxMapLink = '&nbsp;<strong><a class="npx-cbox-map-link" data-colorbox-inline="#npx-form-colorbox-inline-map" href="#">Zobacz na mapie</a></strong>';

      $locationTxt = '';
      if(!$npxDateObj->isDateOnline()) {
        $locationTxt = 'Miejsce szkolenia dla formy stacjonarnej: ';
        $locationTxt .= $roomInfo;

        $form['npx_top_wrapper']['npx_date_info_wrapper']['npx_location_info'] = [
          '#markup' => Markup::create('<div class="npx-location-info npx-noborder-inner">' . $locationTxt . '</div>'),
          '#prefix' => '<div class="npx-location-info-wrapper npx-noborder">',
          '#suffix' => '</div><p class="mb-0">&nbsp;</p>',
          '#weight' => -33,
          '#allowed_tags' => ['strong'],
        ];

        $mapout = '<div class="hidden"><div id="npx-form-colorbox-inline-map">';
        $mapout .= '<iframe allowfullscreen="" frameborder="0" height="440" src="" data-src="' . $roomUri . '" style="border:0" width="100%"></iframe>';
        $mapout .= '</div></div>';

        $form['npx_top_wrapper']['npx_date_info_wrapper']['npx_map_box'] = [
          '#markup' => $mapout,
          '#allowed_tags' => ['div', 'iframe', 'script'],
        ];
      } else {
        $locationTxt = $roomInfo;
        $form['npx_top_wrapper']['npx_date_info_wrapper']['npx_location_info'] = [
          '#markup' => Markup::create('<div class="npx-location-info npx-noborder-inner">' . $locationTxt . '</div>'),
          '#prefix' => '<div class="npx-location-info-wrapper npx-noborder">',
          '#suffix' => '</div><p>&nbsp;</p>',
          '#weight' => -33
        ];
      }

    } else {
      $tmp_tr_title = $page_node->title->value;
      if($form_state->hasValue('npx_training')) {
        $id = (int)$form_state->getValue('npx_training');
        try {
          $tmp_tr = new NpxTraining($id);
          $tmp_tr_title = $tmp_tr->getTitle();
        } catch (\Exception $e) {
          //nothing to do
        }
      }
      $form['npx_top_wrapper']['npx_date_info_wrapper']['npx_date_info_wrapper']['npx_no_dates_info'] = [
        '#markup' => '<div class="npx-no-dates-info npx-border-green-inner">Zaproponuj termin szkolenia - czekamy na Twojego <a href="mailto:<EMAIL>?subject=Propozycja terminu szkolenia otwartego ' . $tmp_tr_title . '">maila</a>!</div>',
        '#prefix' => '<div class="npx-no-dates-info-wrapper npx-border-green">',
//         '#suffix' => '</div><br /></div>',
        '#suffix' => '</div><br />',
        '#weight' => -33
      ];

//       return $form;
    }

    $form['npx_top_wrapper']['npx_training_type_info_wrapper'] = [
      '#type' => 'container',
      '#id' => 'npx-training-type-info-wrapper-ajax',
      '#weight' => -32,
      '#access' => false,
    ];
    if($training_has_online_dates && !$training_is_online && !$training_is_webinar) {
      $form['npx_top_wrapper']['npx_training_type_info_wrapper']['npx_training_type_info_content'] = [
        '#theme' => 'npx_training_form_type_info',
        '#content' => Markup::create($config->get('form_info_5.value')),
      ];
    }

    $form['npx_top_wrapper']['npx_participants_amount_wrapper'] = [
      '#type' => 'container',
      '#id' => 'npx-participants-amount-wrapper',
      '#attributes' => [
        'class' => ['container-inline'],
      ],
      '#weight' => 0
    ];

    $form['npx_top_wrapper']['npx_participants_amount_wrapper']['npx_participants_amount'] = [
      '#type' => 'textfield',
      '#prefix' => '<h4 class="npx-participants-amount-header text-black fw-bolf">Liczba uczestników:</h4>',
      '#title' => 'Rezerwuję dla uczestników',
      '#description' => '<span class="red-text">rabat 20%</span> dla 2 i każdej kolejnej osoby',
      '#min' => 1,
      '#max' => 10,
      '#required' => true,
      '#default_value' => 1,
      '#attributes' => [
        'data-max-val' => 10,
      ],
      '#ajax' => [
        'callback' => [$this, 'ajaxCalculate'],
        'event' => 'change',
        'effect' => 'fade',
        'progress' => [
          'type' => 'throbber',
          'message' => 'Obliczam',
        ],
      ],
    ];

    if($npxDateObj instanceof NpxTrainingDate && $npxDateObj->isPlaceLimitDisabled()) {
      $form['npx_top_wrapper']['npx_participants_amount_wrapper']['npx_participants_amount']['#max'] = 10000;
      $form['npx_top_wrapper']['npx_participants_amount_wrapper']['npx_participants_amount']['#attributes']['data-max-val'] = 10000;
    }

    if($last_user_available) {
      $form['npx_top_wrapper']['npx_participants_amount_wrapper']['npx_participants_amount']['#max'] = 1;
      $form_state->setValue('npx_participants_amount', 1);
    }
    if($form_state->getValue('npx_pair_only') == true) {
      $form['npx_top_wrapper']['npx_participants_amount_wrapper']['npx_participants_amount']['#max'] = 2;
      $form['npx_top_wrapper']['npx_participants_amount_wrapper']['npx_participants_amount']['#min'] = 2;
      $form_state->setValue('npx_participants_amount', 2);
      $form['npx_top_wrapper']['npx_participants_amount_wrapper']['npx_participants_amount']['#default_value'] = 2;
      $form['npx_top_wrapper']['npx_participants_amount_wrapper']['npx_participants_amount']['#type'] = 'hidden';
      $form['npx_top_wrapper']['npx_participants_amount_wrapper']['npx_participants_amount']['#prefix'] = '<label>Liczba uczestników: 2</label>';
    }

    if($training_is_seminar) {
      $form['npx_top_wrapper']['npx_participants_amount_wrapper']['npx_participants_amount']['#description'] = '';
    }

    if($training_dates_count == 0) {
      $form['npx_top_wrapper']['npx_participants_amount_wrapper']['npx_participants_amount']['#access'] = false;
    }

    $form['npx_top_wrapper']['npx_price_info_wrapper'] = [
      '#type' => 'container',
      '#id' => 'npx-price-info-wrapper',
    ];

    $form['npx_top_wrapper']['npx_price_info_wrapper']['npx_private_participant_wrapper'] = [
      '#type' => 'container',
      '#id' => 'npx-private-participant-wrapper',
      '#attributes' => [
        'class' => ['container-inline'],
      ],
      '#weight' => 0
    ];

    $form['npx_top_wrapper']['npx_price_info_wrapper']['npx_private_participant_wrapper']['npx_private_participant'] = [
      '#type' => 'checkbox',
      '#title' => 'Zgłaszam się na szkolenie prywatnie',
      '#ajax' => [
        'callback' => [$this, 'ajaxCalculate'],
        'event' => 'change',
        'effect' => 'fade',
        'progress' => [
          'type' => 'throbber',
          'message' => 'Obliczam',
        ],
      ],
    ];

    if($form_state->hasValue('npx_participants_amount') && $form_state->getValue('npx_participants_amount') > 1) {
      $form['npx_top_wrapper']['npx_price_info_wrapper']['npx_private_participant_wrapper']['#access'] = false;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['npx_private_participant_wrapper']['npx_private_participant']['#value'] = false;
      $form_state->setValue('npx_private_participant', false);
    }

    $form['npx_bottom_wrapper'] = [
      '#type' => 'container',
      '#id' => 'npx-bottom-wrapper',
      '#attributes' => [ 'class' => ['p-3'] ],
      '#suffix' => '</div>',
    ];
    if($training_is_webinar || $training_is_online) {
      $form['npx_bottom_wrapper']['#attributes']['class'] = ['n-webinar-mode'];
    }

    $form['npx_bottom_wrapper']['npx_payment_wrapper'] = [
      '#type' => 'container',
      '#id' => 'npx-payment-wrapper',
    ];

    $regular_box = $this->createCalculatorFields($form, $form_state, $base_price, $training_discount, $display_calculator, $training_has_online_dates, $training_is_webinar, $training_is_online, $training_is_seminar, $hide_benefits_mask, $first_rel_id, $first_date_option);

    $form['npx_bottom_wrapper']['npx_online_training_wrapper'] = [
      '#type' => 'container',
      '#id' => 'npx-online-training-wrapper',
      '#weight' => -100,
    ];
    $form['npx_bottom_wrapper']['npx_online_training_wrapper']['npx_online_training'] = [
      '#type' => 'radios',
      '#title' => 'Rodzaj szkolenia',
//       '#prefix' => '<h4 class="npx-online-training-header">Rodzaj szkolenia</h4>',
      '#field_prefix' => 'Mój wybór to szkolenie:',
      '#options' => [
        0 => 'STACJONARNE',
        1 => 'ONLINE',
      ],
      '#attributes' => [ 'class' => ['npx-training-type'] ],
      '#default_value' => $form_state->getValue('npx_online_training', 0),
//       '#ajax' => [
//         'callback' => [$this, 'ajaxTrainingDate'],
//         'event' => 'change',
//         'effect' => 'fade',
//         'progress' => [
//           'type' => 'throbber',
//           'message' => '',
//         ],
//       ],
    ];
    if(!$training_has_online_dates) {
      $form['npx_bottom_wrapper']['npx_online_training_wrapper']['npx_online_training']['#access'] = false;
    }

    if(isset($training_date_labels['stationary']) && !isset($training_date_labels['online'])) {
      $form['npx_bottom_wrapper']['npx_online_training_wrapper']['npx_online_training']['#default_value'] = 0;
      $form_state->setValue('npx_online_training', 0);
      $form['npx_bottom_wrapper']['npx_online_training_wrapper']['npx_online_training']['#value'] = 0;
      $form['npx_bottom_wrapper']['npx_online_training_wrapper']['npx_online_training']['#access'] = false;
    } elseif(isset($training_date_labels['online']) && !isset($training_date_labels['stationary'])) {
      $form['npx_bottom_wrapper']['npx_online_training_wrapper']['npx_online_training']['#default_value'] = 1;
      $form_state->setValue('npx_online_training', 1);
      $form['npx_bottom_wrapper']['npx_online_training_wrapper']['npx_online_training']['#value'] = 1;
      $form['npx_bottom_wrapper']['npx_online_training_wrapper']['npx_online_training']['#access'] = false;
    } else {
      //
    }

    $this->createParticipantsFields($form, $form_state);

    $this->createFVFields($form, $form_state);

    $form['npx_bottom_wrapper']['npx_hotel_info'] = [
      '#type' => 'checkbox',
      '#title' => 'Potrzebuję hotelu - proszę o rekomendację',
      '#weight' => 1
    ];
    $form['npx_bottom_wrapper']['npx_info_4_wrapper'] = [
      '#type' => 'container',
      '#attributes' => ['class' => ['npx-info-4-wrapper']],
      '#weight' => 1,
      '#states' => [
        'visible' => [
          ':input[name="npx_hotel_info"]' => [
            'checked' => true,
          ],
        ],
      ],
      '#access' => false,
    ];
    $form['npx_bottom_wrapper']['npx_info_4_wrapper']['npx_info_4'] = [
      '#type' => 'container',
      '#markup' => $config->get('form_info_4.value'),
      '#weight' => 1
    ];

    $form['npx_bottom_wrapper']['npx_accept_wrapper'] = [
      '#type' => 'container',
      '#attributes' => ['class' => ['npx-accept-wrapper']],
      '#weight' => 10,
    ];
    $form['npx_bottom_wrapper']['npx_accept_wrapper']['npx_accept_1'] = [
      '#type' => 'checkbox',
      '#title' => 'Chcę otrzymywać powiadomienia o promocjach dotyczących szkoleń (np. LAST MINUTE) poprzez email.',
    ];
    $form['npx_bottom_wrapper']['npx_accept_wrapper']['npx_accept_4'] = [
      '#type' => 'checkbox',
      '#title' => 'Wyrażam zgodę na przetwarzanie danych w celu realizacji usługi szkoleniowej i akceptuję <a href="/regulamin" target="_blank">Regulamin</a>.',
      '#required' => true,
    ];

    $form['npx_bottom_wrapper']['npx_actions_wrapper'] = [
      '#type' => 'container',
      '#weight' => 20,
    ];
    $form['npx_bottom_wrapper']['npx_actions_wrapper']['submit'] = [
      '#type' => 'submit',
      '#id' => 'npx-training-form-submit-button',
      '#value' => t('Dokonaj rezerwacji'),
    ];

    $fake_discount = 0;
    $fake_discount_upval = 0;
    if(NpxDiscountHelper::isParamFakeDiscountSet()) {
      $fake_discount = NpxDiscountHelper::getParamFakeDiscountVal();
      $fake_discount_upval = NpxDiscountHelper::getParamFakeDiscountUpVal();
    }

    $disable_regular = (($form_state->getValue('npx_pair_only') == true) || $training_is_seminar);

    if($form_state->hasValue('npx_participants_amount')) {
      $amount = $form_state->getValue('npx_participants_amount');
    } else {
      $amount = 1;
    }

    $discount_info = [];
    $npx_training_date_id = $form_state->getValue('npx_training_date', $first_date_option);

    $code_info = [];

    $is_date_online = false;
    if($form_state->getValue('npx_online_training', false)) {
      $is_date_online = true;
    }

    if($form_state->hasValue('npx_discount_code') && !$is_date_online) {
      $code = $form_state->getValue('npx_discount_code');
      $code_info = NpxCouponHelper::getCodeInfo($code);
    }

    if ($npx_training_date_id) {
      $discount_info = NpxDiscountHelper::getDiscountInfo($npx_training_date_id);
    }
    $calc_info = NpxCalculatorHelper::getCalculation($base_price + $fake_discount + $fake_discount_upval, $amount, $discount_info, $code_info, $disable_regular);

    if(!is_null($regular_box) && $calc_info['info']['has_active_last'] && $calc_info['info']['has_active_first']) {
      $form['npx_regular_box_wrapper'] = [
        '#type' => 'container',
        '#id' => 'npx-regular-box-wrapper',
        '#weight' => 10,
        '#attributes' => [ 'class' => ['mb-7', 'd-block', 'd-xl-none', 'px-1'] ],
        '#suffix' => '</div>',
      ];

      $form['npx_regular_box_wrapper']['regular_box'] = $regular_box;
    }

//     if(!$form_state->hasValue('npx_training_date')) {
//       $form['npx_bottom_wrapper']['npx_actions_wrapper']['submit']['#access'] = false;
//     }

    // Disable old multi date display
    //$this->handleMultiDates($form, $form_state);
//     $this->debugLog($form_state, __LINE__);

    return $form;
  }

  /**
   * Build calculator fields
   *
   * @param float $base_price
   *  training price
   * @param array $discount (optional)
   *  ['discount_type' => '', 'discount_value' => '']
   */
  public function createCalculatorFields(array &$form, FormStateInterface $form_state, $base_price, $discount=null, $display_calculator=true, $training_has_online_dates=false, $training_is_webinar=false, $training_is_online=false, $training_is_seminar=false, $hide_benefits_mask=[], $first_rel_id=null, $first_date_option=null) {
    $npx_training_id = $form_state->getValue('npx_training', $first_rel_id);
    $npx_training_date_id = $form_state->getValue('npx_training_date', $first_date_option);
    $npx_places_limit_disabled = false;

    $is_date_online = false;
    if($form_state->getValue('npx_online_training', false)) {
      $is_date_online = true;
    }

    if($form_state->hasValue('npx_participants_amount')) {
      $amount = $form_state->getValue('npx_participants_amount');
    } else {
      $amount = 1;
    }

    $code_info = [];

    if($form_state->hasValue('npx_discount_code') && !$is_date_online) {
      $code = $form_state->getValue('npx_discount_code');
      $code_info = NpxCouponHelper::getCodeInfo($code);
    }

    $discount_info = [];

    $training_days_count = 1;

    $training_date_available = true;

    $training_minutes_override = false;

    $training_date_labels = [];

    $historic_minor_price = null;
    $historic_minor_discount = null;

    if($npx_training_date_id) {
//       $training_date_id = $form_state->getValue('npx_training_date', $first_date_option);
      $discount_info = NpxDiscountHelper::getDiscountInfo($npx_training_date_id);

      $npxtrainingdate = new NpxTrainingDate($npx_training_date_id);
      $training_days_count = $npxtrainingdate->getDaysCount();

      $training_date_available = $npxtrainingdate->isDateAvailable();

      $training_minutes_override = $npxtrainingdate->getMinutesOverride();

      $training_date_labels = $npxtrainingdate->getDateLabels();

      $npx_places_limit_disabled = $npxtrainingdate->isPlaceLimitDisabled();

      $historic_minor_price = $npxtrainingdate->getHistoricMinorPrice();
      $historic_minor_discount = $npxtrainingdate->getHistoricMinorDiscount();
    }

    $fake_discount = 0;
    $fake_discount_upval = 0;
    if(NpxDiscountHelper::isParamFakeDiscountSet()) {
      $fake_discount = NpxDiscountHelper::getParamFakeDiscountVal();
      $fake_discount_upval = NpxDiscountHelper::getParamFakeDiscountUpVal();
    }

    $disable_regular = (($form_state->getValue('npx_pair_only') == true) || $training_is_seminar);

    $calc_info = NpxCalculatorHelper::getCalculation($base_price + $fake_discount + $fake_discount_upval, $amount, $discount_info, $code_info, $disable_regular);

    $regular_calculation = $calc_info['regular'];
    $first_calculation = $calc_info['first'];
    $last_calculation = $calc_info['last'];

    if(!$base_price || $amount == 0 || !$display_calculator) {
//       return;
    }

    $can_display_places = false;
    if($npx_training_id && $npx_training_date_id) {
      $available_places = NpxAmountHelper::getAvailablePlaces(intval($npx_training_id), intval($npx_training_date_id));
      $can_display_places = NpxAmountHelper::canDisplayAmount($available_places, intval($npx_training_date_id));
    }

    $regular_date_info = '';
//     if($regular_calculation['start']) {
//       //$regular_date_info = 'Od ' . self::reformatDate($regular_calculation['start']);
//     } elseif ($regular_calculation['end']) {
//       //$regular_date_info = 'Do ' . self::reformatDate($regular_calculation['end']);
//     } else {
    if($can_display_places || ($calc_info['info']['has_active_first'] || $calc_info['info']['has_active_last'])) {
      if($available_places != null) {
        $word_remain = NpxLangHelper::getWord(NpxWord::REMAIN_SHORT, $available_places, '', ' ');
        $word_free = NpxLangHelper::getWord(NpxWord::FREE, $available_places, ' ');
        $word_place = NpxLangHelper::getWord(NpxWord::PLACE, $available_places, ' ');
      }
      $amount_without_first = $available_places;
      if(isset($first_calculation['amount'])) {
        $amount_without_first = $available_places - $first_calculation['amount'];
      }
      if($amount_without_first < 0) {
        $amount_without_first = 0;
      }

      if($can_display_places) {
        $available_places_display = $available_places;

        if($calc_info['info']['has_active_first']) {
          $available_places_display = $available_places - $first_calculation['amount'];
          if($available_places_display < 0) {
            $available_places_display = 0;
          }
        } elseif($calc_info['info']['has_active_last']) {
          $available_places_display = $available_places - $last_calculation['amount'];
          if($available_places_display < 0) {
            $available_places_display = 0;
          }
        }

        $regular_date_info = ucfirst($word_remain) . $available_places_display . $word_free . $word_place;
      } else {
        if($training_date_available) {
          $regular_date_info = 'Do wyczerpania miejsc';
        } else {
          $regular_date_info = 'Brak wolnych miejsc';
        }
      }
    } else {
      if($training_date_available) {
        $regular_date_info = 'Do wyczerpania miejsc';
      } else {
        $regular_date_info = 'Brak wolnych miejsc';
      }
    }
//     }
    if($npx_places_limit_disabled) {
      $regular_date_info = '&nbsp;';
    }

    $regular_box = ['#markup' => ''];
    $regular_box['#markup'] .= '<div class="npx-price d-flex flex-nowrap flex-column flex-md-row">';
    $regular_box['#markup'] .= '<div property="offers" class="hidden" typeof="Offer">';
    $regular_box['#markup'] .= '<meta property="schema:priceCurrency" content="PLN"><span property="priceCurrency" content="PLN">pln</span>';
    $regular_box['#markup'] .= '<meta property="schema:price" content="'. number_format(round($regular_calculation['total_with_discount']), 0, ',', ''). '">' . number_format(round($regular_calculation['total_with_discount']), 0, ',', '');
    $regular_box['#markup'] .= '<span property="price" content="'. number_format(round($regular_calculation['total_with_discount']), 0, ',', '') .'">';
    $regular_box['#markup'] .= number_format(round($regular_calculation['total_with_discount']), 0, ',', '') . '</span>';
    $regular_box['#markup'] .= '<span property="availability">InStock</span><span property="priceValidUntil">2023-01-01</span>';
    $regular_box['#markup'] .= '<span property="url">' . \Drupal::service('path_alias.manager')->getAliasByPath(\Drupal::service('path.current')->getPath()) . '</span>';
    $regular_box['#markup'] .= '<link property="schema:itemCondition" href="https://schema.org/NewCondition"></div>';
    $regular_box['#markup'] .= '<span class="npx-price-a">';
    $regular_box['#markup'] .= '<span class="npx-price-a-a d-block">Cena </span>';
    $regular_box['#markup'] .= '<span class="npx-price-a-b d-block fw-bold">Regular: </span>';
    if($training_is_webinar || $training_is_online) {
      $regular_box['#markup'] .= '<div class="npx-price-b-c position-relative">Do wyczerpania miejsc.</div>';
    } else {
      $regular_box['#markup'] .= '<div class="npx-price-b-c position-relative">' . $regular_date_info . '</div>';
    }
    $regular_box['#markup'] .= '</span>';
    $regular_box['#markup'] .= '<span class="npx-price-b d-block">';
    if($form_state->getValue('npx_pair_only') == true) {
      $pair_summary_price = $regular_calculation['total_with_discount'];
      $pair_vat = $pair_summary_price * 0.23;
      $pair_summary_price += $pair_vat;

      $regular_box['#markup'] .= '<span class="npx-price-b-a fw-bold">' . number_format(round($pair_summary_price), 0, ',', ' ') . ' zł brutto</span>';
    } elseif($form_state->getValue('npx_private_participant', false)) {
      $regular_box['#markup'] .= '<span class="npx-price-b-a fw-bold">' . number_format(round($regular_calculation['total_with_discount']), 0, ',', ' ') . ' zł brutto</span>';
    } else {
      $regular_box['#markup'] .= '<span class="npx-price-b-a fw-bold">' . number_format(round($regular_calculation['total_with_discount']), 0, ',', ' ') . ' zł netto</span>';
    }

    if($regular_calculation['total_savings']) {
      $regular_box['#markup'] .= '<span class="npx-price-b-b"> (w tym rabat ' . number_format(round($regular_calculation['total_savings']), 0, ',', ' ') . ' zł)</span>';
    }
    if ($npx_training_id == 3039) {
      $regular_box['#markup'] .= '<span class="npx-price-b-b"> - za parę</span>';
    }
    $regular_box['#markup'] .= '</span>';
    $regular_box['#markup'] .= '</div>';


    $historic_price_info['#markup'] = '';

    if($historic_minor_price) {
      $historic_price_info['#markup'] .= '<div class="npx-price d-flex flex-nowrap flex-column flex-md-row">';
      $historic_price_info['#markup'] .= '<span class="npx-price-a">';
      $historic_price_info['#markup'] .= '<span class="npx-price-a-a d-block">Cena </span>';
      $historic_price_info['#markup'] .= '<div class="npx-price-a-c position-relative fw-bold">Najniższa z ostatnich 30 dni:</div>';
      $historic_price_info['#markup'] .= '</span>';
      $historic_price_info['#markup'] .= '<span class="npx-price-b">';
      $historic_price_info['#markup'] .= '<span class="npx-price-b-c"><s>';
      $historic_price_info['#markup'] .= '<strong>' . number_format($historic_minor_price, 0, ',', ' ') . ' zł netto</strong>';
      if($historic_minor_discount) {
        $historic_price_info['#markup'] .= ' (w tym rabat ' . $historic_minor_discount . ' zł)';
      }
      $historic_price_info['#markup'] .= '</s></span>';
      $historic_price_info['#markup'] .= '</span>';
      $historic_price_info['#markup'] .= '</div>';
    }

    $first_date_info = '';
    if($calc_info['info']['has_active_first']) {
      $word_remain = NpxLangHelper::getWord(NpxWord::REMAIN_SHORT, $first_calculation['amount'], '', ' ');
      //       $word_free = NpxLangHelper::getWord(NpxWord::FREE, $first_calculation['amount'], ' ');
      $word_place = NpxLangHelper::getWord(NpxWord::PLACE, $first_calculation['amount'], ' ');

      $amount_without_first = $available_places - $first_calculation['amount'];
      if($amount_without_first < 0) {
        $amount_without_first = 0;
      }
      $first_date_info = "Rabat ważny do " . self::reformatDate($first_calculation['end'], self::DATE_FORMAT_DISPLAY) . ' lub do wyczerpania miejsc: ' . $first_calculation['amount'];
  //    $first_date_info = ucfirst($word_remain) . $first_calculation['amount'] . $word_place .' do '. self::reformatDate($first_calculation['end_display'], self::DATE_FORMAT_DISPLAY_SHORT) . ' lub wyczerpania';
/*      if($can_display_places && !$training_is_webinar && !$training_is_online) {
        $first_date_info = ucfirst($word_remain) . $first_calculation['amount'] . $word_place . ' w cenie promocyjnej i ' . $amount_without_first . ' w regularnej';
      } else {
        $first_date_info = ucfirst($word_remain) . $first_calculation['amount'] . $word_place . ' w cenie promocyjnej, pozostałe w cenie regularnej';
      }
    } elseif ($calc_info['info']['has_first']) {
	    $first_date_info = 'Do ' . self::reformatDate($first_calculation['end_display']);*/
    }
  /*
    if(NpxDiscountHelper::isParamDiscountSet() || NpxDiscountHelper::isParamFakeDiscountSet() || isset($first_calculation['islinkdiscount'])) {
      $first_date_info = 'Do wyczerpania miejsc.';
    }*/

    $first_minute_box = ['#markup' => ''];
    $first_minute_box['#markup'] .= '<div class="npx-price d-flex flex-nowrap flex-column flex-md-row">';
    $first_minute_box['#markup'] .= '<span class="npx-price-a">';
    $first_minute_box['#markup'] .= '<span class="npx-price-a-a d-block">Cena </span>';
    if(NpxDiscountHelper::isParamDiscountSet() || NpxDiscountHelper::isParamFakeDiscountSet() || isset($first_calculation['islinkdiscount'])) {
      $first_minute_box['#markup'] .= '<span class="npx-price-a-b d-block fw-bold">Cena promocyjna: </span>';
    } else {
//       $first_minute_box['#markup'] .= '<span class="npx-price-a-b d-block fw-bold">First minute: </span>';
      if($this->config('npx_training_form.settings')->get('holiday_discount')) {
        $first_minute_box['#markup'] .= '<span class="npx-price-a-b d-block fw-bold">'.t('Wakacyjna promocja').': </span>';
      } else {
        $first_minute_box['#markup'] .= '<span class="npx-price-a-b d-block fw-bold">'.t('First minute').': </span>';
      }
    }
    $first_minute_box['#markup'] .= '<div class="npx-price-b-c position-relative">' . $first_date_info . '</div>';
    $first_minute_box['#markup'] .= '</span>';
    $first_minute_box['#markup'] .= '<span class="npx-price-b">';
    if(isset($first_calculation['total_with_discount'])) {
      if($form_state->getValue('npx_private_participant', false)) {
        $first_minute_box['#markup'] .= '<span class="npx-price-b-a fw-bold">' . number_format(round($first_calculation['total_with_discount']), 0, ',', ' ') . ' zł brutto</span>';
      } else {
        $first_minute_box['#markup'] .= '<span class="npx-price-b-a fw-bold">' . number_format(round($first_calculation['total_with_discount']), 0, ',', ' ') . ' zł netto</span>';
      }
    }
    if(isset($first_calculation['total_savings'])) {
      $first_minute_box['#markup'] .= '<span class="npx-price-b-b"> (w tym rabat ' . number_format(round($first_calculation['total_savings']), 0, ',', ' ') . ' zł)</span>';
    }
    $first_minute_box['#markup'] .= '</span>';
    $first_minute_box['#markup'] .= '</div>';

    $last_date_info = '';
    if($calc_info['info']['has_active_last']) {
      $word_remain = NpxLangHelper::getWord(NpxWord::REMAIN_SHORT, $last_calculation['amount'], '', ' ');
      //       $word_free = NpxLangHelper::getWord(NpxWord::FREE, $first_calculation['amount'], ' ');
      $word_place = NpxLangHelper::getWord(NpxWord::PLACE, $last_calculation['amount'], ' ');

      $amount_without_last = $available_places - $last_calculation['amount'];
      if($amount_without_last < 0) {
        $amount_without_last = 0;
      }

  //     $last_date_info = ucfirst($word_remain) . $last_calculation['amount'] . $word_place .' do '. self::reformatDate($last_calculation['end_display'], self::DATE_FORMAT_DISPLAY_SHORT) . ' lub wyczerpania';
      if($can_display_places && !$training_is_webinar && !$training_is_online) {
        $last_date_info = ucfirst($word_remain) . $last_calculation['amount'] . $word_place . ' w cenie promocyjnej i ' . $amount_without_last . ' w regularnej';
      } else {
        $last_date_info = ucfirst($word_remain) . $last_calculation['amount'] . $word_place . ' w cenie promocyjnej, pozostałe w cenie regularnej';
      }
    }

    if(NpxDiscountHelper::isParamDiscountSet() || NpxDiscountHelper::isParamFakeDiscountSet() || isset($last_calculation['islinkdiscount'])) {
      $last_date_info = 'Do wyczerpania miejsc.';
    }

    $last_minute_box = ['#markup' => ''];
    $last_minute_box['#markup'] .= '<div class="npx-price d-flex flex-nowrap flex-column flex-md-row">';
    $last_minute_box['#markup'] .= '<span class="npx-price-a">';
    $last_minute_box['#markup'] .= '<span class="npx-price-a-a d-block">Cena </span>';
    if(NpxDiscountHelper::isParamDiscountSet() || NpxDiscountHelper::isParamFakeDiscountSet() || isset($last_calculation['islinkdiscount'])) {
      $last_minute_box['#markup'] .= '<span class="npx-price-a-b d-block fw-bold">Cena promocyjna: </span>';
    } else {
      $last_minute_box['#markup'] .= '<span class="npx-price-a-b d-block fw-bold">Last minute: </span>';
    }
    $last_minute_box['#markup'] .= '<div class="npx-price-b-c position-relative">' . $last_date_info . '</div>';
    $last_minute_box['#markup'] .= '</span>';
    $last_minute_box['#markup'] .= '<span class="npx-price-b">';
    if($form_state->getValue('npx_private_participant', false)) {
      $last_minute_box['#markup'] .= '<span class="npx-price-b-a fw-bold">' . number_format(round($last_calculation['total_with_discount']), 0, ',', ' ') . ' zł brutto</span>';
    } else {
      $last_minute_box['#markup'] .= '<span class="npx-price-b-a fw-bold">' . number_format(round($last_calculation['total_with_discount']), 0, ',', ' ') . ' zł netto</span>';
    }
    $last_minute_box['#markup'] .= '<span class="npx-price-b-b"> (w tym rabat ' . number_format(round($last_calculation['total_savings']), 0, ',', ' ') . ' zł)</span>';
    $last_minute_box['#markup'] .= '</span>';
    $last_minute_box['#markup'] .= '</div>';


    $benefits_days = '60 - 90';

    if($training_days_count == 1) {
      $benefits_days = '60';
    } elseif ($training_days_count == 2) {
      $benefits_days = '90';
    } elseif ($training_days_count == 3) {
      $benefits_days = '150';
    } elseif ($training_days_count == 4) {
      $benefits_days = '180';
    }

    if($training_minutes_override) {
      $benefits_days = $training_minutes_override;
    }

    $benefits_list = [
      '#theme' => 'item_list',
      '#list_type' => 'ul',
      '#items' => [],
    ];

    if(!in_array("1", $hide_benefits_mask)) {
      $benefits_list['#items'][] = [ '#markup' => 'Trening indywidualny ' . $benefits_days . ' minut <span class="npx-red-text">gratis</span>'];
    }
    if(!in_array('2', $hide_benefits_mask)) {
      $benefits_list['#items'][] = [ '#markup' => 'Kontakt z trenerem bez limitu godzin <span class="npx-red-text">gratis</span>'];
    }
    if(!in_array('3', $hide_benefits_mask)) {
//       $benefits_list['#items'][] = [ '#markup' => 'Dodatkowe materiały edukacyjne przez rok <span class="npx-red-text">gratis</span>'];
      $benefits_list['#items'][] = [ '#markup' => 'Nielimitowany dostęp do platformy online 4GROW <span class="npx-red-text">gratis</span>'];
    }
    if(!in_array('4', $hide_benefits_mask) && !$training_is_seminar) {
      $benefits_list['#items'][] = [ '#markup' => 'Jeśli to Twoje kolejne szkolenie odbierz <span class="npx-red-text">rabat 100zł</span>'];
    }
    if(!in_array('5', $hide_benefits_mask) ) {
      $benefits_list['#items'][] = [ '#markup' => 'Nielimitowany dostęp do tematycznej, zamkniętej grupy rozwojowej online'];
    }

    $npx_discount_code = [
      '#type' => 'textfield',
      '#title' => 'Kod rabatowy',
      '#title_display' => 'hidden',
      '#attributes' => [
        'placeholder' => 'Kod rabatowy',
      ],
      '#field_suffix' => '<a href="#" class="npx-discout-button">Sprawdź</a>',
      '#ajax' => [
        'callback' => [$this, 'ajaxCalculate'],
        'event' => 'change',
        'effect' => 'fade',
        'progress' => [
          'type' => 'throbber',
          'message' => 'Obliczam',
        ],
      ],
    ];

    if($training_is_webinar || $training_is_online) {
      $npx_discount_code['#access'] = false;
      $benefits_list['#access'] = false;
    }

    $npx_discount_code_active = [];

    if ($code_info) {
      $npx_discount_code_active['#markup'] = '<div class="npx-discount-code-info">Cenę wyjściową pomniejszyliśmy o ' . $code_info['value'] . ' zł.</div>';

      $npx_discount_code['#type'] = 'hidden';
      $npx_discount_code['#value'] = $code_info['code'];
    }

//     if($amount > 1) {
//       $expand_bottom_label = 'Zarezerwuj miejsca';
//     } else {
//       $expand_bottom_label = 'Zarezerwuj miejsce';
//     }

    $expand_bottom_wrapper = [
      '#type' => 'container',
      '#attributes' => [ 'class' => ['npx-container', 'npx-container-inline'] ],
    ];

    if(isset($training_date_labels['stationary'])) {
      $expand_bottom_wrapper['expand_bottom_offline'] = [
        '#type' => 'html_tag',
        '#tag' => 'a',
        '#value' => 'Zarezerwuj stacjonarne',
        '#attributes' => [
          'href' => '#',
          'id' => 'npx-expand-bottom-wrapper',
          'class' => ['npx-program-button', 'text-uppercase', 'mb-xl-3'],
        ],
      ];
    }
    if(isset($training_date_labels['online'])) {
      $expand_bottom_wrapper['expand_bottom_online'] = [
        '#type' => 'html_tag',
        '#tag' => 'a',
        '#value' => 'Zarezerwuj online',
        '#attributes' => [
          'href' => '#',
          'id' => 'npx-expand-bottom-wrapper-online',
          'class' => ['npx-program-button-dark', 'text-uppercase'],
        ],
      ];
    }
/*
    if(!$training_is_webinar && !$training_is_online) {
      $expand_bottom_wrapper['expand_bottom_offline'] = [
        '#type' => 'html_tag',
        '#tag' => 'a',
  //       '#value' => $expand_bottom_label,
        '#value' => 'Zarezerwuj stacjonarne',
        '#attributes' => [
          'href' => '#',
	  'id' => 'npx-expand-bottom-wrapper',
          'class' => ['npx-program-button', 'text-uppercase', 'mb-xl-3'],
        ],
      ];
      if($training_has_online_dates) {
        $expand_bottom_wrapper['expand_bottom_online'] = [
          '#type' => 'html_tag',
          '#tag' => 'a',
          '#value' => 'Zarezerwuj online',
          '#attributes' => [
            'href' => '#',
	    'id' => 'npx-expand-bottom-wrapper-online',
            'class' => ['npx-program-button-dark', 'text-uppercase'],
          ],
        ];
      }
    } else {
      if($training_has_online_dates) {
        $expand_bottom_wrapper['expand_bottom_online'] = [
          '#type' => 'html_tag',
          '#tag' => 'a',
          '#value' => 'Zarezerwuj online webinar',
          '#attributes' => [
            'href' => '#',
	    'id' => 'npx-expand-bottom-wrapper-online',
            'class' => ['npx-program-button-dark', 'text-uppercase'],
          ],
        ];

        if($training_is_online) {
          $expand_bottom_wrapper['expand_bottom_online']['#value'] = 'Zarezerwuj online';
        }
      }
    }
  */

//     if(!$form_state->hasValue('npx_training_date')) {
    if(!$npx_training_date_id) {
      $expand_bottom_wrapper['#access'] = false;
    }

    $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box'] = [
      '#type' => 'container',
      '#attributes' => [ 'class' => ['npx-calculation-box d-flex flex-column flex-xl-row justify-content-sm-start'] ],
    ];
    $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left'] = [
      '#type' => 'container',
      '#attributes' => [ 'class' => ['npx-box-left', 'mb-xl-3'] ],
    ];
    $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['right'] = [
      '#type' => 'container',
      '#attributes' => [ 'class' => ['npx-box-right', 'mx-xl-6'] ],
    ];

    $counter_count = 0;

//     if($form_state->hasValue('npx_training')) {
    if($npx_training_id) {
      $counter_count = NpxCounter::getViewsCount((int)$npx_training_id);
    }

    if(NpxCounter::canDisplay($counter_count)) {
      $word_person = NpxLangHelper::getWord(NpxWord::PERSON, $counter_count, ' ');
      $word_interested = NpxLangHelper::getWord(NpxWord::INTERESTED, $counter_count, ' ');

      $counter_markup =  '<div class="npx-counter-wrapper d-flex mt-2 visible">';
      $counter_markup .=   '<div class="npx-counter-left">';
      $counter_markup .=     '<div class="npx-counter-spacer"></div>';
      $counter_markup .=   '</div>';
      $counter_markup .=   '<div class="npx-counter-right">';
      $counter_markup .=     '<span class="npx-counter-icon">&nbsp;</span>';
      $counter_markup .=     '<span class="npx-counter-info fw-bold">' . $counter_count . $word_person . $word_interested . ' się dziś tym szkoleniem!</span>';
      $counter_markup .=   '</div>';
      $counter_markup .= '</div>';

      $counter_info = [
        '#markup' => $counter_markup,
      ];
    } else {
      $counter_info = [
        '#markup' => '',
      ];
    }

    if(!$training_is_seminar) {
      $social_link = '<div class="npx-social-colorbox-link position-relative"><a class="text-decoration-none fw-bold" data-colorbox-inline="#npx-form-colorbox-inline-social" href="#">Obniż cenę do 0 zł!</a></div>';
    } else {
      $social_link = '';
    }
    $social_container = '<div class="hidden"><div id="npx-form-colorbox-inline-social">';
    $social_container .= $this->config('npx_training_form.settings')->get('cbox_content.value');
    $social_container .= $this->getSocialMediaBlock();
    $social_container .= $this->config('npx_training_form.settings')->get('cbox_content_bottom.value');
    $social_container .= '</div></div>';
    $social_info = [
     '#markup' => $social_link . $social_container,
    ];


    if($calc_info['info']['has_active_last']) {
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['price'] = $regular_box;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['right']['price'] = $last_minute_box;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['social'] = $social_info;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['counter'] = $counter_info;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['benefits'] = $benefits_list;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['npx_discount_code'] = $npx_discount_code;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['npx_discount_code_active'] = $npx_discount_code_active;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['npx_expand_bottom_wrapper'] = $expand_bottom_wrapper;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['right']['#attributes']['class'][] = 'npx-active-box';
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['#attributes']['class'][] = 'npx-box-with-addons';

      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['right']['historic_price'] = $historic_price_info;
    } elseif($calc_info['info']['has_active_first']) {
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['right']['price'] = $first_minute_box;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['price'] = $regular_box;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['social'] = $social_info;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['counter'] = $counter_info;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['benefits'] = $benefits_list;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['npx_discount_code'] = $npx_discount_code;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['npx_discount_code_active'] = $npx_discount_code_active;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['npx_expand_bottom_wrapper'] = $expand_bottom_wrapper;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['right']['#attributes']['class'][] = 'npx-active-box';
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['#attributes']['class'][] = 'npx-box-with-addons';

      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['right']['historic_price'] = $historic_price_info;
    } else {
//       if($calc_info['info']['has_first']) {
//         $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['price'] = $first_minute_box;
//         $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['right']['price'] = $regular_box;
//         $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['social'] = $social_info;
//         $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['counter'] = $counter_info;
//         $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['benefits'] = $benefits_list;
//         $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['npx_discount_code'] = $npx_discount_code;
//         $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['npx_discount_code_active'] = $npx_discount_code_active;
//         $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['npx_expand_bottom_wrapper'] = $expand_bottom_wrapper;
//         $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['#attributes']['class'][] = 'npx-active-box';
//         $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['#attributes']['class'][] = 'npx-box-with-addons';
//       } else {
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['price'] = $regular_box;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['social'] = $social_info;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['counter'] = $counter_info;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['benefits'] = $benefits_list;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['npx_discount_code'] = $npx_discount_code;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['npx_discount_code_active'] = $npx_discount_code_active;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['npx_expand_bottom_wrapper'] = $expand_bottom_wrapper;
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['#attributes']['class'][] = 'npx-active-box';
      $form['npx_top_wrapper']['npx_price_info_wrapper']['calculation_box']['left']['#attributes']['class'][] = 'npx-box-with-addons';
//       }
    }

    $active_price = $calc_info['info']['active_price'];

    // ----------------- hidden fields -----------------//
    $form['npx_top_wrapper']['npx_price_info_wrapper']['npx_base_price'] = [
      '#type' => 'hidden',
      '#value' => round($calc_info[$active_price]['price']),
    ];
    $form['npx_top_wrapper']['npx_price_info_wrapper']['npx_total'] = [
      '#type' => 'hidden',
      '#value' => round($calc_info[$active_price]['total_raw']),
    ];
    $form['npx_top_wrapper']['npx_price_info_wrapper']['npx_total_discount'] = [
      '#type' => 'hidden',
      '#value' => round($calc_info[$active_price]['total_with_discount']),
    ];
    $form['npx_top_wrapper']['npx_price_info_wrapper']['npx_regular_price_info'] = [
      '#type' => 'hidden',
      '#value' => $calc_info['regular']['total_with_discount'],
    ];

    if($calc_info[$active_price]['id']) {
      $form['npx_top_wrapper']['npx_price_info_wrapper']['npx_used_discount'] = [
        '#type' => 'hidden',
        '#value' => $calc_info[$active_price]['id'],
      ];
    }

    if(isset($calc_info[$active_price]['nld_id'])) {
      $form['npx_top_wrapper']['npx_price_info_wrapper']['npx_used_link_discount'] = [
        '#type' => 'hidden',
        '#value' => $calc_info[$active_price]['nld_id'],
      ];
    }

    if ($calc_info['info']['has_first'] or $calc_info['info']['has_active_last'] or $calc_info['info']['has_active_first'] or $calc_info['info']['has_active_first']) {
      return $regular_box;
    }
    else {
      return null;
    }
  }

  public function handleMultiDates(array &$form, FormStateInterface $form_state) {
    if($form_state->hasValue('npx_training_date')) {
      $id = $form_state->getValue('npx_training_date');
      $npxdate = new NpxTrainingDate($id);
      $count = $npxdate->getDatesCount();

      if($count > 1) {
        $form['npx_top_wrapper']['npx_training_date_wrapper']['npx_dates_header']['#value'] = 'Najbliższe zjazdy:';

        $form['npx_top_wrapper']['npx_training_date_wrapper']['npx_training_date']['#attributes']['class'][] = 'hidden';

        $dates = $npxdate->getDatesArray(false, true);

        $markup = '';

        foreach($dates as $date) {
          $markup .= '<div class="npx-date-table-elem d-block m-0 position-relative bg-white">';
          $markup .=  '<div class="npx-date-title fw-bold text-black d-block">';
          $markup .=    $date['date'];
          $markup .=  '</div>';
          $markup .=  '<div class="npx-date-desc d-block">';
          $markup .=    $date['days'] . ' dni, ' . $date['hours'] . 'h';
          $markup .=  '</div>';
          $markup .= '</div>';
        }

        $form['npx_top_wrapper']['npx_training_date_wrapper']['npx_training_date_table'] = [
          '#markup' => $markup,
          '#prefix' => '<div class="npx-dates-table-wrapper d-flex flex-wrap align-items-stretch">',
          '#suffix' => '</div>',
        ];

      }
    }

  }

  /**
   * Create participants fields
   *
   * @param array $form
   * @param FormStateInterface $form_state
   */
  public function createParticipantsFields(array &$form, FormStateInterface $form_state, $last_user=false) {
    if($form_state->hasValue('npx_participants_amount')) {
      $amount = $form_state->getValue('npx_participants_amount');
    } else {
      $amount = 1;
//       return;
    }

    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper'] = [
      '#type' => 'container',
      '#attributes' => ['class' => ['edit-participants-wrapper']],
      '#weight' => 10,
    ];

    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participants_header'] = [
      '#markup' => '<h2>Dane Uczestników</h2>',
      '#access' => false,
    ];

    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_files_wrapper'] = [
      '#type' => 'container',
      '#attributes' => [ 'class' => ['npx-form-files-wrapper'] ],
      '#weight' => -100,
      '#access' => false,
    ];
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_files_wrapper']['files_content'] = [ '#markup' => ''];
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_files_wrapper']['files_content']['#markup'] .= '<strong>Pobierz papierowy formularz zgłoszenia</strong>';
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_files_wrapper']['files_content']['#markup'] .= '<div class="npx-files">';
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_files_wrapper']['files_content']['#markup'] .= '<a href="/sites/default/files/formularz-zgloszenia-na-szkolenie-do-pdf.pdf" class="npx-pdf"><span>PDF</span></a>';
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_files_wrapper']['files_content']['#markup'] .= '<a href="/sites/default/files/formularz-zgloszenia-na-szkolenie.docx" class="npx-docx"><span>DOCX</span></a>';
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_files_wrapper']['files_content']['#markup'] .= '</div>';


    // FV PERSON
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper'] = [
      '#type' => 'container',
    ];
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['title'] = [
      '#type' => 'html_tag',
      '#tag' => 'h4',
      '#attributes' => [ 'class' => ['npx-fv-user-title'] ],
      '#value' => 'Dane zgłaszającego',
    ];
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['npx_fv_person_toggle'] = [
      '#type' => 'checkbox',
      '#title' => 'Zgłaszający jest uczestnikiem szkolenia',
      '#attributes' => ['class' => ['npx-fv-person-toggle']],
    ];
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['fields_wrapper'] = [
      '#type' => 'container',
      '#attributes' => [ 'class' => ['npx-float', 'npx-float-5', 'npx-dynamic-fields'] ],
    ];
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['fields_wrapper']['npx_fv_firstname'] = [
      '#type' => 'textfield',
      '#title' => 'Imię',
      '#field_suffix' => '<div class="npx-form-error">Wpisz imię</div>',
      '#required' => true,
      '#title_display' => 'invisible',
      '#attributes' => [
        'placeholder' => 'Imię *',
      ],
      '#size' => false,
    ];
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['fields_wrapper']['npx_fv_name'] = [
      '#type' => 'textfield',
      '#title' => 'Nazwisko',
      '#field_suffix' => '<div class="npx-form-error">Wpisz nazwisko</div>',
      '#required' => true,
      '#title_display' => 'invisible',
      '#attributes' => [
        'placeholder' => 'Nazwisko *',
      ],
      '#size' => false,
    ];
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['fields_wrapper']['npx_fv_position'] = [
      '#type' => 'textfield',
      '#title' => 'Stanowisko zgłaszającego',
      '#field_suffix' => '<div class="npx-form-error">Wpisz stanowisko/rolę</div>',
      '#required' => true,
      '#title_display' => 'invisible',
//       '#description' => 'Powyższe dane służą nam do badania potrzeb oraz dopasowania programu do specyfiki zawodowej i potrzeb uczestnika.',
      '#attributes' => [
        'placeholder' => 'Stanowisko *',
      ],
      '#size' => false,
//       '#field_suffix' => '<a class="npx-i-icon" href="#" data-toggle="tooltip" title="Powyższe dane służą nam do badania potrzeb oraz dopasowania programu do specyfiki zawodowej i potrzeb uczestnika.">I</a>',
    ];
    if ($form_state->getValue('npx_pair_only') == true) {
      $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['fields_wrapper']['npx_fv_position']['#required'] = false;
      $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['fields_wrapper']['npx_fv_position']['#attributes']['placeholder'] = 'Stanowisko';
    }

    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['fields_wrapper']['npx_fv_email'] = [
      '#type' => 'email',
      '#title' => 'E-mail zgłaszającego',
      '#field_suffix' => '<div class="npx-form-error">Wpisz poprawny email małymi literami</div>',
      '#required' => true,
      '#title_display' => 'invisible',
      '#attributes' => [
        'pattern' => '[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$',
        'placeholder' => 'E-mail *',
      ],
      '#size' => false,
    ];
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['fields_wrapper']['npx_fv_phone'] = [
      '#type' => 'textfield',
      '#title' => 'Telefon',
      '#title_display' => 'invisible',
      '#field_suffix' => '<div class="npx-form-error">Wpisz poprawny nr telefonu</div>',
      '#required' => true,
      '#attributes' => [
        'placeholder' => 'Tel. komórkowy *',
      ],
      '#size' => false,
    ];
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['npx_fv_fields_additional_description'] = [
      '#markup' => '<div class="npx-form-additional-description">Informacja o stanowisku pozwala trenerowi dostosowywać treści szkolenia do profilu zawodowego Uczestnika a E-mail oraz telefon kontaktowy umożliwiają nam zbadanie potrzeb Uczestnika i przekazanie szczegółów organizacyjnych nt. szkolenia (SMS).</div>',
    ];


    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['npx_fv_paper'] = [
      '#type' => 'radios',
      '#title' => 'Materiały',
      '#title_display' => 'invisible',
      '#field_prefix' => 'Chcę otrzymać materiały w formie:',
      '#options' => [
        0 => 'ELEKTRONICZNEJ',
        1 => 'PAPIEROWEJ',
      ],
      '#default_value' => $form_state->getValue('npx_fv_paper', 0),
      '#attributes' => ['class' => ['hidden']],
      '#prefix' => '<div class="npx-fv-paper-wrapper">',
      '#suffix' => '</div>',
      '#states' => [
        'visible' => [
          ':input[name="npx_online_training"]' => [
            'value' => 1,
          ],
        ],
      ],
    ];
    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['npx_fv_paper_info'] =[
      '#type' => 'item',
      '#markup' => '<div class="npx-form-additional-description npx-fv-paper-info">Podaj dokładny adres do wysyłki w polu uwag poniżej.</div>',
      '#states' => [
        'visible' => [
          ':input[name="npx_fv_paper"]' => [
            'value' => 1,
          ],
        ],
      ],
    ];

    $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['npx_fv_comment'] = [
      '#type' => 'textarea',
      '#title' => 'Uwagi',
      '#title_display' => 'invisible',
      '#attributes' => [
        'placeholder' => 'Uwagi - tu wpisz np. nr zamówienia; deklarację "Szkolenie jest finansowane w co najmniej 70% ze środków publicznych" by uzyskać stawkę zwolnioną z VAT lub inne ważne informacje.',
        'class' => ['npx-float-5-after-textarea'],
      ],
    ];
    if ($form_state->getValue('npx_show_laptop_info') == true) {
      $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['npx_fv_person_wrapper']['npx_fv_comment']['#attributes']['placeholder'] = 'Uwagi - tu wpisz, że potrzebujesz wypożyczyć laptopa, nr zamówienia; deklarację "Szkolenie jest finansowane w co najmniej 70% ze środków publicznych" by uzyskać stawkę zwolnioną z VAT lub inne ważne informacje.';
    }


    for($i = 1; $i <= $amount; $i++) {
      $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper'] = [
        '#type' => 'container',
        '#attributes' => [ 'class' => ['npx-dynamic-fields'] ],
      ];
      $person_required = true;
      $required_state = [];
      if($i == 1) {
        $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper']['#states'] = [
          'visible' => [
            ':input[name="npx_fv_person_toggle"]' => [
              'checked' => FALSE,
            ],
          ],
        ];
        $person_required = false;
        $required_state = [
          'required' => [
            ':input[name="npx_fv_person_toggle"]' => [
              'checked' => FALSE,
            ],
          ],
        ];
      }


      $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper']['title'] = [
        '#type' => 'html_tag',
        '#tag' => 'h4',
        '#value' => 'Dane ' . $i . ' uczestnika szkolenia',
      ];
      $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper']['fields_wrapper'] = [
        '#type' => 'container',
        '#attributes' => [ 'class' => ['npx-float', 'npx-float-5'] ],
      ];
      $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper']['fields_wrapper']['participant_gender_' . $i] = [
        '#type' => 'radios',
        '#title' => 'Zwrot',
        '#options' => [
          'f' => 'Pani',
          'm' => 'Pan'
        ],
        '#prefix' => '<div class="container-inline">',
        '#suffix' => '</div>',
        '#required' => $person_required,
        '#default_value' => 'f',
        '#value' => 'f',
        '#access' => false,
      ];
      $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper']['fields_wrapper']['participant_firstname_' . $i] = [
        '#type' => 'textfield',
        '#title' => 'Imię',
        '#field_suffix' => '<div class="npx-form-error">Wpisz imię</div>',
        '#required' => $person_required,
        '#title_display' => 'invisible',
        '#attributes' => [
          'placeholder' => 'Imię*',
        ],
        '#states' => $required_state,
        '#size' => false,
      ];

      $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper']['fields_wrapper']['participant_name_' . $i] = [
        '#type' => 'textfield',
        '#title' => 'Nazwisko',
        '#field_suffix' => '<div class="npx-form-error">Wpisz nazwisko</div>',
        '#required' => $person_required,
        '#title_display' => 'invisible',
        '#attributes' => [
          'placeholder' => 'Nazwisko *',
        ],
        '#states' => $required_state,
        '#size' => false,
      ];
      $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper']['fields_wrapper']['participant_position_' . $i] = [
        '#type' => 'textfield',
        '#title' => 'Stanowisko',
        '#field_suffix' => '<div class="npx-form-error">Wpisz stanowisko/rolę</div>',
        '#title_display' => 'invisible',
        '#attributes' => [
          'placeholder' => 'Stanowisko *',
        ],
        '#required' => $person_required,
        '#states' => $required_state,
        '#size' => false,
      ];
      if ($form_state->getValue('npx_pair_only') == true) {
        $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper']['fields_wrapper']['participant_position_' . $i]['#required'] = false;
        $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper']['fields_wrapper']['participant_position_' . $i]['#attributes']['placeholder'] = 'Stanowisko';
        $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper']['fields_wrapper']['participant_position_' . $i]['#states'] = [];
      }

      if ($i > 1) {
        $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper']['fields_wrapper']['participant_position_' . $i]['#description'] = '';
      }
      $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper']['fields_wrapper']['participant_email_' . $i] = [
        '#type' => 'email',
        '#title' => 'E-mail',
        '#field_suffix' => '<div class="npx-form-error">Wpisz poprawny email małymi literami</div>',
        '#required' => $person_required,
        '#title_display' => 'invisible',
        '#attributes' => [
          'pattern' => '[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$',
          'placeholder' => 'E-mail *',
        ],
        '#states' => $required_state,
        '#size' => false,
      ];
      $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper']['fields_wrapper']['participant_phone_' . $i] = [
        '#type' => 'textfield',
        '#title' => 'Telefon',
//           '#description' => 'Telefon kontaktowy umożliwia nam zbadanie potrzeb Uczestnika i uwzględnienie ich w programie szkolenia. Prosimy o podanie numeru prywatnego telefonu komórkowego, gdyż trener może dzwonić poza standardowymi godzinami pracy.',
        '#field_suffix' => '<div class="npx-form-error">Wpisz poprawny nr telefonu</div>',
        '#title_display' => 'invisible',
        '#attributes' => [
          'placeholder' => 'Tel. komórkowy *',
        ],
        '#required' => $person_required,
        '#states' => $required_state,
        '#size' => false,
      ];
      if ($i > 1) {
        $form['npx_bottom_wrapper']['npx_payment_wrapper']['participants_wrapper']['participant_' . $i . '_wrapper']['fields_wrapper']['participant_phone_' . $i]['#description'] = '';
      }
    }
  }

  /**
   * Create FV fields
   *
   * @param array $form
   * @param FormStateInterface $form_state
   */
  public function createFVFields(array &$form, FormStateInterface $form_state) {
//     $form['npx_bottom_wrapper']['npx_fv_toggle'] = [
//       '#type' => 'checkbox',
//       '#title' => 'Chcę otrzymać fakturę VAT',
//       '#weight' => 1,
//     ];
    $form['npx_bottom_wrapper']['npx_fv_wrapper'] = [
      '#type' => 'container',
      '#title' => 'Faktura VAT',
      '#weight' => 1,
//       '#states' => [
//         'visible' => [
//           ':input[name="npx_fv_toggle"]' => [
//             'checked' => TRUE,
//           ],
//         ],
//       ],
    ];

    $form['npx_bottom_wrapper']['npx_fv_wrapper']['title'] = [
      '#type' => 'html_tag',
      '#tag' => 'h4',
      '#value' => 'Dane do faktury',
    ];
    $form['npx_bottom_wrapper']['npx_fv_wrapper']['fields_wrapper'] = [
      '#type' => 'container',
      '#attributes' => [ 'class' => ['npx-float', 'npx-float-5', 'npx-dynamic-fields'] ],
    ];
    $form['npx_bottom_wrapper']['npx_fv_wrapper']['fields_wrapper']['npx_fv_firm'] = [
      '#type' => 'textfield',
      '#title' => 'Firma',
      '#title_display' => 'invisible',
      '#attributes' => [
        'placeholder' => 'Firma',
      ],
//       '#required' => true,
    ];
    $form['npx_bottom_wrapper']['npx_fv_wrapper']['fields_wrapper']['npx_fv_address'] = [
      '#type' => 'textfield',
      '#title' => 'Adres',
      '#title_display' => 'invisible',
      '#attributes' => [
        'placeholder' => 'Adres *',
      ],
      '#required' => true,
    ];
    $form['npx_bottom_wrapper']['npx_fv_wrapper']['fields_wrapper']['npx_fv_post_code'] = [
      '#type' => 'textfield',
      '#title' => 'Kod',
      '#title_display' => 'invisible',
      '#attributes' => [
        'placeholder' => 'Kod *',
      ],
      '#required' => true,
    ];
    $form['npx_bottom_wrapper']['npx_fv_wrapper']['fields_wrapper']['npx_fv_city'] = [
      '#type' => 'textfield',
      '#title' => 'Miasto',
      '#title_display' => 'invisible',
      '#attributes' => [
        'placeholder' => 'Miasto *',
      ],
      '#required' => true,
    ];
    $form['npx_bottom_wrapper']['npx_fv_wrapper']['fields_wrapper']['npx_fv_nip'] = [
      '#type' => 'textfield',
      '#title' => 'NIP',
      '#title_display' => 'invisible',
      '#attributes' => [
        'placeholder' => 'NIP',
      ],
//       '#required' => true,
    ];

    if($form_state->getValue('npx_pair_only') == true) {
      $form['npx_bottom_wrapper']['npx_fv_toggle']['#default_value'] = true;
      $form['npx_bottom_wrapper']['npx_fv_toggle']['#disabled'] = true;
      $form['npx_bottom_wrapper']['npx_fv_wrapper']['title']['#value'] = 'Dane do faktury';
      $form['npx_bottom_wrapper']['npx_fv_wrapper']['fields_wrapper']['npx_fv_firm']['#attributes']['placeholder'] = 'Imię i nazwisko *';
      $form['npx_bottom_wrapper']['npx_fv_wrapper']['fields_wrapper']['npx_fv_firm']['#required'] = true;
      $form['npx_bottom_wrapper']['npx_fv_wrapper']['fields_wrapper']['npx_fv_address']['#attributes']['placeholder'] .= ' *';
      $form['npx_bottom_wrapper']['npx_fv_wrapper']['fields_wrapper']['npx_fv_address']['#required'] = true;
      $form['npx_bottom_wrapper']['npx_fv_wrapper']['fields_wrapper']['npx_fv_post_code']['#attributes']['placeholder'] .= ' *';
      $form['npx_bottom_wrapper']['npx_fv_wrapper']['fields_wrapper']['npx_fv_post_code']['#required'] = true;
      $form['npx_bottom_wrapper']['npx_fv_wrapper']['fields_wrapper']['npx_fv_city']['#attributes']['placeholder'] .= ' *';
      $form['npx_bottom_wrapper']['npx_fv_wrapper']['fields_wrapper']['npx_fv_city']['#required'] = true;
    }

  }

//   public function validateForm(array &$form, FormStateInterface $form_state) {

//   }


  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $message_data = [];

    $calculation = "Łączny koszt\n";
    $calculation .= sprintf("netto przed rabatami: %s zł\n", $form_state->getValue('npx_total'));
    $calculation .= sprintf("netto po rabatach: %s zł\n", $form_state->getValue('npx_total_discount'));

    $message_data['calculation'] = $calculation;

    $message_data['participants'] = [];

    if($form_state->getValue('npx_fv_person_toggle', false)) {
      $form_state->setValue('participant_firstname_1', trim($form_state->getValue('npx_fv_firstname')));
      $form_state->setValue('participant_name_1', trim($form_state->getValue('npx_fv_name')));
      $form_state->setValue('participant_position_1', trim($form_state->getValue('npx_fv_position')));
      $form_state->setValue('participant_email_1', trim($form_state->getValue('npx_fv_email')));
      $form_state->setValue('participant_phone_1', trim($form_state->getValue('npx_fv_phone')));
    }

    $amount = $form_state->getValue('npx_participants_amount');
    $participants = [];
    for ($i = 1; $i <= $amount; $i++) {
      $participant_values = [
        'type' => 'npxparticipant',
        'title' => trim($form_state->getValue('participant_firstname_' . $i)) . ' ' . trim($form_state->getValue('participant_name_' . $i)),
        'field_npxparticipant_gender' => $form_state->getValue('participant_gender_' . $i),
        'field_npxparticipant_firstname' => trim($form_state->getValue('participant_firstname_' . $i)),
        'field_npxparticipant_name' => trim($form_state->getValue('participant_name_' . $i)),
        'field_npxparticipant_position' => trim($form_state->getValue('participant_position_' . $i)),
        'field_npxparticipant_email' => trim($form_state->getValue('participant_email_' . $i)),
        'field_npxparticipant_phone' => trim($form_state->getValue('participant_phone_' . $i)),
        'created' => \Drupal::time()->getRequestTime(),
      ];
      $participant = $this->entityTypeManager->getStorage('npxparticipant')->create($participant_values);
      $participant->save();

      $participants[] = $participant;
      $message_data['participants'][] = $participant_values;
    }

    $submission_values = [
      'type' => 'npxsubmission',
      'title' => date(self::DATE_FORMAT),
      'field_npxsubmission_nid' => $form_state->getValue('npx_training'),
      'field_npxsubmission_date_nid' => $form_state->getValue('npx_training_date'),
      'field_npxsubmission_online' => $form_state->getValue('npx_online_training', 0),
      'field_npxsubmission_dis_payment' => 0,
      'field_npxsubmission_amount' => $form_state->getValue('npx_participants_amount'),
      'field_npxsubmission_paper' => $form_state->getValue('npx_fv_paper', 0),
      'field_npxsubmission_firm' => $form_state->getValue('npx_fv_firm', ''),
      'field_npxsubmission_address' => $form_state->getValue('npx_fv_address', ''),
      'field_npxsubmission_city' => $form_state->getValue('npx_fv_city', ''),
      'field_npxsubmission_post_code' => $form_state->getValue('npx_fv_post_code', ''),
      'field_npxsubmission_nip' => $form_state->getValue('npx_fv_nip', ''),
      'field_npxsubmission_firstname' => $form_state->getValue('npx_fv_firstname', ''),
      'field_npxsubmission_name' => $form_state->getValue('npx_fv_name', ''),
      'field_npxsubmission_position' => $form_state->getValue('npx_fv_position', ''),
      'field_npxsubmission_email' => $form_state->getValue('npx_fv_email', ''),
      'field_npxsubmission_phone' => $form_state->getValue('npx_fv_phone', ''),
      'field_npxsubmission_comment' => $form_state->getValue('npx_fv_comment', ''),
      'field_npxsubmission_pub_funding' => $form_state->getValue('npx_public_funding', 0),
      'field_npxsubmission_source' => 'Brak',
      'field_npxsubmission_accept_1' => $form_state->getValue('npx_accept_1', 0),
      'field_npxsubmission_accept_2' => $form_state->getValue('npx_accept_2', 0),
      'field_npxsubmission_accept_3' => $form_state->getValue('npx_accept_3', 0),
      'field_npxsubmission_accept_4' => $form_state->getValue('npx_accept_4', 0),
      'field_npxsubmission_hotel' => $form_state->getValue('npx_hotel_info', 0),
      'field_npxsubmission_calc_bp' => $form_state->getValue('npx_base_price'),
      'field_npxsubmission_calc_cb' => 0,
      'field_npxsubmission_calc_cd' => 0,
      'field_npxsubmission_calc_cdb' => 0,
      'field_npxsubmission_calc_t' => $form_state->getValue('npx_total'),
      'field_npxsubmission_calc_tb' => 0,
      'field_npxsubmission_calc_td' => $form_state->getValue('npx_total_discount'),
      'field_npxsubmission_calc_tdb' => 0,
      'field_npxsubmission_participants' => $participants,
      'field_npxsubmission_private' => $form_state->getValue('npx_private_participant', false),
      'field_npxsubmission_hash' => md5(\Drupal::time()->getRequestTime() . \Drupal::request()->getClientIp()),
      'created' => \Drupal::time()->getRequestTime(),
      'field_npxsubmission_user_ip' => $this->getRequest()->getClientIp(),
    ];

//     $info_regular_price = $form_state->getValue('npx_regular_price_info');
    $total_raw_price = $form_state->getValue('npx_total');
    if($form_state->getValue('npx_total_discount') < $total_raw_price) {
      $tdf = NpxDateUtils::createNow();
      $tdf->modify('+2 weekday');
      $tdf_txt = NpxDateUtils::getDateString($tdf);

      $submission_values['field_npxsubmission_price_desc'] = '* Koszt szkolenia w przypadku potwierdzenia zgłoszenia do ' . $tdf_txt . ', zaś w przypadku potwierdzenia zgłoszenia po tym terminie koszt szkolenia wynosi ' . $total_raw_price . 'zł.';
    }

    if($form_state->hasValue('npx_discount_code')) {
      $used_code = $form_state->getValue('npx_discount_code');
      NpxCouponHelper::setUsedFlag($used_code);
      $submission_values['field_npxsubmission_dcode'] = $used_code;
      $submission_values['field_npxsubmission_dvalue'] = self::DISCOUNT_CODE_VALUE;
    }

    $submission = $this->entityTypeManager->getStorage('npxsubmission')->create($submission_values);

    $submission->save();

    $new_discount_code = NpxCouponHelper::generateCode();
    if(!empty($new_discount_code)) {
      $coupon_id = NpxCouponHelper::saveCode($new_discount_code, self::DISCOUNT_CODE_VALUE, (int)$submission->id());
      $submission->field_npxsubmission_coupon_ref->target_id = $coupon_id;

      $message_data['coupon_code'] = $new_discount_code;
    }


    $submission->title->value = '#' .$submission->id() . ' ' . $submission->title->value;
    $submission->field_npxsubmission_hash = md5($submission->field_npxsubmission_hash->value . $submission->id());
    $submission->save();

    $message_data['submission'] = $submission_values;
    $message_data['submission_id'] = $submission->id();

    $this->moduleHandler->invokeAll('npx_training_form_submitted', [$submission]);

    $trainer_message = NpxTrainingFormHelper::prepareTrainerEmailMessage($message_data);
    $training_date = $this->entityTypeManager->getStorage('node')->load($submission->field_npxsubmission_date_nid->entity->id());
    $trainer = $this->entityTypeManager->getStorage('node')->load($training_date->field_npxtraining_date_trainer->entity->id());
    NpxTrainingFormHelper::sendTrainerNotification($submission->id(), $trainer_message, $trainer->field_npxtrainer_email->value);

    $submission->field_npxsubmission_trainer = $trainer->title->value;
    $submission->save();

    //check if last
    if ($training_date->field_npxtraining_date_available->value == 'ostatnie') {
      $training_date->field_npxtraining_date_available->setValue('nie');
      $training_date->save();
    }

    //use discount
    if($form_state->hasValue('npx_used_discount')) {
      $amount = (int) $form_state->getValue('npx_participants_amount');
      $discount_id = (int) $form_state->getValue('npx_used_discount');

      if($discount_id > 0) {
        $npxdiscount = new NpxDiscount($discount_id);
        $npxdiscount->useDiscount($amount);
      }
    }

    //use LINK discount
    if($form_state->hasValue('npx_used_link_discount')) {
      $amount = (int) $form_state->getValue('npx_participants_amount');
      $link_discount_id = (int) $form_state->getValue('npx_used_link_discount');

      if($link_discount_id > 0) {
        $npxlinkdiscount = new NpxLinkDiscount($link_discount_id);
        $npxlinkdiscount->useDiscount($amount);
      }
    }

    $redirect_query = ['npxsid' => $submission->id()];
    $training_node = $this->entityTypeManager->getStorage('node')->load($submission->field_npxsubmission_nid->entity->id());
    if($training_node->field_npx_thx_code->value != '') {
      $redirect_query['npxfrom'] = $training_node->field_npx_thx_code->value;
    }

    $form_state->setRedirect('npx_training_form.training_form_message', $redirect_query);
  }

  /**
   * Ajax callback
   *
   * Set training date field, set participant fields and recalculate price fields
   */
  static public function ajaxTrainingDate(array &$form, FormStateInterface $form_state) {
//     $renderer = \Drupal::service('renderer');
//     $form_state->setRebuild(true);
    $response = new AjaxResponse();
    $response->addCommand(new ReplaceCommand('#npx-training-date-wrapper', $form['npx_top_wrapper']['npx_dates_variant_wrapper']['npx_training_date_wrapper']));
    $response->addCommand(new ReplaceCommand('#npx-price-info-wrapper', $form['npx_top_wrapper']['npx_price_info_wrapper']));
    $response->addCommand(new ReplaceCommand('#npx-payment-wrapper', $form['npx_bottom_wrapper']['npx_payment_wrapper']));
//     $response->addCommand(new InvokeCommand('#npx-training-date-wrapper .form-item-npx-training-date:first-child input', 'click'));
    $response->addCommand(new ReplaceCommand('#npx-date-info-wrapper', $form['npx_top_wrapper']['npx_date_info_wrapper']));
    $response->addCommand(new ReplaceCommand('#npx-participants-amount-wrapper', $form['npx_top_wrapper']['npx_participants_amount_wrapper']));
//     $response->addCommand(new ReplaceCommand('#npx-training-type-info-wrapper-ajax', $form['npx_top_wrapper']['npx_training_type_info_wrapper']));
    $response->addCommand(new ReplaceCommand('#npx-online-training-wrapper', $form['npx_bottom_wrapper']['npx_online_training_wrapper']));
//     $form_state->setValue('npx_training_date', null);
    return $response;
  }

  /**
   * Ajax callback
   *
   * Set participant fields and recalculate price fields
   */
  public function ajaxCalculate(array &$form, FormStateInterface $form_state) {
    $response = new AjaxResponse();
    $response->addCommand(new ReplaceCommand('#npx-price-info-wrapper', $form['npx_top_wrapper']['npx_price_info_wrapper']));
    $response->addCommand(new ReplaceCommand('#npx-payment-wrapper', $form['npx_bottom_wrapper']['npx_payment_wrapper']));
//     $response->addCommand(new ReplaceCommand('#npx-training-type-info-wrapper-ajax', $form['npx_top_wrapper']['npx_training_type_info_wrapper']));
//     $response->addCommand(new ReplaceCommand('#npx-date-info-wrapper', $form['npx_top_wrapper']['npx_date_info_wrapper']));
    return $response;
  }

  /**
   * Ajax callback
   *
   * Set training date field, set participant fields and recalculate price fields
   */
  public function ajaxCalculateDeadline(array &$form, FormStateInterface $form_state) {
    $response = new AjaxResponse();
    $response->addCommand(new ReplaceCommand('#npx-payment-wrapper', $form['npx_bottom_wrapper']['npx_payment_wrapper']));
//     $response->addCommand(new ReplaceCommand('#npx-payment-deadline-wrapper', $form['npx_top_wrapper']['npx_payment_deadline_wrapper']));
    $response->addCommand(new ReplaceCommand('#npx-price-info-wrapper', $form['npx_top_wrapper']['npx_price_info_wrapper']));
    $response->addCommand(new ReplaceCommand('#npx-participants-amount-wrapper', $form['npx_top_wrapper']['npx_participants_amount_wrapper']));
    $response->addCommand(new ReplaceCommand('#npx-date-info-wrapper', $form['npx_top_wrapper']['npx_date_info_wrapper']));
    $response->addCommand(new ReplaceCommand('#npx-online-training-wrapper', $form['npx_bottom_wrapper']['npx_online_training_wrapper']));
//     $response->addCommand(new ReplaceCommand('#npx-training-type-info-wrapper-ajax', $form['npx_top_wrapper']['npx_training_type_info_wrapper']));
//     $form_state->setRebuild(true);
    return $response;
  }

  /**
   * Get npxtraining node NIDs
   *
   * @return array
   *  array of NIDs
   */
  public function getTrainingNids() {
    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->condition('type', 'npxtraining')
      ->condition('status', 1)
      ->sort('field_npxtraining_mailtitle')
      ->accessCheck(FALSE);
    return $query->execute();
  }

  /**
   * Get training date NIDs from training node
   *
   * @param object $training
   *  npxtraining node
   * @return array
   *  array of NIDs
   */
  public function getTrainingDateNids($training) {
    $nids = [];
    foreach($training->field_npxtraining_dates as $training_date) {
      if($training_date->entity->isPublished()) {
        $nids[] = $training_date->entity->id();
      }
    }
    return $nids;
  }


  /**
   * Get training taxonomy terms
   *
   * @return array
   *  array of taxonomy terms (TID => name)
   */
  public function getTrainingCategories() {
    $terms = $this->entityTypeManager->getStorage('taxonomy_term')->loadTree('training_category');
    $arr = [];
    if (!empty($terms)) {
      foreach($terms as $term) {
        $arr[$term->tid] = $term->name;
      }
    }
    return $arr;
  }

  /**
   * Get training NID from URL
   *
   * @return int or null
   */
  public function getTrainingIdFromUrl() {
    $params = UrlHelper::filterQueryParameters(\Drupal::request()->query->all());
    return (isset($params[self::TRAINING_ID_URL_PARAM]) ? intval($params[self::TRAINING_ID_URL_PARAM]) : null);
  }

  /**
   * Get training date NID from URL
   *
   * @return int or null
   */
  public function getTrainingDateIdFromUrl() {
    $params = UrlHelper::filterQueryParameters(\Drupal::request()->query->all());
    return (isset($params[self::TRAINING_DATE_ID_URL_PARAM]) ? intval($params[self::TRAINING_DATE_ID_URL_PARAM]) : null);
  }

  /**
   * Check training date
   *
   * @param object $training_date
   *  training date node
   * @return boolean
   */
  public function checkTrainingDate($training_date) {
    if ($training_date->field_npxtraining_date_available[0]->value != 'nie' || true) {
      $now = new \DateTime(date(self::DATE_FORMAT));
      $date = \DateTime::createFromFormat(self::DATE_FORMAT, substr($training_date->field_npxtraining_date_start->value, 0, 10));
      $date->setTime(0,0,0);

      return $date > $now;
    }
    return false;
  }

  /**
   * Check date month interval
   *
   * @param object $training_date
   *  training date node
   * @return boolean
   */
  public function checkDateMonthInterval($training_date) {
    $now = new \DateTime(date(self::DATE_FORMAT));
    $date = \DateTime::createFromFormat(self::DATE_FORMAT, substr($training_date->field_npxtraining_date_start[0]->value, 0, 10));
    $date->modify('-33 days');
    return ($date >= $now);
  }

  public function modifyDate($training_date) {
    $date = \DateTime::createFromFormat(self::DATE_FORMAT, substr($training_date->field_npxtraining_date_start[0]->value, 0, 10));
    $date->modify('-30 days');
    return $date->format('d.m');
  }

  public function reformatDate(string $date_string, string $date_format = self::DATE_FORMAT_DISPLAY) : string {
    $date = \DateTime::createFromFormat(self::DATE_FORMAT, $date_string);

    return $date->format($date_format);
  }

  public function getHours($node) {
    $out = '';
    $dcount = count($node->field_npxtraining_dates);
    if($dcount > 0) {
      $date = $node->field_npxtraining_dates[$dcount - 1];
    } else {
      return $out;
    }

    if($date->entity) {
      $fulldate = $this->entityTypeManager->getStorage('node')->load($date->entity->id());

      if($fulldate->get('field_npxtraining_date_hours')->value) {
        $out = $fulldate->get('field_npxtraining_date_hours')->value;
      } else {
        $count = count($fulldate->field_npxtraining_date_start);
        $days = 0;

        for($i = 0; $i < $count; $i++) {
          $days += self::calculateDays($fulldate->field_npxtraining_date_start[$i]->value, $fulldate->field_npxtraining_date_end[$i]->value);
        }
        $days_txt = $days > 1 ? ' dni | ' : ' dzień | ';
        $out =  $days . $days_txt . ($days * 8) . 'h';
      }
    }

    return $out;
  }

  public function getDateLabels(NodeInterface $node): array {
    $labels = [];

    $ids = [];
    foreach($node->get('field_npxtraining_dates') as $val) {
      if($val->entity) {
        $ids[] = $val->entity->id();
      }
    }
    $dates = $this->entityTypeManager->getStorage('node')->loadMultiple($ids);

    foreach($dates as $date_node) {
      if($this->checkTrainingDate($date_node)) {
        foreach($date_node->get('field_npxtraining_date_form') as $val) {
          $label = npx_field_npxtraining_date_form_values()[$val->value];
          $labels[$val->value] = $label;
        }
      }
    }

    return $labels;
  }

  static public function calculateDays($start, $end) {
    $date_start = new \DateTime($start);
    $date_end = new \DateTime($end);

    $date_diff = $date_start->diff($date_end);

    return $date_diff->format('%d') + 1;
  }

  protected function getSocialMediaBlock() {
    $config = [];
    $block = $this->blockManager->createInstance('social_sharing_block', $config);
    $build = $block->build();
    //     $variables['farmjournal_social_sharing'] = render($render);
    return \Drupal::service('renderer')->render($build);
  }

  private function debugLog(FormStateInterface $form_state, $line) {
    error_log('-------------------------------', 0);
    error_log('L: ' . $line . ' TRID: ' . $form_state->getValue('npx_training'), 0);
    error_log('L: ' . $line . ' VRID: ' . $form_state->getValue('npx_variant'), 0);
    error_log('L: ' . $line . ' TRDID: ' . $form_state->getValue('npx_training_date'), 0);
    error_log('-------------------------------', 0);
  }
}
