(function ($, Drupal, drupalSettings, once) {
  "use strict";

  var animateScroll = function (object, offset) {
    $("html, body").animate(
      { scrollTop: $(object).offset().top - offset },
      {
        duration: 500,
        step: function (now, tween) {
          var newOffset = $(object).offset().top - offset;
          if (tween.end !== newOffset) {
            tween.end = newOffset;
          }
        },
      }
    );
  };

  Drupal.behaviors.npxTrainingFormReset = {
    attach: function (context) {
      let $form = $(context).find("#npx-training-form");
      if ($form.length) {
        const formElements = once(
          "npx-training-form-reset",
          "#npx-training-form",
          context
        );
        formElements.forEach(function (element) {
          element.reset();
        });
      }
    },
  };

  Drupal.behaviors.npxTrainingFormDynamicFields = {
    attach: function (context) {
      let $form = $("#npx-training-form");
      if ($form.length) {
        let $inputs = $form.find(".npx-dynamic-fields input");
        if ($inputs.length) {
          $inputs.each(function () {
            let initial_width = false;
            if (this.value.length) {
              initial_width = $(this).closest("div").parent().width();
              let width = (this.value.length + 1) * 9;
              if (width >= initial_width) {
                $(this).closest("div").parent().width(width);
              }
            }

            const dynamicFieldElements = once(
              "npx-training-form-dynamic-fields",
              this,
              document
            );
            dynamicFieldElements.forEach(function (element) {
              $(element).keydown(function () {
                if (!initial_width) {
                  initial_width = $(this).closest("div").parent().width();
                }
                let width = (this.value.length + 1) * 9;
                if (width >= initial_width) {
                  $(this).closest("div").parent().width(width);
                }
              });
            });
          });
        }
      }
    },
  };

  Drupal.behaviors.npxTrainingFormExpand = {
    attach: function (context) {
      let $form = $("#npx-training-form");
      let $btn = $form.find("#npx-expand-bottom-wrapper");
      if ($form.length && $btn.length) {
        const expandBtnElements = once(
          "npx-training-form-expand",
          $btn,
          document
        );
        expandBtnElements.forEach(function (element) {
          $(element).click(function (e) {
            e.preventDefault();
            $form.addClass("with-bottom-wrapper");
            $form
              .find('input[name="npx_online_training"][value="0"]')
              .prop("checked", true)
              .change();
            $form
              .find('input[name="npx_online_training"][value="1"]')
              .prop("checked", false)
              .change();
            // Drupal.behaviors.npxTrainingForm.selectOption(true);
          });
        });
      }
    },
  };
  Drupal.behaviors.npxTrainingFormExpandOnline = {
    attach: function (context) {
      let $form = $("#npx-training-form");
      let $btn = $form.find("#npx-expand-bottom-wrapper-online");
      if ($form.length && $btn.length) {
        const expandBtnElements = once(
          "npx-training-form-expand",
          $btn,
          document
        );
        expandBtnElements.forEach(function (element) {
          $(element).click(function (e) {
            e.preventDefault();
            $form.addClass("with-bottom-wrapper");
            $form
              .find('input[name="npx_online_training"][value="0"]')
              .prop("checked", false)
              .change();
            $form
              .find('input[name="npx_online_training"][value="1"]')
              .prop("checked", true)
              .change();
            // Drupal.behaviors.npxTrainingForm.selectOption(true);
          });
        });
      }
    },
  };

  Drupal.behaviors.npxTrainingFormDiscountSubmit = {
    attach: function (context) {
      let $form = $("#npx-training-form");
      let $input = $form.find(".form-item-npx-discount-code input");
      let $btn = $form.find(".npx-discout-button");
      if ($form.length && $input.length && $btn.length) {
        const discountSubmitBtnElements = once(
          "npx-training-form-discount-submit",
          $btn,
          document
        );
        discountSubmitBtnElements.forEach(function (element) {
          $(element).click(function (e) {
            e.preventDefault();
            $input.change();
          });
        });
      }
    },
  };

  Drupal.behaviors.npxTrainingFormSpinner = {
    attach: function (context) {
      let $form = $("#npx-training-form");
      let $input = $form.find(".form-item-npx-participants-amount input");
      let max_val = $input.attr("data-max-val");

      if ($form.length && $input.length) {
        const spinnerInputElements = once(
          "npx-training-form-spinner",
          $input,
          document
        );
        spinnerInputElements.forEach(function (element) {
          $(element).spinner({
            spin: function (event, ui) {
              if (ui.value > max_val) {
                $(this).spinner("value", max_val);
                return false;
              } else if (ui.value < 1) {
                $(this).spinner("value", 1);
                return false;
              }
            },
            stop: function (event, ui) {
              $input.change();
            },
            icons: { down: "ui-icon-minus", up: "ui-icon-plus" },
          });
        });
      }
    },
  };

  Drupal.behaviors.npxTrainingFormCheckboxradio = {
    attach: function (context) {
      let $form = $("#npx-training-form");
      let $input = $form.find(
        ".js-form-item-npx-training-date input.form-radio"
      );
      if ($form.length && $input.length) {
        $input.each(function () {
          const checkboxradioElements = once(
            "npxTrainingFormCheckboxradio",
            this,
            document
          );
          checkboxradioElements.forEach(function (element) {
            $(element).change(function () {
              $input
                .closest(".js-form-item-npx-training-date")
                .removeClass("active");
              $(this)
                .closest(".js-form-item-npx-training-date")
                .addClass("active");
            });
          });
        });
      }

      let $input_radio = $form.find("input.form-checkbox");
      if ($input_radio.length) {
        $input_radio.each(function () {
          const checkboxradioElements = once(
            "npxTrainingFormCheckboxradio",
            this,
            document
          );
          checkboxradioElements.forEach(function (element) {
            $(element).change(function () {
              if ($(this).is(":checked")) {
                $(this).closest(".js-form-type-checkbox").addClass("active");
              } else {
                $(this).closest(".js-form-type-checkbox").removeClass("active");
              }
            });
          });
        });
      }
    },
  };

  Drupal.behaviors.npxTrainingFormSpoiler = {
    attach: function (context) {
      const spoilerTitleElements = once(
        "npx-training-form-spoiler",
        "#npx-training-form div.npx-spoiler-title",
        document
      );
      spoilerTitleElements.forEach(function (element) {
        $(element).click(function () {
          $(this)
            .children()
            .first()
            .toggleClass("show-icon")
            .toggleClass("hide-icon");
          $(this).parent().children().last().toggle();
        });
      });
    },
  };

  Drupal.behaviors.npxTrainingFormDateOption = {
    attach: function (context) {
      //        Drupal.behaviors.npxTrainingForm.selectOption();
    },
  };

  Drupal.behaviors.npxTrainingForm = {};
  Drupal.behaviors.npxTrainingForm.selectOption = function (force) {
    if (typeof force == undefined) {
      force = false;
    }
    setTimeout(function () {
      let $form = $("#npx-training-form");
      let hideHotel = false;
      if ($form.hasClass("with-bottom-wrapper")) {
        let isOnline = $form
          .find('input[name="npx_online_training"]')
          .prop("checked");
        if (isOnline) {
          //          let $select = $form.find('#npx-training-date-wrapper .form-item-npx-training-date.npx-option-date-online-wrapper:first input');
          hideHotel = true;
        } else {
          //          let $select = $form.find('#npx-training-date-wrapper .form-item-npx-training-date:not(".npx-option-date-online-wrapper"):first input');
        }
      } else {
      }
      let $select = $form.find(
        "#npx-training-date-wrapper .form-item-npx-training-date:first input"
      );
      if ($form.length && $select.length) {
        if (force) {
          $select.click();
        } else {
          const dateOptionSelectElements = once(
            "npx-training-form-date-option",
            $select,
            document
          );
          dateOptionSelectElements.forEach(function (element) {
            $(element).click();
          });
        }
        if (hideHotel) {
          $form.addClass("n-hide-hotel");
        } else {
          $form.removeClass("n-hide-hotel");
        }
      }
    }, 500);
  };

  Drupal.behaviors.npxTrainingFormTabs = {
    attach: function (context) {
      let $form = $(context).find("#npx-training-form");
      let $tabs = $form.find("#npx-tabs a");
      let $select = $form.find("#edit-npx-training");
      if ($form.length && $tabs.length) {
        //          let $program_tabs = $('.npx-program-tabs-wrapper .nav-tabs a');
        $tabs.each(function () {
          let $tab = $(this);
          let id = $tab.attr("data-npx-nid");
          const tabReadyElements = once("npx-tab-ready", this, document);
          tabReadyElements.forEach(function (element) {
            $(element).click(function (e) {
              e.preventDefault();
              $tabs.removeClass("npx-active-tab");
              $tab.addClass("npx-active-tab");
              $select.val(id);
              $select.change();

              // if($program_tabs.length) {
              //   $program_tabs.filter("[data-npx-nid='" + id + "']").on('shown.bs.tab', function (e) {
              //     let offset = $form.offset().top - 100;
              //     $('html, body').animate({scrollTop: offset}, 500);
              //   }).tab('show');
              // }
            });
          });
        });
      }
    },
  };

  Drupal.behaviors.npxTrainingFormVariants = {
    attach: function (context) {
      let $form = $(context).find("#npx-training-form");
      let $variants = $form.find('input[name="npx_variant"]');
      let $select = $form.find("#edit-npx-training");
      if ($form.length && $variants.length) {
        $variants.change(function () {
          let id = this.value;
          $select.val(id);
          $select.change();
        });
      }
    },
  };

  Drupal.behaviors.npxTrainingUserSwitch = {
    attach: function (context) {
      let $form = $("#npx-training-form");
      let $chkbox = $form.find(".npx-fv-person-toggle");
      let $title = $form.find(".npx-fv-user-title");
      if ($form.length && $chkbox.length) {
        //          console.debug('npxTrainingUserSwitch');
        const userSwitchCheckboxElements = once(
          "npx-training-user-switch",
          $chkbox,
          document
        );
        userSwitchCheckboxElements.forEach(function (element) {
          $(element).click(function () {
            if ($(this).prop("checked")) {
              $title.text("Dane zgłaszającego / 1 uczestnika");
            } else {
              $title.text("Dane zgłaszającego");
            }
          });
        });
      }
    },
  };

  Drupal.behaviors.npxTrainingFormValidation = {
    attach: function (context) {
      let formElements = once(
        "npx-training-form-validation",
        "form#npx-training-form",
        context
      );
      let $form = $(formElements);
      if ($form.length) {
        let $submit = $form.find("#edit-submit");
        $submit.click(function (e) {
          //            console.debug('npxTrainingFormValidation');
          let $fields = $form.find("input.form-text, input.form-email");
          $fields.each(function () {
            let $msg = $(this).closest("div.form-item").find(".npx-form-error");
            if ($(this).get(0).validity.valid) {
              if ($msg.length) {
                $msg.hide();
              }
            } else {
              if ($msg.length) {
                $msg.show();
              }
            }
          });
        });
      }
    },
  };

  Drupal.behaviors.npxTrainingFormCounterWidth = {
    attach: function (context) {
      let $form = $("form#npx-training-form");
      if ($form.length) {
        Drupal.behaviors.npxTrainingFormCounterWidth.activeBoxCalc($form);
        let npxIsResizing = false;
        let timer = setInterval(function () {
          if (npxIsResizing) {
            npxIsResizing = false;
            Drupal.behaviors.npxTrainingFormCounterWidth.activeBoxCalc($form);
          }
        }, 250);
        $(window).on("resize", function () {
          npxIsResizing = true;
        });
        $(document).ajaxComplete(function (e, xhr, settings) {
          Drupal.behaviors.npxTrainingFormCounterWidth.activeBoxCalc($form);
        });
      }
    },
    activeBoxCalc: function ($form) {
      let $activeBox = $form.find(".npx-calculation-box .npx-box-with-addons");
      if ($activeBox.length) {
        let leftWidth = $activeBox.find(".npx-price .npx-price-a").outerWidth();
        if ($(window).width() <= 766) {
          leftWidth = 0;
        }
        let $counterspacer = $activeBox.find(
          ".npx-counter-wrapper .npx-counter-spacer"
        );
        if ($counterspacer.length) {
          $counterspacer.width(leftWidth);
          $activeBox.find(".npx-counter-wrapper").css("visibility", "visible");
        }
        let $colorbox = $activeBox.find(".npx-social-colorbox-link");
        if ($colorbox.length) {
          //    $('.npx-price-b-c').css("top", "42px");
          $colorbox.css("padding-left", leftWidth);
          $colorbox.css("visibility", "visible");
        }
        /*   else {
          $('.npx-price-b-c').css("top", "20px");
        }*/
      }
    },
  };
  Drupal.behaviors.npxTrainingFormEmailToLower = {
    attach: function (context) {
      let $form = $("form#npx-training-form");
      if ($form.length) {
        $form.find('input[type="email"]').each(function () {
          const emailElements = once("npx-email-tolower", this, document);
          emailElements.forEach(function (element) {
            $(element).change(function () {
              let val = $(this).val();
              $(this).val(val.toLowerCase());
            });
          });
        });
      }
    },
  };

  Drupal.behaviors.npxTrainingFormDateOptionClass = {
    attach: function (context) {
      let $form = $("form#npx-training-form");
      if ($form.length) {
        $form.find(".npx-option-date-online").each(function () {
          $(this)
            .closest("div.form-item")
            .addClass("npx-option-date-online-wrapper");
        });
        Drupal.behaviors.npxTrainingForm.selectOption();
      }
    },
  };

  Drupal.behaviors.npxTrainingFormDateOptionToggle = {
    attach: function (context) {
      //        let $form = $('form#npx-training-form');
      //        if($form.length) {
      //          if($form.hasClass('with-bottom-wrapper')) {
      //            let isOnline = $form.find('input[name="npx_online_training"]').prop('checked');
      //            let $dates = $form.find('.form-item-npx-training-date');
      //            $dates.each( function () {
      //              let onlineOption = $(this).hasClass('npx-option-date-online-wrapper');
      //              $(this).show();
      //              if(isOnline && !onlineOption) {
      //                $(this).hide();
      //              } else if(!isOnline && onlineOption) {
      //                $(this).hide();
      //              }
      //            });
      //          }
      //        }
    },
  };

  Drupal.behaviors.npxTrainingFormStationaryBtn = {
    attach: function (context) {
      let $buttons = $("div.npx-form-stationary-btn");
      if ($buttons.length) {
        $buttons.each(function () {
          let text = $(this).html();
          $(this).replaceWith(
            '<div class="npx-form-stationary-btn-wrapper"><a class="npx-form-stationary-btn" href="#npx-participants-amount-wrapper">' +
              text +
              "</a></div>"
          );
          const stationaryBtnElements = once(
            "npx-training-form-stationary-btn",
            "a.npx-form-stationary-btn",
            document
          );
          stationaryBtnElements.forEach(function (element) {
            $(element).click(function (e) {
              e.preventDefault();
              let $form = $("#npx-training-form");
              $form.addClass("with-bottom-wrapper");
              $form
                .find('input[name="npx_online_training"][value="0"]')
                .prop("checked", true)
                .change();
              $form
                .find('input[name="npx_online_training"][value="1"]')
                .prop("checked", false)
                .change();
              Drupal.behaviors.npxTrainingForm.selectOption(true);
              animateScroll($.attr(this, "href"), 100);
            });
          });
        });
      }
    },
  };
  Drupal.behaviors.npxTrainingFormLiveBtn = {
    attach: function (context) {
      let $buttons = $("div.npx-form-live-btn");
      if ($buttons.length) {
        $buttons.each(function () {
          let text = $(this).html();
          $(this).replaceWith(
            '<div class="npx-form-live-btn-wrapper"><a class="npx-form-live-btn" href="#npx-participants-amount-wrapper">' +
              text +
              "</a></div>"
          );
          const liveBtnElements = once(
            "npx-training-form-live-btn",
            "a.npx-form-live-btn",
            document
          );
          liveBtnElements.forEach(function (element) {
            $(element).click(function (e) {
              e.preventDefault();
              let $form = $("#npx-training-form");
              $form.addClass("with-bottom-wrapper");
              $form
                .find('input[name="npx_online_training"][value="0"]')
                .prop("checked", false)
                .change();
              $form
                .find('input[name="npx_online_training"][value="1"]')
                .prop("checked", true)
                .change();
              Drupal.behaviors.npxTrainingForm.selectOption(true);
              animateScroll($.attr(this, "href"), 100);
            });
          });
        });
      }
    },
  };
  Drupal.behaviors.npxTrainingFormSpoilerToggle = {
    attach: function (context) {
      let $form = $("form#npx-training-form");
      let $btn = $form.find(".n-spoiler-toggle");
      const spoilerToggleElements = once(
        "npx-training-form-spoiler-toggle",
        $btn,
        document
      );
      spoilerToggleElements.forEach(function (element) {
        $(element).click(function (e) {
          e.preventDefault();
          $(this).closest(".npx-spoiler").find(".npx-spoiler-title").click();
        });
      });
    },
  };
  Drupal.behaviors.npxTrainingFormMoreDatesToggle = {
    attach: function (context) {
      const $form = $("form#npx-training-form");
      const $options_wrapper = $form.find("#edit-npx-training-date");
      const $options_to_hide = $options_wrapper.find('.hide-me');

      once('npx-training-form-more-dates-hide', $options_to_hide, context).forEach((el) => {
        $(el).closest('.form-item-npx-training-date').addClass('hidden');
      });

      const $btn = $form.find(".npx-more-dates-link");
      once("npx-training-form-more-dates-toggle", $btn, document).forEach(function (element) {
        $(element).click(function (e) {
          e.preventDefault();
          $('.form-item-npx-training-date').removeClass('hidden');
          $(this).addClass('hidden');
        });
      });
    },
  };
})(jQuery, Drupal, drupalSettings, once);
