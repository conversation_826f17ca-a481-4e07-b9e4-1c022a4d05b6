<?php

namespace Dr<PERSON>al\npx_test\Form;

use <PERSON><PERSON><PERSON>\Core\Form\FormBase;
use <PERSON><PERSON><PERSON>\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\Core\Messenger\MessengerInterface;
use <PERSON><PERSON>al\Core\Url;
use Symfony\Component\DependencyInjection\ContainerInterface;
use <PERSON><PERSON>al\npx_test\NpxTestManagerInterface;

/**
 * Class NpxTestImportForm.
 */
class NpxTestImportForm extends FormBase {
  const UPLOAD_LOCATION = 'public://';
  /**
   * Drupal\npx_test\NpxTestManagerInterface definition.
   *
   * @var \Drupal\npx_test\NpxTestManagerInterface
   */
  protected $npxTestManager;
  
  /**
   * The Messenger service.
   *
   * @var \Drupal\Core\Messenger\MessengerInterface
   */
  protected $messenger;
  
  /**
   * Class constructor.
   */
  public function __construct(NpxTestManagerInterface $npx_test_manager, MessengerInterface $messenger) {
    $this->npxTestManager = $npx_test_manager;
    $this->messenger = $messenger;
  }
  
  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    // Instantiates this form class.
    return new static(
      // Load the service required to construct this class.
      $container->get('npx_test.manager'),
      $container->get('messenger')
    );
  }
  
  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'npx_test_import_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    
//     $this->npxTestManager->deleteAll();
    
    $test_entities = $this->npxTestManager->getTestEntities();
    
    $options = [];
    
    foreach($test_entities as $entity) {
      if($entity->isEmpty()) {
        $options[$entity->id()] = $entity->getName();
      }
    }
    
    if(empty($options)) {
      $this->messenger->addWarning('Brak pustych testów kompetencji.');
      
      $destination_path = Url::fromRoute('npx_test.npx_test_import_form')->toString();
      
      $form['add_test_link'] = [
        '#type' => 'link',
        '#title' => 'Dodaj test kompetencji',
        '#url' => Url::fromRoute('entity.npxtest.add_form', [],
            [
              'query' => ['destination' => $destination_path],
              'attributes' => ['class' => ['button', 'button-action', 'button--primary', 'button--small']],
            ]
          ),
      ];
      
      return $form;
    }
    
//     kint($test_entities);
    
    $options = [];
    
    foreach($test_entities as $entity) {
      if($entity->isEmpty()) {
        $options[$entity->id()] = $entity->getName();
      }
    }
    
    $form['test_entity'] = [
      '#type' => 'select',
      '#title' => 'Test kompetencji',
      '#options' => $options,
      '#empty_option' => '- Wybierz test kompetencji -',
      '#required' => true,
    ];
    
    $form['fid_competences'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('CSV Kompetencje'),
      '#upload_location' => self::UPLOAD_LOCATION,
      '#upload_validators' => [
        'file_validate_extensions' => ['csv'],
      ],
      '#required' => true,
      '#weight' => '0',
    ];
    $form['fid_questions'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('CSV Pytania'),
      '#upload_location' => self::UPLOAD_LOCATION,
      '#upload_validators' => [
        'file_validate_extensions' => ['csv'],
      ],
      '#required' => true,
      '#weight' => '0',
    ];
    $form['fid_answers'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('CSV Odpowiedzi'),
      '#upload_location' => self::UPLOAD_LOCATION,
      '#upload_validators' => [
        'file_validate_extensions' => ['csv'],
      ],
      '#weight' => '0',
    ];
    
    $form['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Submit'),
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    parent::validateForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $import_status = false;
    $id_test = (int)$form_state->getValue('test_entity');
    $fid_competences = $form_state->getValue(['fid_competences', 0]);
    $fid_questions = $form_state->getValue(['fid_questions', 0]);
    $fid_answers = $form_state->getValue(['fid_answers', 0]);
    //$test_entity = $this->npxTestManager->getTestEntity($id_test);
    
    if($id_test && !empty($fid_competences)) {
      $import_status = $this->npxTestManager->importData($id_test, $fid_competences, $fid_questions, $fid_answers);
    }
    
    if($import_status) {
      $this->messenger()->addStatus('OK');
    } else {
      $this->messenger()->addError('ERROR!');
    }
  }
  
//   protected function get
}
