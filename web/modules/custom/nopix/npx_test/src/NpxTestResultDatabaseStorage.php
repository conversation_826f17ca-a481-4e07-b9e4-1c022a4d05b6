<?php

namespace Drupal\npx_test;
use Drupal\Core\Database\Connection;
use <PERSON>upal\Component\Datetime\TimeInterface;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * Class NpxTestResultDatabaseStorage.
 */
class NpxTestResultDatabaseStorage implements NpxTestResultDatabaseStorageInterface {
  const DB_TABLE_NAME = 'npx_test_result';
  
  /**
   * Drupal\Core\Database\Connection definition.
   *
   * @var \Drupal\Core\Database\Connection
   */
  protected $database;

  /**
   * @var \Drupal\Component\Datetime\TimeInterface
   */
  protected $time;
  
  /**
   * @var \Symfony\Component\HttpFoundation\Request
   */
  protected $request;
  
  /**
   * Constructs a new NpxTestResultDatabaseStorage object.
   */
  public function __construct(Connection $database, TimeInterface $time, RequestStack $request_stack) {
    $this->database = $database;
    $this->time = $time;
    $this->request = $request_stack->getCurrentRequest();
  }
  
  /**
   * {@inheritDoc}
   * @see \Drupal\npx_test\NpxTestResultDatabaseStorageInterface::get()
   */
  public function get(int $test_id, string $email): array {
    $query = $this->database->select(self::DB_TABLE_NAME, 'res');
    $query->fields('res', ['result']);
    $query->condition('res.tid', $test_id);
    $query->condition('res.email', $email);
    return $query->execute()->fetchAll();
  }
  
  /**
   * {@inheritDoc}
   * @see \Drupal\npx_test\NpxTestResultDatabaseStorageInterface::create()
   */
  public function create(int $test_id, string $email, string $result_data, bool $marketing_agreement): bool {
    $query = $this->database
    ->insert(self::DB_TABLE_NAME)
    ->fields([
      'tid' => $test_id,
      'email' => $email,
      'result' => $result_data,
      'ip' => $this->request->getClientIp(),
      'timestamp' => $this->time->getRequestTime(),
      'marketing_agreement' => $marketing_agreement ? 1 : 0,
    ]);
    try {
      return $query->execute();
    } catch (\Exception $e) {
      \Drupal::logger('npx_test')->error($e->getMessage());
      return false;
    }
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_test\NpxTestResultDatabaseStorageInterface::getAllByTestId()
   */
  public function getAllByTestId(int $test_id): array {
    $query = $this->database->select(self::DB_TABLE_NAME, 'res');
    $query->fields('res', ['result']);
    $query->condition('res.tid', $test_id);
    return $query->execute()->fetchAll();
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_test\NpxTestResultDatabaseStorageInterface::delete()
   */
  public function delete(int $test_id, string $email) {
    $this->database
    ->delete(self::DB_TABLE_NAME)
    ->condition('tid', $test_id)
    ->condition('email', $email)
    ->execute();
  }

  public function deleteAllByTestId(int $test_id) {
    $this->database
    ->delete(self::DB_TABLE_NAME)
    ->condition('tid', $test_id)
    ->execute();
  }
  
  /**
   * {@inheritDoc}
   * @see \Drupal\npx_test\NpxTestResultDatabaseStorageInterface::countByTestId()
   */
  public function countByTestId(int $test_id): int {
    $query = $this->database->select(self::DB_TABLE_NAME, 'res');
    $query->condition('res.tid', $test_id);
    
    return $query->countQuery()->execute()->fetchField();
  }
}
