<?php

namespace Drupal\npx_member_client\Form;

use Drupal\Core\Form\ConfigFormBase;
use <PERSON>upal\Core\Form\FormStateInterface;

/**
 * Class MemberClientConfigForm.
 */
class MemberClientConfigForm extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return [
      'npx_member_client.memberclientconfig',
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'member_client_config_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('npx_member_client.memberclientconfig');
    $form['service_enabled'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('Service enabled.'),
      '#description' => $this->t('Service enabled if checked.'),
      '#default_value' => $config->get('service_enabled'),
    ];
    $form['service_url'] = [
      '#type' => 'url',
      '#title' => $this->t('Service url'),
      '#description' => $this->t('Add member service URL.'),
      '#default_value' => $config->get('service_url'),
    ];
    $form['user_name'] = [
      '#type' => 'textfield',
      '#title' => $this->t('User name'),
      '#description' => $this->t('Remote user name.'),
      '#maxlength' => 64,
      '#size' => 64,
      '#default_value' => $config->get('user_name'),
    ];
    $form['password'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Password'),
      '#description' => $this->t('Remote user password.'),
      '#maxlength' => 64,
      '#size' => 64,
      '#default_value' => $config->get('password'),
    ];
    $form['backend_email'] = [
      '#type' => 'email',
      '#title' => $this->t('Backend email'),
      '#description' => $this->t('Admin error notifications email.'),
      '#maxlength' => 64,
      '#size' => 64,
      '#default_value' => $config->get('backend_email'),
    ];
    
    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    parent::submitForm($form, $form_state);

    $this->config('npx_member_client.memberclientconfig')
      ->set('service_enabled', $form_state->getValue('service_enabled'))
      ->set('service_url', $form_state->getValue('service_url'))
      ->set('user_name', $form_state->getValue('user_name'))
      ->set('password', $form_state->getValue('password'))
      ->set('backend_email', $form_state->getValue('backend_email'))
      ->save();
  }

}
