<?php

namespace Drupal\npx_member_client\Plugin\QueueWorker;

use <PERSON><PERSON><PERSON>\Core\Queue\QueueWorkerBase;
use <PERSON><PERSON>al\Core\Plugin\ContainerFactoryPluginInterface;
use Dr<PERSON>al\npx_member_client\MemberClientManagerService;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Plugin implementation of the npx_add_member_queue queueworker.
 *
 * @QueueWorker (
 *   id = "npx_add_member_queue",
 *   title = @Translation("Add remote members."),
 *   cron = {"time" = 30}
 * )
 */
class AddMemberWorker extends QueueWorkerBase  implements ContainerFactoryPluginInterface {
  
  /**
   * MemberClientManagerService
   * 
   * @var \Drupal\npx_member_client\MemberClientManagerService
   */
  protected $memberClientManager;
  
  /**
   * Constructs a new NpxFreshmailSubscribeBlock object.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param string $plugin_definition
   *   The plugin implementation definition.
   */
  public function __construct(
      array $configuration,
      $plugin_id,
      $plugin_definition,
      MemberClientManagerService $member_client_manager
      ) {
        parent::__construct($configuration, $plugin_id, $plugin_definition);
        $this->memberClientManager = $member_client_manager;
  }
  
  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('npx_member_client.manager')
    );
  }
  
  /**
   * {@inheritdoc}
   */
  public function processItem($data) {
    $this->memberClientManager->addMemberRemote((int)$data['nid'], (int)$data['dnid'], (int)$data['pid']);
  }

}
