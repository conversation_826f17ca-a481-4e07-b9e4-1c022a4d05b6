<?php
namespace Drupal\npx_notify;


use Drupal\npx\NpxTrainingDate;
use Drupal\npx\NpxTrainingClosed;
use Drupal\Core\Render\Markup;
// use Drupal\npx_notify\NameHelper;

/**
 * Notification helper class
 *
 * <AUTHOR>
 */
class NotifyHelper{
  /** Date format for date comparison */
  const DATE_FORMAT = 'Y-m-d';

  /**
   * Create Notification Entities
   *
   * @param object $submission
   *   npxsubmission Entity
   */
  public static function createNotifications($submission) {
    $container = \Drupal::getContainer();

    $training = $container->get('entity_type.manager')->getStorage('node')->load($submission->field_npxsubmission_nid->entity->id());
    $training_date = $container->get('entity_type.manager')->getStorage('node')->load($submission->field_npxsubmission_date_nid->entity->id());
    //$trainer = $container->get('entity.manager')->getStorage('node')->load($training_date->field_npxtraining_date_trainer->entity->id());

    foreach($training->field_npxtraining_messages as $msg) {
      if (empty($msg->entity)) {
        continue;
      }

      $message = $container->get('entity_type.manager')->getStorage('npxmessage')->load($msg->entity->id());

      $date_to_send = self::calculateDateToSend($submission, $message, $training_date);

      $mail_values = [
        'type' => 'npxmail',
        'title' => sprintf('Zgłoszenie: #%s, do wysłania: %s, szablon: %s', $submission->id(), $date_to_send, $message->title->value),
        'field_npxmail_submission' => $submission,
        'field_npxmail_message' => $message,
        'field_npxmail_date_to_send' => $date_to_send,
        'field_npxmail_sent' => 0,
        'created' => \Drupal::time()->getRequestTime(),
      ];
      $mail = \Drupal::entityTypeManager()->getStorage('npxmail')->create($mail_values);
      $mail->save();
    }
  }
  
  /**
   * Create Notification Entities for closed training
   *
   * @param object $node
   *   node Entity
   */
  public static function createNotificationsForClosed($node) {
    $container = \Drupal::getContainer();

    foreach($node->field_npxtraining_messages as $msg) {
      if (empty($msg->entity)) {
        continue;
      }

      $message = $container->get('entity_type.manager')->getStorage('npxmessage')->load($msg->entity->id());

      $date_to_send = self::calculateDateToSendForClosed($message, $node);

      $mail_values = [
        'type' => 'npxmail',
        'title' => sprintf('Szkolenie zamknięte: #%s, do wysłania: %s, szablon: %s', $node->id(), $date_to_send, $message->title->value),
        'field_npxmail_node' => $node,
        'field_npxmail_submission' => null,
        'field_npxmail_message' => $message,
        'field_npxmail_date_to_send' => $date_to_send,
        'field_npxmail_sent' => 0,
        'created' => \Drupal::time()->getRequestTime(),
      ];
      $mail = \Drupal::entityTypeManager()->getStorage('npxmail')->create($mail_values);
      $mail->save();
    }
  }

  /**
   * Delete Notification Entities
   *
   * @param object $submission
   *   npxsubmission Entity
   */
  public static function deleteNotifications($submission) {
    /**
     * @var $query \Drupal\Core\Entity\Query\QueryInterface
     */
    $query = \Drupal::entityQuery('npxmail')
    ->condition('field_npxmail_submission.target_id', $submission->id())
    ->accessCheck(FALSE);


    $ids = $query->execute();

    /**
     * @var $manager \Drupal\Core\Entity\EntityStorageInterface
     */
    $manager = \Drupal::getContainer()->get('entity_type.manager')->getStorage('npxmail');
    $entities = $manager->loadMultiple($ids);
    $manager->delete($entities);
  }
  
  /**
   * Delete Notification Entities
   *
   * @param object $submission
   *   node Entity
   */
  public static function deleteNotificationsClosed($node) {
    /**
     * @var $query \Drupal\Core\Entity\Query\QueryInterface
     */
    $query = \Drupal::entityQuery('npxmail')
      ->condition('field_npxmail_node.target_id', $node->id())
      ->accessCheck(FALSE);

    $ids = $query->execute();

    /**
     * @var $manager \Drupal\Core\Entity\EntityStorageInterface
     */
    $manager = \Drupal::getContainer()->get('entity_type.manager')->getStorage('npxmail');
    $entities = $manager->loadMultiple($ids);
    $manager->delete($entities);
  }

  /**
   * Calculate date to send
   *
   * @param object $submission
   *   npxsubmission Entity
   * @param object $message
   *   npxmessage Entity
   * @param object $training_date
   *   npxtraining_date Node
   *
   * @return string
   *   formatted date string
   */
  public static function calculateDateToSend($submission, $message, $training_date) {
    $interval = $message->field_npxmessage_interval_value->value;
    $date_to_send = '';
    switch ($message->field_npxmessage_interval_type->value) {
      case 'AS': //Po zgłoszeniu
        $start_date = \DateTime::createFromFormat(self::DATE_FORMAT, $training_date->field_npxtraining_date_start->value);
        $date = new \DateTime();
        $date->setTimestamp($submission->created->value);
        $date->modify('+ ' . $interval . ' weekdays');
        if($date >= $start_date) {
          $now = new \DateTime();
          $date_to_send = $now->format(self::DATE_FORMAT);
        }
        else {
          $date_to_send = $date->format(self::DATE_FORMAT);
        }
        break;
      case 'BT': //Przed szkoleniem
        $date = new \DateTime($training_date->field_npxtraining_date_start->value);
        $date->modify('- ' . $interval . ' weekdays');
        $date_to_send = $date->format(self::DATE_FORMAT);
        break;
      case 'AT': //Po szkoleniu
        $count = count($training_date->field_npxtraining_date_end);
        $date = new \DateTime($training_date->field_npxtraining_date_end[$count - 1]->value);
        $date->modify('+ ' . $interval . ' weekdays');
        $date_to_send = $date->format(self::DATE_FORMAT);
        break;
    }

    return $date_to_send;
  }
  
  /**
   * Calculate date to send
   *
   * @param object $message
   *   npxmessage Entity
   * @param object $training_date
   *   npxtraining_date Node
   *
   * @return string
   *   formatted date string
   */
  public static function calculateDateToSendForClosed($message, $training_date) {
    $interval = $message->field_npxmessage_interval_value->value;
    $date_to_send = '';
    switch ($message->field_npxmessage_interval_type->value) {
      case 'AS': //Po zgłoszeniu
        $start_date = \DateTime::createFromFormat(self::DATE_FORMAT, $training_date->field_npxtraining_date_start->value);
        $date = new \DateTime();
        $date->setTimestamp($training_date->changed->value);
        $date->modify('+ ' . $interval . ' weekdays');
        if($date >= $start_date) {
          $now = new \DateTime();
          $date_to_send = $now->format(self::DATE_FORMAT);
        }
        else {
          $date_to_send = $date->format(self::DATE_FORMAT);
        }
        break;
      case 'BT': //Przed szkoleniem
        $date = new \DateTime($training_date->field_npxtraining_date_start->value);
        $date->modify('- ' . $interval . ' weekdays');
        $date_to_send = $date->format(self::DATE_FORMAT);
        break;
      case 'AT': //Po szkoleniu
        $count = count($training_date->field_npxtraining_date_end);
        $date = new \DateTime($training_date->field_npxtraining_date_end[$count - 1]->value);
        $date->modify('+ ' . $interval . ' weekdays');
        $date_to_send = $date->format(self::DATE_FORMAT);
        break;
    }

    return $date_to_send;
  }

  public static function subtractWeekDays($date_str, $days = 5) {
    $date = \DateTime::createFromFormat(self::DATE_FORMAT, $date_str);
    $date->setTime(0,0,0);
    $date->modify('- ' . $days . ' weekday');
    return $date->format(self::DATE_FORMAT);
  }

  /**
   * Prepare notification queue.
   */
  public static function prepareNotificationQueue() {
    $now = new \DateTime();

    $h = (int)$now->format('H');
    if( $h >= 8 && $h <= 17 ) {

      $query = \Drupal::entityQuery('npxmail')
      ->condition('field_npxmail_date_to_send', $now->format(self::DATE_FORMAT), '<=')
      ->condition('field_npxmail_sent', 0)
      ->accessCheck(FALSE);


      $ids = $query->execute();
      $queue = \Drupal::queue('npx_notify_sender');
      foreach ($ids as $id) {
        $data = ['id' => $id];
        $queue->createItem($data);
      }
    }
  }

  /**
   * Send notifications
   *
   * @param integer $sid
   *   npxmail entity ID
   */
  public static function sendNotifications($mid) {
    \Drupal::logger('npx_notify')->notice('NotificationSender: @id', ['@id' => $mid]);
    $container = \Drupal::getContainer();
    
    $mail = $container->get('entity_type.manager')->getStorage('npxmail')->load($mid);
    if($mail->field_npxmail_node->entity) {
      self::sendNotificationsClosed($mail);
    } elseif($mail->field_npxmail_submission->entity) {
      self::sendNotificationsOpen($mail);
    }
  }
  
  protected static function sendNotificationsOpen($mail) {
    $container = \Drupal::getContainer();
    if(!$mail->field_npxmail_submission->entity) {
      return;
    }
    if(!$mail->field_npxmail_message->entity) {
      return;
    }
    $message = $container->get('entity_type.manager')->getStorage('npxmessage')->load($mail->field_npxmail_message->entity->id());

    $submission = $container->get('entity_type.manager')->getStorage('npxsubmission')->load($mail->field_npxmail_submission->entity->id());
    $training = $container->get('entity_type.manager')->getStorage('node')->load($submission->field_npxsubmission_nid->entity->id());
    if (!isset($submission->field_npxsubmission_date_nid->entity)) {
      return;
    }
    $date = $container->get('entity_type.manager')->getStorage('node')->load($submission->field_npxsubmission_date_nid->entity->id());
    $trainer = $container->get('entity_type.manager')->getStorage('node')->load($date->field_npxtraining_date_trainer->entity->id());

    $subject = $message->field_npxmessage_subject->value;
    $body_m = $message->field_npxmessage_body_m->value;
    $body_f = $message->field_npxmessage_body_f->value;
    $from = $message->field_npxmessage_from->value;
    $attach_certificate = (bool)$message->field_npxmessage_attach_cert->value;

    $mailManager = \Drupal::service('plugin.manager.mail');
    $module = 'npx_notify';
    $key = NPX_NOTIFY_EMAIL_KEY;
    $langcode = \Drupal::languageManager()->getDefaultLanguage()->getId();
    $send = true;

    global $base_url;

    $date_day_value = $date->field_npxtraining_date_start->value;
    if($date->field_npxtraining_date_start->value != $date->field_npxtraining_date_end->value) {
      $date_day_value .= ' - ' . $date->field_npxtraining_date_end->value;
    }

    $date_5_prev = self::subtractWeekDays($date->field_npxtraining_date_start->value);

    $npxDateObj = new NpxTrainingDate((int)$submission->field_npxsubmission_date_nid->entity->id());

    $tokens = [
      '%training:title%' => $training->field_npxtraining_mailtitle->value,
      '%training:url%' => $base_url . '/node/'.$training->id(),
      'url%' => $base_url . '/node/'.$training->id(),
      '%date:day%' => $date_day_value,
      '%date:day-5%' => $date_5_prev,
      '%date:hours%' => $npxDateObj->getHoursOverride(),
      '%trainer%' => $trainer->field_npxtrainer_signature->value,
      '%trainer:name%' => $trainer->title->value,
      '%trainer:email%' => $trainer->field_npxtrainer_email->value,
      '%trainer:phone%' => $trainer->field_npxtrainer_phone->value,
      '%trainer:inslnk%' => '<a href="' . $trainer->field_npxtrainer_inslnk->value . '">TEN LINK</a>',
      '%room:name%' => $npxDateObj->getRoomName(),
      '%room:desc%' => $npxDateObj->getRoomDescription(),
      '%room:link%' => $npxDateObj->getRoomMapLinkNotEmbed(),
      '%room:info%' => $npxDateObj->getRoomInformation(),
      '%room:food%' => $npxDateObj->getRoomFeeding(),
      '%zoom:link%' => $npxDateObj->getZoomLink(),
      '%zoom:message%' => $npxDateObj->getZoomMessage(),
    ];
    self::replaceTokens($tokens, $subject);
    self::replaceTokens($tokens, $body_f);
    self::replaceTokens($tokens, $body_m);
    self::replaceTokens($tokens, $from);

    foreach ($submission->field_npxsubmission_participants as $elem) {
      $user_tokens = [];
      $files = [];
      $cert_file = null;
      if($elem->entity) {
        $participant = $container->get('entity_type.manager')->getStorage('npxparticipant')->load($elem->entity->id());
        
        if($attach_certificate) {
          $cert_file = \Drupal::entityTypeManager()->getStorage('file')->load($participant->field_npxparticipant_cert->target_id);
        }
        
        $user_tokens = [
          '%user:rawname%' => $participant->field_npxparticipant_firstname->value,
          '%user:name%' => NameHelper::getName($participant->field_npxparticipant_firstname->value),
          '%user:phrase%' => NameHelper::getPhrase($participant->field_npxparticipant_firstname->value),
          '%user:phrasename%' => NameHelper::getNameWithPhrase($participant->field_npxparticipant_firstname->value),
          '%user:phrase-mian%' => NameHelper::getPhraseVariation($participant->field_npxparticipant_firstname->value, 'mian'),
          '%user:phrase-dop%' => NameHelper::getPhraseVariation($participant->field_npxparticipant_firstname->value, 'dop'),
          '%user:phrase-cel%' => NameHelper::getPhraseVariation($participant->field_npxparticipant_firstname->value, 'cel'),
          '%user:phrase-bier%' => NameHelper::getPhraseVariation($participant->field_npxparticipant_firstname->value, 'bier'),
          '%user:phrase-narz%' => NameHelper::getPhraseVariation($participant->field_npxparticipant_firstname->value, 'narz'),
        ];
      }
      
      $pattern = '/%attach-\d+%/';
      if ($submission->field_npxsubmission_online->value) {
        $msg_arr = [
          '#markup' => $body_m,
        ];

        preg_match_all($pattern, $body_m, $matches);
      } else {
        $msg_arr = [
          '#markup' => $body_f,
        ];

        preg_match_all($pattern, $body_f, $matches);
      }

      self::replaceTokens($user_tokens, $msg_arr['#markup']);

      $attachmentFieldValue = $training->get('field_messages_attachments')->getValue();
      $params = [];
      if (!empty($attachmentFieldValue) && !empty($matches) !== false) {

        foreach ($matches[0] as $match) {
          $msg_arr['#markup'] = str_replace($match, '', $msg_arr['#markup']);

          $matchArray = explode('-', $match);
          $matchPosition = intval($matchArray[1]);

          if (empty($matchPosition) || !isset($attachmentFieldValue[$matchPosition - 1])) {
            continue;
          }

          /** @var \Drupal\file\FileInterface $file */
          $file = \Drupal::entityTypeManager()->getStorage('file')->load($attachmentFieldValue[$matchPosition - 1]['target_id']);
          $attachment = new \stdClass();
          $attachment->uri = $file->getFileUri();
          $attachment->filename = $file->getFilename();
          $attachment->filemime = $file->getMimeType();
          $file_path_1 = \Drupal::service('file_system')->realpath($attachment->uri);
          $file_data_1 = file_get_contents($file_path_1);
          $file_mime_type_1 = \Drupal::service('file.mime_type.guesser')->guessMimeType($file_path_1);

          $params['attachments'][] = [
            'filecontent' => $file_data_1,
            'filename' => $attachment->filename,
            'filemime' => $file_mime_type_1,
          ];
        }
      }
      
      if($attach_certificate && $cert_file) {
        $cert_attachment = new \stdClass();
        $cert_attachment->uri = $cert_file->getFileUri();
        $cert_attachment->filename = $cert_file->getFilename();
        $cert_attachment->filemime = $cert_file->getMimeType();
        $cert_file_path = \Drupal::service('file_system')->realpath($cert_attachment->uri);
        $cert_file_data = file_get_contents($cert_file_path);
        $cert_file_mime_type = \Drupal::service('file.mime_type.guesser')->guessMimeType($cert_file_path);
        
        $params['attachments'][] = [
          'filecontent' => $cert_file_data,
          'filename' => $cert_attachment->filename,
          'filemime' => $cert_file_mime_type,
        ];
      }
      
      $message = Markup::create($msg_arr['#markup']);

      $params['subject'] = $subject;
      $params['body'] = $message;
      $params['from'] = $from;

      $to = $participant->field_npxparticipant_email->value;
      //$to = '<EMAIL>';

      //$to = '<EMAIL>';

      if(!self::checkEmailSent($mail, $to)) {
        $mailManager->mail($module, $key, $to, $langcode, $params, $from, $send);
        self::addEmailSent($mail, $to);
      }
    }
    $mail->field_npxmail_sent->value = 1;
    $mail->save();
  }
  
  protected static function sendNotificationsClosed($mail) {
    $container = \Drupal::getContainer();
    if(!$mail->field_npxmail_node->entity || !$mail->field_npxmail_message->entity) {
        return;
    }
    $message = $container->get('entity_type.manager')->getStorage('npxmessage')->load($mail->field_npxmail_message->entity->id());
    $closed_date = $container->get('entity_type.manager')->getStorage('node')->load($mail->field_npxmail_node->entity->id());
    $training = $container->get('entity_type.manager')->getStorage('node')->load($closed_date->field_npxtraining_closed_nid->entity->id());
    $trainer = $container->get('entity_type.manager')->getStorage('node')->load($closed_date->field_npxtraining_date_trainer->entity->id());
    $subject = $message->field_npxmessage_subject->value;
    $body_m = $message->field_npxmessage_body_m->value;
    $body_f = $message->field_npxmessage_body_f->value;
    $from = $message->field_npxmessage_from->value;
    $attach_certificate = (bool)$message->field_npxmessage_attach_cert->value;
    $mailManager = \Drupal::service('plugin.manager.mail');
    $module = 'npx_notify';
    $key = NPX_NOTIFY_EMAIL_KEY;
    $langcode = \Drupal::languageManager()->getDefaultLanguage()->getId();
    $send = true;
    global $base_url;
    $date_day_value = $closed_date->field_npxtraining_date_start->value;
    if($closed_date->field_npxtraining_date_start->value != $closed_date->field_npxtraining_date_end->value) {
        $date_day_value .= ' - ' . $closed_date->field_npxtraining_date_end->value;
    }
    $date_5_prev = self::subtractWeekDays($closed_date->field_npxtraining_date_start->value);
    $npxClosedObj = new NpxTrainingClosed((int)$closed_date->id());
    $tokens = [
        '%training:title%' => $training->field_npxtraining_mailtitle->value,
        '%training:url%' => $base_url . '/node/'.$training->id(),
        'url%' => $base_url . '/node/'.$training->id(),
        '%date:day%' => $date_day_value,
        '%date:day-5%' => $date_5_prev,
        '%trainer%' => $trainer->field_npxtrainer_signature->value,
        '%trainer:name%' => $trainer->title->value,
        '%trainer:email%' => $trainer->field_npxtrainer_email->value,
        '%trainer:phone%' => $trainer->field_npxtrainer_phone->value,
        '%trainer:inslnk%' => '<a href="' . $trainer->field_npxtrainer_inslnk->value . '">TEN LINK</a>',
        '%room:name%' => $npxClosedObj->getRoomName(),
        '%room:desc%' => $npxClosedObj->getRoomDescription(),
        '%room:link%' => $npxClosedObj->getRoomMapLinkNotEmbed(),
        '%room:info%' => $npxClosedObj->getRoomInformation(),
        '%room:food%' => $npxClosedObj->getRoomFeeding(),
    ];
    self::replaceTokens($tokens, $subject);
    self::replaceTokens($tokens, $body_f);
    self::replaceTokens($tokens, $body_m);
    self::replaceTokens($tokens, $from);
    foreach ($closed_date->field_npxparticipants as $elem) {
        $user_tokens = [];
        $files = [];
        $cert_file = null;
        if($elem->entity) {
            $participant = $container->get('entity_type.manager')->getStorage('npxparticipant')->load($elem->entity->id());
            if($attach_certificate) {
              $cert_file = \Drupal::entityTypeManager()->getStorage('file')->load($participant->field_npxparticipant_cert->target_id);
            }
            
            $user_tokens = [
              '%user:rawname%' => $participant->field_npxparticipant_firstname->value,
              '%user:name%' => NameHelper::getName($participant->field_npxparticipant_firstname->value),
              '%user:phrase%' => NameHelper::getPhrase($participant->field_npxparticipant_firstname->value),
              '%user:phrasename%' => NameHelper::getNameWithPhrase($participant->field_npxparticipant_firstname->value),
              '%user:phrase-mian%' => NameHelper::getPhraseVariation($participant->field_npxparticipant_firstname->value, 'mian'),
              '%user:phrase-dop%' => NameHelper::getPhraseVariation($participant->field_npxparticipant_firstname->value, 'dop'),
              '%user:phrase-cel%' => NameHelper::getPhraseVariation($participant->field_npxparticipant_firstname->value, 'cel'),
              '%user:phrase-bier%' => NameHelper::getPhraseVariation($participant->field_npxparticipant_firstname->value, 'bier'),
              '%user:phrase-narz%' => NameHelper::getPhraseVariation($participant->field_npxparticipant_firstname->value, 'narz'),
              '%user:survey%' => \Drupal::service('npx_survey.fill_survey')->generateClosedSurveyUrl($closed_date->id(), $participant->field_npxparticipant_email->value, true),
            ];
        }
        $pattern = '/%attach-\d+%/';
        $msg_arr = [
            '#markup' => $body_f,
        ];
        preg_match_all($pattern, $body_f, $matches);
        self::replaceTokens($user_tokens, $msg_arr['#markup']);
        $attachmentFieldValue = $closed_date->get('field_messages_attachments')->getValue();
        $params = [];
if(!empty($attachmentFieldValue) && !empty($matches) !== false) {
  foreach ($matches[0] as $match) {
    $msg_arr['#markup'] = str_replace($match, '', $msg_arr['#markup']);
    $matchArray = explode('-', $match);
    $matchPosition = intval($matchArray[1]);
    
    if(empty($matchPosition) || !isset($attachmentFieldValue[$matchPosition - 1])) {
      continue;
    }
    
    /** @var \Drupal\file\FileInterface $file */
    $file = \Drupal::entityTypeManager()->getStorage('file')->load($attachmentFieldValue[$matchPosition - 1]['target_id']);
    if (!$file) {
      continue;
    }
    
    $attachment = new \stdClass();
    $attachment->uri = $file->getFileUri();
    $attachment->filename = $file->getFilename();
    $attachment->filemime = $file->getMimeType();
    
    $file_path = \Drupal::service('file_system')->realpath($attachment->uri);
    if (empty($file_path) || !file_exists($file_path)) {
      \Drupal::logger('npx_notify')->error('File not found: @uri', ['@uri' => $attachment->uri]);
      continue;
    }
    
    $file_data = file_get_contents($file_path);
    $file_mime_type = \Drupal::service('file.mime_type.guesser')->guessMimeType($file_path);
    
    $params['attachments'][] = [
      'filecontent' => $file_data,
      'filename' => $attachment->filename,
      'filemime' => $file_mime_type,
    ];
  }
}
        
        if($attach_certificate && $cert_file) {
          $cert_attachment = new \stdClass();
          $cert_attachment->uri = $cert_file->getFileUri();
          $cert_attachment->filename = $cert_file->getFilename();
          $cert_attachment->filemime = $cert_file->getMimeType();
          $cert_file_path = \Drupal::service('file_system')->realpath($cert_attachment->uri);
          $cert_file_data = file_get_contents($cert_file_path);
          $cert_file_mime_type = \Drupal::service('file.mime_type.guesser')->guessMimeType($cert_file_path);
          
          $params['attachments'][] = [
            'filecontent' => $cert_file_data,
            'filename' => $cert_attachment->filename,
            'filemime' => $cert_file_mime_type,
          ];
        }
        
        $message = Markup::create($msg_arr['#markup']);
        $params['subject'] = $subject;
        $params['body'] = $message;
        $params['from'] = $from;

        $to = $participant->field_npxparticipant_email->value;
        //$to = '<EMAIL>';

        if(!self::checkEmailSent($mail, $to)) {
            $mailManager->mail($module, $key, $to, $langcode, $params, $from, $send);
            self::addEmailSent($mail, $to);
        }
    }
    $mail->field_npxmail_sent->value = 1;
    $mail->save();
}


  public static function sendEmailToApplyingPerson($submission) {
     $training = \Drupal::entityTypeManager()->getStorage('node')->load($submission->field_npxsubmission_nid->entity->id());
    if(!$submission->field_npxsubmission_coupon_ref->entity) {
      return;
    }
    $coupon = \Drupal::entityTypeManager()->getStorage('npxcoupon')->load($submission->field_npxsubmission_coupon_ref->entity->id());
    $tokens = [
      '%training:title%' => $training->field_npxtraining_mailtitle->value,
      '%coupon:code%' => $coupon->field_npxcoupon_code->value,
      '%coupon:value%' => $coupon->field_npxcoupon_value->value,
    ];
  
    $config = \Drupal::config('npx_training_form.settings');
  
    $subject = $config->get('notify_email_subject');
    $body = $config->get('notify_email_body.value');
  
    $override_body = $training->get('field_npxtraining_cetxt')->value;
  
    if($override_body != '') {
      $body = $override_body;
    }

    self::replaceTokens($tokens, $subject);
    self::replaceTokens($tokens, $body);

    $msg_arr = [
      '#markup' => $body,
    ];
    $message = Markup::create($msg_arr['#markup']);
    $from = $config->get('notify_email_from');
    $to = '<EMAIL>';
    //$to = '<EMAIL>';

    $params = [
      'subject' => $subject,
      'body' => $message,
      'from' => $from,
    ];
  
    $mailManager = \Drupal::service('plugin.manager.mail');
    $module = 'npx_notify';
    $key = NPX_NOTIFY_EMAIL_KEY; // Replace with your actual email key.
    $langcode = \Drupal::languageManager()->getDefaultLanguage()->getId();
    $send = true;
  
    $mailManager->mail($module, $key, $to, $langcode, $params, NULL, $send);
  }

  public static function replaceTokens($tokens, &$string) {
    foreach ($tokens AS $key => $value) {
      $string = str_replace($key, $value, $string);
    }
  }

  public static function checkEmailSent($mailObject, $email) {
    $count = count($mailObject->field_npxmail_sended_emails);

    for($i = 0; $i < $count; $i++) {
      if($mailObject->field_npxmail_sended_emails[$i]->value == $email) {
        return true;
      }
    }

    return false;
  }

  public static function addEmailSent($mailObject, $email) {
    $mailObject->field_npxmail_sended_emails[] = $email;
  }
  
  public static function noNameFoundMessageSend($name) {
    $mailManager = \Drupal::service('plugin.manager.mail'); 
    $module = 'npx_notify'; 
    $key = NPX_NOTIFY_EMAIL_KEY; 
    $langcode = \Drupal::languageManager()->getDefaultLanguage()->getId(); 
    $send = true;
    
    $config = \Drupal::config('npx_training_form.settings');

    $from = $config->get('notify_email_from');
    $to = '<EMAIL>';
    //$to = '<EMAIL>';

    $params = [
      'subject' => 'Brak imienia ' . $name . ' w konfiguracji',
      'body' => 'Brak imienia ' . $name . ' w konfiguracji',
      'from' => $from,
    ];

    $mailManager->mail($module, $key, $to, $langcode, $params, NULL, $send);
  }
}
