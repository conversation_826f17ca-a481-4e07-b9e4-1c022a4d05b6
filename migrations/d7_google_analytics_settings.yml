id: d7_google_analytics_settings
label: Google Analytics 7 configuration
migration_tags:
  - Drupal 7
  - Configuration
source:
  plugin: variable
  variables:
    - googleanalytics_account
    - googleanalytics_cache
    - googleanalytics_codesnippet_after
    - googleanalytics_codesnippet_before
    - googleanalytics_codesnippet_create
    - googleanalytics_cross_domains
    - googleanalytics_custom
    - googleanalytics_custom_dimension
    - googleanalytics_custom_metric
    - googleanalytics_domain_mode
    - googleanalytics_pages
    - googleanalytics_roles
    - googleanalytics_site_search
    - googleanalytics_trackadsense
    - googleanalytics_trackcolorbox
    - googleanalytics_trackdoubleclick
    - googleanalytics_trackmessages
    - googleanalytics_tracker_anonymizeip
    - googleanalytics_trackfiles
    - googleanalytics_trackfiles_extensions
    - googleanalytics_tracklinkid
    - googleanalytics_trackmailto
    - googleanalytics_trackurlfragments
    - googleanalytics_trackuserid
    - googleanalytics_trackoutbound
    - googleanalytics_translation_set
    - googleanalytics_visibility_pages
    - googleanalytics_visibility_roles
  source_module: googleanalytics    
process:
  account: googleanalytics_account
  cache: googleanalytics_cache
  'codesnippet/after': googleanalytics_codesnippet_after
  'codesnippet/before': googleanalytics_codesnippet_before
  'codesnippet/create': googleanalytics_codesnippet_create
  cross_domains: googleanalytics_cross_domains
  'custom/parameters':
    plugin: google_analytics_parameter_pages
    source:
      - googleanalytics_custom_dimension
      - googleanalytics_custom_metric
  domain_mode: googleanalytics_domain_mode
  'privacy/anonymizeip': googleanalytics_tracker_anonymizeip
  'track/adsense': googleanalytics_trackadsense
  'track/colorbox': googleanalytics_trackcolorbox
  'track/displayfeatures': googleanalytics_trackdoubleclick
  'track/files': googleanalytics_trackfiles
  'track/files_extensions': googleanalytics_trackfiles_extensions
  'track/linkid': googleanalytics_tracklinkid
  'track/mailto': googleanalytics_trackmailto
  'track/messages': googleanalytics_trackmessages
  'track/site_search': googleanalytics_site_search
  'track/urlfragments': googleanalytics_trackurlfragments
  'track/userid': googleanalytics_trackuserid
  'track/outbound': googleanalytics_trackoutbound
  'translation_set': googleanalytics_translation_set
  'visibility/user_account_mode': googleanalytics_custom
  'visibility/request_path_mode': googleanalytics_visibility_pages
  'visibility/request_path_pages':
    plugin: google_analytics_visibility_pages
    source:
      - googleanalytics_visibility_pages
      - googleanalytics_pages
    # If Google Analytics uses PHP visibility, don't migrate it unless the PHP
    # module is enabled.
    skip_php: true
  'visibility/user_role_mode': googleanalytics_visibility_roles
  'visibility/user_role_roles':
    plugin: google_analytics_visibility_roles
    source:
      - googleanalytics_roles
destination:
  plugin: config
  config_name: google_analytics.settings
migration_dependencies:
  optional:
    - d7_user_role
