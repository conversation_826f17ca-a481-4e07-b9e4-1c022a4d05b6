langcode: en
status: true
dependencies:
  config:
    - field.field.node.page.body
    - field.field.node.page.field_image
    - field.field.node.page.field_media
    - node.type.page
  module:
    - text
    - user
id: node.page.default
targetEntityType: node
bundle: page
mode: default
content:
  body:
    label: hidden
    type: text_default
    weight: 100
    region: content
    settings: {  }
    third_party_settings: {  }
  links:
    weight: 101
    region: content
hidden:
  field_image: true
  field_media: true
