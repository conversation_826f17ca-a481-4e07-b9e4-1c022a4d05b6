langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_media
    - media.type.image
    - media.type.remote_video
    - node.type.page
id: node.page.field_media
field_name: field_media
entity_type: node
bundle: page
label: Media
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
      remote_video: remote_video
    sort:
      field: _none
    auto_create: false
    auto_create_bundle: image
field_type: entity_reference
