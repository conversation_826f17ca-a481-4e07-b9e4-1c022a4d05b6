langcode: en
status: true
dependencies:
  config:
    - field.field.media.remote_video.field_media_oembed_video
    - media.type.remote_video
  module:
    - media
id: media.remote_video.default
targetEntityType: media
bundle: remote_video
mode: default
content:
  field_media_oembed_video:
    type: oembed
    weight: 0
    label: hidden
    settings:
      max_width: 0
      max_height: 0
    third_party_settings: {  }
    region: content
hidden:
  created: true
  name: true
  thumbnail: true
  uid: true
