langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.page.body
    - field.field.node.page.field_image
    - field.field.node.page.field_media
    - node.type.page
  module:
    - text
    - user
id: node.page.teaser
targetEntityType: node
bundle: page
mode: teaser
content:
  body:
    label: hidden
    type: text_summary_or_trimmed
    weight: 100
    region: content
    settings:
      trim_length: 600
    third_party_settings: {  }
  links:
    weight: 101
    region: content
hidden:
  field_image: true
  field_media: true
