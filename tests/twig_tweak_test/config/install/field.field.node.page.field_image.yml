langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_image
    - node.type.page
  module:
    - image
id: node.page.field_image
field_name: field_image
entity_type: node
bundle: page
label: Image
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  file_directory: '[date:custom:Y]-[date:custom:m]'
  file_extensions: 'png gif jpg jpeg'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: true
  alt_field_required: true
  title_field: false
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
  handler: 'default:file'
  handler_settings: {  }
field_type: image
