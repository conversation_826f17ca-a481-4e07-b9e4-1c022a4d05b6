<?php

/**
 * @file
 * Test module for testing the paragraphs module.
 */

use Drupal\Core\Entity\Display\EntityViewDisplayInterface;
use Dr<PERSON>al\paragraphs\ParagraphInterface;
use Drupal\paragraphs\Plugin\Field\FieldWidget\ParagraphsWidget;

/**
 * Implements hook_paragraphs_widget_actions_alter().
 */
function paragraphs_test_paragraphs_widget_actions_alter(&$widget_actions, &$context) {
  if (!$context['allow_reference_changes']) {
    return;
  }

  if (\Drupal::state()->get('paragraphs_test_dropbutton')) {
    $widget_actions['dropdown_actions']['test_button'] = ParagraphsWidget::expandButton([
      '#type' => 'submit',
      '#value' => t('Add to library'),
      '#delta' => 0,
      '#name' => 'field_paragraphs_test',
      '#weight' => 504,
      '#paragraphs_mode' => 'remove',
    ]);
  }
}

/**
 * Implements hook_ENTITY_TYPE_view().
 */
function paragraphs_test_paragraph_view(array &$build, ParagraphInterface $entity, EntityViewDisplayInterface $display, $view_mode) {
  if (!\Drupal::state('paragraphs_test_parent')) {
    return;
  }
  $parent_type = $entity->get('parent_type')->value;
  $parent_id = $entity->get('parent_id')->value;
  $parent_field_name = $entity->get('parent_field_name')->value;
  \Drupal::messenger()->addStatus("Parent: $parent_type/$parent_id/$parent_field_name", TRUE);
}

/**
 * Implements hook_field_widget_single_element_WIDGET_TYPE_form_alter().
 */
function paragraphs_test_field_widget_single_element_entity_reference_paragraphs_form_alter(&$element, &$form_state, $context) {
  if ($element['#paragraph_type'] == 'altered_paragraph') {
    $element['subform']['field_text']['widget'][0]['#title'] = 'Altered title';
  }
}
