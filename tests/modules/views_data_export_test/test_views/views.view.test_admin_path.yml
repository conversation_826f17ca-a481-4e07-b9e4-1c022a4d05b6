langcode: en
status: true
dependencies:
  module:
    - rest
    - user
id: test_admin_path
label: 'Test admin path'
module: views_data_export
description: ''
tag: ''
base_table: entity_test
base_field: id
core: 8.x
display:
  default:
    display_plugin: default
    id: default
    display_title: Master
    position: null
    display_options:
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
      query:
        type: views_query
      exposed_form:
        type: basic
      style:
        type: data_export
      row:
        type: data_entity
      sorts:
        id:
          id: standard
          table: entity_test
          field: id
          order: DESC
          plugin_id: date
          entity_type: entity_test
          entity_field: id
      title: 'Test admin path'
      arguments: {  }
  data_export_1:
    display_plugin: data_export
    id: data_export_1
    display_title: serializer
    position: null
    display_options:
      path: admin/test/data_export/entity
      filename: foo.csv
      style:
        type: data_export
        options:
          formats:
            json: json
      export_method: batch
      export_batch_size: 4
      export_limit: 3
      displays:
        page_1: page_1
        default: '0'
  data_export_2:
    display_plugin: data_export
    id: data_export_2
    display_title: serializer
    position: null
    display_options:
      path: test/data_export/entity
      filename: foo.csv
      style:
        type: data_export
        options:
          formats:
            json: json
      export_method: batch
      export_batch_size: 4
      export_limit: 3
      displays:
        page_1: '0'
        default: '0'
  page_1:
    display_plugin: page
    id: page_1
    display_title: page
    position: null
    display_options:
      defaults:
        access: false
        style: false
        row: false
      style:
        type: default
      row:
        type: entity:entity_test
      path: test/data_export/page
