langcode: en
status: true
dependencies:
  config:
    - search_api.index.database_search_index
  module:
    - csv_serialization
    - rest
    - search_api
    - serialization
    - user
    - views_data_export
id: search_api_tests
label: 'Search API tests'
module: views
description: ''
tag: ''
base_table: search_api_index_database_search_index
base_field: search_api_id
core: 8.x
display:
  default:
    display_plugin: default
    id: default
    display_title: Master
    position: 0
    display_options:
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      query:
        type: search_api_query
        options:
          bypass_access: false
          skip_access: false
          preserve_facet_query_args: false
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        type: mini
        options:
          items_per_page: 10
          offset: 0
          id: 0
          total_pages: null
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          tags:
            previous: ‹‹
            next: ››
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      row:
        type: fields
        options:
          inline: {  }
          separator: ''
          hide_empty: false
          default_field_elements: true
      fields:
        id:
          table: search_api_index_database_search_index
          field: id
          id: id
          entity_type: null
          entity_field: null
          plugin_id: search_api_field
          relationship: none
          group_type: group
          admin_label: ''
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api_numeric
          fallback_options:
            set_precision: false
            precision: 0
            decimal: .
            separator: ','
            format_plural: false
            format_plural_string: !!binary MQNAY291bnQ=
            prefix: ''
            suffix: ''
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
            format_plural_values: {  }
      filters: {  }
      sorts: {  }
      header: {  }
      footer: {  }
      empty: {  }
      relationships: {  }
      arguments: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url.query_args
        - user.permissions
      tags: {  }
  data_export_1:
    display_plugin: data_export
    id: data_export_1
    display_title: 'Data export'
    position: 1
    display_options:
      display_extenders: {  }
      path: views_data_export/test_search_api
      filename: test_sapi
      automatic_download: true
      redirect_path: ''
      export_method: batch
      export_batch_size: 2
      style:
        type: data_export
        options:
          formats:
            csv: csv
          csv_settings:
            delimiter: ','
            enclosure: '"'
            escape_char: \
            strip_tags: true
            trim: true
            encoding: utf8
            utf8_bom: '0'
      export_limit: 8
      store_in_public_file_directory: false
      redirect_to_display: none
      custom_redirect_path: false
      include_query_params: false
      pager:
        type: some
        options:
          items_per_page: 8
          offset: 0
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - user.permissions
      tags: {  }
