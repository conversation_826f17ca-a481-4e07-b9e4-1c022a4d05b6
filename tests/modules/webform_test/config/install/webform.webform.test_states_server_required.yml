uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_test
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_states_server_required
title: 'Test: Form API #states server-side required validation'
description: 'Test Drupal''s #states API via server-side required validation.'
categories:
  - 'Test: Form API #states'
elements: |
  multiple_triggers:
    '#type': details
    '#title': multiple_triggers
    '#open': true
    trigger_checkbox:
      '#type': checkbox
      '#title': trigger_checkbox
    trigger_textfield:
      '#type': textfield
      '#title': trigger_textfield
    trigger_select:
      '#type': select
      '#title': trigger_select
      '#options':
        option: Option
  multiple_dependents:
    '#type': details
    '#title': multiple_dependents
    '#open': true
    dependent_textfield_required_and:
      '#type': textfield
      '#title': dependent_textfield_required_and
      '#description': '<b>Required:</b> checkbox:checked=true AND textfield:filled=true AND select:value=options'
      '#states':
        required:
          ':input[name="trigger_checkbox"]':
            checked: true
          ':input[name="trigger_textfield"]':
            filled: true
          ':input[name="trigger_select"]':
            value: option
    dependent_textfield_required_or:
      '#type': textfield
      '#title': dependent_textfield_required_or
      '#description': '<b>Required:</b> checkbox:checked=true OR textfield:filled=true OR select:value=options'
      '#states':
        required:
          - ':input[name="trigger_checkbox"]':
              checked: true
          - or
          - ':input[name="trigger_textfield"]':
              filled: true
          - or
          - ':input[name="trigger_select"]':
              value: option
    dependent_textfield_required_xor:
      '#type': textfield
      '#title': dependent_textfield_required_xor
      '#description': '<b>Required:</b> checkbox:checked=true XOR textfield:filled=true XOR select:value=options'
      '#states':
        required:
          - ':input[name="trigger_checkbox"]':
              checked: true
          - xor
          - ':input[name="trigger_textfield"]':
              filled: true
          - xor
          - ':input[name="trigger_select"]':
              value: option
  required_hidden_trigger_details:
    '#type': details
    '#title': required_hidden_trigger
    '#open': true
    required_hidden_trigger:
      '#type': checkbox
      '#title': required_hidden_trigger
    required_hidden_dependent_required:
      '#type': textfield
      '#title': required_hidden_dependent_required
      '#required': true
      '#states':
        visible:
          ':input[name="required_hidden_trigger"]':
            checked: true
  minlength_hidden_trigger_details:
    '#type': details
    '#title': minlength_hidden_trigger
    '#open': true
    minlength_hidden_trigger:
      '#type': checkbox
      '#title': minlength_hidden_trigger
    minlength_hidden_dependent:
      '#type': textfield
      '#title': minlength_hidden_dependent
      '#minlength': 5
      '#states':
        visible:
          ':input[name="minlength_hidden_trigger"]':
            checked: true
  checkboxes_trigger_details:
    '#type': details
    '#title': checkboxes_trigger
    '#open': true
    checkboxes_trigger:
      '#type': checkboxes
      '#title': checkboxes_trigger
      '#options':
        one: One
        two: Two
        three: Three
    checkboxes_dependent_required:
      '#type': textfield
      '#title': checkboxes_dependent_required
      '#description': '<b>Required:</b> checkboxes[one]:checked=true'
      '#states':
        required:
          ':input[name="checkboxes_trigger[one]"]':
            checked: true
  checkboxes_other_trigger_details:
    '#type': details
    '#title': checkboxes_other_trigger
    '#open': true
    checkboxes_other_trigger:
      '#type': webform_checkboxes_other
      '#title': checkboxes_other_trigger
      '#options':
        one: One
        two: Two
        three: Three
    checkboxes_other_dependent_required:
      '#type': textfield
      '#title': checkboxes_other_dependent_required
      '#description': '<b>Required:</b> checkboxes_other[one]:checked=true OR checkboxes_other[other]:filled=true'
      '#states':
        required:
          - ':input[name="checkboxes_other_trigger[checkboxes][one]"]':
              checked: true
          - or
          - ':input[name="checkboxes_other_trigger[other]"]':
              filled: true
  text_format_trigger_details:
    '#type': details
    '#title': text_format_trigger
    '#open': true
    text_format_trigger:
      '#type': text_format
      '#title': text_format_trigger
      '#format': basic_html
    text_format_dependent_required:
      '#type': textfield
      '#title': text_format_dependent_required
      '#description': '<b>Required:</b> text_format_trigger[value]:filled=true OR text_format_trigger[format]:value=full_html'
      '#states':
        required:
          - ':input[name="text_format_trigger[value]"]':
              filled: true
          - or
          - ':input[name="text_format_trigger[format]"]':
              value: full_html
  select_other_trigger_details:
    '#type': details
    '#title': select_other_trigger
    '#open': true
    select_other_trigger:
      '#type': webform_select_other
      '#title': select_other_trigger
      '#options':
        one: One
        two: Two
        three: Three
    select_other_dependent_required:
      '#type': textfield
      '#title': select_other_dependent_required
      '#description': '<b>Required:</b> select_other_trigger[select]:value=one OR select_other_trigger[other]:filled=true'
      '#states':
        required:
          - ':input[name="select_other_trigger[select]"]':
              value: one
          - or
          - ':input[name="select_other_trigger[other]"]':
              filled: true
  select_other_multiple_trigger_details:
    '#type': details
    '#title': select_other_multiple_trigger
    '#open': true
    select_other_multiple_trigger:
      '#type': webform_select_other
      '#title': select_other_multiple_trigger
      '#multiple': true
      '#options':
        one: One
        two: Two
        three: Three
    select_other_multiple_dependent_required:
      '#type': textfield
      '#title': select_other_multiple_dependent_required
      '#description': '<b>Required:</b> select_other_multiple_trigger[select]:value=one OR select_other_multiple_trigger[other]:filled=true'
      '#states':
        required:
          - ':input[name="select_other_multiple_trigger[select]"]':
              value: one
          - or
          - ':input[name="select_other_multiple_trigger[other]"]':
              filled: true
  select_values_trigger_details:
    '#type': details
    '#title': select_values_trigger
    '#open': true
    select_values_trigger:
      '#type': select
      '#title': select_values_trigger
      '#options':
        one: One
        two: Two
        three: Three
    select_values_trigger_dependent_required:
      '#type': textfield
      '#title': select_values_trigger_dependent_required
      '#description': '<b>Required:</b> select_values_trigger:value=one OR select_values_trigger=two'
      '#states':
        required:
          ':input[name="select_values_trigger"]':
            - value: one
            - value: two
  email_confirm_trigger_details:
    '#type': details
    '#title': email_confirm_trigger
    '#open': true
    email_confirm_trigger:
      '#type': webform_email_confirm
      '#title': email_confirm_trigger
    email_confirm_dependent_required:
      '#type': textfield
      '#title': email_confirm_dependent_required
      '#description': '<b>Required:</b> email_confirm_trigger[mail_1]:filled=true'
      '#states':
        required:
          ':input[name="email_confirm_trigger[mail_1]"]':
            filled: true
  likert_trigger_details:
    '#type': details
    '#title': likert_trigger
    '#open': true
    likert_trigger:
      '#type': webform_likert
      '#title': likert_trigger
      '#questions':
        q1: q1
        q2: q2
      '#answers':
        a1: a1
        a2: a2
    likert_dependent_required:
      '#type': textfield
      '#title': likert_dependent_required
      '#description': '<b>Required:</b> likert_trigger[q1]:value=a1'
      '#states':
        required:
          ':input[name="likert_trigger[q1]"]':
            value: a1
  datelist_trigger_details:
    '#type': details
    '#title': datelist_trigger
    '#open': true
    datelist_trigger:
      '#type': datelist
      '#title': datelist_trigger
      '#date_part_order':
        - year
        - month
        - day
        - hour
        - minute
        - second
        - ampm
    datelist_dependent_required:
      '#type': textfield
      '#title': datelist_dependent_required
      '#description': '<b>Required:</b> datelist_trigger[*]:!value='''''
      '#states':
        required:
          ':input[name="datelist_trigger[year]"]':
            '!value': ''
          ':input[name="datelist_trigger[month]"]':
            '!value': ''
          ':input[name="datelist_trigger[day]"]':
            '!value': ''
          ':input[name="datelist_trigger[hour]"]':
            '!value': ''
          ':input[name="datelist_trigger[minute]"]':
            '!value': ''
          ':input[name="datelist_trigger[second]"]':
            '!value': ''
          ':input[name="datelist_trigger[ampm]"]':
            '!value': ''
  datetime_trigger_details:
    '#type': details
    '#title': datetime_trigger
    '#open': true
    datetime_trigger:
      '#type': datetime
      '#title': datetime_trigger
    datetime_dependent_required:
      '#type': textfield
      '#title': datetime_dependent_required
      '#description': '<b>Required:</b> datetime_trigger[date]:!value='''' AND datetime_trigger[time]:!value='''''
      '#states':
        required:
          ':input[name="datetime_trigger[date]"]':
            '!value': ''
          ':input[name="datetime_trigger[time]"]':
            '!value': ''
  currency_trigger_details:
    '#type': details
    '#title': currency_trigger
    '#open': true
    currency_trigger:
      '#type': checkbox
      '#title': currency_trigger
    currency_dependent_required:
      '#type': textfield
      '#title': currency_dependent_required
      '#input_mask': '''alias'': ''currency'''
      '#required': true
      '#states':
        visible:
          ':input[name="currency_trigger"]':
            checked: true
  address_trigger_details:
    '#type': details
    '#title': address_trigger
    '#open': true
    address_trigger:
      '#type': webform_address
      '#title': address_trigger
    address_dependent_required:
      '#type': textfield
      '#title': address_dependent_required
      '#description': '<b>Required:</b> address_trigger[*]:!value='''''
      '#states':
        required:
          ':input[name="address_trigger[address]"]':
            '!value': ''
          ':input[name="address_trigger[address_2]"]':
            '!value': ''
          ':input[name="address_trigger[city]"]':
            '!value': ''
          ':input[name="address_trigger[state_province]"]':
            '!value': ''
          ':input[name="address_trigger[postal_code]"]':
            '!value': ''
          ':input[name="address_trigger[country]"]':
            '!value': ''
  composite_required_details:
    '#type': details
    '#title': composite_required
    '#open': true
    composite_required_trigger:
      '#type': checkbox
      '#title': composite_required_trigger
    composite_required_dependent:
      '#type': webform_address
      '#title': composite_required_dependent
      '#title_display': before
      '#states':
        required:
          ':input[name="composite_required_trigger"]':
            checked: true
  composite_sub_elements_required_details:
    '#type': details
    '#title': composite_sub_elements_required
    '#open': true
    composite_sub_elements_required_trigger:
      '#type': radios
      '#title': composite_sub_elements_required_trigger
      '#options':
        a: A
        b: B
      '#options_display': side_by_side
    composite_subelements_dependent_a:
      '#type': webform_address
      '#title': composite_subelements_dependent_a
      '#multiple': true
      '#multiple__header': true
      '#address__title': address_a
      '#address__states':
        required:
          ':input[name="composite_sub_elements_required_trigger"]':
            value: a
      '#address_2__title': address2_a
      '#city__title': city_a
      '#city__states':
        required:
          ':input[name="composite_sub_elements_required_trigger"]':
            value: a
      '#state_province__title': state_province_a
      '#state_province__states':
        required:
          ':input[name="composite_sub_elements_required_trigger"]':
            value: a
      '#postal_code__title': postal_code_a
      '#postal_code__states':
        required:
          ':input[name="composite_sub_elements_required_trigger"]':
            value: a
      '#country__title': country_a
      '#country__states':
        required:
          ':input[name="composite_sub_elements_required_trigger"]':
            value: a
    composite_subelements_dependent_b:
      '#type': webform_address
      '#title': composite_subelements_dependent_b
      '#multiple': true
      '#multiple__header': true
      '#address__title': address_b
      '#address__states':
        required:
          ':input[name="composite_sub_elements_required_trigger"]':
            value: b
      '#address_2__title': address2_b
      '#city__title': city_b
      '#city__states':
        required:
          ':input[name="composite_sub_elements_required_trigger"]':
            value: b
      '#state_province__title': state_province_b
      '#state_province__states':
        required:
          ':input[name="composite_sub_elements_required_trigger"]':
            value: b
      '#postal_code__title': postal_code_b
      '#postal_code__states':
        required:
          ':input[name="composite_sub_elements_required_trigger"]':
            value: b
      '#country__title': country_b
      '#country__states':
        required:
          ':input[name="composite_sub_elements_required_trigger"]':
            value: b
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: true
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: message
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: true
  results_disabled_ignore: true
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers: {  }
variants: {  }
