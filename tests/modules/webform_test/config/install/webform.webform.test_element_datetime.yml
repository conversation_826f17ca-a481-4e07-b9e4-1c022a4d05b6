uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_test
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_element_datetime
title: 'Test: Element: Date/time'
description: 'Test date/time element'
categories:
  - 'Test: Element'
elements: |
  datetime_default:
    '#type': datetime
    '#title': datetime_default
    '#default_value': '2009-08-18T01:00:00-05:00'
  datetime_html5_datetime:
    '#type': datetime
    '#title': datetime_html5_datetime
    '#default_value': '2009-08-18T01:00:00-05:00'
    '#date_date_element': datetime
    '#date_time_element': none
  datetime_html5_datetime_local:
    '#type': datetime
    '#title': datetime_html5_datetime_local
    '#default_value': '2009-08-18T01:00:00-05:00'
    '#date_date_element': datetime-local
    '#date_time_element': none
  datetime_html5_time:
    '#type': datetime
    '#title': datetime_html5_time
    '#default_value': '2009-08-18T01:00:00-05:00'
    '#date_date_element': none
    '#date_time_element': time
    '#format': html_time
  datetime_text_datetime:
    '#type': datetime
    '#title': datetime_text_datetime
    '#default_value': '2009-08-18T01:00:00-05:00'
    '#date_date_element': text
    '#date_time_element': text
  datetime_year_range:
    '#type': datetime
    '#title': datetime_year_range
    '#description': '(-10:+1)'
    '#default_value': '2009-08-18T01:00:00-05:00'
    '#date_year_range': '-10:+1'
  datetime_min_max:
    '#type': datetime
    '#title': datetime_min_max
    '#description': '(2009-01-01 to 2009-12-31)'
    '#date_date_min': '2009-01-01'
    '#date_date_max': '2009-12-31'
    '#default_value': '2009-08-18'
  datetime_min_max_time:
    '#type': datetime
    '#title': datetime_min_max_time
    '#description': '(2009-01-01 09:00:00 to 2009-12-31 17:00:00)'
    '#date_min': '2009-01-01 09:00:00'
    '#date_max': '2009-12-31 17:00:00'
    '#default_value': '2009-01-01 09:00:00'
  datetime_timepicker:
    '#type': datetime
    '#title': datetime_timepicker
    '#default_value': '2009-08-18T01:00:00-05:00'
    '#date_date_element': text
    '#date_date_format': 'D, m/d/Y'
    '#date_time_element': timepicker
    '#date_time_format': 'g:i A'
  datetime_time_text:
    '#type': datetime
    '#title': datetime_time_text
    '#default_value': '2009-08-18T01:00:00-05:00'
    '#date_time_element': text
  datetime_placeholder:
    '#type': datetime
    '#title': datetime_placeholder
    '#date_date_element': text
    '#date_date_placeholder': '{date}'
    '#date_time_element': timepicker
    '#date_time_placeholder': '{time}'
  datetime_time_min_max:
    '#type': datetime
    '#title': datetime_time_min_max
    '#description': '(2009-01-01 to 2009-12-31 and 09:00:00 to 17:00:00)'
    '#date_date_min': '2009-01-01'
    '#date_date_max': '2009-12-31'
    '#date_time_min': '09:00:00'
    '#date_time_max': '17:00:00'
    '#date_time_step': '1800'
    '#default_value': '2009-08-18T18:00:00-05:00'
  datetime_timepicker_time_min_max:
    '#type': datetime
    '#title': datetime_timepicker_time_min_max
    '#description': '(2009-01-01 to 2009-12-31 and 09:00:00 to 17:00:00)'
    '#date_date_min': '2009-01-01'
    '#date_date_max': '2009-12-31'
    '#date_time_min': '09:00:00'
    '#date_time_max': '17:00:00'
    '#date_time_step': '1800'
    '#date_date_element': text
    '#date_date_format': 'D, m/d/Y'
    '#date_time_element': timepicker
    '#date_time_format': 'g:i A'
    '#default_value': '2009-08-18T18:00:00-05:00'
  datetime_no_seconds:
    '#type': datetime
    '#title': datetime_no_seconds
    '#description': |
      @see <a href="https://www.drupal.org/node/2917107">Issue #2917107: Date and Time validation problem</a><br/>
      @see <a href="https://www.drupal.org/node/2723159">Issue #2723159: Datetime form element cannot validate when using a format without seconds</a><br/>
      @see <a href="https://www.drupal.org/project/webform/issues/3371639">Issue #3371639: The form won't submit if the user enters a time out of the interval/step</a><br/>
    '#date_time_element': text
    '#date_time_format': 'H:i'
    '#date_time_step': '900'
    '#default_value': '2009-08-18T01:00:00-05:00'
  datetime_multiple:
    '#type': datetime
    '#title': datetime_multiple
    '#default_value': '2009-08-18T01:00:00-05:00'
    '#date_date_element': text
    '#date_date_format': 'D, m/d/Y'
    '#date_time_element': timepicker
    '#date_time_format': 'g:i A'
    '#multiple': true
  datetime_custom_composite:
    '#type': webform_custom_composite
    '#title': datetime_custom_composite
    '#element':
      datetime:
        '#type': datetime
        '#title': datetime
        '#date_date_element': text
        '#date_date_format': d/m/Y
        '#date_time_element': timepicker
        '#date_time_format': 'g:i A'
        '#date_time_step': '900'
        '#autocomplete': 'off'
    '#default_value':
      - datetime: '2009-08-18T01:00:00-05:00'
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 1
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: page
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers:
  debug:
    id: debug
    label: Debug
    notes: ''
    handler_id: debug
    status: true
    conditions: {  }
    weight: 1
    settings:
      format: yaml
      submission: false
variants: {  }
