uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_test
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_element_format
title: 'Test: Element: Format'
description: 'Test element formatting.'
categories:
  - 'Test: Element'
elements: |
  basic_elements:
    '#type': details
    '#title': 'Basic elements'
    '#open': true
    checkbox:
      '#type': details
      '#title': Checkbox
      checkbox_value:
        '#type': checkbox
        '#title': 'Checkbox (Value)'
        '#default_value': true
        '#format': value
      checkbox_raw:
        '#type': checkbox
        '#title': 'Checkbox (Raw/return value)'
        '#default_value': true
        '#format': raw
    password:
      '#type': details
      '#title': Password
      password_value:
        '#type': password
        '#title': 'Password (Value)'
        '#default_value': Loremipsum
        '#format': value
      password_raw:
        '#type': password
        '#title': 'Password (Raw value)'
        '#default_value': Loremipsum
        '#format': raw
      password_obscured:
        '#type': password
        '#title': 'Password (Obscured)'
        '#default_value': Loremipsum
        '#format': obscured
    textarea:
      '#type': details
      '#title': Textarea
      textarea_value:
        '#type': textarea
        '#title': 'Textarea (Value)'
        '#rows': 2
        '#default_value': 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Negat esse eam, inquit, propter se expetendam. Primum Theophrasti, Strato, physicum se voluit; Id mihi magnum videtur. Itaque mihi non satis videmini considerare quod iter sit naturae quaeque progressio. Quare hoc videndum est, possitne nobis hoc ratio philosophorum dare. Est enim tanti philosophi tamque nobilis audacter sua decreta defendere.'
        '#format': value
      textarea_raw:
        '#type': textarea
        '#title': 'Textarea (Raw value)'
        '#rows': 2
        '#default_value': 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Negat esse eam, inquit, propter se expetendam. Primum Theophrasti, Strato, physicum se voluit; Id mihi magnum videtur. Itaque mihi non satis videmini considerare quod iter sit naturae quaeque progressio. Quare hoc videndum est, possitne nobis hoc ratio philosophorum dare. Est enim tanti philosophi tamque nobilis audacter sua decreta defendere.'
        '#format': raw
    textfield:
      '#type': details
      '#title': 'Text field'
      textfield_value:
        '#type': textfield
        '#title': 'Text field (Value)'
        '#default_value': Loremipsum
        '#format': value
      textfield_raw:
        '#type': textfield
        '#title': 'Text field (Raw value)'
        '#default_value': Loremipsum
        '#format': raw
  advanced_elements:
    '#type': details
    '#title': 'Advanced elements'
    '#open': true
    webform_autocomplete:
      '#type': details
      '#title': Autocomplete
      webform_autocomplete_value:
        '#type': webform_autocomplete
        '#title': 'Autocomplete (Value)'
        '#default_value': Loremipsum
        '#format': value
      webform_autocomplete_raw:
        '#type': webform_autocomplete
        '#title': 'Autocomplete (Raw value)'
        '#default_value': Loremipsum
        '#format': raw
    webform_codemirror:
      '#type': details
      '#title': CodeMirror
      webform_codemirror_value:
        '#type': webform_codemirror
        '#title': 'CodeMirror (Value)'
        '#mode': yaml
        '#default_value': 'message: ''Hello World'''
        '#format': value
      webform_codemirror_raw:
        '#type': webform_codemirror
        '#title': 'CodeMirror (Raw value)'
        '#mode': yaml
        '#default_value': 'message: ''Hello World'''
        '#format': raw
      webform_codemirror_code:
        '#type': webform_codemirror
        '#title': 'CodeMirror (Code)'
        '#mode': yaml
        '#default_value': 'message: ''Hello World'''
        '#format': code
    color:
      '#type': details
      '#title': Color
      color_value:
        '#type': color
        '#title': 'Color (Value)'
        '#default_value': '#ffffcc'
        '#format': value
      color_raw:
        '#type': color
        '#title': 'Color (Raw value)'
        '#default_value': '#ffffcc'
        '#format': raw
      color_swatch:
        '#type': color
        '#title': 'Color (Color swatch)'
        '#default_value': '#ffffcc'
        '#format': swatch
    email:
      '#type': details
      '#title': Email
      email_value:
        '#type': email
        '#title': 'Email (Value)'
        '#default_value': <EMAIL>
        '#format': value
      email_raw:
        '#type': email
        '#title': 'Email (Raw value)'
        '#default_value': <EMAIL>
        '#format': raw
      email_link:
        '#type': email
        '#title': 'Email (Link)'
        '#default_value': <EMAIL>
        '#format': link
    webform_email_confirm:
      '#type': details
      '#title': 'Email confirm'
      webform_email_confirm_value:
        '#type': webform_email_confirm
        '#title': 'Email confirm (Value)'
        '#default_value': <EMAIL>
        '#format': value
      webform_email_confirm_raw:
        '#type': webform_email_confirm
        '#title': 'Email confirm (Raw value)'
        '#default_value': <EMAIL>
        '#format': raw
      webform_email_confirm_link:
        '#type': webform_email_confirm
        '#title': 'Email confirm (Link)'
        '#default_value': <EMAIL>
        '#format': link
    webform_email_multiple:
      '#type': details
      '#title': 'Email multiple'
      webform_email_multiple_value:
        '#type': webform_email_multiple
        '#title': 'Email multiple (Value)'
        '#default_value': '<EMAIL>, <EMAIL>, <EMAIL>'
        '#format': value
      webform_email_multiple_raw:
        '#type': webform_email_multiple
        '#title': 'Email multiple (Raw value)'
        '#default_value': '<EMAIL>, <EMAIL>, <EMAIL>'
        '#format': raw
      webform_email_multiple_link:
        '#type': webform_email_multiple
        '#title': 'Email multiple (Link)'
        '#default_value': '<EMAIL>, <EMAIL>, <EMAIL>'
        '#format': link
    webform_height:
      '#type': details
      '#title': 'Height (feet/inches)'
      webform_height_value:
        '#type': webform_height
        '#title': 'Height (feet/inches) (Value)'
        '#default_value': 48
        '#format': value
      webform_height_raw:
        '#type': webform_height
        '#title': 'Height (feet/inches) (Raw value)'
        '#default_value': 48
        '#format': raw
    number:
      '#type': details
      '#title': Number
      number_value:
        '#type': number
        '#title': 'Number (Value)'
        '#min': 0
        '#max': 10
        '#step': 1
        '#format': value
      number_raw:
        '#type': number
        '#title': 'Number (Raw value)'
        '#min': 0
        '#max': 10
        '#step': 1
        '#format': raw
    password_confirm:
      '#type': details
      '#title': 'Password confirm'
      password_confirm_value:
        '#type': password_confirm
        '#title': 'Password confirm (Value)'
        '#default_value': Loremipsum
        '#format': value
      password_confirm_raw:
        '#type': password_confirm
        '#title': 'Password confirm (Raw value)'
        '#default_value': Loremipsum
        '#format': raw
      password_confirm_obscured:
        '#type': password_confirm
        '#title': 'Password confirm (Obscured)'
        '#default_value': Loremipsum
        '#format': obscured
    range:
      '#type': details
      '#title': Range
      range_value:
        '#type': range
        '#title': 'Range (Value)'
        '#min': 0
        '#max': 100
        '#step': 1
        '#output': below
        '#output__field_prefix': $
        '#output__field_suffix': '.00'
        '#format': value
      range_raw:
        '#type': range
        '#title': 'Range (Raw value)'
        '#min': 0
        '#max': 100
        '#step': 1
        '#output': below
        '#output__field_prefix': $
        '#output__field_suffix': '.00'
        '#format': raw
    webform_rating:
      '#type': details
      '#title': Rating
      webform_rating_value:
        '#type': webform_rating
        '#title': 'Rating (Value)'
        '#format': value
      webform_rating_raw:
        '#type': webform_rating
        '#title': 'Rating (Raw value)'
        '#format': raw
      webform_rating_star:
        '#type': webform_rating
        '#title': 'Rating (Star)'
        '#format': star
    webform_scale:
      '#type': details
      '#title': Scale
      webform_scale_value:
        '#type': webform_scale
        '#title': 'Scale (Value)'
        '#min': 1
        '#max': 5
        '#default_value': 1
        '#format': value
      webform_scale_raw:
        '#type': webform_scale
        '#title': 'Scale (Raw value)'
        '#min': 1
        '#max': 5
        '#default_value': 1
        '#format': raw
    search:
      '#type': details
      '#title': Search
      search_value:
        '#type': search
        '#title': 'Search (Value)'
        '#default_value': Loremipsum
        '#format': value
      search_raw:
        '#type': search
        '#title': 'Search (Raw value)'
        '#default_value': Loremipsum
        '#format': raw
    webform_signature:
      '#type': details
      '#title': Signature
      webform_signature_status:
        '#type': webform_signature
        '#title': 'Signature (Status)'
        '#default_value': 'data:image/png;base64,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'
        '#format': status
      webform_signature_url:
        '#type': webform_signature
        '#title': 'Signature (URL)'
        '#default_value': 'data:image/png;base64,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'
        '#format': url
      webform_signature_image:
        '#type': webform_signature
        '#title': 'Signature (Image)'
        '#default_value': 'data:image/png;base64,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'
        '#format': image
    tel:
      '#type': details
      '#title': Telephone
      tel_value:
        '#type': tel
        '#title': 'Telephone (Value)'
        '#international': true
        '#default_value': '******-333-4444'
        '#format': value
      tel_raw:
        '#type': tel
        '#title': 'Telephone (Raw value)'
        '#international': true
        '#default_value': '******-333-4444'
        '#format': raw
      tel_link:
        '#type': tel
        '#title': 'Telephone (Link)'
        '#international': true
        '#default_value': '******-333-4444'
        '#format': link
    webform_terms_of_service:
      '#type': details
      '#title': 'I agree to the {terms of service}.'
      webform_terms_of_service_value:
        '#type': webform_terms_of_service
        '#title': 'I agree to the {terms of service}. (Value)'
        '#required': true
        '#terms_type': slideout
        '#terms_content': '<em>These are the terms of service.</em>'
        '#default_value': true
        '#format': value
      webform_terms_of_service_raw:
        '#type': webform_terms_of_service
        '#title': 'I agree to the {terms of service}. (Raw/return value)'
        '#required': true
        '#terms_type': slideout
        '#terms_content': '<em>These are the terms of service.</em>'
        '#default_value': true
        '#format': raw
    url:
      '#type': details
      '#title': URL
      url_value:
        '#type': url
        '#title': 'URL (Value)'
        '#default_value': 'http://example.com'
        '#format': value
      url_raw:
        '#type': url
        '#title': 'URL (Raw value)'
        '#default_value': 'http://example.com'
        '#format': raw
      url_link:
        '#type': url
        '#title': 'URL (Link)'
        '#default_value': 'http://example.com'
        '#format': link
    value:
      '#type': details
      '#title': Value
      value_value:
        '#type': value
        '#title': 'Value (Value)'
        '#value': preview
        '#format': value
      value_raw:
        '#type': value
        '#title': 'Value (Raw value)'
        '#value': preview
        '#format': raw
  file_attachment_elements:
    '#type': details
    '#title': 'File attachment elements'
    '#open': true
    webform_entity_print_attachment_pdf:
      '#type': details
      '#title': 'Attachment PDF'
      webform_entity_print_attachment_pdf_link:
        '#type': webform_entity_print_attachment
        '#title': 'Attachment PDF (File link)'
        '#default_value': Loremipsum
        '#format': link
      webform_entity_print_attachment_pdf_name:
        '#type': webform_entity_print_attachment
        '#title': 'Attachment PDF (File name)'
        '#default_value': Loremipsum
        '#format': name
      webform_entity_print_attachment_pdf_url:
        '#type': webform_entity_print_attachment
        '#title': 'Attachment PDF (File URL)'
        '#default_value': Loremipsum
        '#format': url
    webform_attachment_token:
      '#type': details
      '#title': 'Attachment token'
      webform_attachment_token_link:
        '#type': webform_attachment_token
        '#title': 'Attachment token (File link)'
        '#format': link
      webform_attachment_token_name:
        '#type': webform_attachment_token
        '#title': 'Attachment token (File name)'
        '#format': name
      webform_attachment_token_url:
        '#type': webform_attachment_token
        '#title': 'Attachment token (File URL)'
        '#format': url
    webform_attachment_twig:
      '#type': details
      '#title': 'Attachment Twig'
      webform_attachment_twig_link:
        '#type': webform_attachment_twig
        '#title': 'Attachment Twig (File link)'
        '#format': link
      webform_attachment_twig_name:
        '#type': webform_attachment_twig
        '#title': 'Attachment Twig (File name)'
        '#format': name
      webform_attachment_twig_url:
        '#type': webform_attachment_twig
        '#title': 'Attachment Twig (File URL)'
        '#format': url
    webform_attachment_url:
      '#type': details
      '#title': 'Attachment URL'
      webform_attachment_url_link:
        '#type': webform_attachment_url
        '#title': 'Attachment URL (File link)'
        '#format': link
      webform_attachment_url_name:
        '#type': webform_attachment_url
        '#title': 'Attachment URL (File name)'
        '#format': name
      webform_attachment_url_url:
        '#type': webform_attachment_url
        '#title': 'Attachment URL (File URL)'
        '#format': url
  computed_elements:
    '#type': details
    '#title': 'Computed Elements'
    '#open': true
    webform_computed_token:
      '#type': details
      '#title': 'Computed token'
      webform_computed_token_value:
        '#type': webform_computed_token
        '#title': 'Computed token (Value)'
        '#template': 'This is a Computed token value.'
        '#format': value
      webform_computed_token_raw:
        '#type': webform_computed_token
        '#title': 'Computed token (Raw value)'
        '#template': 'This is a Computed token value.'
        '#format': raw
    webform_computed_twig:
      '#type': details
      '#title': 'Computed Twig'
      webform_computed_twig_value:
        '#type': webform_computed_twig
        '#title': 'Computed Twig (Value)'
        '#template': 'This is a Computed Twig value.'
        '#format': value
      webform_computed_twig_raw:
        '#type': webform_computed_twig
        '#title': 'Computed Twig (Raw value)'
        '#template': 'This is a Computed Twig value.'
        '#format': raw
  date_time_elements:
    '#type': details
    '#title': 'Date/time elements'
    '#open': true
    date:
      '#type': details
      '#title': Date
      date_value:
        '#type': date
        '#title': 'Date (Value)'
        '#default_value': '1942-06-18'
        '#format': value
      date_raw:
        '#type': date
        '#title': 'Date (Raw value)'
        '#default_value': '1942-06-18'
        '#format': raw
      date_fallback:
        '#type': date
        '#title': 'Date (Fallback date format)'
        '#default_value': '1942-06-18'
        '#format': fallback
      date_html_date:
        '#type': date
        '#title': 'Date (HTML Date)'
        '#default_value': '1942-06-18'
        '#format': html_date
      date_html_datetime:
        '#type': date
        '#title': 'Date (HTML Datetime)'
        '#default_value': '1942-06-18'
        '#format': html_datetime
      date_html_month:
        '#type': date
        '#title': 'Date (HTML Month)'
        '#default_value': '1942-06-18'
        '#format': html_month
      date_html_time:
        '#type': date
        '#title': 'Date (HTML Time)'
        '#default_value': '1942-06-18'
        '#format': html_time
      date_html_week:
        '#type': date
        '#title': 'Date (HTML Week)'
        '#default_value': '1942-06-18'
        '#format': html_week
      date_html_year:
        '#type': date
        '#title': 'Date (HTML Year)'
        '#default_value': '1942-06-18'
        '#format': html_year
      date_html_yearless_date:
        '#type': date
        '#title': 'Date (HTML Yearless date)'
        '#default_value': '1942-06-18'
        '#format': html_yearless_date
      date_long:
        '#type': date
        '#title': 'Date (Default long date)'
        '#default_value': '1942-06-18'
        '#format': long
      date_medium:
        '#type': date
        '#title': 'Date (Default medium date)'
        '#default_value': '1942-06-18'
        '#format': medium
      date_short:
        '#type': date
        '#title': 'Date (Default short date)'
        '#default_value': '1942-06-18'
        '#format': short
    datetime:
      '#type': details
      '#title': Date/time
      datetime_value:
        '#type': datetime
        '#title': 'Date/time (Value)'
        '#default_value': '1942-06-18'
        '#format': value
      datetime_raw:
        '#type': datetime
        '#title': 'Date/time (Raw value)'
        '#default_value': '1942-06-18'
        '#format': raw
      datetime_fallback:
        '#type': datetime
        '#title': 'Date/time (Fallback date format)'
        '#default_value': '1942-06-18'
        '#format': fallback
      datetime_html_date:
        '#type': datetime
        '#title': 'Date/time (HTML Date)'
        '#default_value': '1942-06-18'
        '#format': html_date
      datetime_html_datetime:
        '#type': datetime
        '#title': 'Date/time (HTML Datetime)'
        '#default_value': '1942-06-18'
        '#format': html_datetime
      datetime_html_month:
        '#type': datetime
        '#title': 'Date/time (HTML Month)'
        '#default_value': '1942-06-18'
        '#format': html_month
      datetime_html_time:
        '#type': datetime
        '#title': 'Date/time (HTML Time)'
        '#default_value': '1942-06-18'
        '#format': html_time
      datetime_html_week:
        '#type': datetime
        '#title': 'Date/time (HTML Week)'
        '#default_value': '1942-06-18'
        '#format': html_week
      datetime_html_year:
        '#type': datetime
        '#title': 'Date/time (HTML Year)'
        '#default_value': '1942-06-18'
        '#format': html_year
      datetime_html_yearless_date:
        '#type': datetime
        '#title': 'Date/time (HTML Yearless date)'
        '#default_value': '1942-06-18'
        '#format': html_yearless_date
      datetime_long:
        '#type': datetime
        '#title': 'Date/time (Default long date)'
        '#default_value': '1942-06-18'
        '#format': long
      datetime_medium:
        '#type': datetime
        '#title': 'Date/time (Default medium date)'
        '#default_value': '1942-06-18'
        '#format': medium
      datetime_short:
        '#type': datetime
        '#title': 'Date/time (Default short date)'
        '#default_value': '1942-06-18'
        '#format': short
    datelist:
      '#type': details
      '#title': 'Date list'
      datelist_value:
        '#type': datelist
        '#title': 'Date list (Value)'
        '#default_value': '1942-06-18'
        '#format': value
      datelist_raw:
        '#type': datelist
        '#title': 'Date list (Raw value)'
        '#default_value': '1942-06-18'
        '#format': raw
      datelist_fallback:
        '#type': datelist
        '#title': 'Date list (Fallback date format)'
        '#default_value': '1942-06-18'
        '#format': fallback
      datelist_html_date:
        '#type': datelist
        '#title': 'Date list (HTML Date)'
        '#default_value': '1942-06-18'
        '#format': html_date
      datelist_html_datetime:
        '#type': datelist
        '#title': 'Date list (HTML Datetime)'
        '#default_value': '1942-06-18'
        '#format': html_datetime
      datelist_html_month:
        '#type': datelist
        '#title': 'Date list (HTML Month)'
        '#default_value': '1942-06-18'
        '#format': html_month
      datelist_html_time:
        '#type': datelist
        '#title': 'Date list (HTML Time)'
        '#default_value': '1942-06-18'
        '#format': html_time
      datelist_html_week:
        '#type': datelist
        '#title': 'Date list (HTML Week)'
        '#default_value': '1942-06-18'
        '#format': html_week
      datelist_html_year:
        '#type': datelist
        '#title': 'Date list (HTML Year)'
        '#default_value': '1942-06-18'
        '#format': html_year
      datelist_html_yearless_date:
        '#type': datelist
        '#title': 'Date list (HTML Yearless date)'
        '#default_value': '1942-06-18'
        '#format': html_yearless_date
      datelist_long:
        '#type': datelist
        '#title': 'Date list (Default long date)'
        '#default_value': '1942-06-18'
        '#format': long
      datelist_medium:
        '#type': datelist
        '#title': 'Date list (Default medium date)'
        '#default_value': '1942-06-18'
        '#format': medium
      datelist_short:
        '#type': datelist
        '#title': 'Date list (Default short date)'
        '#default_value': '1942-06-18'
        '#format': short
    webform_time:
      '#type': details
      '#title': Time
      webform_time_value:
        '#type': webform_time
        '#title': 'Time (Value)'
        '#default_value': '09:00'
        '#format': value
      webform_time_raw:
        '#type': webform_time
        '#title': 'Time (Raw value)'
        '#default_value': '09:00'
        '#format': raw
  entity_reference_elements:
    '#type': details
    '#title': 'Entity reference elements'
    '#open': true
    entity_autocomplete:
      '#type': details
      '#title': 'Entity autocomplete'
      entity_autocomplete_value:
        '#type': entity_autocomplete
        '#title': 'Entity autocomplete (Value)'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#default_value': 1
        '#format': value
      entity_autocomplete_raw:
        '#type': entity_autocomplete
        '#title': 'Entity autocomplete (Raw value)'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#default_value': 1
        '#format': raw
      entity_autocomplete_link:
        '#type': entity_autocomplete
        '#title': 'Entity autocomplete (Link)'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#default_value': 1
        '#format': link
      entity_autocomplete_id:
        '#type': entity_autocomplete
        '#title': 'Entity autocomplete (Entity ID)'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#default_value': 1
        '#format': id
      entity_autocomplete_label:
        '#type': entity_autocomplete
        '#title': 'Entity autocomplete (Label)'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#default_value': 1
        '#format': label
      entity_autocomplete_text:
        '#type': entity_autocomplete
        '#title': 'Entity autocomplete (Label (ID))'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#default_value': 1
        '#format': text
      entity_autocomplete_teaser:
        '#type': entity_autocomplete
        '#title': 'Entity autocomplete (Teaser)'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#default_value': 1
        '#format': teaser
      entity_autocomplete_default:
        '#type': entity_autocomplete
        '#title': 'Entity autocomplete (Default)'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#default_value': 1
        '#format': default
    webform_entity_radios:
      '#type': details
      '#title': 'Entity radios'
      webform_entity_radios_value:
        '#type': webform_entity_radios
        '#title': 'Entity radios (Value)'
        '#options_display': side_by_side
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': value
      webform_entity_radios_raw:
        '#type': webform_entity_radios
        '#title': 'Entity radios (Raw value)'
        '#options_display': side_by_side
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': raw
      webform_entity_radios_link:
        '#type': webform_entity_radios
        '#title': 'Entity radios (Link)'
        '#options_display': side_by_side
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': link
      webform_entity_radios_id:
        '#type': webform_entity_radios
        '#title': 'Entity radios (Entity ID)'
        '#options_display': side_by_side
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': id
      webform_entity_radios_label:
        '#type': webform_entity_radios
        '#title': 'Entity radios (Label)'
        '#options_display': side_by_side
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': label
      webform_entity_radios_text:
        '#type': webform_entity_radios
        '#title': 'Entity radios (Label (ID))'
        '#options_display': side_by_side
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': text
      webform_entity_radios_teaser:
        '#type': webform_entity_radios
        '#title': 'Entity radios (Teaser)'
        '#options_display': side_by_side
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': teaser
      webform_entity_radios_default:
        '#type': webform_entity_radios
        '#title': 'Entity radios (Default)'
        '#options_display': side_by_side
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': default
    webform_entity_select:
      '#type': details
      '#title': 'Entity select'
      webform_entity_select_value:
        '#type': webform_entity_select
        '#title': 'Entity select (Value)'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': value
      webform_entity_select_raw:
        '#type': webform_entity_select
        '#title': 'Entity select (Raw value)'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': raw
      webform_entity_select_link:
        '#type': webform_entity_select
        '#title': 'Entity select (Link)'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': link
      webform_entity_select_id:
        '#type': webform_entity_select
        '#title': 'Entity select (Entity ID)'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': id
      webform_entity_select_label:
        '#type': webform_entity_select
        '#title': 'Entity select (Label)'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': label
      webform_entity_select_text:
        '#type': webform_entity_select
        '#title': 'Entity select (Label (ID))'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': text
      webform_entity_select_teaser:
        '#type': webform_entity_select
        '#title': 'Entity select (Teaser)'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': teaser
      webform_entity_select_default:
        '#type': webform_entity_select
        '#title': 'Entity select (Default)'
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: true
        '#options':
          1: Administrator
          0: Anonymous
        '#default_value': 1
        '#format': default
    webform_term_select:
      '#type': details
      '#title': 'Term select'
      webform_term_select_value:
        '#type': webform_term_select
        '#title': 'Term select (Value)'
        '#vocabulary': tags
        '#default_value': Loremipsum
        '#format': value
      webform_term_select_raw:
        '#type': webform_term_select
        '#title': 'Term select (Raw value)'
        '#vocabulary': tags
        '#default_value': Loremipsum
        '#format': raw
      webform_term_select_link:
        '#type': webform_term_select
        '#title': 'Term select (Link)'
        '#vocabulary': tags
        '#default_value': Loremipsum
        '#format': link
      webform_term_select_id:
        '#type': webform_term_select
        '#title': 'Term select (Entity ID)'
        '#vocabulary': tags
        '#default_value': Loremipsum
        '#format': id
      webform_term_select_label:
        '#type': webform_term_select
        '#title': 'Term select (Label)'
        '#vocabulary': tags
        '#default_value': Loremipsum
        '#format': label
      webform_term_select_text:
        '#type': webform_term_select
        '#title': 'Term select (Label (ID))'
        '#vocabulary': tags
        '#default_value': Loremipsum
        '#format': text
      webform_term_select_teaser:
        '#type': webform_term_select
        '#title': 'Term select (Teaser)'
        '#vocabulary': tags
        '#default_value': Loremipsum
        '#format': teaser
      webform_term_select_default:
        '#type': webform_term_select
        '#title': 'Term select (Default)'
        '#vocabulary': tags
        '#default_value': Loremipsum
        '#format': default
      webform_term_select_breadcrumb:
        '#type': webform_term_select
        '#title': 'Term select (Breadcrumb)'
        '#vocabulary': tags
        '#default_value': Loremipsum
        '#format': breadcrumb
  file_upload_elements:
    '#type': details
    '#title': 'File upload elements'
    '#open': true
    managed_file:
      '#type': details
      '#title': File
      managed_file_value:
        '#type': managed_file
        '#title': 'File (Value)'
        '#file_extensions': txt
        '#format': value
      managed_file_raw:
        '#type': managed_file
        '#title': 'File (Raw value)'
        '#file_extensions': txt
        '#format': raw
      managed_file_file:
        '#type': managed_file
        '#title': 'File (File)'
        '#file_extensions': txt
        '#format': file
      managed_file_link:
        '#type': managed_file
        '#title': 'File (Link)'
        '#file_extensions': txt
        '#format': link
      managed_file_url:
        '#type': managed_file
        '#title': 'File (URL)'
        '#file_extensions': txt
        '#format': url
      managed_file_name:
        '#type': managed_file
        '#title': 'File (File name)'
        '#file_extensions': txt
        '#format': name
      managed_file_basename:
        '#type': managed_file
        '#title': 'File (File base name (no extension))'
        '#file_extensions': txt
        '#format': basename
      managed_file_id:
        '#type': managed_file
        '#title': 'File (File ID)'
        '#file_extensions': txt
        '#format': id
      managed_file_mime:
        '#type': managed_file
        '#title': 'File (File mime type)'
        '#file_extensions': txt
        '#format': mime
      managed_file_size:
        '#type': managed_file
        '#title': 'File (File size (Bytes))'
        '#file_extensions': txt
        '#format': size
      managed_file_data:
        '#type': managed_file
        '#title': 'File (File content (Base64))'
        '#file_extensions': txt
        '#format': data
      managed_file_extension:
        '#type': managed_file
        '#title': 'File (File extension)'
        '#file_extensions': txt
        '#format': extension
  options_elements:
    '#type': details
    '#title': 'Options elements'
    '#open': true
    webform_image_select:
      '#type': details
      '#title': 'Image select'
      webform_image_select_value:
        '#type': webform_image_select
        '#title': 'Image select (Value)'
        '#show_label': true
        '#images':
          dog_1:
            text: 'Dog 1'
            src: 'https://www.placedog.net/80/100'
          dog_2:
            text: 'Dog 2'
            src: 'https://www.placedog.net/100/100'
          dog_3:
            text: 'Dog 3'
            src: 'https://www.placedog.net/120/100'
        '#default_value': Loremipsum
        '#format': value
      webform_image_select_raw:
        '#type': webform_image_select
        '#title': 'Image select (Raw value)'
        '#show_label': true
        '#images':
          dog_1:
            text: 'Dog 1'
            src: 'https://www.placedog.net/80/100'
          dog_2:
            text: 'Dog 2'
            src: 'https://www.placedog.net/100/100'
          dog_3:
            text: 'Dog 3'
            src: 'https://www.placedog.net/120/100'
        '#default_value': Loremipsum
        '#format': raw
      webform_image_select_image:
        '#type': webform_image_select
        '#title': 'Image select (Image)'
        '#show_label': true
        '#images':
          dog_1:
            text: 'Dog 1'
            src: 'https://www.placedog.net/80/100'
          dog_2:
            text: 'Dog 2'
            src: 'https://www.placedog.net/100/100'
          dog_3:
            text: 'Dog 3'
            src: 'https://www.placedog.net/120/100'
        '#default_value': Loremipsum
        '#format': image
    radios:
      '#type': details
      '#title': Radios
      radios_value:
        '#type': radios
        '#title': 'Radios (Value)'
        '#options':
          one: 'One -- This is a description'
          two: 'Two -- This is a description'
          three: 'Three -- This is a description'
        '#options_display': side_by_side
        '#default_value': one
        '#format': value
      radios_raw:
        '#type': radios
        '#title': 'Radios (Raw value)'
        '#options':
          one: 'One -- This is a description'
          two: 'Two -- This is a description'
          three: 'Three -- This is a description'
        '#options_display': side_by_side
        '#default_value': one
        '#format': raw
      radios_description:
        '#type': radios
        '#title': 'Radios (Option description)'
        '#options':
          one: 'One -- This is a description'
          two: 'Two -- This is a description'
          three: 'Three -- This is a description'
        '#options_display': side_by_side
        '#default_value': one
        '#format': description
      radios_text_description:
        '#type': radios
        '#title': 'Radios (Option text and description)'
        '#options':
          one: 'One -- This is a description'
          two: 'Two -- This is a description'
          three: 'Three -- This is a description'
        '#options_display': side_by_side
        '#default_value': one
        '#format': text_description
    webform_radios_other:
      '#type': details
      '#title': 'Radios other'
      webform_radios_other_value:
        '#type': webform_radios_other
        '#title': 'Radios other (Value)'
        '#options':
          one: One
          two: Two
          three: Three
        '#options_display': side_by_side
        '#default_value': one
        '#format': value
      webform_radios_other_raw:
        '#type': webform_radios_other
        '#title': 'Radios other (Raw value)'
        '#options':
          one: One
          two: Two
          three: Three
        '#options_display': side_by_side
        '#default_value': one
        '#format': raw
      webform_radios_other_description:
        '#type': webform_radios_other
        '#title': 'Radios other (Option description)'
        '#options':
          one: One
          two: Two
          three: Three
        '#options_display': side_by_side
        '#default_value': one
        '#format': description
      webform_radios_other_text_description:
        '#type': webform_radios_other
        '#title': 'Radios other (Option text and description)'
        '#options':
          one: One
          two: Two
          three: Three
        '#options_display': side_by_side
        '#default_value': one
        '#format': text_description
    select:
      '#type': details
      '#title': Select
      select_value:
        '#type': select
        '#title': 'Select (Value)'
        '#options':
          one: One
          two: Two
          three: Three
        '#default_value': one
        '#format': value
      select_raw:
        '#type': select
        '#title': 'Select (Raw value)'
        '#options':
          one: One
          two: Two
          three: Three
        '#default_value': one
        '#format': raw
    webform_select_other:
      '#type': details
      '#title': 'Select other'
      webform_select_other_value:
        '#type': webform_select_other
        '#title': 'Select other (Value)'
        '#options':
          one: One
          two: Two
          three: Three
        '#default_value': one
        '#format': value
      webform_select_other_raw:
        '#type': webform_select_other
        '#title': 'Select other (Raw value)'
        '#options':
          one: One
          two: Two
          three: Three
        '#default_value': one
        '#format': raw
  other_elements:
    '#type': details
    '#title': 'Other elements'
    '#open': true
    machine_name:
      '#type': details
      '#title': 'Machine name'
      machine_name_value:
        '#type': machine_name
        '#title': 'Machine name (Value)'
        '#default_value': loremipsum
        '#format': value
      machine_name_raw:
        '#type': machine_name
        '#title': 'Machine name (Raw value)'
        '#default_value': loremipsum
        '#format': raw
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 1
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: page
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers:
  email_text:
    id: email
    label: 'Email (Text)'
    notes: ''
    handler_id: email_text
    status: true
    conditions: {  }
    weight: 1
    settings:
      states:
        - completed
      to_mail: _default
      to_options: {  }
      cc_mail: ''
      cc_options: {  }
      bcc_mail: ''
      bcc_options: {  }
      from_mail: _default
      from_options: {  }
      from_name: _default
      subject: _default
      body: _default
      excluded_elements: {  }
      ignore_access: false
      exclude_empty: true
      exclude_empty_checkbox: false
      exclude_attachments: false
      html: false
      attachments: false
      twig: false
      theme_name: ''
      parameters: {  }
      debug: true
      reply_to: ''
      return_path: ''
      sender_mail: ''
      sender_name: ''
  email_html:
    id: email
    label: 'Email (HTML)'
    notes: ''
    handler_id: email_html
    status: true
    conditions: {  }
    weight: 2
    settings:
      states:
        - completed
      to_mail: _default
      to_options: {  }
      cc_mail: ''
      cc_options: {  }
      bcc_mail: ''
      bcc_options: {  }
      from_mail: _default
      from_options: {  }
      from_name: _default
      subject: _default
      body: _default
      excluded_elements: {  }
      ignore_access: false
      exclude_empty: true
      exclude_empty_checkbox: false
      exclude_attachments: false
      html: true
      attachments: false
      twig: false
      theme_name: ''
      parameters: {  }
      debug: true
      reply_to: ''
      return_path: ''
      sender_mail: ''
      sender_name: ''
variants: {  }
