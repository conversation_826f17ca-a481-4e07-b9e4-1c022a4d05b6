<?xml version="1.0" encoding="UTF-8"?>
<ruleset name="Module">
  <arg name="extensions" value="php,module,inc,install,test,profile,theme,css,info,txt,md,yml"/>
  <config name="drupal_core_version" value="8"/>
  <file>.</file>
  <exclude-pattern>vendor/*</exclude-pattern>
  <exclude-pattern>web/*</exclude-pattern>

  <!-- Initially include all Drupal and DrupalPractice sniffs. -->
  <rule ref="vendor/drupal/coder/coder_sniffer/Drupal"/>
  <rule ref="vendor/drupal/coder/coder_sniffer/DrupalPractice"/>

  <!-- Use 's' to print the full sniff name in the report. -->
  <!-- A '-' is prefixed to each of these, so s becomes -s, etc. -->
  <arg value="s"/>
  <arg value="-colors"/>
  <arg name='report-width' value='120'/>

  <!-- Ignore all files that match these patterns. They are matched against -->
  <!-- the full file path and there is an implied wildcard at each end. -->
  <!-- Periods must be escaped using \. -->
  <exclude-pattern>_ignore</exclude-pattern>
  <exclude-pattern>\.patch</exclude-pattern>
  <exclude-pattern>interdiff</exclude-pattern>

  <!-- Examples for how you disable rules you do not like. -->
  <!-- Exclude a sniff from running on specific files. -->
  <rule ref="Drupal.Files.TxtFileLineLength.TooLong">
    <!-- Exclude .md files from the line limit rule. -->
    <exclude-pattern>\.md</exclude-pattern>
  </rule>
  <rule ref="Drupal.Commenting.DocComment.ParamNotFirst">
    <!-- Drush commands are most readable with @command at top. -->
    <exclude-pattern>Commands\.php</exclude-pattern>
  </rule>

  <!-- Use a rule, but exclude one of its sniffs from all files. -->
  <rule ref="Drupal.Arrays.Array.LongLineDeclaration">
    <!-- Method declarations should be exempt. -->
    <severity>0</severity>
  </rule>

</ruleset>
