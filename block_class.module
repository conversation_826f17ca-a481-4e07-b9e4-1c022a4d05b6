<?php

/**
 * @file
 * Adding classes to blocks.
 */

use <PERSON><PERSON><PERSON>\block\BlockInterface;
use <PERSON><PERSON><PERSON>\block\Entity\Block;
use Drupal\Component\Utility\Html;
use Drupal\Core\Entity\EntityFormInterface;
use Drupal\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Routing\RouteMatchInterface;
use Drupal\Core\Url;

/**
 * Implements hook_help().
 */
function block_class_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    // Main module help for the forms_to_email module.
    case 'help.page.block_class':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t("Block Class allows users to add classes to any block through the block's configuration interface. Hooray for more powerful block theming!") . '</p>';

      $output .= '<h3>' . t('Installation note') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Enable the module on <a href=":extend_link">extend menu</a>.', [':extend_link' => Url::fromRoute('system.modules_list')->toString()]) . '</dt>';
      $output .= '</dl>';

      $output .= '<h3>' . t('Usage') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t("To add a class to a block, simply visit that block's configuration page at Administration > Structure > Block Layout and click on Configure of the desired block.") . '</dt>';
      $output .= '</dl>';

      return $output;
  }
}

/**
 * Implements hook_ENTITY_TYPE_presave().
 */
function block_class_block_presave(BlockInterface $entity) {
  if (empty($entity->getThirdPartySetting('block_class', 'classes'))) {
    $entity->unsetThirdPartySetting('block_class', 'classes');
  }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function block_class_form_block_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  if (\Drupal::currentUser()->hasPermission('administer block classes')) {
    $form_object = $form_state->getFormObject();
    if ($form_object instanceof EntityFormInterface) {
      /** @var \Drupal\block\BlockInterface $block */
      $block = $form_object->getEntity();

      // This will automatically be saved in the third party settings.
      $form['third_party_settings']['#tree'] = TRUE;
      $form['third_party_settings']['block_class']['classes'] = [
        '#type' => 'textfield',
        '#title' => t('CSS class(es)'),
        '#description' => t('Customize the styling of this block by adding CSS classes. Separate multiple classes by spaces.'),
        '#default_value' => $block->getThirdPartySetting('block_class', 'classes'),
        '#maxlength' => 255,
      ];
    }
  }
}

/**
 * Implements hook_preprocess_HOOK().
 */
function block_class_preprocess_block(&$variables) {
  // Blocks coming from page manager widget does not have id.
  if (!empty($variables['elements']['#id'])) {
    $block = Block::load($variables['elements']['#id']);
    if ($block && $classes = $block->getThirdPartySetting('block_class', 'classes')) {
      $classes_array = explode(' ', $classes);
      foreach ($classes_array as $class) {
        $variables['attributes']['class'][] = Html::cleanCssIdentifier($class, []);
      }
    }
  }
}

/**
 * Implements hook_migration_plugins_alter().
 */
function block_class_migration_plugins_alter(array &$definitions) {
  $d7_block_migrations = array_filter($definitions, function (array $definition) {
    return $definition['id'] === 'd7_block';
  });

  foreach (array_keys($d7_block_migrations) as $d7_block_migration_id) {
    $definitions[$d7_block_migration_id]['process']['third_party_settings/block_class/classes'] = [
      'plugin' => 'default_value',
      'source' => 'css_class',
      'default_value' => NULL,
    ];
  }
}
