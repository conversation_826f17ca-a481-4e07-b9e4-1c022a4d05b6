{"name": "@flexslider/flexslider", "description": "Build and test tools for Flexslider. Provided by Dropfort Module Build.", "version": "3.0.0-alpha1", "private": true, "scripts": {"init": "composer install && npm install", "preinstall": "npx npm-force-resolutions || true", "prepare": "husky install || true", "commit": "npx git-cz", "release": "standard-version", "clean": "rm -rf node_modules && rm -rf bin && rm -rf vendor", "build": "npm run compile:styles && npm run compile:scripts", "build:prod": "npm run build && npm run clean && npm install --omit dev", "build:dev": "npm run compile:styles:dev && npm run compile:scripts:dev", "compile:scripts": "npm run compile:js", "compile:scripts:dev": "npm run compile:js -- --sourcemap inline", "compile:styles": "echo 'This module does not require scss compilation'", "compile:styles:dev": "echo 'This module does not require scss compilation'", "compile:js": "rollup --config rollup.config.js", "compile:sass": "sass scss:dist/css", "compile:css": "postcss dist/css/**/*[!.min].css --dir dist/css --ext min.css", "lint": "npm run lint:styles && npm run lint:scripts && npm run lint:php", "lint:fix": "npm run lint:styles:fix && npm run lint:scripts:fix && npm run lint:php:fix", "lint:styles": "npx stylelint '**/*.{scss,sass}'", "lint:styles:fix": "npm run lint:styles -- --fix", "lint:scripts": "npx eslint --ext .js .", "lint:scripts:fix": "npm run lint:scripts -- --fix", "lint:php": "phpcs --extensions=php,module,inc,install,test,profile,theme --ignore=bin/*,config/*,node_modules/*,vendor/*,*.md --standard=Drupal,DrupalPractice ./", "lint:php:fix": "phpcbf --extensions=php,module,inc,install,test,profile,theme --ignore=bin/*,config/*,node_modules/*,vendor/*,*.md --standard=Drupal,DrupalPractice ./", "test:compatibility": "npm run test:compatibility:php && test:compatibility:js", "test:compatibility:php": "phpcs --extensions=php,module,inc,install,test,profile,theme --ignore=**/testing/*,**/test/*,**/node_modules/*,**/vendor/*,**/bin/*,**/docker/*,*.md,web/autoload.php,web/*/contrib/*,web/core/*,web/libraries/*,web/sites/*/files/* --standard=PHPCompatibility -p --runtime-set testVersion 8.0- ./", "test:compatibility:js": "echo 'This project does not define javascript compatiblity tests'"}, "repository": {"type": "git", "url": "https://git.drupalcode.org/project/flexslider.git"}, "keywords": ["drupal"], "author": "Coldfront Labs Inc.", "license": "GPL-2.0-or-later", "dependencies": {}, "devDependencies": {"@babel/core": "^7.17.10", "@babel/preset-env": "^7.17.10", "@commitlint/cli": "^17.0.0", "@commitlint/config-conventional": "^17.0.0", "@rollup/plugin-babel": "^5.3.1", "autoprefixer": "^10.4.7", "commitizen": "^4.2.4", "core-js": "^3.22.4", "core-js-compat": "^3.22.4", "cssnano": "^5.1.7", "eslint": "^8.15.0", "eslint-plugin-drupal-contrib": "^1.1.2", "husky": "^8.0.1", "lint-staged": "^12.4.1", "postcss": "^8.4.13", "postcss-cli": "^9.1.0", "postcss-discard-comments": "^5.1.1", "prettier": "^2.6.2", "rollup": "^2.72.0", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-terser": "^7.0.2", "sass": "^1.51.0", "standard-version": "^9.3.2", "standard-version-updater-yaml": "^1.0.3", "stylelint": "^14.8.2", "stylelint-config-coldfront": "^3.0.1", "terser": "^5.13.1"}, "resolutions": {"is-fullwidth-code-point": "3.0.0"}}