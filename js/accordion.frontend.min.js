!function(e,v,m){"use strict";let f=!1,k=[];const w={duration:300,easing:(e,t,i,o)=>(e/=o/2)<1?i/2*e*e+t:-i/2*(--e*(e-2)-1)+t},L={OPEN:1,CLOSE:2},A=(t,i)=>{let o=!1;for(let e=0;e<t.length;e++){var n=t[e][0],a=t[e][1],c=(a.startTime||(a.startTime=i),i-a.startTime);(o=c<a.duration)&&(c=a.easing(c,a.startingHeight,a.distanceHeight,a.duration),n.style.height=c.toFixed(2)+"px")}if(o)window.requestAnimationFrame(e=>A(t,e));else{for(let e=0;e<t.length;e++){var r=t[e][0],l=t[e][1];l.direction===L.CLOSE&&(r.style.display="none"),l.direction===L.OPEN&&(r.style.display="block"),r.style.height=null,r.style.overflow=null,r.style.marginTop=null,r.style.marginBottom=null,r.style.paddingTop=null,r.style.paddingBottom=null}k=[],f=!1}};e.behaviors.ckeditorAccordion={attach:function(e,t){var i=m("ckeditorAccordions",".ckeditor-accordion",e),o=v.ckeditorAccordion.accordionStyle.animateAccordionOpenAndClose??!0,n=v.ckeditorAccordion.accordionStyle.openTabsWithHash??!1,a=v.ckeditorAccordion.accordionStyle.allowHtmlInTitles??!1;for(let e=0;e<i.length;e++){var c=i[e],r=(v.ckeditorAccordion.accordionStyle.collapseAll||(c.querySelector("dt:first-child").classList.add("active"),(g=c.querySelector("dd:first-of-type")).classList.add("active"),g.style.display="block"),Array.from(c.children).filter(function(e){return"dt"==e.tagName.toLowerCase()}));for(let t=0;t<r.length;t++){var l=r[t],s=l.textContent.trim(),d=a?l.innerHTML.trim():s,h=l.classList.contains("active")?" active":"";let e='href="#"';n&&(s=encodeURIComponent(s.replace(/[^A-Za-z0-9]/g,"")),e='href="#'+s+'" id="'+s+'" onclick="return false;"'),l.innerHTML='<a class="ckeditor-accordion-toggler" '+e+'><span class="ckeditor-accordion-toggle'+h+'"></span>'+d+"</a>"}c.classList.add("styled"),c.classList.remove("ckeditor-accordion");var g=document.createElement("div"),p=(g.classList.add("ckeditor-accordion-container"),o||g.classList.add("no-animations"),c.after(g),g.appendChild(c),new Event("ckeditorAccordionAttached")),y=(c.dispatchEvent(p),c.querySelectorAll(".ckeditor-accordion-toggler"));for(let e=0;e<y.length;e++)y[e].addEventListener("click",function(e){var t=this.parentNode,i=t.nextElementSibling,n=t.parentNode;if(!f)if(f=!0,t.classList.contains("active")?(t.classList.remove("active"),i.classList.remove("active"),k.push(["slideUp",i])):(v.ckeditorAccordion.accordionStyle.keepRowsOpen||Array.from(n.children).filter(function(e){return e.classList.contains("active")}).forEach(function(e){e.classList.remove("active"),"dd"==e.tagName.toLowerCase()&&k.push(["slideUp",e])}),t.classList.add("active"),i.classList.add("active"),k.push(["slideDown",i])),n=k,v.ckeditorAccordion.accordionStyle.animateAccordionOpenAndClose??!0){let o=[];n.forEach(e=>{var t=e[1],i=Object.assign({},w);"slideUp"==e[0]?(i.direction=L.CLOSE,i.to=0,i.startingHeight=t.scrollHeight,i.distanceHeight=-i.startingHeight,t.style.display="block",t.style.overflow="hidden",t.style.marginTop="0",t.style.marginBottom="0",t.style.paddingTop="0",t.style.paddingBottom="0"):(t.style.height="0px",t.style.display="block",t.style.overflow="hidden",t.style.marginTop="0",t.style.marginBottom="0",t.style.paddingTop="0",t.style.paddingBottom="0",i.direction=L.OPEN,i.to=t.scrollHeight,i.startingHeight=0,i.distanceHeight=i.to),delete i.startTime,o.push([t,i])}),window.requestAnimationFrame(e=>A(o,e))}else n.forEach(e=>{var t=e[1];"slideUp"==e[0]?t.style.display="none":t.style.display="block"}),f=!1;e.preventDefault()});if(n){var u=document.querySelectorAll('a[href*="#"]:not(.ckeditor-accordion-toggler):not(.visually-hidden)');for(let e=0;e<u.length;e++)u[e].addEventListener("click",function(e){var t=document.createElement("a"),i=!1;t.href=this.getAttribute("href"),(t=t.hash)&&null!=document.querySelector('a.ckeditor-accordion-toggler[href="'+t+'"]')&&(window.location.hash===t?window.dispatchEvent(new Event("hashchange")):window.location.hash=t,i=!0),i&&e.preventDefault()});window.addEventListener("hashchange",function(){var e=document.querySelector('a.ckeditor-accordion-toggler[href="'+window.location.hash+'"]');null==e||e.parentNode.classList.contains("active")||e.click()}),window.dispatchEvent(new Event("hashchange"))}}}}}(Drupal,drupalSettings,once);