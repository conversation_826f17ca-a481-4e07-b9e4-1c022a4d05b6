!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.CKEditor5=t():(e.CKEditor5=e.CKEditor5||{},e.CKEditor5.editorAdvancedLink=t())}(self,(()=>(()=>{var e={"ckeditor5/src/core.js":(e,t,i)=>{e.exports=i("dll-reference CKEditor5.dll")("./src/core.js")},"ckeditor5/src/typing.js":(e,t,i)=>{e.exports=i("dll-reference CKEditor5.dll")("./src/typing.js")},"ckeditor5/src/ui.js":(e,t,i)=>{e.exports=i("dll-reference CKEditor5.dll")("./src/ui.js")},"dll-reference CKEditor5.dll":e=>{"use strict";e.exports=CKEditor5.dll}},t={};function i(r){var o=t[r];if(void 0!==o)return o.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,i),n.exports}i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var r={};return(()=>{"use strict";i.d(r,{default:()=>c});var e=i("ckeditor5/src/core.js"),t=i("ckeditor5/src/typing.js");const o={linkTitle:{label:Drupal.t("Title"),viewAttribute:"title"},linkAriaLabel:{label:Drupal.t("ARIA label"),viewAttribute:"aria-label",group:"advanced"},linkClass:{label:Drupal.t("CSS classes"),viewAttribute:"class",group:"advanced"},linkId:{label:Drupal.t("ID"),viewAttribute:"id",group:"advanced"},linkRel:{label:Drupal.t("Link relationship"),viewAttribute:"rel",group:"advanced"}},n={advanced:{label:Drupal.t("Advanced")}};class s extends e.Plugin{static get pluginName(){return"EditorAdvancedLinkEditing"}init(){const e=this.editor.config.get("editorAdvancedLink");if(!e.options)return void(this.enabledModelNames=[]);const t=Object.values(e.options);this.enabledModelNames=Object.keys(o).filter((e=>t.includes(o[e].viewAttribute))),this.enabledModelNames.forEach((e=>{this._allowAndConvertExtraAttribute(e,o[e].viewAttribute),this._removeExtraAttributeOnUnlinkCommandExecute(e),this._refreshExtraAttributeValue(e)})),this._addExtraAttributeOnLinkCommandExecute(Object.keys(o))}_allowAndConvertExtraAttribute(e,t){const{editor:i}=this;i.model.schema.extend("$text",{allowAttributes:e}),i.conversion.for("downcast").attributeToElement({model:e,view:(e,{writer:i})=>{const r=i.createAttributeElement("a",{[t]:e},{priority:5});return i.setCustomProperty("link",!0,r),r}}),i.conversion.for("upcast").elementToAttribute({view:{name:"a",attributes:{[t]:!0}},model:{key:e,value:e=>e.getAttribute(t)}})}_addExtraAttributeOnLinkCommandExecute(e){const{editor:t}=this,i=t.commands.get("link");let r=!1;i.on("execute",((i,o)=>{if(r)return void(r=!1);i.stop(),r=!0;const n=o[o.length-1]||o[1],{model:s}=this.editor,{selection:a}=s.document;s.change((i=>{t.execute("link",...o);const r=a.getFirstPosition();e.forEach((e=>{if(a.isCollapsed){const t=r.textNode||r.nodeBefore;n[e]?i.setAttribute(e,n[e],i.createRangeOn(t)):i.removeAttribute(e,i.createRangeOn(t)),i.removeSelectionAttribute(e)}else{const t=s.schema.getValidRanges(a.getRanges(),e);for(const r of t)n[e]?i.setAttribute(e,n[e],r):i.removeAttribute(e,r)}}))}))}),{priority:"high"})}_removeExtraAttributeOnUnlinkCommandExecute(e){const{editor:i}=this,r=i.commands.get("unlink"),{model:o}=this.editor,{selection:n}=o.document;let s=!1;r.on("execute",(r=>{s||(r.stop(),o.change((()=>{s=!0,i.execute("unlink"),s=!1,o.change((i=>{let r;r=n.isCollapsed?[(0,t.findAttributeRange)(n.getFirstPosition(),e,n.getAttribute(e),o)]:o.schema.getValidRanges(n.getRanges(),e);for(const t of r)i.removeAttribute(e,t)}))})))}),{priority:"high"})}_refreshExtraAttributeValue(e){const{editor:t}=this,i=t.commands.get("link"),{model:r}=this.editor,{selection:o}=r.document;i.set(e,null),r.document.on("change",(()=>{i[e]=o.getAttribute(e)}))}}var a=i("ckeditor5/src/ui.js");class l extends e.Plugin{init(){this.groups={},this.editor.plugins.get("LinkUI")._createViews&&this.editor.plugins.get("LinkUI")._createViews(),this._changeFormToVertical(),this._addExtraFormFields()}_addExtraFormFields(){const{editor:e}=this;e.plugins.get("ContextualBalloon").on("set:visibleView",((t,i,r,n)=>{const s=e.plugins.get("LinkUI").formView;if(r===n||r!==s)return;const{enabledModelNames:a}=e.plugins.get("EditorAdvancedLinkEditing");a.reverse().forEach((e=>{this._createExtraFormField(e,o[e])})),this._handleExtraFormFieldSubmit(a),this._addGroupsToFormView(),this._moveTargetDecoratorToAdvancedGroup()}))}_changeFormToVertical(){this.editor.plugins.get("LinkUI").formView.extendTemplate({attributes:{class:["ck-vertical-form","ck-link-form_layout-vertical"]}})}_createExtraFormField(e,t){const{editor:i}=this,{locale:r}=i,o=i.plugins.get("LinkUI").formView,n=i.commands.get("link");if(void 0===o[e]){const i=t.group?this._getGroup(t.group):o,s=new a.LabeledFieldView(r,a.createLabeledInputText);s.label=t.label,s.class="ck-labeled-field-view--editor-advanced-link",i.children.add(s,i===o?1:0),t.group||(o._focusables.add(s,1),o.focusTracker.add(s.element)),o[e]=s,o[e].fieldView.bind("value").to(n,e),o[e].fieldView.element.value=n[e]||""}}_addGroupsToFormView(){if(0===Object.entries(this.groups).length)return;const{editor:e}=this,t=e.plugins.get("LinkUI").formView;Object.values(this.groups).reverse().forEach((e=>{e.added||(t.children.add(e,2),e.parent=t,t._focusables.add(e,2),t.focusTracker.add(e.element),e.added=!0)}));t.children.filter((e=>e.template.attributes.class.indexOf("ck-button-save")>-1||e.template.attributes.class.indexOf("ck-button-cancel")>-1)).forEach(((e,i)=>{t.children.remove(e),t._focusables.remove(e),t.focusTracker.remove(e.element),t.children.add(e,3+i),t._focusables.add(e,3+i),t.focusTracker.add(e.element)}))}_getGroup(e){if(!this.groups[e]){const{editor:t}=this,{locale:i}=t,r=new a.CollapsibleView(i);r.label=n[e].label,r.set("isCollapsed",!0),this.groups[e]=r}return this.groups[e]}_moveTargetDecoratorToAdvancedGroup(){const{editor:e}=this,t=e.plugins.get("LinkUI").formView;t.targetMoved||void 0===t._manualDecoratorSwitches||(t._manualDecoratorSwitches._items.forEach((e=>{e.label===Drupal.t("Open in new window")&&this._getGroup("advanced").children.add(e)})),t.targetMoved=!0)}_handleExtraFormFieldSubmit(e){const{editor:t}=this,i=t.plugins.get("LinkUI").formView,r=t.commands.get("link");this.listenTo(i,"submit",(()=>{const t=e.reduce(((e,t)=>(e[t]=i[t].fieldView.element.value,e)),{});r.once("execute",((e,i)=>{if(i.length<3&&i.length>1)Object.assign(i[1],t);else if(3===i.length&&void 0!==i[2])Object.assign(i[2],t);else{if(3!==i.length)throw Error("The link command has more than 3 arguments.");Object.assign(i[1],t)}}),{priority:"highest"})}),{priority:"high"})}}class d extends e.Plugin{static get requires(){return[s,l]}static get pluginName(){return"EditorAdvancedLink"}}const c={EditorAdvancedLink:d}})(),r=r.default})()));