/**
 * @file
 * A Backbone Model subclass that enforces validation when calling set().
 */

(function (<PERSON><PERSON><PERSON>, Backbone) {
  Drupal.quickedit.BaseModel = Backbone.Model.extend(
    /** @lends Drupal.quickedit.BaseModel# */ {
      /**
       * @constructs
       *
       * @augments Backbone.Model
       *
       * @param {object} options
       *   Options for the base model-
       *
       * @return {Drupal.quickedit.BaseModel}
       *   A quickedit base model.
       */
      initialize(options) {
        this.__initialized = true;
        return Backbone.Model.prototype.initialize.call(this, options);
      },

      /**
       * Set a value on the model
       *
       * @param {object|string} key
       *   The key to set a value for.
       * @param {*} val
       *   The value to set.
       * @param {object} [options]
       *   Options for the model.
       *
       * @return {*}
       *   The result of `Backbone.Model.prototype.set` with the specified
       *   parameters.
       */
      set(key, val, options) {
        if (this.__initialized) {
          // Deal with both the "key", value and {key:value}-style arguments.
          if (typeof key === 'object') {
            key.validate = true;
          } else {
            if (!options) {
              options = {};
            }
            options.validate = true;
          }
        }
        return Backbone.Model.prototype.set.call(this, key, val, options);
      },
    },
  );
})(Drupal, Backbone);
