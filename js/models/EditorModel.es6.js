/**
 * @file
 * A Backbone Model for the state of an in-place editor.
 *
 * @see Drupal.quickedit.EditorView
 */

(function (Backbone, Drupal) {
  /**
   * @constructor
   *
   * @augments Backbone.Model
   */
  Drupal.quickedit.EditorModel = Backbone.Model.extend(
    /** @lends Drupal.quickedit.EditorModel# */ {
      /**
       * @type {object}
       *
       * @prop {string} originalValue
       * @prop {string} currentValue
       * @prop {Array} validationErrors
       */
      defaults: /** @lends Drupal.quickedit.EditorModel# */ {
        /**
         * Not the full HTML representation of this field, but the "actual"
         * original value of the field, stored by the used in-place editor, and
         * in a representation that can be chosen by the in-place editor.
         *
         * @type {string}
         */
        originalValue: null,

        /**
         * Analogous to originalValue, but the current value.
         *
         * @type {string}
         */
        currentValue: null,

        /**
         * Stores any validation errors to be rendered.
         *
         * @type {Array}
         */
        validationErrors: null,
      },
    },
  );
})(<PERSON><PERSON>, <PERSON>upal);
