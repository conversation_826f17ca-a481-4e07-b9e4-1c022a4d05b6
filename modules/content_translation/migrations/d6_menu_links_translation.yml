# cspell:ignore mlid
id: d6_menu_links_translation
label: <PERSON>u links
migration_tags:
  - Drupal 6
  - Content
  - Multilingual
source:
  plugin: d6_menu_link_translation
process:
  id: mlid
  langcode: language
  title:
    -
      plugin: callback
      source:
        - title_translated
        - link_title
      callable: array_filter
    -
      plugin: callback
      callable: current
  description:
    -
      plugin: callback
      source:
        - description_translated
        - description
      callable: array_filter
    -
      plugin: callback
      callable: current
  menu_name:
    -
      plugin: migration_lookup
      # The menu migration is in the system module.
      migration: d6_menu
      source: menu_name
    -
      plugin: skip_on_empty
      method: row
    -
      plugin: static_map
      map:
        management: admin
      bypass: true
destination:
  plugin: entity:menu_link_content
  default_bundle: menu_link_content
  no_stub: true
  translations: true
migration_dependencies:
  required:
    - language
    - d6_menu
    - d6_menu_links
