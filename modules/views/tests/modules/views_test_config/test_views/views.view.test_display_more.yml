langcode: en
status: true
dependencies: {  }
id: test_display_more
label: 'Test display more'
module: views
description: ''
tag: ''
base_table: views_test_data
base_field: nid
display:
  default:
    display_options:
      defaults:
        fields: false
        pager: false
        sorts: false
      fields:
        id:
          field: id
          id: id
          relationship: none
          table: views_test_data
          plugin_id: numeric
      pager:
        options:
          offset: 0
        type: none
      sorts:
        id:
          field: id
          id: id
          order: ASC
          relationship: none
          table: views_test_data
          plugin_id: numeric
      use_more: true
      use_more_always: true
      use_more_text: 'custom more text'
      link_display: page_1
    display_plugin: default
    display_title: Default
    id: default
    position: 0
  page_1:
    display_plugin: page
    id: page_1
    display_title: Page
    position: 1
    display_options:
      path: test_display_more
