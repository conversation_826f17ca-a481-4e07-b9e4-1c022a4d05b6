# Schema for the views default arguments.

views.argument_default.*:
  type: mapping
  label: 'Base default argument'

views.argument_default.fixed:
  type: mapping
  label: 'Fixed'
  mapping:
    argument:
      type: string
      label: 'Fixed value'

views.argument_default.raw:
  type: mapping
  label: 'Raw value from URL'
  mapping:
    index:
      type: integer
      label: 'Path component'
    use_alias:
      type: boolean
      label: 'Use path alias'

views.argument_default.query_parameter:
  type: mapping
  label: 'Query parameter'
  mapping:
    query_param:
      type: string
      label: 'Parameter'
    fallback:
      type: string
      label: 'Fallback value'
    multiple:
      type: string
      label: 'Multiple values'
