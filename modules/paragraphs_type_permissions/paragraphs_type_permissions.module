<?php

/**
 * @file
 * Contains paragraphs_type_permissions.module
 */

use <PERSON>upal\Core\Url;
use <PERSON>upal\Core\Access\AccessResult;
use Drupal\Core\Session\AccountInterface;
use Drupal\paragraphs\ParagraphInterface;
use <PERSON>upal\Core\Routing\RouteMatchInterface;

/**
 * Implements hook_help().
 */
function paragraphs_type_permissions_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    // Help for the Paragraphs type permissions module.
    case 'help.page.paragraphs_type_permissions':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Paragraphs Type permission module allows administrators to configure permissions individually for each <em>Paragraphs type</em>. For more information, see the <a href=":online">online documentation for the Paragraphs module</a>.', [':online' => 'https://www.drupal.org/node/2444881']) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dt>' . t('Configuring permissions per Paragraphs type') . '</dt>';
      $output .= '<dd>' . t('Administrators can configure the permissions to view, create, edit, and delete each <em>Paragraphs type</em> individually on the <a href=":permissions">Permissions page</a>.', [':permissions' => Url::fromRoute('user.admin_permissions')->toString()]) . '</dd>';
      return $output;
    break;
  }
}

/**
 * Implements hook_ENTITY_TYPE_access() for entity type "paragraph".
 */
function paragraphs_type_permissions_paragraph_access(ParagraphInterface $entity, $operation, AccountInterface $account) {
  $permissions = &drupal_static(__FUNCTION__, array());

  if (!in_array($operation, array('view', 'update', 'delete'), TRUE)) {
    // If there was no type to check against, or the $op was not one of the
    // supported ones, we return access denied.
    return AccessResult::neutral();
  }

  // Set static cache id to use the type machine name.
  $type = $entity->getType();

  if ($operation == 'view' && !$entity->status->value) {
    return AccessResult::forbidden();
  }

  // If we've already checked access for this type, user and op, return from
  // cache.
  if (isset($permissions[$account->id()][$type][$operation])) {
    return $permissions[$account->id()][$type][$operation];
  }

  // If the current user has access to this type/operation, return access
  // allowed, forbidden otherwise.
  if ($account->hasPermission('bypass paragraphs type content access') || $account->hasPermission($operation . ' paragraph content ' . $type)) {
    $permissions[$account->id()][$type][$operation] = AccessResult::allowed()->cachePerPermissions();
  }
  else {
    $permissions[$account->id()][$type][$operation] = AccessResult::forbidden()->cachePerPermissions();
  }

  return $permissions[$account->id()][$type][$operation];
}

/**
 * Implements hook_ENTITY_TYPE_create_access() for entity type "paragraph".
 */
function paragraphs_type_permissions_paragraph_create_access(?AccountInterface $account = NULL, array $context = array(), $entity_bundle = NULL) {
  $permissions = &drupal_static(__FUNCTION__, array());

  // Set static cache id to use the type machine name.
  $type = $entity_bundle;
  $op = 'create';

  // If we've already checked access for this type, user and op, return from
  // cache.
  if (isset($permissions[$account->id()][$type][$op])) {
    return $permissions[$account->id()][$type][$op];
  }

  // If the current user has access to this type/op, return access allowed,
  // forbidden otherwise.
  if ($account->hasPermission('bypass paragraphs type content access') || $account->hasPermission($op . ' paragraph content ' . $type)) {
    $permissions[$account->id()][$type][$op] = AccessResult::allowed()->cachePerPermissions();
  }
  else {
    $permissions[$account->id()][$type][$op] = AccessResult::forbidden()->cachePerPermissions();
  }

  return $permissions[$account->id()][$type][$op];
}
