services:
  _defaults:
    autoconfigure: true
  cache.dynamic_page_cache:
    class: Drupal\Core\Cache\CacheBackendInterface
    tags:
      - { name: cache.bin }
    factory: ['@cache_factory', 'get']
    arguments: [dynamic_page_cache]
  variation_cache.dynamic_page_cache:
    class: Drupal\Core\Cache\VariationCacheInterface
    factory: ['@variation_cache_factory', 'get']
    arguments: [dynamic_page_cache]
  dynamic_page_cache_subscriber:
    class: Drupal\dynamic_page_cache\EventSubscriber\DynamicPageCacheSubscriber
    arguments: ['@dynamic_page_cache_request_policy', '@dynamic_page_cache_response_policy', '@variation_cache.dynamic_page_cache', '@cache_contexts_manager', '%renderer.config%']

  # Request & response policies.
  dynamic_page_cache_request_policy:
    class: Drupal\dynamic_page_cache\PageCache\RequestPolicy\DefaultRequestPolicy
    tags:
      - { name: service_collector, tag: dynamic_page_cache_request_policy, call: addPolicy}
  dynamic_page_cache_response_policy:
    class: Drupal\Core\PageCache\ChainResponsePolicy
    tags:
      - { name: service_collector, tag: dynamic_page_cache_response_policy, call: addPolicy}
    lazy: true
  dynamic_page_cache_deny_admin_routes:
    class: Drupal\dynamic_page_cache\PageCache\ResponsePolicy\DenyAdminRoutes
    arguments: ['@current_route_match']
    public: false
    tags:
      - { name: dynamic_page_cache_response_policy }
