entity.paragraphs_library_item.collection:
  title: Paragraphs
  route_name: entity.paragraphs_library_item.collection
  base_route: system.admin_content
entity.paragraphs_library_item.canonical:
  route_name: entity.paragraphs_library_item.canonical
  base_route: entity.paragraphs_library_item.canonical
  title: View
entity.paragraphs_library_item.edit_form:
  route_name: entity.paragraphs_library_item.edit_form
  base_route: entity.paragraphs_library_item.canonical
  title: Edit
entity.paragraphs_library_item.delete_form:
  route_name: entity.paragraphs_library_item.delete_form
  base_route: entity.paragraphs_library_item.canonical
  title: Delete
  weight: 10
entity.paragraphs_library_item_version_history:
  route_name: entity.paragraphs_library_item.version_history
  base_route: entity.paragraphs_library_item.canonical
  title: 'Revisions'
  weight: 20
paragraphs_library_item.settings:
  route_name: paragraphs_library_item.settings
  base_route: paragraphs_library_item.settings
  title: 'Paragraphs library item settings'
