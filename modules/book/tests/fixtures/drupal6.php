<?php
// phpcs:ignoreFile
/**
 * @file
 * A database agnostic dump for testing purposes.
 *
 * This file was generated by the Drupal 11.0-dev db-tools.php script.
 */

use Drupal\Core\Database\Database;

$connection = Database::getConnection();
// Ensure any tables with a serial column with a value of 0 are created as
// expected.
if ($connection->databaseType() === 'mysql') {
  $sql_mode = $connection->query("SELECT @@sql_mode;")->fetchField();
  $connection->query("SET sql_mode = '$sql_mode,NO_AUTO_VALUE_ON_ZERO'");
}

$connection->schema()->createTable('access', array(
  'fields' => array(
    'aid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'mask' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'type' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'status' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'aid',
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('actions', array(
  'fields' => array(
    'aid' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '0',
    ),
    'type' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '32',
      'default' => '',
    ),
    'callback' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'parameters' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'big',
    ),
    'description' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'aid',
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('actions')
->fields(array(
  'aid',
  'type',
  'callback',
  'parameters',
  'description',
))
->values(array(
  'aid' => 'comment_publish_action',
  'type' => 'comment',
  'callback' => 'comment_publish_action',
  'parameters' => '',
  'description' => 'Publish comment',
))
->values(array(
  'aid' => 'comment_unpublish_action',
  'type' => 'comment',
  'callback' => 'comment_unpublish_action',
  'parameters' => '',
  'description' => 'Unpublish comment',
))
->values(array(
  'aid' => 'node_make_sticky_action',
  'type' => 'node',
  'callback' => 'node_make_sticky_action',
  'parameters' => '',
  'description' => 'Make post sticky',
))
->values(array(
  'aid' => 'node_make_unsticky_action',
  'type' => 'node',
  'callback' => 'node_make_unsticky_action',
  'parameters' => '',
  'description' => 'Make post unsticky',
))
->values(array(
  'aid' => 'node_promote_action',
  'type' => 'node',
  'callback' => 'node_promote_action',
  'parameters' => '',
  'description' => 'Promote post to front page',
))
->values(array(
  'aid' => 'node_publish_action',
  'type' => 'node',
  'callback' => 'node_publish_action',
  'parameters' => '',
  'description' => 'Publish post',
))
->values(array(
  'aid' => 'node_save_action',
  'type' => 'node',
  'callback' => 'node_save_action',
  'parameters' => '',
  'description' => 'Save post',
))
->values(array(
  'aid' => 'node_unpromote_action',
  'type' => 'node',
  'callback' => 'node_unpromote_action',
  'parameters' => '',
  'description' => 'Remove post from front page',
))
->values(array(
  'aid' => 'node_unpublish_action',
  'type' => 'node',
  'callback' => 'node_unpublish_action',
  'parameters' => '',
  'description' => 'Unpublish post',
))
->values(array(
  'aid' => 'user_block_ip_action',
  'type' => 'user',
  'callback' => 'user_block_ip_action',
  'parameters' => '',
  'description' => 'Ban IP address of current user',
))
->values(array(
  'aid' => 'user_block_user_action',
  'type' => 'user',
  'callback' => 'user_block_user_action',
  'parameters' => '',
  'description' => 'Block current user',
))
->execute();
$connection->schema()->createTable('actions_aid', array(
  'fields' => array(
    'aid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
  ),
  'primary key' => array(
    'aid',
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('authmap', array(
  'fields' => array(
    'aid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
    'uid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'authname' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '128',
      'default' => '',
    ),
    'module' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '128',
      'default' => '',
    ),
  ),
  'primary key' => array(
    'aid',
  ),
  'unique keys' => array(
    'authname' => array(
      'authname',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('batch', array(
  'fields' => array(
    'bid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
    'token' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '64',
    ),
    'timestamp' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'batch' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'big',
    ),
  ),
  'primary key' => array(
    'bid',
  ),
  'indexes' => array(
    'token' => array(
      'token',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('blocks', array(
  'fields' => array(
    'bid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'module' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '64',
      'default' => '',
    ),
    'delta' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '32',
      'default' => '0',
    ),
    'theme' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '64',
      'default' => '',
    ),
    'status' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
    'weight' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
    'region' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '64',
      'default' => '',
    ),
    'custom' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
    'throttle' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
    'visibility' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
    'pages' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'title' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '64',
      'default' => '',
    ),
    'cache' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '1',
    ),
  ),
  'primary key' => array(
    'bid',
  ),
  'unique keys' => array(
    'tmd' => array(
      'theme',
      'module',
      'delta',
    ),
  ),
  'indexes' => array(
    'list' => array(
      'theme',
      'status',
      'region',
      'weight',
      'module',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('blocks')
->fields(array(
  'bid',
  'module',
  'delta',
  'theme',
  'status',
  'weight',
  'region',
  'custom',
  'throttle',
  'visibility',
  'pages',
  'title',
  'cache',
))
->values(array(
  'bid' => '1',
  'module' => 'user',
  'delta' => '0',
  'theme' => 'garland',
  'status' => '1',
  'weight' => '-4',
  'region' => 'left',
  'custom' => '0',
  'throttle' => '0',
  'visibility' => '0',
  'pages' => '',
  'title' => '',
  'cache' => '-1',
))
->values(array(
  'bid' => '2',
  'module' => 'user',
  'delta' => '1',
  'theme' => 'garland',
  'status' => '1',
  'weight' => '-5',
  'region' => 'left',
  'custom' => '0',
  'throttle' => '0',
  'visibility' => '0',
  'pages' => '',
  'title' => '',
  'cache' => '-1',
))
->values(array(
  'bid' => '3',
  'module' => 'system',
  'delta' => '0',
  'theme' => 'garland',
  'status' => '1',
  'weight' => '-5',
  'region' => 'footer',
  'custom' => '0',
  'throttle' => '0',
  'visibility' => '0',
  'pages' => '',
  'title' => '',
  'cache' => '-1',
))
->values(array(
  'bid' => '4',
  'module' => 'book',
  'delta' => '0',
  'theme' => 'garland',
  'status' => '1',
  'weight' => '-3',
  'region' => 'left',
  'custom' => '0',
  'throttle' => '0',
  'visibility' => '0',
  'pages' => '',
  'title' => 'Books',
  'cache' => '5',
))
->values(array(
  'bid' => '5',
  'module' => 'comment',
  'delta' => '0',
  'theme' => 'garland',
  'status' => '0',
  'weight' => '0',
  'region' => '',
  'custom' => '0',
  'throttle' => '0',
  'visibility' => '0',
  'pages' => '',
  'title' => '',
  'cache' => '1',
))
->values(array(
  'bid' => '6',
  'module' => 'menu',
  'delta' => 'primary-links',
  'theme' => 'garland',
  'status' => '0',
  'weight' => '0',
  'region' => '',
  'custom' => '0',
  'throttle' => '0',
  'visibility' => '0',
  'pages' => '',
  'title' => '',
  'cache' => '-1',
))
->values(array(
  'bid' => '7',
  'module' => 'menu',
  'delta' => 'secondary-links',
  'theme' => 'garland',
  'status' => '0',
  'weight' => '0',
  'region' => '',
  'custom' => '0',
  'throttle' => '0',
  'visibility' => '0',
  'pages' => '',
  'title' => '',
  'cache' => '-1',
))
->values(array(
  'bid' => '8',
  'module' => 'node',
  'delta' => '0',
  'theme' => 'garland',
  'status' => '0',
  'weight' => '0',
  'region' => '',
  'custom' => '0',
  'throttle' => '0',
  'visibility' => '0',
  'pages' => '',
  'title' => '',
  'cache' => '-1',
))
->values(array(
  'bid' => '9',
  'module' => 'user',
  'delta' => '2',
  'theme' => 'garland',
  'status' => '0',
  'weight' => '0',
  'region' => '',
  'custom' => '0',
  'throttle' => '0',
  'visibility' => '0',
  'pages' => '',
  'title' => '',
  'cache' => '1',
))
->values(array(
  'bid' => '10',
  'module' => 'user',
  'delta' => '3',
  'theme' => 'garland',
  'status' => '0',
  'weight' => '0',
  'region' => '',
  'custom' => '0',
  'throttle' => '0',
  'visibility' => '0',
  'pages' => '',
  'title' => '',
  'cache' => '-1',
))
->execute();
$connection->schema()->createTable('blocks_roles', array(
  'fields' => array(
    'module' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '64',
    ),
    'delta' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '32',
    ),
    'rid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
  ),
  'primary key' => array(
    'module',
    'delta',
    'rid',
  ),
  'indexes' => array(
    'rid' => array(
      'rid',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('book', array(
  'fields' => array(
    'mlid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'nid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'bid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
  ),
  'primary key' => array(
    'mlid',
  ),
  'unique keys' => array(
    'nid' => array(
      'nid',
    ),
  ),
  'indexes' => array(
    'bid' => array(
      'bid',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('book')
->fields(array(
  'mlid',
  'nid',
  'bid',
))
->values(array(
  'mlid' => '121',
  'nid' => '1',
  'bid' => '1',
))
->values(array(
  'mlid' => '122',
  'nid' => '2',
  'bid' => '1',
))
->values(array(
  'mlid' => '123',
  'nid' => '3',
  'bid' => '1',
))
->values(array(
  'mlid' => '124',
  'nid' => '4',
  'bid' => '1',
))
->values(array(
  'mlid' => '125',
  'nid' => '5',
  'bid' => '1',
))
->values(array(
  'mlid' => '126',
  'nid' => '6',
  'bid' => '6',
))
->values(array(
  'mlid' => '127',
  'nid' => '7',
  'bid' => '6',
))
->values(array(
  'mlid' => '128',
  'nid' => '8',
  'bid' => '6',
))
->values(array(
  'mlid' => '129',
  'nid' => '9',
  'bid' => '6',
))
->values(array(
  'mlid' => '130',
  'nid' => '10',
  'bid' => '6',
))
->execute();
$connection->schema()->createTable('boxes', array(
  'fields' => array(
    'bid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
    'body' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'big',
    ),
    'info' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '128',
      'default' => '',
    ),
    'format' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'bid',
  ),
  'unique keys' => array(
    'info' => array(
      'info',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('cache', array(
  'fields' => array(
    'cid' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'data' => array(
      'type' => 'blob',
      'not null' => FALSE,
      'size' => 'big',
    ),
    'expire' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'created' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'headers' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'normal',
    ),
    'serialized' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'cid',
  ),
  'indexes' => array(
    'expire' => array(
      'expire',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('cache_block', array(
  'fields' => array(
    'cid' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'data' => array(
      'type' => 'blob',
      'not null' => FALSE,
      'size' => 'big',
    ),
    'expire' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'created' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'headers' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'normal',
    ),
    'serialized' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'cid',
  ),
  'indexes' => array(
    'expire' => array(
      'expire',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('cache_filter', array(
  'fields' => array(
    'cid' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'data' => array(
      'type' => 'blob',
      'not null' => FALSE,
      'size' => 'big',
    ),
    'expire' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'created' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'headers' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'normal',
    ),
    'serialized' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'cid',
  ),
  'indexes' => array(
    'expire' => array(
      'expire',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('cache_form', array(
  'fields' => array(
    'cid' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'data' => array(
      'type' => 'blob',
      'not null' => FALSE,
      'size' => 'big',
    ),
    'expire' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'created' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'headers' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'normal',
    ),
    'serialized' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'cid',
  ),
  'indexes' => array(
    'expire' => array(
      'expire',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('cache_menu', array(
  'fields' => array(
    'cid' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'data' => array(
      'type' => 'blob',
      'not null' => FALSE,
      'size' => 'big',
    ),
    'expire' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'created' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'headers' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'normal',
    ),
    'serialized' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'cid',
  ),
  'indexes' => array(
    'expire' => array(
      'expire',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('cache_page', array(
  'fields' => array(
    'cid' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'data' => array(
      'type' => 'blob',
      'not null' => FALSE,
      'size' => 'big',
    ),
    'expire' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'created' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'headers' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'normal',
    ),
    'serialized' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'cid',
  ),
  'indexes' => array(
    'expire' => array(
      'expire',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('comments', array(
  'fields' => array(
    'cid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'pid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'nid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'uid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'subject' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '64',
      'default' => '',
    ),
    'comment' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'big',
    ),
    'hostname' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '128',
      'default' => '',
    ),
    'timestamp' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'status' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'format' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
    'thread' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
    ),
    'name' => array(
      'type' => 'varchar',
      'not null' => FALSE,
      'length' => '60',
    ),
    'mail' => array(
      'type' => 'varchar',
      'not null' => FALSE,
      'length' => '64',
    ),
    'homepage' => array(
      'type' => 'varchar',
      'not null' => FALSE,
      'length' => '255',
    ),
  ),
  'primary key' => array(
    'cid',
  ),
  'indexes' => array(
    'pid' => array(
      'pid',
    ),
    'nid' => array(
      'nid',
    ),
    'comment_uid' => array(
      'uid',
    ),
    'status' => array(
      'status',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('files', array(
  'fields' => array(
    'fid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
    'uid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'filename' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'filepath' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'filemime' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'filesize' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'status' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'timestamp' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
  ),
  'primary key' => array(
    'fid',
  ),
  'indexes' => array(
    'uid' => array(
      'uid',
    ),
    'status' => array(
      'status',
    ),
    'timestamp' => array(
      'timestamp',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('filter_formats', array(
  'fields' => array(
    'format' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'name' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'roles' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'cache' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'format',
  ),
  'unique keys' => array(
    'name' => array(
      'name',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('filter_formats')
->fields(array(
  'format',
  'name',
  'roles',
  'cache',
))
->values(array(
  'format' => '1',
  'name' => 'Filtered HTML',
  'roles' => ',1,2,',
  'cache' => '1',
))
->values(array(
  'format' => '2',
  'name' => 'Full HTML',
  'roles' => '',
  'cache' => '1',
))
->execute();
$connection->schema()->createTable('filters', array(
  'fields' => array(
    'fid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'format' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'module' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '64',
      'default' => '',
    ),
    'delta' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
    'weight' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'fid',
  ),
  'unique keys' => array(
    'fmd' => array(
      'format',
      'module',
      'delta',
    ),
  ),
  'indexes' => array(
    'list' => array(
      'format',
      'weight',
      'module',
      'delta',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('filters')
->fields(array(
  'fid',
  'format',
  'module',
  'delta',
  'weight',
))
->values(array(
  'fid' => '1',
  'format' => '1',
  'module' => 'filter',
  'delta' => '2',
  'weight' => '0',
))
->values(array(
  'fid' => '2',
  'format' => '1',
  'module' => 'filter',
  'delta' => '0',
  'weight' => '1',
))
->values(array(
  'fid' => '3',
  'format' => '1',
  'module' => 'filter',
  'delta' => '1',
  'weight' => '2',
))
->values(array(
  'fid' => '4',
  'format' => '1',
  'module' => 'filter',
  'delta' => '3',
  'weight' => '10',
))
->values(array(
  'fid' => '5',
  'format' => '2',
  'module' => 'filter',
  'delta' => '2',
  'weight' => '0',
))
->values(array(
  'fid' => '6',
  'format' => '2',
  'module' => 'filter',
  'delta' => '1',
  'weight' => '1',
))
->values(array(
  'fid' => '7',
  'format' => '2',
  'module' => 'filter',
  'delta' => '3',
  'weight' => '10',
))
->execute();
$connection->schema()->createTable('flood', array(
  'fields' => array(
    'fid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'event' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '64',
      'default' => '',
    ),
    'hostname' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '128',
      'default' => '',
    ),
    'timestamp' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'fid',
  ),
  'indexes' => array(
    'allow' => array(
      'event',
      'hostname',
      'timestamp',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('history', array(
  'fields' => array(
    'uid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'nid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'timestamp' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'uid',
    'nid',
  ),
  'indexes' => array(
    'nid' => array(
      'nid',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('history')
->fields(array(
  'uid',
  'nid',
  'timestamp',
))
->values(array(
  'uid' => '1',
  'nid' => '1',
  'timestamp' => '1689993849',
))
->values(array(
  'uid' => '1',
  'nid' => '2',
  'timestamp' => '1689990134',
))
->values(array(
  'uid' => '1',
  'nid' => '3',
  'timestamp' => '1689993835',
))
->values(array(
  'uid' => '1',
  'nid' => '4',
  'timestamp' => '1689990623',
))
->values(array(
  'uid' => '1',
  'nid' => '5',
  'timestamp' => '1689990252',
))
->values(array(
  'uid' => '1',
  'nid' => '6',
  'timestamp' => '1689993836',
))
->values(array(
  'uid' => '1',
  'nid' => '7',
  'timestamp' => '1689990337',
))
->values(array(
  'uid' => '1',
  'nid' => '8',
  'timestamp' => '1689993839',
))
->values(array(
  'uid' => '1',
  'nid' => '9',
  'timestamp' => '1689992935',
))
->values(array(
  'uid' => '1',
  'nid' => '10',
  'timestamp' => '1689992924',
))
->execute();
$connection->schema()->createTable('menu_custom', array(
  'fields' => array(
    'menu_name' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '32',
      'default' => '',
    ),
    'title' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'description' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'normal',
    ),
  ),
  'primary key' => array(
    'menu_name',
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('menu_custom')
->fields(array(
  'menu_name',
  'title',
  'description',
))
->values(array(
  'menu_name' => 'navigation',
  'title' => 'Navigation',
  'description' => 'The navigation menu is provided by Drupal and is the main interactive menu for any site. It is usually the only menu that contains personalized links for authenticated users, and is often not even visible to anonymous users.',
))
->values(array(
  'menu_name' => 'primary-links',
  'title' => 'Primary links',
  'description' => 'Primary links are often used at the theme layer to show the major sections of a site. A typical representation for primary links would be tabs along the top.',
))
->values(array(
  'menu_name' => 'secondary-links',
  'title' => 'Secondary links',
  'description' => 'Secondary links are often used for pages like legal notices, contact details, and other secondary navigation items that play a lesser role than primary links',
))
->execute();
$connection->schema()->createTable('menu_links', array(
  'fields' => array(
    'menu_name' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '32',
      'default' => '',
    ),
    'mlid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
    'plid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'link_path' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'router_path' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'link_title' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'options' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'normal',
    ),
    'module' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => 'system',
    ),
    'hidden' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
    'external' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
    'has_children' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
    'expanded' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
    'weight' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'depth' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
    'customized' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
    'p1' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'p2' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'p3' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'p4' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'p5' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'p6' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'p7' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'p8' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'p9' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'updated' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'mlid',
  ),
  'indexes' => array(
    'path_menu' => array(
      array(
        'link_path',
        '128',
      ),
      'menu_name',
    ),
    'menu_plid_expand_child' => array(
      'menu_name',
      'plid',
      'expanded',
      'has_children',
    ),
    'menu_parents' => array(
      'menu_name',
      'p1',
      'p2',
      'p3',
      'p4',
      'p5',
      'p6',
      'p7',
      'p8',
      'p9',
    ),
    'router_path' => array(
      array(
        'router_path',
        '128',
      ),
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('menu_links')
->fields(array(
  'menu_name',
  'mlid',
  'plid',
  'link_path',
  'router_path',
  'link_title',
  'options',
  'module',
  'hidden',
  'external',
  'has_children',
  'expanded',
  'weight',
  'depth',
  'customized',
  'p1',
  'p2',
  'p3',
  'p4',
  'p5',
  'p6',
  'p7',
  'p8',
  'p9',
  'updated',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '1',
  'plid' => '0',
  'link_path' => 'batch',
  'router_path' => 'batch',
  'link_title' => '',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '1',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '2',
  'plid' => '0',
  'link_path' => 'admin',
  'router_path' => 'admin',
  'link_title' => 'Administer',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '1',
  'expanded' => '0',
  'weight' => '9',
  'depth' => '1',
  'customized' => '0',
  'p1' => '2',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '3',
  'plid' => '0',
  'link_path' => 'node',
  'router_path' => 'node',
  'link_title' => 'Content',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '3',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '4',
  'plid' => '0',
  'link_path' => 'logout',
  'router_path' => 'logout',
  'link_title' => 'Log out',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '10',
  'depth' => '1',
  'customized' => '0',
  'p1' => '4',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '5',
  'plid' => '0',
  'link_path' => 'rss.xml',
  'router_path' => 'rss.xml',
  'link_title' => 'RSS feed',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '5',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '6',
  'plid' => '0',
  'link_path' => 'user',
  'router_path' => 'user',
  'link_title' => 'User account',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '6',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '7',
  'plid' => '0',
  'link_path' => 'node/%',
  'router_path' => 'node/%',
  'link_title' => '',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '7',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '8',
  'plid' => '2',
  'link_path' => 'admin/compact',
  'router_path' => 'admin/compact',
  'link_title' => 'Compact mode',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '2',
  'customized' => '0',
  'p1' => '2',
  'p2' => '8',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '9',
  'plid' => '0',
  'link_path' => 'filter/tips',
  'router_path' => 'filter/tips',
  'link_title' => 'Compose tips',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '9',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '10',
  'plid' => '2',
  'link_path' => 'admin/content',
  'router_path' => 'admin/content',
  'link_title' => 'Content management',
  'options' => "a:1:{s:10:\"attributes\";a:1:{s:5:\"title\";s:27:\"Manage your site's content.\";}}",
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '1',
  'expanded' => '0',
  'weight' => '-10',
  'depth' => '2',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '11',
  'plid' => '0',
  'link_path' => 'node/add',
  'router_path' => 'node/add',
  'link_title' => 'Create content',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '1',
  'expanded' => '0',
  'weight' => '1',
  'depth' => '1',
  'customized' => '0',
  'p1' => '11',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '12',
  'plid' => '0',
  'link_path' => 'comment/delete',
  'router_path' => 'comment/delete',
  'link_title' => 'Delete comment',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '12',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '13',
  'plid' => '0',
  'link_path' => 'comment/edit',
  'router_path' => 'comment/edit',
  'link_title' => 'Edit comment',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '13',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '14',
  'plid' => '0',
  'link_path' => 'system/files',
  'router_path' => 'system/files',
  'link_title' => 'File download',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '14',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '15',
  'plid' => '2',
  'link_path' => 'admin/help',
  'router_path' => 'admin/help',
  'link_title' => 'Help',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '9',
  'depth' => '2',
  'customized' => '0',
  'p1' => '2',
  'p2' => '15',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '16',
  'plid' => '2',
  'link_path' => 'admin/reports',
  'router_path' => 'admin/reports',
  'link_title' => 'Reports',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:59:"View reports from system logs and other status information.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '1',
  'expanded' => '0',
  'weight' => '5',
  'depth' => '2',
  'customized' => '0',
  'p1' => '2',
  'p2' => '16',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '17',
  'plid' => '2',
  'link_path' => 'admin/build',
  'router_path' => 'admin/build',
  'link_title' => 'Site building',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:38:"Control how your site looks and feels.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '1',
  'expanded' => '0',
  'weight' => '-10',
  'depth' => '2',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '18',
  'plid' => '2',
  'link_path' => 'admin/settings',
  'router_path' => 'admin/settings',
  'link_title' => 'Site configuration',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:40:"Adjust basic site configuration options.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '1',
  'expanded' => '0',
  'weight' => '-5',
  'depth' => '2',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '19',
  'plid' => '0',
  'link_path' => 'user/autocomplete',
  'router_path' => 'user/autocomplete',
  'link_title' => 'User autocomplete',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '19',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '20',
  'plid' => '2',
  'link_path' => 'admin/user',
  'router_path' => 'admin/user',
  'link_title' => 'User management',
  'options' => "a:1:{s:10:\"attributes\";a:1:{s:5:\"title\";s:61:\"Manage your site's users, groups and access to site features.\";}}",
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '1',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '2',
  'customized' => '0',
  'p1' => '2',
  'p2' => '20',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '21',
  'plid' => '0',
  'link_path' => 'user/%',
  'router_path' => 'user/%',
  'link_title' => 'My account',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '21',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '22',
  'plid' => '20',
  'link_path' => 'admin/user/rules',
  'router_path' => 'admin/user/rules',
  'link_title' => 'Access rules',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:80:"List and create rules to disallow usernames, e-mail addresses, and IP addresses.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '20',
  'p3' => '22',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '23',
  'plid' => '18',
  'link_path' => 'admin/settings/actions',
  'router_path' => 'admin/settings/actions',
  'link_title' => 'Actions',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:41:"Manage the actions defined for your site.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '23',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '24',
  'plid' => '18',
  'link_path' => 'admin/settings/admin',
  'router_path' => 'admin/settings/admin',
  'link_title' => 'Administration theme',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:55:"Settings for how your administrative pages should look.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '24',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '25',
  'plid' => '17',
  'link_path' => 'admin/build/block',
  'router_path' => 'admin/build/block',
  'link_title' => 'Blocks',
  'options' => "a:1:{s:10:\"attributes\";a:1:{s:5:\"title\";s:79:\"Configure what block content appears in your site's sidebars and other regions.\";}}",
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '25',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '26',
  'plid' => '18',
  'link_path' => 'admin/settings/clean-urls',
  'router_path' => 'admin/settings/clean-urls',
  'link_title' => 'Clean URLs',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:43:"Enable or disable clean URLs for your site.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '26',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '27',
  'plid' => '10',
  'link_path' => 'admin/content/comment',
  'router_path' => 'admin/content/comment',
  'link_title' => 'Comments',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:61:"List and edit site comments and the comment moderation queue.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '27',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '28',
  'plid' => '10',
  'link_path' => 'admin/content/node',
  'router_path' => 'admin/content/node',
  'link_title' => 'Content',
  'options' => "a:1:{s:10:\"attributes\";a:1:{s:5:\"title\";s:43:\"View, edit, and delete your site's content.\";}}",
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '28',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '29',
  'plid' => '10',
  'link_path' => 'admin/content/types',
  'router_path' => 'admin/content/types',
  'link_title' => 'Content types',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:82:"Manage posts by content type, including default status, front page promotion, etc.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '29',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '30',
  'plid' => '18',
  'link_path' => 'admin/settings/date-time',
  'router_path' => 'admin/settings/date-time',
  'link_title' => 'Date and time',
  'options' => "a:1:{s:10:\"attributes\";a:1:{s:5:\"title\";s:89:\"Settings for how Drupal displays date and time, as well as the system's default timezone.\";}}",
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '30',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '31',
  'plid' => '0',
  'link_path' => 'node/%/delete',
  'router_path' => 'node/%/delete',
  'link_title' => 'Delete',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '1',
  'depth' => '1',
  'customized' => '0',
  'p1' => '31',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '32',
  'plid' => '21',
  'link_path' => 'user/%/delete',
  'router_path' => 'user/%/delete',
  'link_title' => 'Delete',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '2',
  'customized' => '0',
  'p1' => '21',
  'p2' => '32',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '33',
  'plid' => '18',
  'link_path' => 'admin/settings/error-reporting',
  'router_path' => 'admin/settings/error-reporting',
  'link_title' => 'Error reporting',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:93:"Control how Drupal deals with errors including 403/404 errors as well as PHP error reporting.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '33',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '34',
  'plid' => '18',
  'link_path' => 'admin/settings/file-system',
  'router_path' => 'admin/settings/file-system',
  'link_title' => 'File system',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:68:"Tell Drupal where to store uploaded files and how they are accessed.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '34',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '35',
  'plid' => '18',
  'link_path' => 'admin/settings/image-toolkit',
  'router_path' => 'admin/settings/image-toolkit',
  'link_title' => 'Image toolkit',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:74:"Choose which image toolkit to use if you have installed optional toolkits.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '35',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '36',
  'plid' => '18',
  'link_path' => 'admin/settings/filters',
  'router_path' => 'admin/settings/filters',
  'link_title' => 'Input formats',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:127:"Configure how content input by users is filtered, including allowed HTML tags. Also allows enabling of module-provided filters.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '36',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '37',
  'plid' => '18',
  'link_path' => 'admin/settings/logging',
  'router_path' => 'admin/settings/logging',
  'link_title' => 'Logging and alerts',
  'options' => "a:1:{s:10:\"attributes\";a:1:{s:5:\"title\";s:156:\"Settings for logging and alerts modules. Various modules can route Drupal's system events to different destination, such as syslog, database, email, ...etc.\";}}",
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '1',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '37',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '38',
  'plid' => '17',
  'link_path' => 'admin/build/menu',
  'router_path' => 'admin/build/menu',
  'link_title' => 'Menus',
  'options' => "a:1:{s:10:\"attributes\";a:1:{s:5:\"title\";s:116:\"Control your site's navigation menu, primary links and secondary links, as well as rename and reorganize menu items.\";}}",
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '1',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '38',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '39',
  'plid' => '17',
  'link_path' => 'admin/build/modules',
  'router_path' => 'admin/build/modules',
  'link_title' => 'Modules',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:47:"Enable or disable add-on modules for your site.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '39',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '40',
  'plid' => '18',
  'link_path' => 'admin/settings/performance',
  'router_path' => 'admin/settings/performance',
  'link_title' => 'Performance',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:101:"Enable or disable page caching for anonymous users and set CSS and JS bandwidth optimization options.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '40',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '41',
  'plid' => '20',
  'link_path' => 'admin/user/permissions',
  'router_path' => 'admin/user/permissions',
  'link_title' => 'Permissions',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:64:"Determine access to features by selecting permissions for roles.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '20',
  'p3' => '41',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '42',
  'plid' => '10',
  'link_path' => 'admin/content/node-settings',
  'router_path' => 'admin/content/node-settings',
  'link_title' => 'Post settings',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:126:"Control posting behavior, such as teaser length, requiring previews before posting, and the number of posts on the front page.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '42',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '43',
  'plid' => '10',
  'link_path' => 'admin/content/rss-publishing',
  'router_path' => 'admin/content/rss-publishing',
  'link_title' => 'RSS publishing',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:92:"Configure the number of items per feed and whether feeds should be titles/teasers/full-text.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '43',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '44',
  'plid' => '0',
  'link_path' => 'comment/reply/%',
  'router_path' => 'comment/reply/%',
  'link_title' => 'Reply to comment',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '44',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '45',
  'plid' => '20',
  'link_path' => 'admin/user/roles',
  'router_path' => 'admin/user/roles',
  'link_title' => 'Roles',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:30:"List, edit, or add user roles.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '20',
  'p3' => '45',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '46',
  'plid' => '18',
  'link_path' => 'admin/settings/site-information',
  'router_path' => 'admin/settings/site-information',
  'link_title' => 'Site information',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:107:"Change basic site information, such as the site name, slogan, e-mail address, mission, front page and more.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '46',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '47',
  'plid' => '18',
  'link_path' => 'admin/settings/site-maintenance',
  'router_path' => 'admin/settings/site-maintenance',
  'link_title' => 'Site maintenance',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:63:"Take the site off-line for maintenance or bring it back online.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '47',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '48',
  'plid' => '16',
  'link_path' => 'admin/reports/status',
  'router_path' => 'admin/reports/status',
  'link_title' => 'Status report',
  'options' => "a:1:{s:10:\"attributes\";a:1:{s:5:\"title\";s:74:\"Get a status report about your site's operation and any detected problems.\";}}",
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '10',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '16',
  'p3' => '48',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '49',
  'plid' => '17',
  'link_path' => 'admin/build/themes',
  'router_path' => 'admin/build/themes',
  'link_title' => 'Themes',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:57:"Change which theme your site uses or allows users to set.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '49',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '50',
  'plid' => '20',
  'link_path' => 'admin/user/settings',
  'router_path' => 'admin/user/settings',
  'link_title' => 'User settings',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:101:"Configure default behavior of users, including registration requirements, e-mails, and user pictures.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '20',
  'p3' => '50',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '51',
  'plid' => '20',
  'link_path' => 'admin/user/user',
  'router_path' => 'admin/user/user',
  'link_title' => 'Users',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:26:"List, add, and edit users.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '20',
  'p3' => '51',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '52',
  'plid' => '15',
  'link_path' => 'admin/help/block',
  'router_path' => 'admin/help/block',
  'link_title' => 'block',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '15',
  'p3' => '52',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '53',
  'plid' => '15',
  'link_path' => 'admin/help/color',
  'router_path' => 'admin/help/color',
  'link_title' => 'color',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '15',
  'p3' => '53',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '54',
  'plid' => '15',
  'link_path' => 'admin/help/comment',
  'router_path' => 'admin/help/comment',
  'link_title' => 'comment',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '15',
  'p3' => '54',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '55',
  'plid' => '15',
  'link_path' => 'admin/help/filter',
  'router_path' => 'admin/help/filter',
  'link_title' => 'filter',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '15',
  'p3' => '55',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '56',
  'plid' => '15',
  'link_path' => 'admin/help/help',
  'router_path' => 'admin/help/help',
  'link_title' => 'help',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '15',
  'p3' => '56',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '57',
  'plid' => '15',
  'link_path' => 'admin/help/menu',
  'router_path' => 'admin/help/menu',
  'link_title' => 'menu',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '15',
  'p3' => '57',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '58',
  'plid' => '15',
  'link_path' => 'admin/help/node',
  'router_path' => 'admin/help/node',
  'link_title' => 'node',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '15',
  'p3' => '58',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '59',
  'plid' => '15',
  'link_path' => 'admin/help/system',
  'router_path' => 'admin/help/system',
  'link_title' => 'system',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '15',
  'p3' => '59',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '60',
  'plid' => '15',
  'link_path' => 'admin/help/user',
  'router_path' => 'admin/help/user',
  'link_title' => 'user',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '15',
  'p3' => '60',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '61',
  'plid' => '36',
  'link_path' => 'admin/settings/filters/%',
  'router_path' => 'admin/settings/filters/%',
  'link_title' => '',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '36',
  'p4' => '61',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '62',
  'plid' => '26',
  'link_path' => 'admin/settings/clean-urls/check',
  'router_path' => 'admin/settings/clean-urls/check',
  'link_title' => 'Clean URL check',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '26',
  'p4' => '62',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '63',
  'plid' => '23',
  'link_path' => 'admin/settings/actions/configure',
  'router_path' => 'admin/settings/actions/configure',
  'link_title' => 'Configure an advanced action',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '23',
  'p4' => '63',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '64',
  'plid' => '25',
  'link_path' => 'admin/build/block/configure',
  'router_path' => 'admin/build/block/configure',
  'link_title' => 'Configure block',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '25',
  'p4' => '64',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '65',
  'plid' => '17',
  'link_path' => 'admin/build/menu-customize/%',
  'router_path' => 'admin/build/menu-customize/%',
  'link_title' => 'Customize menu',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '65',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '66',
  'plid' => '30',
  'link_path' => 'admin/settings/date-time/lookup',
  'router_path' => 'admin/settings/date-time/lookup',
  'link_title' => 'Date and time lookup',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '30',
  'p4' => '66',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '67',
  'plid' => '25',
  'link_path' => 'admin/build/block/delete',
  'router_path' => 'admin/build/block/delete',
  'link_title' => 'Delete block',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '25',
  'p4' => '67',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '68',
  'plid' => '36',
  'link_path' => 'admin/settings/filters/delete',
  'router_path' => 'admin/settings/filters/delete',
  'link_title' => 'Delete input format',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '36',
  'p4' => '68',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '69',
  'plid' => '22',
  'link_path' => 'admin/user/rules/delete',
  'router_path' => 'admin/user/rules/delete',
  'link_title' => 'Delete rule',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '20',
  'p3' => '22',
  'p4' => '69',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '70',
  'plid' => '45',
  'link_path' => 'admin/user/roles/edit',
  'router_path' => 'admin/user/roles/edit',
  'link_title' => 'Edit role',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '20',
  'p3' => '45',
  'p4' => '70',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '71',
  'plid' => '22',
  'link_path' => 'admin/user/rules/edit',
  'router_path' => 'admin/user/rules/edit',
  'link_title' => 'Edit rule',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '20',
  'p3' => '22',
  'p4' => '71',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '72',
  'plid' => '48',
  'link_path' => 'admin/reports/status/php',
  'router_path' => 'admin/reports/status/php',
  'link_title' => 'PHP',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '16',
  'p3' => '48',
  'p4' => '72',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '73',
  'plid' => '42',
  'link_path' => 'admin/content/node-settings/rebuild',
  'router_path' => 'admin/content/node-settings/rebuild',
  'link_title' => 'Rebuild permissions',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '42',
  'p4' => '73',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '74',
  'plid' => '23',
  'link_path' => 'admin/settings/actions/orphan',
  'router_path' => 'admin/settings/actions/orphan',
  'link_title' => 'Remove orphans',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '23',
  'p4' => '74',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '75',
  'plid' => '48',
  'link_path' => 'admin/reports/status/run-cron',
  'router_path' => 'admin/reports/status/run-cron',
  'link_title' => 'Run cron',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '16',
  'p3' => '48',
  'p4' => '75',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '76',
  'plid' => '48',
  'link_path' => 'admin/reports/status/sql',
  'router_path' => 'admin/reports/status/sql',
  'link_title' => 'SQL',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '16',
  'p3' => '48',
  'p4' => '76',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '77',
  'plid' => '23',
  'link_path' => 'admin/settings/actions/delete/%',
  'router_path' => 'admin/settings/actions/delete/%',
  'link_title' => 'Delete action',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:17:"Delete an action.";}}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '23',
  'p4' => '77',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '78',
  'plid' => '0',
  'link_path' => 'admin/build/menu-customize/%/delete',
  'router_path' => 'admin/build/menu-customize/%/delete',
  'link_title' => 'Delete menu',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '78',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '79',
  'plid' => '25',
  'link_path' => 'admin/build/block/list/js',
  'router_path' => 'admin/build/block/list/js',
  'link_title' => 'JavaScript List Form',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '25',
  'p4' => '79',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '80',
  'plid' => '39',
  'link_path' => 'admin/build/modules/list/confirm',
  'router_path' => 'admin/build/modules/list/confirm',
  'link_title' => 'List',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '39',
  'p4' => '80',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '81',
  'plid' => '0',
  'link_path' => 'user/reset/%/%/%',
  'router_path' => 'user/reset/%/%/%',
  'link_title' => 'Reset password',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '81',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '82',
  'plid' => '39',
  'link_path' => 'admin/build/modules/uninstall/confirm',
  'router_path' => 'admin/build/modules/uninstall/confirm',
  'link_title' => 'Uninstall',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '39',
  'p4' => '82',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '83',
  'plid' => '0',
  'link_path' => 'node/%/revisions/%/delete',
  'router_path' => 'node/%/revisions/%/delete',
  'link_title' => 'Delete earlier revision',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '83',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '84',
  'plid' => '0',
  'link_path' => 'node/%/revisions/%/revert',
  'router_path' => 'node/%/revisions/%/revert',
  'link_title' => 'Revert to earlier revision',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '84',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '85',
  'plid' => '0',
  'link_path' => 'node/%/revisions/%/view',
  'router_path' => 'node/%/revisions/%/view',
  'link_title' => 'Revisions',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '85',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '86',
  'plid' => '38',
  'link_path' => 'admin/build/menu/item/%/delete',
  'router_path' => 'admin/build/menu/item/%/delete',
  'link_title' => 'Delete menu item',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '38',
  'p4' => '86',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '87',
  'plid' => '38',
  'link_path' => 'admin/build/menu/item/%/edit',
  'router_path' => 'admin/build/menu/item/%/edit',
  'link_title' => 'Edit menu item',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '38',
  'p4' => '87',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '88',
  'plid' => '38',
  'link_path' => 'admin/build/menu/item/%/reset',
  'router_path' => 'admin/build/menu/item/%/reset',
  'link_title' => 'Reset menu item',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '38',
  'p4' => '88',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '89',
  'plid' => '38',
  'link_path' => 'admin/build/menu-customize/navigation',
  'router_path' => 'admin/build/menu-customize/%',
  'link_title' => 'Navigation',
  'options' => 'a:0:{}',
  'module' => 'menu',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '38',
  'p4' => '89',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '90',
  'plid' => '38',
  'link_path' => 'admin/build/menu-customize/primary-links',
  'router_path' => 'admin/build/menu-customize/%',
  'link_title' => 'Primary links',
  'options' => 'a:0:{}',
  'module' => 'menu',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '38',
  'p4' => '90',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '91',
  'plid' => '38',
  'link_path' => 'admin/build/menu-customize/secondary-links',
  'router_path' => 'admin/build/menu-customize/%',
  'link_title' => 'Secondary links',
  'options' => 'a:0:{}',
  'module' => 'menu',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '17',
  'p3' => '38',
  'p4' => '91',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '92',
  'plid' => '0',
  'link_path' => 'taxonomy/autocomplete',
  'router_path' => 'taxonomy/autocomplete',
  'link_title' => 'Autocomplete taxonomy',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '92',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '93',
  'plid' => '16',
  'link_path' => 'admin/reports/dblog',
  'router_path' => 'admin/reports/dblog',
  'link_title' => 'Recent log entries',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:43:"View events that have recently been logged.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '-1',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '16',
  'p3' => '93',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '94',
  'plid' => '10',
  'link_path' => 'admin/content/taxonomy',
  'router_path' => 'admin/content/taxonomy',
  'link_title' => 'Taxonomy',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:67:"Manage tagging, categorization, and classification of your content.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '94',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '95',
  'plid' => '0',
  'link_path' => 'taxonomy/term/%',
  'router_path' => 'taxonomy/term/%',
  'link_title' => 'Taxonomy term',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '95',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '96',
  'plid' => '16',
  'link_path' => 'admin/reports/access-denied',
  'router_path' => 'admin/reports/access-denied',
  'link_title' => "Top 'access denied' errors",
  'options' => "a:1:{s:10:\"attributes\";a:1:{s:5:\"title\";s:35:\"View 'access denied' errors (403s).\";}}",
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '16',
  'p3' => '96',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '97',
  'plid' => '16',
  'link_path' => 'admin/reports/page-not-found',
  'router_path' => 'admin/reports/page-not-found',
  'link_title' => "Top 'page not found' errors",
  'options' => "a:1:{s:10:\"attributes\";a:1:{s:5:\"title\";s:36:\"View 'page not found' errors (404s).\";}}",
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '16',
  'p3' => '97',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '98',
  'plid' => '15',
  'link_path' => 'admin/help/dblog',
  'router_path' => 'admin/help/dblog',
  'link_title' => 'dblog',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '15',
  'p3' => '98',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '99',
  'plid' => '15',
  'link_path' => 'admin/help/taxonomy',
  'router_path' => 'admin/help/taxonomy',
  'link_title' => 'taxonomy',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '15',
  'p3' => '99',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '100',
  'plid' => '37',
  'link_path' => 'admin/settings/logging/dblog',
  'router_path' => 'admin/settings/logging/dblog',
  'link_title' => 'Database logging',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:169:"Settings for logging to the Drupal database logs. This is the most common method for small to medium sites on shared hosting. The logs are viewable from the admin pages.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '18',
  'p3' => '37',
  'p4' => '100',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '101',
  'plid' => '16',
  'link_path' => 'admin/reports/event/%',
  'router_path' => 'admin/reports/event/%',
  'link_title' => 'Details',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '16',
  'p3' => '101',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '102',
  'plid' => '94',
  'link_path' => 'admin/content/taxonomy/%',
  'router_path' => 'admin/content/taxonomy/%',
  'link_title' => 'List terms',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '94',
  'p4' => '102',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '103',
  'plid' => '94',
  'link_path' => 'admin/content/taxonomy/edit/term',
  'router_path' => 'admin/content/taxonomy/edit/term',
  'link_title' => 'Edit term',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '94',
  'p4' => '103',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '104',
  'plid' => '94',
  'link_path' => 'admin/content/taxonomy/edit/vocabulary/%',
  'router_path' => 'admin/content/taxonomy/edit/vocabulary/%',
  'link_title' => 'Edit vocabulary',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '94',
  'p4' => '104',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '105',
  'plid' => '11',
  'link_path' => 'node/add/page',
  'router_path' => 'node/add/page',
  'link_title' => 'Page',
  'options' => "a:1:{s:10:\"attributes\";a:1:{s:5:\"title\";s:296:\"A <em>page</em>, similar in form to a <em>story</em>, is a simple method for creating and displaying information that rarely changes, such as an \"About us\" section of a website. By default, a <em>page</em> entry does not allow visitor comments and is not featured on the site's initial home page.\";}}",
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '2',
  'customized' => '0',
  'p1' => '11',
  'p2' => '105',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '106',
  'plid' => '11',
  'link_path' => 'node/add/story',
  'router_path' => 'node/add/story',
  'link_title' => 'Story',
  'options' => "a:1:{s:10:\"attributes\";a:1:{s:5:\"title\";s:392:\"A <em>story</em>, similar in form to a <em>page</em>, is ideal for creating and displaying content that informs or engages website visitors. Press releases, site announcements, and informal blog-like entries may all be created with a <em>story</em> entry. By default, a <em>story</em> entry is automatically featured on the site's initial home page, and provides the ability to post comments.\";}}",
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '2',
  'customized' => '0',
  'p1' => '11',
  'p2' => '106',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '107',
  'plid' => '10',
  'link_path' => 'admin/content/node-type/page',
  'router_path' => 'admin/content/node-type/page',
  'link_title' => 'Page',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '107',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '108',
  'plid' => '10',
  'link_path' => 'admin/content/node-type/story',
  'router_path' => 'admin/content/node-type/story',
  'link_title' => 'Story',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '108',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '109',
  'plid' => '0',
  'link_path' => 'admin/content/node-type/page/delete',
  'router_path' => 'admin/content/node-type/page/delete',
  'link_title' => 'Delete',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '109',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '110',
  'plid' => '0',
  'link_path' => 'admin/content/node-type/story/delete',
  'router_path' => 'admin/content/node-type/story/delete',
  'link_title' => 'Delete',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '110',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '111',
  'plid' => '0',
  'link_path' => 'book',
  'router_path' => 'book',
  'link_title' => 'Books',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '111',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '112',
  'plid' => '10',
  'link_path' => 'admin/content/book',
  'router_path' => 'admin/content/book',
  'link_title' => 'Books',
  'options' => "a:1:{s:10:\"attributes\";a:1:{s:5:\"title\";s:33:\"Manage your site's book outlines.\";}}",
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '112',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '113',
  'plid' => '111',
  'link_path' => 'book/js/form',
  'router_path' => 'book/js/form',
  'link_title' => '',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '2',
  'customized' => '0',
  'p1' => '111',
  'p2' => '113',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '114',
  'plid' => '11',
  'link_path' => 'node/add/book',
  'router_path' => 'node/add/book',
  'link_title' => 'Book page',
  'options' => 'a:1:{s:10:"attributes";a:1:{s:5:"title";s:283:"A <em>book page</em> is a page of content, organized into a collection of related entries collectively known as a <em>book</em>. A <em>book page</em> automatically displays links to adjacent pages, providing a simple navigation system for organizing and reviewing structured content.";}}',
  'module' => 'system',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '2',
  'customized' => '0',
  'p1' => '11',
  'p2' => '114',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '115',
  'plid' => '15',
  'link_path' => 'admin/help/book',
  'router_path' => 'admin/help/book',
  'link_title' => 'book',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '15',
  'p3' => '115',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '116',
  'plid' => '111',
  'link_path' => 'book/export/%/%',
  'router_path' => 'book/export/%/%',
  'link_title' => '',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '2',
  'customized' => '0',
  'p1' => '111',
  'p2' => '116',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '117',
  'plid' => '10',
  'link_path' => 'admin/content/node-type/book',
  'router_path' => 'admin/content/node-type/book',
  'link_title' => 'Book page',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '117',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '118',
  'plid' => '112',
  'link_path' => 'admin/content/book/%',
  'router_path' => 'admin/content/book/%',
  'link_title' => 'Re-order book pages and change titles',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '4',
  'customized' => '0',
  'p1' => '2',
  'p2' => '10',
  'p3' => '112',
  'p4' => '118',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '119',
  'plid' => '0',
  'link_path' => 'node/%/outline/remove',
  'router_path' => 'node/%/outline/remove',
  'link_title' => 'Remove from outline',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '119',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'navigation',
  'mlid' => '120',
  'plid' => '0',
  'link_path' => 'admin/content/node-type/book/delete',
  'router_path' => 'admin/content/node-type/book/delete',
  'link_title' => 'Delete',
  'options' => 'a:0:{}',
  'module' => 'system',
  'hidden' => '-1',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '120',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'book-toc-1',
  'mlid' => '121',
  'plid' => '0',
  'link_path' => 'node/1',
  'router_path' => 'node/%',
  'link_title' => 'Birds',
  'options' => 'a:0:{}',
  'module' => 'book',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '1',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '121',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'book-toc-1',
  'mlid' => '122',
  'plid' => '121',
  'link_path' => 'node/2',
  'router_path' => 'node/%',
  'link_title' => 'Emu',
  'options' => 'a:0:{}',
  'module' => 'book',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '2',
  'customized' => '0',
  'p1' => '121',
  'p2' => '122',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'book-toc-1',
  'mlid' => '123',
  'plid' => '121',
  'link_path' => 'node/3',
  'router_path' => 'node/%',
  'link_title' => 'Parrots',
  'options' => 'a:0:{}',
  'module' => 'book',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '1',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '2',
  'customized' => '0',
  'p1' => '121',
  'p2' => '123',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'book-toc-1',
  'mlid' => '124',
  'plid' => '123',
  'link_path' => 'node/4',
  'router_path' => 'node/%',
  'link_title' => 'Kea',
  'options' => 'a:0:{}',
  'module' => 'book',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '121',
  'p2' => '123',
  'p3' => '124',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'book-toc-1',
  'mlid' => '125',
  'plid' => '123',
  'link_path' => 'node/5',
  'router_path' => 'node/%',
  'link_title' => 'Kakapo',
  'options' => 'a:0:{}',
  'module' => 'book',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '121',
  'p2' => '123',
  'p3' => '125',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'book-toc-6',
  'mlid' => '126',
  'plid' => '0',
  'link_path' => 'node/6',
  'router_path' => 'node/%',
  'link_title' => 'Tree',
  'options' => 'a:0:{}',
  'module' => 'book',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '1',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '1',
  'customized' => '0',
  'p1' => '126',
  'p2' => '0',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'book-toc-6',
  'mlid' => '127',
  'plid' => '126',
  'link_path' => 'node/7',
  'router_path' => 'node/%',
  'link_title' => 'Rimu',
  'options' => 'a:0:{}',
  'module' => 'book',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '2',
  'customized' => '0',
  'p1' => '126',
  'p2' => '127',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'book-toc-6',
  'mlid' => '128',
  'plid' => '126',
  'link_path' => 'node/8',
  'router_path' => 'node/%',
  'link_title' => 'Oaks',
  'options' => 'a:0:{}',
  'module' => 'book',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '1',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '2',
  'customized' => '0',
  'p1' => '126',
  'p2' => '128',
  'p3' => '0',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'book-toc-6',
  'mlid' => '129',
  'plid' => '128',
  'link_path' => 'node/9',
  'router_path' => 'node/%',
  'link_title' => 'Cork oak',
  'options' => 'a:0:{}',
  'module' => 'book',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '126',
  'p2' => '128',
  'p3' => '129',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->values(array(
  'menu_name' => 'book-toc-6',
  'mlid' => '130',
  'plid' => '128',
  'link_path' => 'node/10',
  'router_path' => 'node/%',
  'link_title' => 'White oak',
  'options' => 'a:0:{}',
  'module' => 'book',
  'hidden' => '0',
  'external' => '0',
  'has_children' => '0',
  'expanded' => '0',
  'weight' => '0',
  'depth' => '3',
  'customized' => '0',
  'p1' => '126',
  'p2' => '128',
  'p3' => '130',
  'p4' => '0',
  'p5' => '0',
  'p6' => '0',
  'p7' => '0',
  'p8' => '0',
  'p9' => '0',
  'updated' => '0',
))
->execute();
$connection->schema()->createTable('menu_router', array(
  'fields' => array(
    'path' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'load_functions' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'to_arg_functions' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'access_callback' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'access_arguments' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'normal',
    ),
    'page_callback' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'page_arguments' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'normal',
    ),
    'fit' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'number_parts' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
    'tab_parent' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'tab_root' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'title' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'title_callback' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'title_arguments' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'type' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'block_callback' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'description' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'position' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'weight' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'file' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'medium',
    ),
  ),
  'primary key' => array(
    'path',
  ),
  'indexes' => array(
    'fit' => array(
      'fit',
    ),
    'tab_parent' => array(
      'tab_parent',
    ),
    'tab_root_weight_title' => array(
      array(
        'tab_root',
        '64',
      ),
      'weight',
      'title',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('menu_router')
->fields(array(
  'path',
  'load_functions',
  'to_arg_functions',
  'access_callback',
  'access_arguments',
  'page_callback',
  'page_arguments',
  'fit',
  'number_parts',
  'tab_parent',
  'tab_root',
  'title',
  'title_callback',
  'title_arguments',
  'type',
  'block_callback',
  'description',
  'position',
  'weight',
  'file',
))
->values(array(
  'path' => 'admin',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'system_main_admin_page',
  'page_arguments' => 'a:0:{}',
  'fit' => '1',
  'number_parts' => '1',
  'tab_parent' => '',
  'tab_root' => 'admin',
  'title' => 'Administer',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '9',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'system_admin_menu_block_page',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'admin/build',
  'title' => 'Site building',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Control how your site looks and feels.',
  'position' => 'right',
  'weight' => '-10',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/block',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:17:"administer blocks";}',
  'page_callback' => 'block_admin_display',
  'page_arguments' => 'a:0:{}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/build/block',
  'title' => 'Blocks',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => "Configure what block content appears in your site's sidebars and other regions.",
  'position' => '',
  'weight' => '0',
  'file' => 'modules/block/block.admin.inc',
))
->values(array(
  'path' => 'admin/build/block/add',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:17:"administer blocks";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:20:"block_add_block_form";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/build/block',
  'tab_root' => 'admin/build/block',
  'title' => 'Add block',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/block/block.admin.inc',
))
->values(array(
  'path' => 'admin/build/block/configure',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:17:"administer blocks";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:21:"block_admin_configure";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/build/block/configure',
  'title' => 'Configure block',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/block/block.admin.inc',
))
->values(array(
  'path' => 'admin/build/block/delete',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:17:"administer blocks";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:16:"block_box_delete";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/build/block/delete',
  'title' => 'Delete block',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/block/block.admin.inc',
))
->values(array(
  'path' => 'admin/build/block/list',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:17:"administer blocks";}',
  'page_callback' => 'block_admin_display',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/build/block',
  'tab_root' => 'admin/build/block',
  'title' => 'List',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '-10',
  'file' => 'modules/block/block.admin.inc',
))
->values(array(
  'path' => 'admin/build/block/list/bluemarine',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '_block_themes_access',
  'access_arguments' => 'a:1:{i:0;O:8:"stdClass":12:{s:8:"filename";s:33:"themes/bluemarine/bluemarine.info";s:4:"name";s:10:"bluemarine";s:4:"type";s:5:"theme";s:5:"owner";s:45:"themes/engines/phptemplate/phptemplate.engine";s:6:"status";s:1:"0";s:8:"throttle";s:1:"0";s:9:"bootstrap";s:1:"0";s:14:"schema_version";s:2:"-1";s:6:"weight";s:1:"0";s:4:"info";a:11:{s:4:"name";s:10:"Bluemarine";s:11:"description";s:66:"Table-based multi-column theme with a marine and ash color scheme.";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:6:"engine";s:11:"phptemplate";s:7:"regions";a:5:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";s:7:"content";s:7:"Content";s:6:"header";s:6:"Header";s:6:"footer";s:6:"Footer";}s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:27:"themes/bluemarine/style.css";}}s:7:"scripts";a:1:{s:9:"script.js";s:27:"themes/bluemarine/script.js";}s:10:"screenshot";s:32:"themes/bluemarine/screenshot.png";s:3:"php";s:5:"4.3.5";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:27:"themes/bluemarine/style.css";}}s:6:"engine";s:11:"phptemplate";}}',
  'page_callback' => 'block_admin_display',
  'page_arguments' => 'a:1:{i:0;s:10:"bluemarine";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/block/list',
  'tab_root' => 'admin/build/block',
  'title' => 'Bluemarine',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/block/block.admin.inc',
))
->values(array(
  'path' => 'admin/build/block/list/chameleon',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '_block_themes_access',
  'access_arguments' => 'a:1:{i:0;O:8:"stdClass":11:{s:8:"filename";s:31:"themes/chameleon/chameleon.info";s:4:"name";s:9:"chameleon";s:4:"type";s:5:"theme";s:5:"owner";s:32:"themes/chameleon/chameleon.theme";s:6:"status";s:1:"0";s:8:"throttle";s:1:"0";s:9:"bootstrap";s:1:"0";s:14:"schema_version";s:2:"-1";s:6:"weight";s:1:"0";s:4:"info";a:10:{s:4:"name";s:9:"Chameleon";s:11:"description";s:42:"Minimalist tabled theme with light colors.";s:7:"regions";a:2:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";}s:8:"features";a:4:{i:0;s:4:"logo";i:1;s:7:"favicon";i:2;s:4:"name";i:3;s:6:"slogan";}s:11:"stylesheets";a:1:{s:3:"all";a:2:{s:9:"style.css";s:26:"themes/chameleon/style.css";s:10:"common.css";s:27:"themes/chameleon/common.css";}}s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:7:"scripts";a:1:{s:9:"script.js";s:26:"themes/chameleon/script.js";}s:10:"screenshot";s:31:"themes/chameleon/screenshot.png";s:3:"php";s:5:"4.3.5";}s:11:"stylesheets";a:1:{s:3:"all";a:2:{s:9:"style.css";s:26:"themes/chameleon/style.css";s:10:"common.css";s:27:"themes/chameleon/common.css";}}}}',
  'page_callback' => 'block_admin_display',
  'page_arguments' => 'a:1:{i:0;s:9:"chameleon";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/block/list',
  'tab_root' => 'admin/build/block',
  'title' => 'Chameleon',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/block/block.admin.inc',
))
->values(array(
  'path' => 'admin/build/block/list/garland',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '_block_themes_access',
  'access_arguments' => 'a:1:{i:0;O:8:"stdClass":12:{s:8:"filename";s:27:"themes/garland/garland.info";s:4:"name";s:7:"garland";s:4:"type";s:5:"theme";s:5:"owner";s:45:"themes/engines/phptemplate/phptemplate.engine";s:6:"status";s:1:"1";s:8:"throttle";s:1:"0";s:9:"bootstrap";s:1:"0";s:14:"schema_version";s:2:"-1";s:6:"weight";s:1:"0";s:4:"info";a:11:{s:4:"name";s:7:"Garland";s:11:"description";s:66:"Tableless, recolorable, multi-column, fluid width theme (default).";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:6:"engine";s:11:"phptemplate";s:11:"stylesheets";a:2:{s:3:"all";a:1:{s:9:"style.css";s:24:"themes/garland/style.css";}s:5:"print";a:1:{s:9:"print.css";s:24:"themes/garland/print.css";}}s:7:"regions";a:5:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";s:7:"content";s:7:"Content";s:6:"header";s:6:"Header";s:6:"footer";s:6:"Footer";}s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:7:"scripts";a:1:{s:9:"script.js";s:24:"themes/garland/script.js";}s:10:"screenshot";s:29:"themes/garland/screenshot.png";s:3:"php";s:5:"4.3.5";}s:11:"stylesheets";a:2:{s:3:"all";a:1:{s:9:"style.css";s:24:"themes/garland/style.css";}s:5:"print";a:1:{s:9:"print.css";s:24:"themes/garland/print.css";}}s:6:"engine";s:11:"phptemplate";}}',
  'page_callback' => 'block_admin_display',
  'page_arguments' => 'a:1:{i:0;s:7:"garland";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/block/list',
  'tab_root' => 'admin/build/block',
  'title' => 'Garland',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '-10',
  'file' => 'modules/block/block.admin.inc',
))
->values(array(
  'path' => 'admin/build/block/list/js',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:17:"administer blocks";}',
  'page_callback' => 'block_admin_display_js',
  'page_arguments' => 'a:0:{}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => '',
  'tab_root' => 'admin/build/block/list/js',
  'title' => 'JavaScript List Form',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/block/block.admin.inc',
))
->values(array(
  'path' => 'admin/build/block/list/marvin',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '_block_themes_access',
  'access_arguments' => 'a:1:{i:0;O:8:"stdClass":12:{s:8:"filename";s:35:"themes/chameleon/marvin/marvin.info";s:4:"name";s:6:"marvin";s:4:"type";s:5:"theme";s:5:"owner";s:0:"";s:6:"status";s:1:"0";s:8:"throttle";s:1:"0";s:9:"bootstrap";s:1:"0";s:14:"schema_version";s:2:"-1";s:6:"weight";s:1:"0";s:4:"info";a:11:{s:4:"name";s:6:"Marvin";s:11:"description";s:31:"Boxy tabled theme in all grays.";s:7:"regions";a:2:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";}s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:10:"base theme";s:9:"chameleon";s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:33:"themes/chameleon/marvin/style.css";}}s:7:"scripts";a:1:{s:9:"script.js";s:33:"themes/chameleon/marvin/script.js";}s:10:"screenshot";s:38:"themes/chameleon/marvin/screenshot.png";s:3:"php";s:5:"4.3.5";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:33:"themes/chameleon/marvin/style.css";}}s:10:"base_theme";s:9:"chameleon";}}',
  'page_callback' => 'block_admin_display',
  'page_arguments' => 'a:1:{i:0;s:6:"marvin";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/block/list',
  'tab_root' => 'admin/build/block',
  'title' => 'Marvin',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/block/block.admin.inc',
))
->values(array(
  'path' => 'admin/build/block/list/minnelli',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '_block_themes_access',
  'access_arguments' => 'a:1:{i:0;O:8:"stdClass":13:{s:8:"filename";s:37:"themes/garland/minnelli/minnelli.info";s:4:"name";s:8:"minnelli";s:4:"type";s:5:"theme";s:5:"owner";s:45:"themes/engines/phptemplate/phptemplate.engine";s:6:"status";s:1:"0";s:8:"throttle";s:1:"0";s:9:"bootstrap";s:1:"0";s:14:"schema_version";s:2:"-1";s:6:"weight";s:1:"0";s:4:"info";a:12:{s:4:"name";s:8:"Minnelli";s:11:"description";s:56:"Tableless, recolorable, multi-column, fixed width theme.";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:10:"base theme";s:7:"garland";s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:12:"minnelli.css";s:36:"themes/garland/minnelli/minnelli.css";}}s:7:"regions";a:5:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";s:7:"content";s:7:"Content";s:6:"header";s:6:"Header";s:6:"footer";s:6:"Footer";}s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:7:"scripts";a:1:{s:9:"script.js";s:33:"themes/garland/minnelli/script.js";}s:10:"screenshot";s:38:"themes/garland/minnelli/screenshot.png";s:3:"php";s:5:"4.3.5";s:6:"engine";s:11:"phptemplate";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:12:"minnelli.css";s:36:"themes/garland/minnelli/minnelli.css";}}s:6:"engine";s:11:"phptemplate";s:10:"base_theme";s:7:"garland";}}',
  'page_callback' => 'block_admin_display',
  'page_arguments' => 'a:1:{i:0;s:8:"minnelli";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/block/list',
  'tab_root' => 'admin/build/block',
  'title' => 'Minnelli',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/block/block.admin.inc',
))
->values(array(
  'path' => 'admin/build/block/list/pushbutton',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '_block_themes_access',
  'access_arguments' => 'a:1:{i:0;O:8:"stdClass":12:{s:8:"filename";s:33:"themes/pushbutton/pushbutton.info";s:4:"name";s:10:"pushbutton";s:4:"type";s:5:"theme";s:5:"owner";s:45:"themes/engines/phptemplate/phptemplate.engine";s:6:"status";s:1:"0";s:8:"throttle";s:1:"0";s:9:"bootstrap";s:1:"0";s:14:"schema_version";s:2:"-1";s:6:"weight";s:1:"0";s:4:"info";a:11:{s:4:"name";s:10:"Pushbutton";s:11:"description";s:52:"Tabled, multi-column theme in blue and orange tones.";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:6:"engine";s:11:"phptemplate";s:7:"regions";a:5:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";s:7:"content";s:7:"Content";s:6:"header";s:6:"Header";s:6:"footer";s:6:"Footer";}s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:27:"themes/pushbutton/style.css";}}s:7:"scripts";a:1:{s:9:"script.js";s:27:"themes/pushbutton/script.js";}s:10:"screenshot";s:32:"themes/pushbutton/screenshot.png";s:3:"php";s:5:"4.3.5";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:27:"themes/pushbutton/style.css";}}s:6:"engine";s:11:"phptemplate";}}',
  'page_callback' => 'block_admin_display',
  'page_arguments' => 'a:1:{i:0;s:10:"pushbutton";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/block/list',
  'tab_root' => 'admin/build/block',
  'title' => 'Pushbutton',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/block/block.admin.inc',
))
->values(array(
  'path' => 'admin/build/menu',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:15:"administer menu";}',
  'page_callback' => 'menu_overview_page',
  'page_arguments' => 'a:0:{}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/build/menu',
  'title' => 'Menus',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => "Control your site's navigation menu, primary links and secondary links, as well as rename and reorganize menu items.",
  'position' => '',
  'weight' => '0',
  'file' => 'modules/menu/menu.admin.inc',
))
->values(array(
  'path' => 'admin/build/menu-customize/%',
  'load_functions' => 'a:1:{i:3;s:9:"menu_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:15:"administer menu";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:18:"menu_overview_form";i:1;i:3;}',
  'fit' => '14',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/build/menu-customize/%',
  'title' => 'Customize menu',
  'title_callback' => 'menu_overview_title',
  'title_arguments' => 'a:1:{i:0;i:3;}',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/menu/menu.admin.inc',
))
->values(array(
  'path' => 'admin/build/menu-customize/%/add',
  'load_functions' => 'a:1:{i:3;s:9:"menu_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:15:"administer menu";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:4:{i:0;s:14:"menu_edit_item";i:1;s:3:"add";i:2;N;i:3;i:3;}',
  'fit' => '29',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/menu-customize/%',
  'tab_root' => 'admin/build/menu-customize/%',
  'title' => 'Add item',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/menu/menu.admin.inc',
))
->values(array(
  'path' => 'admin/build/menu-customize/%/delete',
  'load_functions' => 'a:1:{i:3;s:9:"menu_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:15:"administer menu";}',
  'page_callback' => 'menu_delete_menu_page',
  'page_arguments' => 'a:1:{i:0;i:3;}',
  'fit' => '29',
  'number_parts' => '5',
  'tab_parent' => '',
  'tab_root' => 'admin/build/menu-customize/%/delete',
  'title' => 'Delete menu',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/menu/menu.admin.inc',
))
->values(array(
  'path' => 'admin/build/menu-customize/%/edit',
  'load_functions' => 'a:1:{i:3;s:9:"menu_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:15:"administer menu";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:3:{i:0;s:14:"menu_edit_menu";i:1;s:4:"edit";i:2;i:3;}',
  'fit' => '29',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/menu-customize/%',
  'tab_root' => 'admin/build/menu-customize/%',
  'title' => 'Edit menu',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/menu/menu.admin.inc',
))
->values(array(
  'path' => 'admin/build/menu-customize/%/list',
  'load_functions' => 'a:1:{i:3;s:9:"menu_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:15:"administer menu";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:18:"menu_overview_form";i:1;i:3;}',
  'fit' => '29',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/menu-customize/%',
  'tab_root' => 'admin/build/menu-customize/%',
  'title' => 'List items',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '-10',
  'file' => 'modules/menu/menu.admin.inc',
))
->values(array(
  'path' => 'admin/build/menu/add',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:15:"administer menu";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:14:"menu_edit_menu";i:1;s:3:"add";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/build/menu',
  'tab_root' => 'admin/build/menu',
  'title' => 'Add menu',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/menu/menu.admin.inc',
))
->values(array(
  'path' => 'admin/build/menu/item/%/delete',
  'load_functions' => 'a:1:{i:4;s:14:"menu_link_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:15:"administer menu";}',
  'page_callback' => 'menu_item_delete_page',
  'page_arguments' => 'a:1:{i:0;i:4;}',
  'fit' => '61',
  'number_parts' => '6',
  'tab_parent' => '',
  'tab_root' => 'admin/build/menu/item/%/delete',
  'title' => 'Delete menu item',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/menu/menu.admin.inc',
))
->values(array(
  'path' => 'admin/build/menu/item/%/edit',
  'load_functions' => 'a:1:{i:4;s:14:"menu_link_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:15:"administer menu";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:4:{i:0;s:14:"menu_edit_item";i:1;s:4:"edit";i:2;i:4;i:3;N;}',
  'fit' => '61',
  'number_parts' => '6',
  'tab_parent' => '',
  'tab_root' => 'admin/build/menu/item/%/edit',
  'title' => 'Edit menu item',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/menu/menu.admin.inc',
))
->values(array(
  'path' => 'admin/build/menu/item/%/reset',
  'load_functions' => 'a:1:{i:4;s:14:"menu_link_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:15:"administer menu";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:23:"menu_reset_item_confirm";i:1;i:4;}',
  'fit' => '61',
  'number_parts' => '6',
  'tab_parent' => '',
  'tab_root' => 'admin/build/menu/item/%/reset',
  'title' => 'Reset menu item',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/menu/menu.admin.inc',
))
->values(array(
  'path' => 'admin/build/menu/list',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:15:"administer menu";}',
  'page_callback' => 'menu_overview_page',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/build/menu',
  'tab_root' => 'admin/build/menu',
  'title' => 'List menus',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '-10',
  'file' => 'modules/menu/menu.admin.inc',
))
->values(array(
  'path' => 'admin/build/menu/settings',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:15:"administer menu";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:14:"menu_configure";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/build/menu',
  'tab_root' => 'admin/build/menu',
  'title' => 'Settings',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '5',
  'file' => 'modules/menu/menu.admin.inc',
))
->values(array(
  'path' => 'admin/build/modules',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:14:"system_modules";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/build/modules',
  'title' => 'Modules',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Enable or disable add-on modules for your site.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/modules/list',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:14:"system_modules";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/build/modules',
  'tab_root' => 'admin/build/modules',
  'title' => 'List',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/modules/list/confirm',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:14:"system_modules";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => '',
  'tab_root' => 'admin/build/modules/list/confirm',
  'title' => 'List',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/modules/uninstall',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:24:"system_modules_uninstall";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/build/modules',
  'tab_root' => 'admin/build/modules',
  'title' => 'Uninstall',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/modules/uninstall/confirm',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:24:"system_modules_uninstall";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => '',
  'tab_root' => 'admin/build/modules/uninstall/confirm',
  'title' => 'Uninstall',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/themes',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:18:"system_themes_form";i:1;N;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/build/themes',
  'title' => 'Themes',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Change which theme your site uses or allows users to set.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/themes/select',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:18:"system_themes_form";i:1;N;}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/build/themes',
  'tab_root' => 'admin/build/themes',
  'title' => 'List',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => 'Select the default theme.',
  'position' => '',
  'weight' => '-1',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/themes/settings',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:21:"system_theme_settings";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/build/themes',
  'tab_root' => 'admin/build/themes',
  'title' => 'Configure',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/themes/settings/bluemarine',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '_system_themes_access',
  'access_arguments' => 'a:1:{i:0;O:8:"stdClass":12:{s:8:"filename";s:33:"themes/bluemarine/bluemarine.info";s:4:"name";s:10:"bluemarine";s:4:"type";s:5:"theme";s:5:"owner";s:45:"themes/engines/phptemplate/phptemplate.engine";s:6:"status";s:1:"0";s:8:"throttle";s:1:"0";s:9:"bootstrap";s:1:"0";s:14:"schema_version";s:2:"-1";s:6:"weight";s:1:"0";s:4:"info";a:11:{s:4:"name";s:10:"Bluemarine";s:11:"description";s:66:"Table-based multi-column theme with a marine and ash color scheme.";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:6:"engine";s:11:"phptemplate";s:7:"regions";a:5:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";s:7:"content";s:7:"Content";s:6:"header";s:6:"Header";s:6:"footer";s:6:"Footer";}s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:27:"themes/bluemarine/style.css";}}s:7:"scripts";a:1:{s:9:"script.js";s:27:"themes/bluemarine/script.js";}s:10:"screenshot";s:32:"themes/bluemarine/screenshot.png";s:3:"php";s:5:"4.3.5";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:27:"themes/bluemarine/style.css";}}s:6:"engine";s:11:"phptemplate";}}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:21:"system_theme_settings";i:1;s:10:"bluemarine";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/themes/settings',
  'tab_root' => 'admin/build/themes',
  'title' => 'Bluemarine',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/themes/settings/chameleon',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '_system_themes_access',
  'access_arguments' => 'a:1:{i:0;O:8:"stdClass":11:{s:8:"filename";s:31:"themes/chameleon/chameleon.info";s:4:"name";s:9:"chameleon";s:4:"type";s:5:"theme";s:5:"owner";s:32:"themes/chameleon/chameleon.theme";s:6:"status";s:1:"0";s:8:"throttle";s:1:"0";s:9:"bootstrap";s:1:"0";s:14:"schema_version";s:2:"-1";s:6:"weight";s:1:"0";s:4:"info";a:10:{s:4:"name";s:9:"Chameleon";s:11:"description";s:42:"Minimalist tabled theme with light colors.";s:7:"regions";a:2:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";}s:8:"features";a:4:{i:0;s:4:"logo";i:1;s:7:"favicon";i:2;s:4:"name";i:3;s:6:"slogan";}s:11:"stylesheets";a:1:{s:3:"all";a:2:{s:9:"style.css";s:26:"themes/chameleon/style.css";s:10:"common.css";s:27:"themes/chameleon/common.css";}}s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:7:"scripts";a:1:{s:9:"script.js";s:26:"themes/chameleon/script.js";}s:10:"screenshot";s:31:"themes/chameleon/screenshot.png";s:3:"php";s:5:"4.3.5";}s:11:"stylesheets";a:1:{s:3:"all";a:2:{s:9:"style.css";s:26:"themes/chameleon/style.css";s:10:"common.css";s:27:"themes/chameleon/common.css";}}}}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:21:"system_theme_settings";i:1;s:9:"chameleon";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/themes/settings',
  'tab_root' => 'admin/build/themes',
  'title' => 'Chameleon',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/themes/settings/garland',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '_system_themes_access',
  'access_arguments' => 'a:1:{i:0;O:8:"stdClass":12:{s:8:"filename";s:27:"themes/garland/garland.info";s:4:"name";s:7:"garland";s:4:"type";s:5:"theme";s:5:"owner";s:45:"themes/engines/phptemplate/phptemplate.engine";s:6:"status";s:1:"1";s:8:"throttle";s:1:"0";s:9:"bootstrap";s:1:"0";s:14:"schema_version";s:2:"-1";s:6:"weight";s:1:"0";s:4:"info";a:11:{s:4:"name";s:7:"Garland";s:11:"description";s:66:"Tableless, recolorable, multi-column, fluid width theme (default).";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:6:"engine";s:11:"phptemplate";s:11:"stylesheets";a:2:{s:3:"all";a:1:{s:9:"style.css";s:24:"themes/garland/style.css";}s:5:"print";a:1:{s:9:"print.css";s:24:"themes/garland/print.css";}}s:7:"regions";a:5:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";s:7:"content";s:7:"Content";s:6:"header";s:6:"Header";s:6:"footer";s:6:"Footer";}s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:7:"scripts";a:1:{s:9:"script.js";s:24:"themes/garland/script.js";}s:10:"screenshot";s:29:"themes/garland/screenshot.png";s:3:"php";s:5:"4.3.5";}s:11:"stylesheets";a:2:{s:3:"all";a:1:{s:9:"style.css";s:24:"themes/garland/style.css";}s:5:"print";a:1:{s:9:"print.css";s:24:"themes/garland/print.css";}}s:6:"engine";s:11:"phptemplate";}}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:21:"system_theme_settings";i:1;s:7:"garland";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/themes/settings',
  'tab_root' => 'admin/build/themes',
  'title' => 'Garland',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/themes/settings/global',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:21:"system_theme_settings";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/themes/settings',
  'tab_root' => 'admin/build/themes',
  'title' => 'Global settings',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '-1',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/themes/settings/marvin',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '_system_themes_access',
  'access_arguments' => 'a:1:{i:0;O:8:"stdClass":12:{s:8:"filename";s:35:"themes/chameleon/marvin/marvin.info";s:4:"name";s:6:"marvin";s:4:"type";s:5:"theme";s:5:"owner";s:0:"";s:6:"status";s:1:"0";s:8:"throttle";s:1:"0";s:9:"bootstrap";s:1:"0";s:14:"schema_version";s:2:"-1";s:6:"weight";s:1:"0";s:4:"info";a:11:{s:4:"name";s:6:"Marvin";s:11:"description";s:31:"Boxy tabled theme in all grays.";s:7:"regions";a:2:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";}s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:10:"base theme";s:9:"chameleon";s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:33:"themes/chameleon/marvin/style.css";}}s:7:"scripts";a:1:{s:9:"script.js";s:33:"themes/chameleon/marvin/script.js";}s:10:"screenshot";s:38:"themes/chameleon/marvin/screenshot.png";s:3:"php";s:5:"4.3.5";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:33:"themes/chameleon/marvin/style.css";}}s:10:"base_theme";s:9:"chameleon";}}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:21:"system_theme_settings";i:1;s:6:"marvin";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/themes/settings',
  'tab_root' => 'admin/build/themes',
  'title' => 'Marvin',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/themes/settings/minnelli',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '_system_themes_access',
  'access_arguments' => 'a:1:{i:0;O:8:"stdClass":13:{s:8:"filename";s:37:"themes/garland/minnelli/minnelli.info";s:4:"name";s:8:"minnelli";s:4:"type";s:5:"theme";s:5:"owner";s:45:"themes/engines/phptemplate/phptemplate.engine";s:6:"status";s:1:"0";s:8:"throttle";s:1:"0";s:9:"bootstrap";s:1:"0";s:14:"schema_version";s:2:"-1";s:6:"weight";s:1:"0";s:4:"info";a:12:{s:4:"name";s:8:"Minnelli";s:11:"description";s:56:"Tableless, recolorable, multi-column, fixed width theme.";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:10:"base theme";s:7:"garland";s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:12:"minnelli.css";s:36:"themes/garland/minnelli/minnelli.css";}}s:7:"regions";a:5:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";s:7:"content";s:7:"Content";s:6:"header";s:6:"Header";s:6:"footer";s:6:"Footer";}s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:7:"scripts";a:1:{s:9:"script.js";s:33:"themes/garland/minnelli/script.js";}s:10:"screenshot";s:38:"themes/garland/minnelli/screenshot.png";s:3:"php";s:5:"4.3.5";s:6:"engine";s:11:"phptemplate";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:12:"minnelli.css";s:36:"themes/garland/minnelli/minnelli.css";}}s:6:"engine";s:11:"phptemplate";s:10:"base_theme";s:7:"garland";}}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:21:"system_theme_settings";i:1;s:8:"minnelli";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/themes/settings',
  'tab_root' => 'admin/build/themes',
  'title' => 'Minnelli',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/build/themes/settings/pushbutton',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '_system_themes_access',
  'access_arguments' => 'a:1:{i:0;O:8:"stdClass":12:{s:8:"filename";s:33:"themes/pushbutton/pushbutton.info";s:4:"name";s:10:"pushbutton";s:4:"type";s:5:"theme";s:5:"owner";s:45:"themes/engines/phptemplate/phptemplate.engine";s:6:"status";s:1:"0";s:8:"throttle";s:1:"0";s:9:"bootstrap";s:1:"0";s:14:"schema_version";s:2:"-1";s:6:"weight";s:1:"0";s:4:"info";a:11:{s:4:"name";s:10:"Pushbutton";s:11:"description";s:52:"Tabled, multi-column theme in blue and orange tones.";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:6:"engine";s:11:"phptemplate";s:7:"regions";a:5:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";s:7:"content";s:7:"Content";s:6:"header";s:6:"Header";s:6:"footer";s:6:"Footer";}s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:27:"themes/pushbutton/style.css";}}s:7:"scripts";a:1:{s:9:"script.js";s:27:"themes/pushbutton/script.js";}s:10:"screenshot";s:32:"themes/pushbutton/screenshot.png";s:3:"php";s:5:"4.3.5";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:27:"themes/pushbutton/style.css";}}s:6:"engine";s:11:"phptemplate";}}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:21:"system_theme_settings";i:1;s:10:"pushbutton";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/build/themes/settings',
  'tab_root' => 'admin/build/themes',
  'title' => 'Pushbutton',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/by-module',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'system_admin_by_module',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => 'admin',
  'tab_root' => 'admin',
  'title' => 'By module',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '2',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/by-task',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'system_main_admin_page',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => 'admin',
  'tab_root' => 'admin',
  'title' => 'By task',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/compact',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'system_admin_compact_page',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'admin/compact',
  'title' => 'Compact mode',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/content',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'system_admin_menu_block_page',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'admin/content',
  'title' => 'Content management',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => "Manage your site's content.",
  'position' => 'left',
  'weight' => '-10',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/content/book',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:24:"administer book outlines";}',
  'page_callback' => 'book_admin_overview',
  'page_arguments' => 'a:0:{}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/content/book',
  'title' => 'Books',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => "Manage your site's book outlines.",
  'position' => '',
  'weight' => '0',
  'file' => 'modules/book/book.admin.inc',
))
->values(array(
  'path' => 'admin/content/book/%',
  'load_functions' => 'a:1:{i:3;s:9:"node_load";}',
  'to_arg_functions' => '',
  'access_callback' => '_book_outline_access',
  'access_arguments' => 'a:1:{i:0;i:3;}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:15:"book_admin_edit";i:1;i:3;}',
  'fit' => '14',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/content/book/%',
  'title' => 'Re-order book pages and change titles',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/book/book.admin.inc',
))
->values(array(
  'path' => 'admin/content/book/list',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:24:"administer book outlines";}',
  'page_callback' => 'book_admin_overview',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/content/book',
  'tab_root' => 'admin/content/book',
  'title' => 'List',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/book/book.admin.inc',
))
->values(array(
  'path' => 'admin/content/book/settings',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:19:"book_admin_settings";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/content/book',
  'tab_root' => 'admin/content/book',
  'title' => 'Settings',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '8',
  'file' => 'modules/book/book.admin.inc',
))
->values(array(
  'path' => 'admin/content/comment',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"administer comments";}',
  'page_callback' => 'comment_admin',
  'page_arguments' => 'a:0:{}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/content/comment',
  'title' => 'Comments',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'List and edit site comments and the comment moderation queue.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/comment/comment.admin.inc',
))
->values(array(
  'path' => 'admin/content/comment/approval',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"administer comments";}',
  'page_callback' => 'comment_admin',
  'page_arguments' => 'a:1:{i:0;s:8:"approval";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/content/comment',
  'tab_root' => 'admin/content/comment',
  'title' => 'Approval queue',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/comment/comment.admin.inc',
))
->values(array(
  'path' => 'admin/content/comment/new',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"administer comments";}',
  'page_callback' => 'comment_admin',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/content/comment',
  'tab_root' => 'admin/content/comment',
  'title' => 'Published comments',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '-10',
  'file' => 'modules/comment/comment.admin.inc',
))
->values(array(
  'path' => 'admin/content/node',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:16:"administer nodes";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:18:"node_admin_content";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/content/node',
  'title' => 'Content',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => "View, edit, and delete your site's content.",
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/node.admin.inc',
))
->values(array(
  'path' => 'admin/content/node-settings',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:16:"administer nodes";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:14:"node_configure";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/content/node-settings',
  'title' => 'Post settings',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Control posting behavior, such as teaser length, requiring previews before posting, and the number of posts on the front page.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/node.admin.inc',
))
->values(array(
  'path' => 'admin/content/node-settings/rebuild',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:30:"node_configure_rebuild_confirm";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/content/node-settings/rebuild',
  'title' => 'Rebuild permissions',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/node.admin.inc',
))
->values(array(
  'path' => 'admin/content/node-type/book',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:24:"administer content types";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:14:"node_type_form";i:1;O:8:"stdClass":14:{s:4:"type";s:4:"book";s:4:"name";s:9:"Book page";s:6:"module";s:4:"node";s:11:"description";s:283:"A <em>book page</em> is a page of content, organized into a collection of related entries collectively known as a <em>book</em>. A <em>book page</em> automatically displays links to adjacent pages, providing a simple navigation system for organizing and reviewing structured content.";s:4:"help";s:0:"";s:9:"has_title";s:1:"1";s:11:"title_label";s:5:"Title";s:8:"has_body";s:1:"1";s:10:"body_label";s:4:"Body";s:14:"min_word_count";s:1:"0";s:6:"custom";s:1:"1";s:8:"modified";s:1:"1";s:6:"locked";s:1:"0";s:9:"orig_type";s:4:"book";}}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/content/node-type/book',
  'title' => 'Book page',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/content_types.inc',
))
->values(array(
  'path' => 'admin/content/node-type/book/delete',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:24:"administer content types";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:24:"node_type_delete_confirm";i:1;O:8:"stdClass":14:{s:4:"type";s:4:"book";s:4:"name";s:9:"Book page";s:6:"module";s:4:"node";s:11:"description";s:283:"A <em>book page</em> is a page of content, organized into a collection of related entries collectively known as a <em>book</em>. A <em>book page</em> automatically displays links to adjacent pages, providing a simple navigation system for organizing and reviewing structured content.";s:4:"help";s:0:"";s:9:"has_title";s:1:"1";s:11:"title_label";s:5:"Title";s:8:"has_body";s:1:"1";s:10:"body_label";s:4:"Body";s:14:"min_word_count";s:1:"0";s:6:"custom";s:1:"1";s:8:"modified";s:1:"1";s:6:"locked";s:1:"0";s:9:"orig_type";s:4:"book";}}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => '',
  'tab_root' => 'admin/content/node-type/book/delete',
  'title' => 'Delete',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/content_types.inc',
))
->values(array(
  'path' => 'admin/content/node-type/book/edit',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:24:"administer content types";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:14:"node_type_form";i:1;O:8:"stdClass":14:{s:4:"type";s:4:"book";s:4:"name";s:9:"Book page";s:6:"module";s:4:"node";s:11:"description";s:283:"A <em>book page</em> is a page of content, organized into a collection of related entries collectively known as a <em>book</em>. A <em>book page</em> automatically displays links to adjacent pages, providing a simple navigation system for organizing and reviewing structured content.";s:4:"help";s:0:"";s:9:"has_title";s:1:"1";s:11:"title_label";s:5:"Title";s:8:"has_body";s:1:"1";s:10:"body_label";s:4:"Body";s:14:"min_word_count";s:1:"0";s:6:"custom";s:1:"1";s:8:"modified";s:1:"1";s:6:"locked";s:1:"0";s:9:"orig_type";s:4:"book";}}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/content/node-type/book',
  'tab_root' => 'admin/content/node-type/book',
  'title' => 'Edit',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/content_types.inc',
))
->values(array(
  'path' => 'admin/content/node-type/page',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:24:"administer content types";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => "a:2:{i:0;s:14:\"node_type_form\";i:1;O:8:\"stdClass\":14:{s:4:\"type\";s:4:\"page\";s:4:\"name\";s:4:\"Page\";s:6:\"module\";s:4:\"node\";s:11:\"description\";s:296:\"A <em>page</em>, similar in form to a <em>story</em>, is a simple method for creating and displaying information that rarely changes, such as an \"About us\" section of a website. By default, a <em>page</em> entry does not allow visitor comments and is not featured on the site's initial home page.\";s:4:\"help\";s:0:\"\";s:9:\"has_title\";s:1:\"1\";s:11:\"title_label\";s:5:\"Title\";s:8:\"has_body\";s:1:\"1\";s:10:\"body_label\";s:4:\"Body\";s:14:\"min_word_count\";s:1:\"0\";s:6:\"custom\";s:1:\"1\";s:8:\"modified\";s:1:\"1\";s:6:\"locked\";s:1:\"0\";s:9:\"orig_type\";s:4:\"page\";}}",
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/content/node-type/page',
  'title' => 'Page',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/content_types.inc',
))
->values(array(
  'path' => 'admin/content/node-type/page/delete',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:24:"administer content types";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => "a:2:{i:0;s:24:\"node_type_delete_confirm\";i:1;O:8:\"stdClass\":14:{s:4:\"type\";s:4:\"page\";s:4:\"name\";s:4:\"Page\";s:6:\"module\";s:4:\"node\";s:11:\"description\";s:296:\"A <em>page</em>, similar in form to a <em>story</em>, is a simple method for creating and displaying information that rarely changes, such as an \"About us\" section of a website. By default, a <em>page</em> entry does not allow visitor comments and is not featured on the site's initial home page.\";s:4:\"help\";s:0:\"\";s:9:\"has_title\";s:1:\"1\";s:11:\"title_label\";s:5:\"Title\";s:8:\"has_body\";s:1:\"1\";s:10:\"body_label\";s:4:\"Body\";s:14:\"min_word_count\";s:1:\"0\";s:6:\"custom\";s:1:\"1\";s:8:\"modified\";s:1:\"1\";s:6:\"locked\";s:1:\"0\";s:9:\"orig_type\";s:4:\"page\";}}",
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => '',
  'tab_root' => 'admin/content/node-type/page/delete',
  'title' => 'Delete',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/content_types.inc',
))
->values(array(
  'path' => 'admin/content/node-type/page/edit',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:24:"administer content types";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => "a:2:{i:0;s:14:\"node_type_form\";i:1;O:8:\"stdClass\":14:{s:4:\"type\";s:4:\"page\";s:4:\"name\";s:4:\"Page\";s:6:\"module\";s:4:\"node\";s:11:\"description\";s:296:\"A <em>page</em>, similar in form to a <em>story</em>, is a simple method for creating and displaying information that rarely changes, such as an \"About us\" section of a website. By default, a <em>page</em> entry does not allow visitor comments and is not featured on the site's initial home page.\";s:4:\"help\";s:0:\"\";s:9:\"has_title\";s:1:\"1\";s:11:\"title_label\";s:5:\"Title\";s:8:\"has_body\";s:1:\"1\";s:10:\"body_label\";s:4:\"Body\";s:14:\"min_word_count\";s:1:\"0\";s:6:\"custom\";s:1:\"1\";s:8:\"modified\";s:1:\"1\";s:6:\"locked\";s:1:\"0\";s:9:\"orig_type\";s:4:\"page\";}}",
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/content/node-type/page',
  'tab_root' => 'admin/content/node-type/page',
  'title' => 'Edit',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/content_types.inc',
))
->values(array(
  'path' => 'admin/content/node-type/story',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:24:"administer content types";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => "a:2:{i:0;s:14:\"node_type_form\";i:1;O:8:\"stdClass\":14:{s:4:\"type\";s:5:\"story\";s:4:\"name\";s:5:\"Story\";s:6:\"module\";s:4:\"node\";s:11:\"description\";s:392:\"A <em>story</em>, similar in form to a <em>page</em>, is ideal for creating and displaying content that informs or engages website visitors. Press releases, site announcements, and informal blog-like entries may all be created with a <em>story</em> entry. By default, a <em>story</em> entry is automatically featured on the site's initial home page, and provides the ability to post comments.\";s:4:\"help\";s:0:\"\";s:9:\"has_title\";s:1:\"1\";s:11:\"title_label\";s:5:\"Title\";s:8:\"has_body\";s:1:\"1\";s:10:\"body_label\";s:4:\"Body\";s:14:\"min_word_count\";s:1:\"0\";s:6:\"custom\";s:1:\"1\";s:8:\"modified\";s:1:\"1\";s:6:\"locked\";s:1:\"0\";s:9:\"orig_type\";s:5:\"story\";}}",
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/content/node-type/story',
  'title' => 'Story',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/content_types.inc',
))
->values(array(
  'path' => 'admin/content/node-type/story/delete',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:24:"administer content types";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => "a:2:{i:0;s:24:\"node_type_delete_confirm\";i:1;O:8:\"stdClass\":14:{s:4:\"type\";s:5:\"story\";s:4:\"name\";s:5:\"Story\";s:6:\"module\";s:4:\"node\";s:11:\"description\";s:392:\"A <em>story</em>, similar in form to a <em>page</em>, is ideal for creating and displaying content that informs or engages website visitors. Press releases, site announcements, and informal blog-like entries may all be created with a <em>story</em> entry. By default, a <em>story</em> entry is automatically featured on the site's initial home page, and provides the ability to post comments.\";s:4:\"help\";s:0:\"\";s:9:\"has_title\";s:1:\"1\";s:11:\"title_label\";s:5:\"Title\";s:8:\"has_body\";s:1:\"1\";s:10:\"body_label\";s:4:\"Body\";s:14:\"min_word_count\";s:1:\"0\";s:6:\"custom\";s:1:\"1\";s:8:\"modified\";s:1:\"1\";s:6:\"locked\";s:1:\"0\";s:9:\"orig_type\";s:5:\"story\";}}",
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => '',
  'tab_root' => 'admin/content/node-type/story/delete',
  'title' => 'Delete',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/content_types.inc',
))
->values(array(
  'path' => 'admin/content/node-type/story/edit',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:24:"administer content types";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => "a:2:{i:0;s:14:\"node_type_form\";i:1;O:8:\"stdClass\":14:{s:4:\"type\";s:5:\"story\";s:4:\"name\";s:5:\"Story\";s:6:\"module\";s:4:\"node\";s:11:\"description\";s:392:\"A <em>story</em>, similar in form to a <em>page</em>, is ideal for creating and displaying content that informs or engages website visitors. Press releases, site announcements, and informal blog-like entries may all be created with a <em>story</em> entry. By default, a <em>story</em> entry is automatically featured on the site's initial home page, and provides the ability to post comments.\";s:4:\"help\";s:0:\"\";s:9:\"has_title\";s:1:\"1\";s:11:\"title_label\";s:5:\"Title\";s:8:\"has_body\";s:1:\"1\";s:10:\"body_label\";s:4:\"Body\";s:14:\"min_word_count\";s:1:\"0\";s:6:\"custom\";s:1:\"1\";s:8:\"modified\";s:1:\"1\";s:6:\"locked\";s:1:\"0\";s:9:\"orig_type\";s:5:\"story\";}}",
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/content/node-type/story',
  'tab_root' => 'admin/content/node-type/story',
  'title' => 'Edit',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/content_types.inc',
))
->values(array(
  'path' => 'admin/content/node/overview',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:16:"administer nodes";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:18:"node_admin_content";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/content/node',
  'tab_root' => 'admin/content/node',
  'title' => 'List',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '-10',
  'file' => 'modules/node/node.admin.inc',
))
->values(array(
  'path' => 'admin/content/rss-publishing',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:25:"system_rss_feeds_settings";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/content/rss-publishing',
  'title' => 'RSS publishing',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Configure the number of items per feed and whether feeds should be titles/teasers/full-text.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/content/taxonomy',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"administer taxonomy";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:30:"taxonomy_overview_vocabularies";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/content/taxonomy',
  'title' => 'Taxonomy',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Manage tagging, categorization, and classification of your content.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/taxonomy/taxonomy.admin.inc',
))
->values(array(
  'path' => 'admin/content/taxonomy/%',
  'load_functions' => 'a:1:{i:3;s:24:"taxonomy_vocabulary_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"administer taxonomy";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:23:"taxonomy_overview_terms";i:1;i:3;}',
  'fit' => '14',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/content/taxonomy/%',
  'title' => 'List terms',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/taxonomy/taxonomy.admin.inc',
))
->values(array(
  'path' => 'admin/content/taxonomy/%/add/term',
  'load_functions' => 'a:1:{i:3;s:24:"taxonomy_vocabulary_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"administer taxonomy";}',
  'page_callback' => 'taxonomy_add_term_page',
  'page_arguments' => 'a:1:{i:0;i:3;}',
  'fit' => '59',
  'number_parts' => '6',
  'tab_parent' => 'admin/content/taxonomy/%',
  'tab_root' => 'admin/content/taxonomy/%',
  'title' => 'Add term',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/taxonomy/taxonomy.admin.inc',
))
->values(array(
  'path' => 'admin/content/taxonomy/%/list',
  'load_functions' => 'a:1:{i:3;s:24:"taxonomy_vocabulary_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"administer taxonomy";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:23:"taxonomy_overview_terms";i:1;i:3;}',
  'fit' => '29',
  'number_parts' => '5',
  'tab_parent' => 'admin/content/taxonomy/%',
  'tab_root' => 'admin/content/taxonomy/%',
  'title' => 'List',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '-10',
  'file' => 'modules/taxonomy/taxonomy.admin.inc',
))
->values(array(
  'path' => 'admin/content/taxonomy/add/vocabulary',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"administer taxonomy";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:24:"taxonomy_form_vocabulary";}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => 'admin/content/taxonomy',
  'tab_root' => 'admin/content/taxonomy',
  'title' => 'Add vocabulary',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/taxonomy/taxonomy.admin.inc',
))
->values(array(
  'path' => 'admin/content/taxonomy/edit/term',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"administer taxonomy";}',
  'page_callback' => 'taxonomy_admin_term_edit',
  'page_arguments' => 'a:0:{}',
  'fit' => '31',
  'number_parts' => '5',
  'tab_parent' => '',
  'tab_root' => 'admin/content/taxonomy/edit/term',
  'title' => 'Edit term',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/taxonomy/taxonomy.admin.inc',
))
->values(array(
  'path' => 'admin/content/taxonomy/edit/vocabulary/%',
  'load_functions' => 'a:1:{i:5;s:24:"taxonomy_vocabulary_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"administer taxonomy";}',
  'page_callback' => 'taxonomy_admin_vocabulary_edit',
  'page_arguments' => 'a:1:{i:0;i:5;}',
  'fit' => '62',
  'number_parts' => '6',
  'tab_parent' => '',
  'tab_root' => 'admin/content/taxonomy/edit/vocabulary/%',
  'title' => 'Edit vocabulary',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/taxonomy/taxonomy.admin.inc',
))
->values(array(
  'path' => 'admin/content/taxonomy/list',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"administer taxonomy";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:30:"taxonomy_overview_vocabularies";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/content/taxonomy',
  'tab_root' => 'admin/content/taxonomy',
  'title' => 'List',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '-10',
  'file' => 'modules/taxonomy/taxonomy.admin.inc',
))
->values(array(
  'path' => 'admin/content/types',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:24:"administer content types";}',
  'page_callback' => 'node_overview_types',
  'page_arguments' => 'a:0:{}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/content/types',
  'title' => 'Content types',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Manage posts by content type, including default status, front page promotion, etc.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/content_types.inc',
))
->values(array(
  'path' => 'admin/content/types/add',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:24:"administer content types";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:14:"node_type_form";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/content/types',
  'tab_root' => 'admin/content/types',
  'title' => 'Add content type',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/content_types.inc',
))
->values(array(
  'path' => 'admin/content/types/list',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:24:"administer content types";}',
  'page_callback' => 'node_overview_types',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/content/types',
  'tab_root' => 'admin/content/types',
  'title' => 'List',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '-10',
  'file' => 'modules/node/content_types.inc',
))
->values(array(
  'path' => 'admin/help',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'help_main',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'admin/help',
  'title' => 'Help',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '9',
  'file' => 'modules/help/help.admin.inc',
))
->values(array(
  'path' => 'admin/help/block',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'help_page',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/help/block',
  'title' => 'block',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/help/help.admin.inc',
))
->values(array(
  'path' => 'admin/help/book',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'help_page',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/help/book',
  'title' => 'book',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/help/help.admin.inc',
))
->values(array(
  'path' => 'admin/help/color',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'help_page',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/help/color',
  'title' => 'color',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/help/help.admin.inc',
))
->values(array(
  'path' => 'admin/help/comment',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'help_page',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/help/comment',
  'title' => 'comment',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/help/help.admin.inc',
))
->values(array(
  'path' => 'admin/help/dblog',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'help_page',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/help/dblog',
  'title' => 'dblog',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/help/help.admin.inc',
))
->values(array(
  'path' => 'admin/help/filter',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'help_page',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/help/filter',
  'title' => 'filter',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/help/help.admin.inc',
))
->values(array(
  'path' => 'admin/help/help',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'help_page',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/help/help',
  'title' => 'help',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/help/help.admin.inc',
))
->values(array(
  'path' => 'admin/help/menu',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'help_page',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/help/menu',
  'title' => 'menu',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/help/help.admin.inc',
))
->values(array(
  'path' => 'admin/help/node',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'help_page',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/help/node',
  'title' => 'node',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/help/help.admin.inc',
))
->values(array(
  'path' => 'admin/help/system',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'help_page',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/help/system',
  'title' => 'system',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/help/help.admin.inc',
))
->values(array(
  'path' => 'admin/help/taxonomy',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'help_page',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/help/taxonomy',
  'title' => 'taxonomy',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/help/help.admin.inc',
))
->values(array(
  'path' => 'admin/help/user',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'help_page',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/help/user',
  'title' => 'user',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/help/help.admin.inc',
))
->values(array(
  'path' => 'admin/reports',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"access site reports";}',
  'page_callback' => 'system_admin_menu_block_page',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'admin/reports',
  'title' => 'Reports',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'View reports from system logs and other status information.',
  'position' => 'left',
  'weight' => '5',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/reports/access-denied',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"access site reports";}',
  'page_callback' => 'dblog_top',
  'page_arguments' => 'a:1:{i:0;s:13:"access denied";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/reports/access-denied',
  'title' => "Top 'access denied' errors",
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => "View 'access denied' errors (403s).",
  'position' => '',
  'weight' => '0',
  'file' => 'modules/dblog/dblog.admin.inc',
))
->values(array(
  'path' => 'admin/reports/dblog',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"access site reports";}',
  'page_callback' => 'dblog_overview',
  'page_arguments' => 'a:0:{}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/reports/dblog',
  'title' => 'Recent log entries',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'View events that have recently been logged.',
  'position' => '',
  'weight' => '-1',
  'file' => 'modules/dblog/dblog.admin.inc',
))
->values(array(
  'path' => 'admin/reports/event/%',
  'load_functions' => 'a:1:{i:3;N;}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"access site reports";}',
  'page_callback' => 'dblog_event',
  'page_arguments' => 'a:1:{i:0;i:3;}',
  'fit' => '14',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/reports/event/%',
  'title' => 'Details',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/dblog/dblog.admin.inc',
))
->values(array(
  'path' => 'admin/reports/page-not-found',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"access site reports";}',
  'page_callback' => 'dblog_top',
  'page_arguments' => 'a:1:{i:0;s:14:"page not found";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/reports/page-not-found',
  'title' => "Top 'page not found' errors",
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => "View 'page not found' errors (404s).",
  'position' => '',
  'weight' => '0',
  'file' => 'modules/dblog/dblog.admin.inc',
))
->values(array(
  'path' => 'admin/reports/status',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'system_status',
  'page_arguments' => 'a:0:{}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/reports/status',
  'title' => 'Status report',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => "Get a status report about your site's operation and any detected problems.",
  'position' => '',
  'weight' => '10',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/reports/status/php',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'system_php',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/reports/status/php',
  'title' => 'PHP',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/reports/status/run-cron',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'system_run_cron',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/reports/status/run-cron',
  'title' => 'Run cron',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/reports/status/sql',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'system_sql',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/reports/status/sql',
  'title' => 'SQL',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/settings',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'system_settings_overview',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'admin/settings',
  'title' => 'Site configuration',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Adjust basic site configuration options.',
  'position' => 'right',
  'weight' => '-5',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/settings/actions',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:18:"administer actions";}',
  'page_callback' => 'system_actions_manage',
  'page_arguments' => 'a:0:{}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/actions',
  'title' => 'Actions',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Manage the actions defined for your site.',
  'position' => '',
  'weight' => '0',
  'file' => '',
))
->values(array(
  'path' => 'admin/settings/actions/configure',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:18:"administer actions";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:24:"system_actions_configure";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/actions/configure',
  'title' => 'Configure an advanced action',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => '',
))
->values(array(
  'path' => 'admin/settings/actions/delete/%',
  'load_functions' => 'a:1:{i:4;s:12:"actions_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:18:"administer actions";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:26:"system_actions_delete_form";i:1;i:4;}',
  'fit' => '30',
  'number_parts' => '5',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/actions/delete/%',
  'title' => 'Delete action',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => 'Delete an action.',
  'position' => '',
  'weight' => '0',
  'file' => '',
))
->values(array(
  'path' => 'admin/settings/actions/manage',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:18:"administer actions";}',
  'page_callback' => 'system_actions_manage',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/settings/actions',
  'tab_root' => 'admin/settings/actions',
  'title' => 'Manage actions',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => 'Manage the actions defined for your site.',
  'position' => '',
  'weight' => '-2',
  'file' => '',
))
->values(array(
  'path' => 'admin/settings/actions/orphan',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:18:"administer actions";}',
  'page_callback' => 'system_actions_remove_orphans',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/actions/orphan',
  'title' => 'Remove orphans',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => '',
))
->values(array(
  'path' => 'admin/settings/admin',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:27:"system_admin_theme_settings";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/admin',
  'title' => 'Administration theme',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => 'system_admin_theme_settings',
  'description' => 'Settings for how your administrative pages should look.',
  'position' => 'left',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/settings/clean-urls',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:25:"system_clean_url_settings";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/clean-urls',
  'title' => 'Clean URLs',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Enable or disable clean URLs for your site.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/settings/clean-urls/check',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '1',
  'access_arguments' => 'a:0:{}',
  'page_callback' => 'drupal_json',
  'page_arguments' => 'a:1:{i:0;a:1:{s:6:"status";b:1;}}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/clean-urls/check',
  'title' => 'Clean URL check',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => '',
))
->values(array(
  'path' => 'admin/settings/date-time',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:25:"system_date_time_settings";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/date-time',
  'title' => 'Date and time',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => "Settings for how Drupal displays date and time, as well as the system's default timezone.",
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/settings/date-time/lookup',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'system_date_time_lookup',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/date-time/lookup',
  'title' => 'Date and time lookup',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/settings/error-reporting',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:31:"system_error_reporting_settings";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/error-reporting',
  'title' => 'Error reporting',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Control how Drupal deals with errors including 403/404 errors as well as PHP error reporting.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/settings/file-system',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:27:"system_file_system_settings";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/file-system',
  'title' => 'File system',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Tell Drupal where to store uploaded files and how they are accessed.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/settings/filters',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:18:"administer filters";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:21:"filter_admin_overview";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/filters',
  'title' => 'Input formats',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Configure how content input by users is filtered, including allowed HTML tags. Also allows enabling of module-provided filters.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/filter/filter.admin.inc',
))
->values(array(
  'path' => 'admin/settings/filters/%',
  'load_functions' => 'a:1:{i:3;s:18:"filter_format_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:18:"administer filters";}',
  'page_callback' => 'filter_admin_format_page',
  'page_arguments' => 'a:1:{i:0;i:3;}',
  'fit' => '14',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/filters/%',
  'title' => '',
  'title_callback' => 'filter_admin_format_title',
  'title_arguments' => 'a:1:{i:0;i:3;}',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/filter/filter.admin.inc',
))
->values(array(
  'path' => 'admin/settings/filters/%/configure',
  'load_functions' => 'a:1:{i:3;s:18:"filter_format_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:18:"administer filters";}',
  'page_callback' => 'filter_admin_configure_page',
  'page_arguments' => 'a:1:{i:0;i:3;}',
  'fit' => '29',
  'number_parts' => '5',
  'tab_parent' => 'admin/settings/filters/%',
  'tab_root' => 'admin/settings/filters/%',
  'title' => 'Configure',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '1',
  'file' => 'modules/filter/filter.admin.inc',
))
->values(array(
  'path' => 'admin/settings/filters/%/edit',
  'load_functions' => 'a:1:{i:3;s:18:"filter_format_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:18:"administer filters";}',
  'page_callback' => 'filter_admin_format_page',
  'page_arguments' => 'a:1:{i:0;i:3;}',
  'fit' => '29',
  'number_parts' => '5',
  'tab_parent' => 'admin/settings/filters/%',
  'tab_root' => 'admin/settings/filters/%',
  'title' => 'Edit',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/filter/filter.admin.inc',
))
->values(array(
  'path' => 'admin/settings/filters/%/order',
  'load_functions' => 'a:1:{i:3;s:18:"filter_format_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:18:"administer filters";}',
  'page_callback' => 'filter_admin_order_page',
  'page_arguments' => 'a:1:{i:0;i:3;}',
  'fit' => '29',
  'number_parts' => '5',
  'tab_parent' => 'admin/settings/filters/%',
  'tab_root' => 'admin/settings/filters/%',
  'title' => 'Rearrange',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '2',
  'file' => 'modules/filter/filter.admin.inc',
))
->values(array(
  'path' => 'admin/settings/filters/add',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:18:"administer filters";}',
  'page_callback' => 'filter_admin_format_page',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/settings/filters',
  'tab_root' => 'admin/settings/filters',
  'title' => 'Add input format',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '1',
  'file' => 'modules/filter/filter.admin.inc',
))
->values(array(
  'path' => 'admin/settings/filters/delete',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:18:"administer filters";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:19:"filter_admin_delete";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/filters/delete',
  'title' => 'Delete input format',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/filter/filter.admin.inc',
))
->values(array(
  'path' => 'admin/settings/filters/list',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:18:"administer filters";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:21:"filter_admin_overview";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/settings/filters',
  'tab_root' => 'admin/settings/filters',
  'title' => 'List',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/filter/filter.admin.inc',
))
->values(array(
  'path' => 'admin/settings/image-toolkit',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:29:"system_image_toolkit_settings";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/image-toolkit',
  'title' => 'Image toolkit',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Choose which image toolkit to use if you have installed optional toolkits.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/settings/logging',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'system_logging_overview',
  'page_arguments' => 'a:0:{}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/logging',
  'title' => 'Logging and alerts',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => "Settings for logging and alerts modules. Various modules can route Drupal's system events to different destination, such as syslog, database, email, ...etc.",
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/settings/logging/dblog',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:20:"dblog_admin_settings";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/logging/dblog',
  'title' => 'Database logging',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Settings for logging to the Drupal database logs. This is the most common method for small to medium sites on shared hosting. The logs are viewable from the admin pages.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/dblog/dblog.admin.inc',
))
->values(array(
  'path' => 'admin/settings/performance',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:27:"system_performance_settings";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/performance',
  'title' => 'Performance',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Enable or disable page caching for anonymous users and set CSS and JS bandwidth optimization options.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/settings/site-information',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:32:"system_site_information_settings";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/site-information',
  'title' => 'Site information',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Change basic site information, such as the site name, slogan, e-mail address, mission, front page and more.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/settings/site-maintenance',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:29:"administer site configuration";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:32:"system_site_maintenance_settings";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/settings/site-maintenance',
  'title' => 'Site maintenance',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Take the site off-line for maintenance or bring it back online.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/user',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:27:"access administration pages";}',
  'page_callback' => 'system_admin_menu_block_page',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'admin/user',
  'title' => 'User management',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => "Manage your site's users, groups and access to site features.",
  'position' => 'left',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'admin/user/permissions',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:22:"administer permissions";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:15:"user_admin_perm";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/user/permissions',
  'title' => 'Permissions',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Determine access to features by selecting permissions for roles.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.admin.inc',
))
->values(array(
  'path' => 'admin/user/roles',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:22:"administer permissions";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:19:"user_admin_new_role";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/user/roles',
  'title' => 'Roles',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'List, edit, or add user roles.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.admin.inc',
))
->values(array(
  'path' => 'admin/user/roles/edit',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:22:"administer permissions";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:15:"user_admin_role";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/user/roles/edit',
  'title' => 'Edit role',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.admin.inc',
))
->values(array(
  'path' => 'admin/user/rules',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:22:"administer permissions";}',
  'page_callback' => 'user_admin_access',
  'page_arguments' => 'a:0:{}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/user/rules',
  'title' => 'Access rules',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'List and create rules to disallow usernames, e-mail addresses, and IP addresses.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.admin.inc',
))
->values(array(
  'path' => 'admin/user/rules/add',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:22:"administer permissions";}',
  'page_callback' => 'user_admin_access_add',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/user/rules',
  'tab_root' => 'admin/user/rules',
  'title' => 'Add rule',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.admin.inc',
))
->values(array(
  'path' => 'admin/user/rules/check',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:22:"administer permissions";}',
  'page_callback' => 'user_admin_access_check',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/user/rules',
  'tab_root' => 'admin/user/rules',
  'title' => 'Check rules',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.admin.inc',
))
->values(array(
  'path' => 'admin/user/rules/delete',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:22:"administer permissions";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:32:"user_admin_access_delete_confirm";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/user/rules/delete',
  'title' => 'Delete rule',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.admin.inc',
))
->values(array(
  'path' => 'admin/user/rules/edit',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:22:"administer permissions";}',
  'page_callback' => 'user_admin_access_edit',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'admin/user/rules/edit',
  'title' => 'Edit rule',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.admin.inc',
))
->values(array(
  'path' => 'admin/user/rules/list',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:22:"administer permissions";}',
  'page_callback' => 'user_admin_access',
  'page_arguments' => 'a:0:{}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/user/rules',
  'tab_root' => 'admin/user/rules',
  'title' => 'List',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '-10',
  'file' => 'modules/user/user.admin.inc',
))
->values(array(
  'path' => 'admin/user/settings',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:16:"administer users";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:19:"user_admin_settings";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/user/settings',
  'title' => 'User settings',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'Configure default behavior of users, including registration requirements, e-mails, and user pictures.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.admin.inc',
))
->values(array(
  'path' => 'admin/user/user',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:16:"administer users";}',
  'page_callback' => 'user_admin',
  'page_arguments' => 'a:1:{i:0;s:4:"list";}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'admin/user/user',
  'title' => 'Users',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'List, add, and edit users.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.admin.inc',
))
->values(array(
  'path' => 'admin/user/user/create',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:16:"administer users";}',
  'page_callback' => 'user_admin',
  'page_arguments' => 'a:1:{i:0;s:6:"create";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/user/user',
  'tab_root' => 'admin/user/user',
  'title' => 'Add user',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.admin.inc',
))
->values(array(
  'path' => 'admin/user/user/list',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:16:"administer users";}',
  'page_callback' => 'user_admin',
  'page_arguments' => 'a:1:{i:0;s:4:"list";}',
  'fit' => '15',
  'number_parts' => '4',
  'tab_parent' => 'admin/user/user',
  'tab_root' => 'admin/user/user',
  'title' => 'List',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '-10',
  'file' => 'modules/user/user.admin.inc',
))
->values(array(
  'path' => 'batch',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '1',
  'access_arguments' => 'a:0:{}',
  'page_callback' => 'system_batch_page',
  'page_arguments' => 'a:0:{}',
  'fit' => '1',
  'number_parts' => '1',
  'tab_parent' => '',
  'tab_root' => 'batch',
  'title' => '',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/system/system.admin.inc',
))
->values(array(
  'path' => 'book',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:14:"access content";}',
  'page_callback' => 'book_render',
  'page_arguments' => 'a:0:{}',
  'fit' => '1',
  'number_parts' => '1',
  'tab_parent' => '',
  'tab_root' => 'book',
  'title' => 'Books',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '20',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/book/book.pages.inc',
))
->values(array(
  'path' => 'book/export/%/%',
  'load_functions' => 'a:2:{i:2;N;i:3;N;}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:31:"access printer-friendly version";}',
  'page_callback' => 'book_export',
  'page_arguments' => 'a:2:{i:0;i:2;i:1;i:3;}',
  'fit' => '12',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'book/export/%/%',
  'title' => '',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/book/book.pages.inc',
))
->values(array(
  'path' => 'book/js/form',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:14:"access content";}',
  'page_callback' => 'book_form_update',
  'page_arguments' => 'a:0:{}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'book/js/form',
  'title' => '',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/book/book.pages.inc',
))
->values(array(
  'path' => 'comment/delete',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:19:"administer comments";}',
  'page_callback' => 'comment_delete',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'comment/delete',
  'title' => 'Delete comment',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/comment/comment.admin.inc',
))
->values(array(
  'path' => 'comment/edit',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:13:"post comments";}',
  'page_callback' => 'comment_edit',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'comment/edit',
  'title' => 'Edit comment',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/comment/comment.pages.inc',
))
->values(array(
  'path' => 'comment/reply/%',
  'load_functions' => 'a:1:{i:2;s:9:"node_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'node_access',
  'access_arguments' => 'a:2:{i:0;s:4:"view";i:1;i:2;}',
  'page_callback' => 'comment_reply',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '6',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'comment/reply/%',
  'title' => 'Reply to comment',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/comment/comment.pages.inc',
))
->values(array(
  'path' => 'filter/tips',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '1',
  'access_arguments' => 'a:0:{}',
  'page_callback' => 'filter_tips_long',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'filter/tips',
  'title' => 'Compose tips',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '20',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/filter/filter.pages.inc',
))
->values(array(
  'path' => 'logout',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_is_logged_in',
  'access_arguments' => 'a:0:{}',
  'page_callback' => 'user_logout',
  'page_arguments' => 'a:0:{}',
  'fit' => '1',
  'number_parts' => '1',
  'tab_parent' => '',
  'tab_root' => 'logout',
  'title' => 'Log out',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '10',
  'file' => 'modules/user/user.pages.inc',
))
->values(array(
  'path' => 'node',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:14:"access content";}',
  'page_callback' => 'node_page_default',
  'page_arguments' => 'a:0:{}',
  'fit' => '1',
  'number_parts' => '1',
  'tab_parent' => '',
  'tab_root' => 'node',
  'title' => 'Content',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => '',
))
->values(array(
  'path' => 'node/%',
  'load_functions' => 'a:1:{i:1;s:9:"node_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'node_access',
  'access_arguments' => 'a:2:{i:0;s:4:"view";i:1;i:1;}',
  'page_callback' => 'node_page_view',
  'page_arguments' => 'a:1:{i:0;i:1;}',
  'fit' => '2',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'node/%',
  'title' => '',
  'title_callback' => 'node_page_title',
  'title_arguments' => 'a:1:{i:0;i:1;}',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => '',
))
->values(array(
  'path' => 'node/%/delete',
  'load_functions' => 'a:1:{i:1;s:9:"node_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'node_access',
  'access_arguments' => 'a:2:{i:0;s:6:"delete";i:1;i:1;}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:19:"node_delete_confirm";i:1;i:1;}',
  'fit' => '5',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'node/%/delete',
  'title' => 'Delete',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '1',
  'file' => 'modules/node/node.pages.inc',
))
->values(array(
  'path' => 'node/%/edit',
  'load_functions' => 'a:1:{i:1;s:9:"node_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'node_access',
  'access_arguments' => 'a:2:{i:0;s:6:"update";i:1;i:1;}',
  'page_callback' => 'node_page_edit',
  'page_arguments' => 'a:1:{i:0;i:1;}',
  'fit' => '5',
  'number_parts' => '3',
  'tab_parent' => 'node/%',
  'tab_root' => 'node/%',
  'title' => 'Edit',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '1',
  'file' => 'modules/node/node.pages.inc',
))
->values(array(
  'path' => 'node/%/outline',
  'load_functions' => 'a:1:{i:1;s:9:"node_load";}',
  'to_arg_functions' => '',
  'access_callback' => '_book_outline_access',
  'access_arguments' => 'a:1:{i:0;i:1;}',
  'page_callback' => 'book_outline',
  'page_arguments' => 'a:1:{i:0;i:1;}',
  'fit' => '5',
  'number_parts' => '3',
  'tab_parent' => 'node/%',
  'tab_root' => 'node/%',
  'title' => 'Outline',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '2',
  'file' => 'modules/book/book.pages.inc',
))
->values(array(
  'path' => 'node/%/outline/remove',
  'load_functions' => 'a:1:{i:1;s:9:"node_load";}',
  'to_arg_functions' => '',
  'access_callback' => '_book_outline_remove_access',
  'access_arguments' => 'a:1:{i:0;i:1;}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:16:"book_remove_form";i:1;i:1;}',
  'fit' => '11',
  'number_parts' => '4',
  'tab_parent' => '',
  'tab_root' => 'node/%/outline/remove',
  'title' => 'Remove from outline',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/book/book.pages.inc',
))
->values(array(
  'path' => 'node/%/revisions',
  'load_functions' => 'a:1:{i:1;s:9:"node_load";}',
  'to_arg_functions' => '',
  'access_callback' => '_node_revision_access',
  'access_arguments' => 'a:1:{i:0;i:1;}',
  'page_callback' => 'node_revision_overview',
  'page_arguments' => 'a:1:{i:0;i:1;}',
  'fit' => '5',
  'number_parts' => '3',
  'tab_parent' => 'node/%',
  'tab_root' => 'node/%',
  'title' => 'Revisions',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '2',
  'file' => 'modules/node/node.pages.inc',
))
->values(array(
  'path' => 'node/%/revisions/%/delete',
  'load_functions' => 'a:2:{i:1;a:1:{s:9:"node_load";a:1:{i:0;i:3;}}i:3;N;}',
  'to_arg_functions' => '',
  'access_callback' => '_node_revision_access',
  'access_arguments' => 'a:2:{i:0;i:1;i:1;s:6:"delete";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:28:"node_revision_delete_confirm";i:1;i:1;}',
  'fit' => '21',
  'number_parts' => '5',
  'tab_parent' => '',
  'tab_root' => 'node/%/revisions/%/delete',
  'title' => 'Delete earlier revision',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/node.pages.inc',
))
->values(array(
  'path' => 'node/%/revisions/%/revert',
  'load_functions' => 'a:2:{i:1;a:1:{s:9:"node_load";a:1:{i:0;i:3;}}i:3;N;}',
  'to_arg_functions' => '',
  'access_callback' => '_node_revision_access',
  'access_arguments' => 'a:2:{i:0;i:1;i:1;s:6:"update";}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:28:"node_revision_revert_confirm";i:1;i:1;}',
  'fit' => '21',
  'number_parts' => '5',
  'tab_parent' => '',
  'tab_root' => 'node/%/revisions/%/revert',
  'title' => 'Revert to earlier revision',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/node.pages.inc',
))
->values(array(
  'path' => 'node/%/revisions/%/view',
  'load_functions' => 'a:2:{i:1;a:1:{s:9:"node_load";a:1:{i:0;i:3;}}i:3;N;}',
  'to_arg_functions' => '',
  'access_callback' => '_node_revision_access',
  'access_arguments' => 'a:1:{i:0;i:1;}',
  'page_callback' => 'node_show',
  'page_arguments' => 'a:3:{i:0;i:1;i:1;N;i:2;b:1;}',
  'fit' => '21',
  'number_parts' => '5',
  'tab_parent' => '',
  'tab_root' => 'node/%/revisions/%/view',
  'title' => 'Revisions',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => '',
))
->values(array(
  'path' => 'node/%/view',
  'load_functions' => 'a:1:{i:1;s:9:"node_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'node_access',
  'access_arguments' => 'a:2:{i:0;s:4:"view";i:1;i:1;}',
  'page_callback' => 'node_page_view',
  'page_arguments' => 'a:1:{i:0;i:1;}',
  'fit' => '5',
  'number_parts' => '3',
  'tab_parent' => 'node/%',
  'tab_root' => 'node/%',
  'title' => 'View',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '-10',
  'file' => '',
))
->values(array(
  'path' => 'node/add',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '_node_add_access',
  'access_arguments' => 'a:0:{}',
  'page_callback' => 'node_add_page',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'node/add',
  'title' => 'Create content',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '1',
  'file' => 'modules/node/node.pages.inc',
))
->values(array(
  'path' => 'node/add/book',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'node_access',
  'access_arguments' => 'a:2:{i:0;s:6:"create";i:1;s:4:"book";}',
  'page_callback' => 'node_add',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'node/add/book',
  'title' => 'Book page',
  'title_callback' => 'check_plain',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => 'A <em>book page</em> is a page of content, organized into a collection of related entries collectively known as a <em>book</em>. A <em>book page</em> automatically displays links to adjacent pages, providing a simple navigation system for organizing and reviewing structured content.',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/node.pages.inc',
))
->values(array(
  'path' => 'node/add/page',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'node_access',
  'access_arguments' => 'a:2:{i:0;s:6:"create";i:1;s:4:"page";}',
  'page_callback' => 'node_add',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'node/add/page',
  'title' => 'Page',
  'title_callback' => 'check_plain',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => "A <em>page</em>, similar in form to a <em>story</em>, is a simple method for creating and displaying information that rarely changes, such as an \"About us\" section of a website. By default, a <em>page</em> entry does not allow visitor comments and is not featured on the site's initial home page.",
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/node.pages.inc',
))
->values(array(
  'path' => 'node/add/story',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'node_access',
  'access_arguments' => 'a:2:{i:0;s:6:"create";i:1;s:5:"story";}',
  'page_callback' => 'node_add',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '7',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'node/add/story',
  'title' => 'Story',
  'title_callback' => 'check_plain',
  'title_arguments' => '',
  'type' => '6',
  'block_callback' => '',
  'description' => "A <em>story</em>, similar in form to a <em>page</em>, is ideal for creating and displaying content that informs or engages website visitors. Press releases, site announcements, and informal blog-like entries may all be created with a <em>story</em> entry. By default, a <em>story</em> entry is automatically featured on the site's initial home page, and provides the ability to post comments.",
  'position' => '',
  'weight' => '0',
  'file' => 'modules/node/node.pages.inc',
))
->values(array(
  'path' => 'rss.xml',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:14:"access content";}',
  'page_callback' => 'node_feed',
  'page_arguments' => 'a:0:{}',
  'fit' => '1',
  'number_parts' => '1',
  'tab_parent' => '',
  'tab_root' => 'rss.xml',
  'title' => 'RSS feed',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => '',
))
->values(array(
  'path' => 'system/files',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '1',
  'access_arguments' => 'a:0:{}',
  'page_callback' => 'file_download',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'system/files',
  'title' => 'File download',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => '',
))
->values(array(
  'path' => 'taxonomy/autocomplete',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:14:"access content";}',
  'page_callback' => 'taxonomy_autocomplete',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'taxonomy/autocomplete',
  'title' => 'Autocomplete taxonomy',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/taxonomy/taxonomy.pages.inc',
))
->values(array(
  'path' => 'taxonomy/term/%',
  'load_functions' => 'a:1:{i:2;N;}',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:14:"access content";}',
  'page_callback' => 'taxonomy_term_page',
  'page_arguments' => 'a:1:{i:0;i:2;}',
  'fit' => '6',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'taxonomy/term/%',
  'title' => 'Taxonomy term',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/taxonomy/taxonomy.pages.inc',
))
->values(array(
  'path' => 'user',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => '1',
  'access_arguments' => 'a:0:{}',
  'page_callback' => 'user_page',
  'page_arguments' => 'a:0:{}',
  'fit' => '1',
  'number_parts' => '1',
  'tab_parent' => '',
  'tab_root' => 'user',
  'title' => 'User account',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.pages.inc',
))
->values(array(
  'path' => 'user/%',
  'load_functions' => 'a:1:{i:1;s:22:"user_uid_optional_load";}',
  'to_arg_functions' => 'a:1:{i:1;s:24:"user_uid_optional_to_arg";}',
  'access_callback' => 'user_view_access',
  'access_arguments' => 'a:1:{i:0;i:1;}',
  'page_callback' => 'user_view',
  'page_arguments' => 'a:1:{i:0;i:1;}',
  'fit' => '2',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'user/%',
  'title' => 'My account',
  'title_callback' => 'user_page_title',
  'title_arguments' => 'a:1:{i:0;i:1;}',
  'type' => '6',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.pages.inc',
))
->values(array(
  'path' => 'user/%/delete',
  'load_functions' => 'a:1:{i:1;s:9:"user_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_delete_access',
  'access_arguments' => 'a:1:{i:0;i:1;}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:2:{i:0;s:19:"user_confirm_delete";i:1;i:1;}',
  'fit' => '5',
  'number_parts' => '3',
  'tab_parent' => '',
  'tab_root' => 'user/%/delete',
  'title' => 'Delete',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.pages.inc',
))
->values(array(
  'path' => 'user/%/edit',
  'load_functions' => 'a:1:{i:1;a:1:{s:18:"user_category_load";a:2:{i:0;s:4:"%map";i:1;s:6:"%index";}}}',
  'to_arg_functions' => '',
  'access_callback' => 'user_edit_access',
  'access_arguments' => 'a:1:{i:0;i:1;}',
  'page_callback' => 'user_edit',
  'page_arguments' => 'a:1:{i:0;i:1;}',
  'fit' => '5',
  'number_parts' => '3',
  'tab_parent' => 'user/%',
  'tab_root' => 'user/%',
  'title' => 'Edit',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.pages.inc',
))
->values(array(
  'path' => 'user/%/edit/account',
  'load_functions' => 'a:1:{i:1;a:1:{s:18:"user_category_load";a:2:{i:0;s:4:"%map";i:1;s:6:"%index";}}}',
  'to_arg_functions' => '',
  'access_callback' => 'user_edit_access',
  'access_arguments' => 'a:1:{i:0;i:1;}',
  'page_callback' => 'user_edit',
  'page_arguments' => 'a:1:{i:0;i:1;}',
  'fit' => '11',
  'number_parts' => '4',
  'tab_parent' => 'user/%/edit',
  'tab_root' => 'user/%',
  'title' => 'Account',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.pages.inc',
))
->values(array(
  'path' => 'user/%/view',
  'load_functions' => 'a:1:{i:1;s:9:"user_load";}',
  'to_arg_functions' => '',
  'access_callback' => 'user_view_access',
  'access_arguments' => 'a:1:{i:0;i:1;}',
  'page_callback' => 'user_view',
  'page_arguments' => 'a:1:{i:0;i:1;}',
  'fit' => '5',
  'number_parts' => '3',
  'tab_parent' => 'user/%',
  'tab_root' => 'user/%',
  'title' => 'View',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '-10',
  'file' => 'modules/user/user.pages.inc',
))
->values(array(
  'path' => 'user/autocomplete',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_access',
  'access_arguments' => 'a:1:{i:0;s:20:"access user profiles";}',
  'page_callback' => 'user_autocomplete',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => '',
  'tab_root' => 'user/autocomplete',
  'title' => 'User autocomplete',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.pages.inc',
))
->values(array(
  'path' => 'user/login',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_is_anonymous',
  'access_arguments' => 'a:0:{}',
  'page_callback' => 'user_page',
  'page_arguments' => 'a:0:{}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => 'user',
  'tab_root' => 'user',
  'title' => 'Log in',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '136',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.pages.inc',
))
->values(array(
  'path' => 'user/password',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_is_anonymous',
  'access_arguments' => 'a:0:{}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:9:"user_pass";}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => 'user',
  'tab_root' => 'user',
  'title' => 'Request new password',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.pages.inc',
))
->values(array(
  'path' => 'user/register',
  'load_functions' => '',
  'to_arg_functions' => '',
  'access_callback' => 'user_register_access',
  'access_arguments' => 'a:0:{}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:1:{i:0;s:13:"user_register";}',
  'fit' => '3',
  'number_parts' => '2',
  'tab_parent' => 'user',
  'tab_root' => 'user',
  'title' => 'Create new account',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '128',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.pages.inc',
))
->values(array(
  'path' => 'user/reset/%/%/%',
  'load_functions' => 'a:3:{i:2;N;i:3;N;i:4;N;}',
  'to_arg_functions' => '',
  'access_callback' => '1',
  'access_arguments' => 'a:0:{}',
  'page_callback' => 'drupal_get_form',
  'page_arguments' => 'a:4:{i:0;s:15:"user_pass_reset";i:1;i:2;i:2;i:3;i:3;i:4;}',
  'fit' => '24',
  'number_parts' => '5',
  'tab_parent' => '',
  'tab_root' => 'user/reset/%/%/%',
  'title' => 'Reset password',
  'title_callback' => 't',
  'title_arguments' => '',
  'type' => '4',
  'block_callback' => '',
  'description' => '',
  'position' => '',
  'weight' => '0',
  'file' => 'modules/user/user.pages.inc',
))
->execute();
$connection->schema()->createTable('node', array(
  'fields' => array(
    'nid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
    'vid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'type' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '32',
      'default' => '',
    ),
    'language' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '12',
      'default' => '',
    ),
    'title' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'uid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'status' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '1',
    ),
    'created' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'changed' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'comment' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'promote' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'moderate' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'sticky' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'tnid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'translate' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'nid',
  ),
  'unique keys' => array(
    'vid' => array(
      'vid',
    ),
  ),
  'indexes' => array(
    'node_changed' => array(
      'changed',
    ),
    'node_created' => array(
      'created',
    ),
    'node_moderate' => array(
      'moderate',
    ),
    'node_promote_status' => array(
      'promote',
      'status',
    ),
    'node_status_type' => array(
      'status',
      'type',
      'nid',
    ),
    'node_title_type' => array(
      'title',
      array(
        'type',
        '4',
      ),
    ),
    'node_type' => array(
      array(
        'type',
        '4',
      ),
    ),
    'uid' => array(
      'uid',
    ),
    'tnid' => array(
      'tnid',
    ),
    'translate' => array(
      'translate',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('node')
->fields(array(
  'nid',
  'vid',
  'type',
  'language',
  'title',
  'uid',
  'status',
  'created',
  'changed',
  'comment',
  'promote',
  'moderate',
  'sticky',
  'tnid',
  'translate',
))
->values(array(
  'nid' => '1',
  'vid' => '1',
  'type' => 'book',
  'language' => '',
  'title' => 'Birds',
  'uid' => '1',
  'status' => '1',
  'created' => '1689990038',
  'changed' => '1689990038',
  'comment' => '2',
  'promote' => '0',
  'moderate' => '0',
  'sticky' => '0',
  'tnid' => '0',
  'translate' => '0',
))
->values(array(
  'nid' => '2',
  'vid' => '2',
  'type' => 'book',
  'language' => '',
  'title' => 'Emu',
  'uid' => '1',
  'status' => '1',
  'created' => '1689990134',
  'changed' => '1689990134',
  'comment' => '2',
  'promote' => '0',
  'moderate' => '0',
  'sticky' => '0',
  'tnid' => '0',
  'translate' => '0',
))
->values(array(
  'nid' => '3',
  'vid' => '3',
  'type' => 'book',
  'language' => '',
  'title' => 'Parrots',
  'uid' => '1',
  'status' => '1',
  'created' => '1689990159',
  'changed' => '1689990159',
  'comment' => '2',
  'promote' => '0',
  'moderate' => '0',
  'sticky' => '0',
  'tnid' => '0',
  'translate' => '0',
))
->values(array(
  'nid' => '4',
  'vid' => '4',
  'type' => 'book',
  'language' => '',
  'title' => 'Kea',
  'uid' => '1',
  'status' => '1',
  'created' => '1689990225',
  'changed' => '1689990225',
  'comment' => '2',
  'promote' => '0',
  'moderate' => '0',
  'sticky' => '0',
  'tnid' => '0',
  'translate' => '0',
))
->values(array(
  'nid' => '5',
  'vid' => '5',
  'type' => 'book',
  'language' => '',
  'title' => 'Kakapo',
  'uid' => '1',
  'status' => '1',
  'created' => '1689990252',
  'changed' => '1689990252',
  'comment' => '2',
  'promote' => '0',
  'moderate' => '0',
  'sticky' => '0',
  'tnid' => '0',
  'translate' => '0',
))
->values(array(
  'nid' => '6',
  'vid' => '6',
  'type' => 'book',
  'language' => '',
  'title' => 'Tree',
  'uid' => '1',
  'status' => '1',
  'created' => '1689990304',
  'changed' => '1689990304',
  'comment' => '2',
  'promote' => '0',
  'moderate' => '0',
  'sticky' => '0',
  'tnid' => '0',
  'translate' => '0',
))
->values(array(
  'nid' => '7',
  'vid' => '7',
  'type' => 'book',
  'language' => '',
  'title' => 'Rimu',
  'uid' => '1',
  'status' => '1',
  'created' => '1689990337',
  'changed' => '1689990337',
  'comment' => '2',
  'promote' => '0',
  'moderate' => '0',
  'sticky' => '0',
  'tnid' => '0',
  'translate' => '0',
))
->values(array(
  'nid' => '8',
  'vid' => '8',
  'type' => 'book',
  'language' => '',
  'title' => 'Oaks',
  'uid' => '1',
  'status' => '1',
  'created' => '1689990481',
  'changed' => '1689990481',
  'comment' => '2',
  'promote' => '0',
  'moderate' => '0',
  'sticky' => '0',
  'tnid' => '0',
  'translate' => '0',
))
->values(array(
  'nid' => '9',
  'vid' => '9',
  'type' => 'book',
  'language' => '',
  'title' => 'Cork oak',
  'uid' => '1',
  'status' => '1',
  'created' => '1689990508',
  'changed' => '1689990508',
  'comment' => '2',
  'promote' => '0',
  'moderate' => '0',
  'sticky' => '0',
  'tnid' => '0',
  'translate' => '0',
))
->values(array(
  'nid' => '10',
  'vid' => '10',
  'type' => 'book',
  'language' => '',
  'title' => 'White oak',
  'uid' => '1',
  'status' => '1',
  'created' => '1689990584',
  'changed' => '1689990584',
  'comment' => '2',
  'promote' => '0',
  'moderate' => '0',
  'sticky' => '0',
  'tnid' => '0',
  'translate' => '0',
))
->execute();
$connection->schema()->createTable('node_access', array(
  'fields' => array(
    'nid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'gid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'realm' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'grant_view' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'grant_update' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'grant_delete' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
      'unsigned' => TRUE,
    ),
  ),
  'primary key' => array(
    'nid',
    'gid',
    'realm',
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('node_access')
->fields(array(
  'nid',
  'gid',
  'realm',
  'grant_view',
  'grant_update',
  'grant_delete',
))
->values(array(
  'nid' => '0',
  'gid' => '0',
  'realm' => 'all',
  'grant_view' => '1',
  'grant_update' => '0',
  'grant_delete' => '0',
))
->execute();
$connection->schema()->createTable('node_comment_statistics', array(
  'fields' => array(
    'nid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'last_comment_timestamp' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'last_comment_name' => array(
      'type' => 'varchar',
      'not null' => FALSE,
      'length' => '60',
    ),
    'last_comment_uid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'comment_count' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
  ),
  'primary key' => array(
    'nid',
  ),
  'indexes' => array(
    'node_comment_timestamp' => array(
      'last_comment_timestamp',
    ),
    'comment_count' => array(
      'comment_count',
    ),
    'last_comment_uid' => array(
      'last_comment_uid',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('node_comment_statistics')
->fields(array(
  'nid',
  'last_comment_timestamp',
  'last_comment_name',
  'last_comment_uid',
  'comment_count',
))
->values(array(
  'nid' => '1',
  'last_comment_timestamp' => '1689990038',
  'last_comment_name' => NULL,
  'last_comment_uid' => '1',
  'comment_count' => '0',
))
->values(array(
  'nid' => '2',
  'last_comment_timestamp' => '1689990134',
  'last_comment_name' => NULL,
  'last_comment_uid' => '1',
  'comment_count' => '0',
))
->values(array(
  'nid' => '3',
  'last_comment_timestamp' => '1689990159',
  'last_comment_name' => NULL,
  'last_comment_uid' => '1',
  'comment_count' => '0',
))
->values(array(
  'nid' => '4',
  'last_comment_timestamp' => '1689990225',
  'last_comment_name' => NULL,
  'last_comment_uid' => '1',
  'comment_count' => '0',
))
->values(array(
  'nid' => '5',
  'last_comment_timestamp' => '1689990252',
  'last_comment_name' => NULL,
  'last_comment_uid' => '1',
  'comment_count' => '0',
))
->values(array(
  'nid' => '6',
  'last_comment_timestamp' => '1689990304',
  'last_comment_name' => NULL,
  'last_comment_uid' => '1',
  'comment_count' => '0',
))
->values(array(
  'nid' => '7',
  'last_comment_timestamp' => '1689990337',
  'last_comment_name' => NULL,
  'last_comment_uid' => '1',
  'comment_count' => '0',
))
->values(array(
  'nid' => '8',
  'last_comment_timestamp' => '1689990481',
  'last_comment_name' => NULL,
  'last_comment_uid' => '1',
  'comment_count' => '0',
))
->values(array(
  'nid' => '9',
  'last_comment_timestamp' => '1689990508',
  'last_comment_name' => NULL,
  'last_comment_uid' => '1',
  'comment_count' => '0',
))
->values(array(
  'nid' => '10',
  'last_comment_timestamp' => '1689990584',
  'last_comment_name' => NULL,
  'last_comment_uid' => '1',
  'comment_count' => '0',
))
->execute();
$connection->schema()->createTable('node_counter', array(
  'fields' => array(
    'nid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'totalcount' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'big',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'daycount' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'medium',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'timestamp' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
  ),
  'primary key' => array(
    'nid',
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('node_revisions', array(
  'fields' => array(
    'nid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'vid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
    'uid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'title' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'body' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'big',
    ),
    'teaser' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'big',
    ),
    'log' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'big',
    ),
    'timestamp' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'format' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'vid',
  ),
  'indexes' => array(
    'nid' => array(
      'nid',
    ),
    'uid' => array(
      'uid',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('node_revisions')
->fields(array(
  'nid',
  'vid',
  'uid',
  'title',
  'body',
  'teaser',
  'log',
  'timestamp',
  'format',
))
->values(array(
  'nid' => '1',
  'vid' => '1',
  'uid' => '1',
  'title' => 'Birds',
  'body' => 'This is a book about birds',
  'teaser' => 'This is a book about birds',
  'log' => '',
  'timestamp' => '1689990038',
  'format' => '1',
))
->values(array(
  'nid' => '2',
  'vid' => '2',
  'uid' => '1',
  'title' => 'Emu',
  'body' => 'About the emu.',
  'teaser' => 'About the emu.',
  'log' => '',
  'timestamp' => '1689990134',
  'format' => '1',
))
->values(array(
  'nid' => '3',
  'vid' => '3',
  'uid' => '1',
  'title' => 'Parrots',
  'body' => 'Types of parrots',
  'teaser' => 'Types of parrots',
  'log' => '',
  'timestamp' => '1689990159',
  'format' => '1',
))
->values(array(
  'nid' => '4',
  'vid' => '4',
  'uid' => '1',
  'title' => 'Kea',
  'body' => 'About the kea.',
  'teaser' => 'About the kea.',
  'log' => '',
  'timestamp' => '1689990225',
  'format' => '1',
))
->values(array(
  'nid' => '5',
  'vid' => '5',
  'uid' => '1',
  'title' => 'Kakapo',
  'body' => 'About the kakapo.',
  'teaser' => 'About the kakapo.',
  'log' => '',
  'timestamp' => '1689990252',
  'format' => '1',
))
->values(array(
  'nid' => '6',
  'vid' => '6',
  'uid' => '1',
  'title' => 'Tree',
  'body' => 'About trees.',
  'teaser' => 'About trees.',
  'log' => '',
  'timestamp' => '1689990304',
  'format' => '1',
))
->values(array(
  'nid' => '7',
  'vid' => '7',
  'uid' => '1',
  'title' => 'Rimu',
  'body' => 'About the rimu.',
  'teaser' => 'About the rimu.',
  'log' => '',
  'timestamp' => '1689990337',
  'format' => '1',
))
->values(array(
  'nid' => '8',
  'vid' => '8',
  'uid' => '1',
  'title' => 'Oaks',
  'body' => 'About the oaks.',
  'teaser' => 'About the oaks.',
  'log' => '',
  'timestamp' => '1689990481',
  'format' => '1',
))
->values(array(
  'nid' => '9',
  'vid' => '9',
  'uid' => '1',
  'title' => 'Cork oak',
  'body' => 'About the cork oak.',
  'teaser' => 'About the cork oak.',
  'log' => '',
  'timestamp' => '1689990508',
  'format' => '1',
))
->values(array(
  'nid' => '10',
  'vid' => '10',
  'uid' => '1',
  'title' => 'White oak',
  'body' => 'About the white oak.',
  'teaser' => 'About the white oak.',
  'log' => '',
  'timestamp' => '1689990584',
  'format' => '1',
))
->execute();
$connection->schema()->createTable('node_type', array(
  'fields' => array(
    'type' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '32',
    ),
    'name' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'module' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
    ),
    'description' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'medium',
    ),
    'help' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'medium',
    ),
    'has_title' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'unsigned' => TRUE,
    ),
    'title_label' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'has_body' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'unsigned' => TRUE,
    ),
    'body_label' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'min_word_count' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'unsigned' => TRUE,
    ),
    'custom' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
    'modified' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
    'locked' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
    'orig_type' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
  ),
  'primary key' => array(
    'type',
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('node_type')
->fields(array(
  'type',
  'name',
  'module',
  'description',
  'help',
  'has_title',
  'title_label',
  'has_body',
  'body_label',
  'min_word_count',
  'custom',
  'modified',
  'locked',
  'orig_type',
))
->values(array(
  'type' => 'book',
  'name' => 'Book page',
  'module' => 'node',
  'description' => 'A <em>book page</em> is a page of content, organized into a collection of related entries collectively known as a <em>book</em>. A <em>book page</em> automatically displays links to adjacent pages, providing a simple navigation system for organizing and reviewing structured content.',
  'help' => '',
  'has_title' => '1',
  'title_label' => 'Title',
  'has_body' => '1',
  'body_label' => 'Body',
  'min_word_count' => '0',
  'custom' => '1',
  'modified' => '1',
  'locked' => '0',
  'orig_type' => 'book',
))
->values(array(
  'type' => 'page',
  'name' => 'Page',
  'module' => 'node',
  'description' => "A <em>page</em>, similar in form to a <em>story</em>, is a simple method for creating and displaying information that rarely changes, such as an \"About us\" section of a website. By default, a <em>page</em> entry does not allow visitor comments and is not featured on the site's initial home page.",
  'help' => '',
  'has_title' => '1',
  'title_label' => 'Title',
  'has_body' => '1',
  'body_label' => 'Body',
  'min_word_count' => '0',
  'custom' => '1',
  'modified' => '1',
  'locked' => '0',
  'orig_type' => 'page',
))
->values(array(
  'type' => 'story',
  'name' => 'Story',
  'module' => 'node',
  'description' => "A <em>story</em>, similar in form to a <em>page</em>, is ideal for creating and displaying content that informs or engages website visitors. Press releases, site announcements, and informal blog-like entries may all be created with a <em>story</em> entry. By default, a <em>story</em> entry is automatically featured on the site's initial home page, and provides the ability to post comments.",
  'help' => '',
  'has_title' => '1',
  'title_label' => 'Title',
  'has_body' => '1',
  'body_label' => 'Body',
  'min_word_count' => '0',
  'custom' => '1',
  'modified' => '1',
  'locked' => '0',
  'orig_type' => 'story',
))
->execute();
$connection->schema()->createTable('permission', array(
  'fields' => array(
    'pid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'rid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'perm' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'big',
    ),
    'tid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
  ),
  'primary key' => array(
    'pid',
  ),
  'indexes' => array(
    'rid' => array(
      'rid',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('permission')
->fields(array(
  'pid',
  'rid',
  'perm',
  'tid',
))
->values(array(
  'pid' => '1',
  'rid' => '1',
  'perm' => 'access content',
  'tid' => '0',
))
->values(array(
  'pid' => '2',
  'rid' => '2',
  'perm' => 'access comments, access content, post comments, post comments without approval',
  'tid' => '0',
))
->execute();
$connection->schema()->createTable('role', array(
  'fields' => array(
    'rid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
    'name' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '64',
      'default' => '',
    ),
  ),
  'primary key' => array(
    'rid',
  ),
  'unique keys' => array(
    'name' => array(
      'name',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('role')
->fields(array(
  'rid',
  'name',
))
->values(array(
  'rid' => '1',
  'name' => 'anonymous user',
))
->values(array(
  'rid' => '2',
  'name' => 'authenticated user',
))
->execute();
$connection->schema()->createTable('semaphore', array(
  'fields' => array(
    'name' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'value' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'expire' => array(
      'type' => 'float',
      'not null' => TRUE,
      'size' => 'big',
    ),
  ),
  'primary key' => array(
    'name',
  ),
  'indexes' => array(
    'expire' => array(
      'expire',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('sessions', array(
  'fields' => array(
    'uid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
    'sid' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '64',
      'default' => '',
    ),
    'hostname' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '128',
      'default' => '',
    ),
    'timestamp' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'cache' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'session' => array(
      'type' => 'blob',
      'not null' => FALSE,
      'size' => 'big',
    ),
  ),
  'primary key' => array(
    'sid',
  ),
  'indexes' => array(
    'timestamp' => array(
      'timestamp',
    ),
    'uid' => array(
      'uid',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('system', array(
  'fields' => array(
    'filename' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'name' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'type' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'owner' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'status' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'throttle' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
    'bootstrap' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'schema_version' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '-1',
    ),
    'weight' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'info' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'normal',
    ),
  ),
  'primary key' => array(
    'filename',
  ),
  'indexes' => array(
    'modules' => array(
      array(
        'type',
        '12',
      ),
      'status',
      'weight',
      'filename',
    ),
    'bootstrap' => array(
      array(
        'type',
        '12',
      ),
      'status',
      'bootstrap',
      'weight',
      'filename',
    ),
    'type_name' => array(
      array(
        'type',
        '12',
      ),
      'name',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('system')
->fields(array(
  'filename',
  'name',
  'type',
  'owner',
  'status',
  'throttle',
  'bootstrap',
  'schema_version',
  'weight',
  'info',
))
->values(array(
  'filename' => 'modules/aggregator/aggregator.module',
  'name' => 'aggregator',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:10:"Aggregator";s:11:"description";s:57:"Aggregates syndicated content (RSS, RDF, and Atom feeds).";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/block/block.module',
  'name' => 'block',
  'type' => 'module',
  'owner' => '',
  'status' => '1',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '0',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:5:"Block";s:11:"description";s:62:"Controls the boxes that are displayed around the main content.";s:7:"package";s:15:"Core - required";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/blog/blog.module',
  'name' => 'blog',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:4:"Blog";s:11:"description";s:69:"Enables keeping easily and regularly updated user web pages or blogs.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/blogapi/blogapi.module',
  'name' => 'blogapi',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:8:"Blog API";s:11:"description";s:79:"Allows users to post content using applications that support XML-RPC blog APIs.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/book/book.module',
  'name' => 'book',
  'type' => 'module',
  'owner' => '',
  'status' => '1',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '6000',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:4:"Book";s:11:"description";s:63:"Allows users to structure site pages in a hierarchy or outline.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/color/color.module',
  'name' => 'color',
  'type' => 'module',
  'owner' => '',
  'status' => '1',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '6001',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:5:"Color";s:11:"description";s:61:"Allows the user to change the color scheme of certain themes.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/comment/comment.module',
  'name' => 'comment',
  'type' => 'module',
  'owner' => '',
  'status' => '1',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '6005',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:7:"Comment";s:11:"description";s:57:"Allows users to comment on and discuss published content.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/contact/contact.module',
  'name' => 'contact',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:7:"Contact";s:11:"description";s:61:"Enables the use of both personal and site-wide contact forms.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/dblog/dblog.module',
  'name' => 'dblog',
  'type' => 'module',
  'owner' => '',
  'status' => '1',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '6000',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:16:"Database logging";s:11:"description";s:47:"Logs and records system events to the database.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/filter/filter.module',
  'name' => 'filter',
  'type' => 'module',
  'owner' => '',
  'status' => '1',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '0',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:6:"Filter";s:11:"description";s:60:"Handles the filtering of content in preparation for display.";s:7:"package";s:15:"Core - required";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/forum/forum.module',
  'name' => 'forum',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:5:"Forum";s:11:"description";s:50:"Enables threaded discussions about general topics.";s:12:"dependencies";a:2:{i:0;s:8:"taxonomy";i:1;s:7:"comment";}s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/help/help.module',
  'name' => 'help',
  'type' => 'module',
  'owner' => '',
  'status' => '1',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '0',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:4:"Help";s:11:"description";s:35:"Manages the display of online help.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/locale/locale.module',
  'name' => 'locale',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:6:"Locale";s:11:"description";s:119:"Adds language handling functionality and enables the translation of the user interface to languages other than English.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/menu/menu.module',
  'name' => 'menu',
  'type' => 'module',
  'owner' => '',
  'status' => '1',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '0',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:4:"Menu";s:11:"description";s:60:"Allows administrators to customize the site navigation menu.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/node/node.module',
  'name' => 'node',
  'type' => 'module',
  'owner' => '',
  'status' => '1',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '0',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:4:"Node";s:11:"description";s:66:"Allows content to be submitted to the site and displayed on pages.";s:7:"package";s:15:"Core - required";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/openid/openid.module',
  'name' => 'openid',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:6:"OpenID";s:11:"description";s:48:"Allows users to log into your site using OpenID.";s:7:"version";s:4:"6.38";s:7:"package";s:15:"Core - optional";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/path/path.module',
  'name' => 'path',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:4:"Path";s:11:"description";s:28:"Allows users to rename URLs.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/php/php.module',
  'name' => 'php',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:10:"PHP filter";s:11:"description";s:50:"Allows embedded PHP code/snippets to be evaluated.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/ping/ping.module',
  'name' => 'ping',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:4:"Ping";s:11:"description";s:51:"Alerts other sites when your site has been updated.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/poll/poll.module',
  'name' => 'poll',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:4:"Poll";s:11:"description";s:95:"Allows your site to capture votes on different topics in the form of multiple choice questions.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/profile/profile.module',
  'name' => 'profile',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:7:"Profile";s:11:"description";s:36:"Supports configurable user profiles.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/search/search.module',
  'name' => 'search',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:6:"Search";s:11:"description";s:36:"Enables site-wide keyword searching.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/statistics/statistics.module',
  'name' => 'statistics',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:10:"Statistics";s:11:"description";s:37:"Logs access statistics for your site.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/syslog/syslog.module',
  'name' => 'syslog',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:6:"Syslog";s:11:"description";s:41:"Logs and records system events to syslog.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/system/system.module',
  'name' => 'system',
  'type' => 'module',
  'owner' => '',
  'status' => '1',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '6056',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:6:"System";s:11:"description";s:54:"Handles general site configuration for administrators.";s:7:"package";s:15:"Core - required";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/taxonomy/taxonomy.module',
  'name' => 'taxonomy',
  'type' => 'module',
  'owner' => '',
  'status' => '1',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '0',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:8:"Taxonomy";s:11:"description";s:38:"Enables the categorization of content.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/throttle/throttle.module',
  'name' => 'throttle',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:8:"Throttle";s:11:"description";s:66:"Handles the auto-throttling mechanism, to control site congestion.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/tracker/tracker.module',
  'name' => 'tracker',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:7:"Tracker";s:11:"description";s:43:"Enables tracking of recent posts for users.";s:12:"dependencies";a:1:{i:0;s:7:"comment";}s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/translation/translation.module',
  'name' => 'translation',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:19:"Content translation";s:11:"description";s:57:"Allows content to be translated into different languages.";s:12:"dependencies";a:1:{i:0;s:6:"locale";}s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/trigger/trigger.module',
  'name' => 'trigger',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:7:"Trigger";s:11:"description";s:90:"Enables actions to be fired on certain system events, such as when new content is created.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/update/update.module',
  'name' => 'update',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:13:"Update status";s:11:"description";s:88:"Checks the status of available updates for Drupal and your installed modules and themes.";s:7:"version";s:4:"6.38";s:7:"package";s:15:"Core - optional";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/upload/upload.module',
  'name' => 'upload',
  'type' => 'module',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:6:"Upload";s:11:"description";s:51:"Allows users to upload and attach files to content.";s:7:"package";s:15:"Core - optional";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'modules/user/user.module',
  'name' => 'user',
  'type' => 'module',
  'owner' => '',
  'status' => '1',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '0',
  'weight' => '0',
  'info' => 'a:8:{s:4:"name";s:4:"User";s:11:"description";s:47:"Manages the user registration and login system.";s:7:"package";s:15:"Core - required";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:12:"dependencies";a:0:{}s:10:"dependents";a:0:{}s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'themes/bluemarine/bluemarine.info',
  'name' => 'bluemarine',
  'type' => 'theme',
  'owner' => 'themes/engines/phptemplate/phptemplate.engine',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:11:{s:4:"name";s:10:"Bluemarine";s:11:"description";s:66:"Table-based multi-column theme with a marine and ash color scheme.";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:6:"engine";s:11:"phptemplate";s:7:"regions";a:5:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";s:7:"content";s:7:"Content";s:6:"header";s:6:"Header";s:6:"footer";s:6:"Footer";}s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:27:"themes/bluemarine/style.css";}}s:7:"scripts";a:1:{s:9:"script.js";s:27:"themes/bluemarine/script.js";}s:10:"screenshot";s:32:"themes/bluemarine/screenshot.png";s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'themes/chameleon/chameleon.info',
  'name' => 'chameleon',
  'type' => 'theme',
  'owner' => 'themes/chameleon/chameleon.theme',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:10:{s:4:"name";s:9:"Chameleon";s:11:"description";s:42:"Minimalist tabled theme with light colors.";s:7:"regions";a:2:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";}s:8:"features";a:4:{i:0;s:4:"logo";i:1;s:7:"favicon";i:2;s:4:"name";i:3;s:6:"slogan";}s:11:"stylesheets";a:1:{s:3:"all";a:2:{s:9:"style.css";s:26:"themes/chameleon/style.css";s:10:"common.css";s:27:"themes/chameleon/common.css";}}s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:7:"scripts";a:1:{s:9:"script.js";s:26:"themes/chameleon/script.js";}s:10:"screenshot";s:31:"themes/chameleon/screenshot.png";s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'themes/chameleon/marvin/marvin.info',
  'name' => 'marvin',
  'type' => 'theme',
  'owner' => '',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:11:{s:4:"name";s:6:"Marvin";s:11:"description";s:31:"Boxy tabled theme in all grays.";s:7:"regions";a:2:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";}s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:10:"base theme";s:9:"chameleon";s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:33:"themes/chameleon/marvin/style.css";}}s:7:"scripts";a:1:{s:9:"script.js";s:33:"themes/chameleon/marvin/script.js";}s:10:"screenshot";s:38:"themes/chameleon/marvin/screenshot.png";s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'themes/garland/garland.info',
  'name' => 'garland',
  'type' => 'theme',
  'owner' => 'themes/engines/phptemplate/phptemplate.engine',
  'status' => '1',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:11:{s:4:"name";s:7:"Garland";s:11:"description";s:66:"Tableless, recolorable, multi-column, fluid width theme (default).";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:6:"engine";s:11:"phptemplate";s:11:"stylesheets";a:2:{s:3:"all";a:1:{s:9:"style.css";s:24:"themes/garland/style.css";}s:5:"print";a:1:{s:9:"print.css";s:24:"themes/garland/print.css";}}s:7:"regions";a:5:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";s:7:"content";s:7:"Content";s:6:"header";s:6:"Header";s:6:"footer";s:6:"Footer";}s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:7:"scripts";a:1:{s:9:"script.js";s:24:"themes/garland/script.js";}s:10:"screenshot";s:29:"themes/garland/screenshot.png";s:3:"php";s:5:"4.3.5";}',
))
->values(array(
  'filename' => 'themes/garland/minnelli/minnelli.info',
  'name' => 'minnelli',
  'type' => 'theme',
  'owner' => 'themes/engines/phptemplate/phptemplate.engine',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:12:{s:4:"name";s:8:"Minnelli";s:11:"description";s:56:"Tableless, recolorable, multi-column, fixed width theme.";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:10:"base theme";s:7:"garland";s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:12:"minnelli.css";s:36:"themes/garland/minnelli/minnelli.css";}}s:7:"regions";a:5:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";s:7:"content";s:7:"Content";s:6:"header";s:6:"Header";s:6:"footer";s:6:"Footer";}s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:7:"scripts";a:1:{s:9:"script.js";s:33:"themes/garland/minnelli/script.js";}s:10:"screenshot";s:38:"themes/garland/minnelli/screenshot.png";s:3:"php";s:5:"4.3.5";s:6:"engine";s:11:"phptemplate";}',
))
->values(array(
  'filename' => 'themes/pushbutton/pushbutton.info',
  'name' => 'pushbutton',
  'type' => 'theme',
  'owner' => 'themes/engines/phptemplate/phptemplate.engine',
  'status' => '0',
  'throttle' => '0',
  'bootstrap' => '0',
  'schema_version' => '-1',
  'weight' => '0',
  'info' => 'a:11:{s:4:"name";s:10:"Pushbutton";s:11:"description";s:52:"Tabled, multi-column theme in blue and orange tones.";s:7:"version";s:4:"6.38";s:4:"core";s:3:"6.x";s:6:"engine";s:11:"phptemplate";s:7:"regions";a:5:{s:4:"left";s:12:"Left sidebar";s:5:"right";s:13:"Right sidebar";s:7:"content";s:7:"Content";s:6:"header";s:6:"Header";s:6:"footer";s:6:"Footer";}s:8:"features";a:10:{i:0;s:20:"comment_user_picture";i:1;s:7:"favicon";i:2;s:7:"mission";i:3;s:4:"logo";i:4;s:4:"name";i:5;s:17:"node_user_picture";i:6;s:6:"search";i:7;s:6:"slogan";i:8;s:13:"primary_links";i:9;s:15:"secondary_links";}s:11:"stylesheets";a:1:{s:3:"all";a:1:{s:9:"style.css";s:27:"themes/pushbutton/style.css";}}s:7:"scripts";a:1:{s:9:"script.js";s:27:"themes/pushbutton/script.js";}s:10:"screenshot";s:32:"themes/pushbutton/screenshot.png";s:3:"php";s:5:"4.3.5";}',
))
->execute();
$connection->schema()->createTable('term_data', array(
  'fields' => array(
    'tid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
    'vid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'name' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'description' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'big',
    ),
    'weight' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'tid',
  ),
  'indexes' => array(
    'taxonomy_tree' => array(
      'vid',
      'weight',
      'name',
    ),
    'vid_name' => array(
      'vid',
      'name',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('term_hierarchy', array(
  'fields' => array(
    'tid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'parent' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
  ),
  'primary key' => array(
    'tid',
    'parent',
  ),
  'indexes' => array(
    'parent' => array(
      'parent',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('term_node', array(
  'fields' => array(
    'nid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'vid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'tid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
  ),
  'primary key' => array(
    'tid',
    'vid',
  ),
  'indexes' => array(
    'vid' => array(
      'vid',
    ),
    'nid' => array(
      'nid',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('term_relation', array(
  'fields' => array(
    'trid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'tid1' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'tid2' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
  ),
  'primary key' => array(
    'trid',
  ),
  'unique keys' => array(
    'tid1_tid2' => array(
      'tid1',
      'tid2',
    ),
  ),
  'indexes' => array(
    'tid2' => array(
      'tid2',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('term_synonym', array(
  'fields' => array(
    'tsid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'tid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'name' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
  ),
  'primary key' => array(
    'tsid',
  ),
  'indexes' => array(
    'tid' => array(
      'tid',
    ),
    'name_tid' => array(
      'name',
      'tid',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('url_alias', array(
  'fields' => array(
    'pid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
    'src' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '128',
      'default' => '',
    ),
    'dst' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '128',
      'default' => '',
    ),
    'language' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '12',
      'default' => '',
    ),
  ),
  'primary key' => array(
    'pid',
  ),
  'unique keys' => array(
    'dst_language_pid' => array(
      'dst',
      'language',
      'pid',
    ),
  ),
  'indexes' => array(
    'src_language_pid' => array(
      'src',
      'language',
      'pid',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('users', array(
  'fields' => array(
    'uid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
    'name' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '60',
      'default' => '',
    ),
    'pass' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '32',
      'default' => '',
    ),
    'mail' => array(
      'type' => 'varchar',
      'not null' => FALSE,
      'length' => '64',
      'default' => '',
    ),
    'mode' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
    'sort' => array(
      'type' => 'int',
      'not null' => FALSE,
      'size' => 'tiny',
      'default' => '0',
    ),
    'threshold' => array(
      'type' => 'int',
      'not null' => FALSE,
      'size' => 'tiny',
      'default' => '0',
    ),
    'theme' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'signature' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'signature_format' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'small',
      'default' => '0',
    ),
    'created' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'access' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'login' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'status' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
    'timezone' => array(
      'type' => 'varchar',
      'not null' => FALSE,
      'length' => '8',
    ),
    'language' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '12',
      'default' => '',
    ),
    'picture' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'init' => array(
      'type' => 'varchar',
      'not null' => FALSE,
      'length' => '64',
      'default' => '',
    ),
    'data' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'big',
    ),
  ),
  'primary key' => array(
    'uid',
  ),
  'unique keys' => array(
    'name' => array(
      'name',
    ),
  ),
  'indexes' => array(
    'access' => array(
      'access',
    ),
    'created' => array(
      'created',
    ),
    'mail' => array(
      'mail',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('users')
->fields(array(
  'uid',
  'name',
  'pass',
  'mail',
  'mode',
  'sort',
  'threshold',
  'theme',
  'signature',
  'signature_format',
  'created',
  'access',
  'login',
  'status',
  'timezone',
  'language',
  'picture',
  'init',
  'data',
))
->values(array(
  'uid' => '0',
  'name' => '',
  'pass' => '',
  'mail' => '',
  'mode' => '0',
  'sort' => '0',
  'threshold' => '0',
  'theme' => '',
  'signature' => '',
  'signature_format' => '0',
  'created' => '0',
  'access' => '0',
  'login' => '0',
  'status' => '0',
  'timezone' => NULL,
  'language' => '',
  'picture' => '',
  'init' => '',
  'data' => NULL,
))
->values(array(
  'uid' => '1',
  'name' => 'root',
  'pass' => '63a9f0ea7bb98050796b649e85481845',
  'mail' => '<EMAIL>',
  'mode' => '0',
  'sort' => '0',
  'threshold' => '0',
  'theme' => '',
  'signature' => '',
  'signature_format' => '0',
  'created' => '1689989946',
  'access' => '1689995368',
  'login' => '1689989986',
  'status' => '1',
  'timezone' => NULL,
  'language' => '',
  'picture' => '',
  'init' => '<EMAIL>',
  'data' => 'a:0:{}',
))
->execute();
$connection->schema()->createTable('users_roles', array(
  'fields' => array(
    'uid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'rid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
  ),
  'primary key' => array(
    'uid',
    'rid',
  ),
  'indexes' => array(
    'rid' => array(
      'rid',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('variable', array(
  'fields' => array(
    'name' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '128',
      'default' => '',
    ),
    'value' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'big',
    ),
  ),
  'primary key' => array(
    'name',
  ),
  'mysql_character_set' => 'utf8',
));

$connection->insert('variable')
->fields(array(
  'name',
  'value',
))
->values(array(
  'name' => 'book_allowed_types',
  'value' => 'a:1:{i:0;s:4:"book";}',
))
->values(array(
  'name' => 'book_block_mode',
  'value' => 's:10:"book pages";',
))
->values(array(
  'name' => 'book_child_type',
  'value' => 's:4:"book";',
))
->values(array(
  'name' => 'clean_url',
  'value' => 's:1:"1";',
))
->values(array(
  'name' => 'comment_page',
  'value' => 'i:0;',
))
->values(array(
  'name' => 'css_js_query_string',
  'value' => 's:20:"hLN00000000000000000";',
))
->values(array(
  'name' => 'date_default_timezone',
  'value' => 's:5:"43200";',
))
->values(array(
  'name' => 'drupal_http_request_fails',
  'value' => 'b:0;',
))
->values(array(
  'name' => 'drupal_private_key',
  'value' => 's:43:"dpTReZORjwK9386BzCjJ1n-sBsdbWPj8i--s9dCO27A";',
))
->values(array(
  'name' => 'file_directory_temp',
  'value' => 's:4:"/tmp";',
))
->values(array(
  'name' => 'filter_html_1',
  'value' => 'i:1;',
))
->values(array(
  'name' => 'install_profile',
  'value' => 's:7:"default";',
))
->values(array(
  'name' => 'install_task',
  'value' => 's:4:"done";',
))
->values(array(
  'name' => 'install_time',
  'value' => 'i:1689989986;',
))
->values(array(
  'name' => 'javascript_parsed',
  'value' => 'a:0:{}',
))
->values(array(
  'name' => 'menu_expanded',
  'value' => 'a:0:{}',
))
->values(array(
  'name' => 'menu_masks',
  'value' => 'a:18:{i:0;i:62;i:1;i:61;i:2;i:59;i:3;i:31;i:4;i:30;i:5;i:29;i:6;i:24;i:7;i:21;i:8;i:15;i:9;i:14;i:10;i:12;i:11;i:11;i:12;i:7;i:13;i:6;i:14;i:5;i:15;i:3;i:16;i:2;i:17;i:1;}',
))
->values(array(
  'name' => 'node_options_book',
  'value' => 'a:1:{i:0;s:6:"status";}',
))
->values(array(
  'name' => 'node_options_forum',
  'value' => 'a:1:{i:0;s:6:"status";}',
))
->values(array(
  'name' => 'node_options_page',
  'value' => 'a:1:{i:0;s:6:"status";}',
))
->values(array(
  'name' => 'site_mail',
  'value' => 's:16:"<EMAIL>";',
))
->values(array(
  'name' => 'site_name',
  'value' => 's:8:"drupal 6";',
))
->values(array(
  'name' => 'theme_default',
  'value' => 's:7:"garland";',
))
->values(array(
  'name' => 'theme_settings',
  'value' => 'a:1:{s:21:"toggle_node_info_page";b:0;}',
))
->values(array(
  'name' => 'user_email_verification',
  'value' => 'b:1;',
))
->execute();
$connection->schema()->createTable('vocabulary', array(
  'fields' => array(
    'vid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
      'unsigned' => TRUE,
    ),
    'name' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'description' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'big',
    ),
    'help' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'relations' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'hierarchy' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'multiple' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'required' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'tags' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'module' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'weight' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'vid',
  ),
  'indexes' => array(
    'list' => array(
      'weight',
      'name',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('vocabulary_node_types', array(
  'fields' => array(
    'vid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'type' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '32',
      'default' => '',
    ),
  ),
  'primary key' => array(
    'type',
    'vid',
  ),
  'indexes' => array(
    'vid' => array(
      'vid',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

$connection->schema()->createTable('watchdog', array(
  'fields' => array(
    'wid' => array(
      'type' => 'serial',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'uid' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
    'type' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '16',
      'default' => '',
    ),
    'message' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'big',
    ),
    'variables' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'big',
    ),
    'severity' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'tiny',
      'default' => '0',
      'unsigned' => TRUE,
    ),
    'link' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '255',
      'default' => '',
    ),
    'location' => array(
      'type' => 'text',
      'not null' => TRUE,
      'size' => 'normal',
    ),
    'referer' => array(
      'type' => 'text',
      'not null' => FALSE,
      'size' => 'normal',
    ),
    'hostname' => array(
      'type' => 'varchar',
      'not null' => TRUE,
      'length' => '128',
      'default' => '',
    ),
    'timestamp' => array(
      'type' => 'int',
      'not null' => TRUE,
      'size' => 'normal',
      'default' => '0',
    ),
  ),
  'primary key' => array(
    'wid',
  ),
  'indexes' => array(
    'type' => array(
      'type',
    ),
  ),
  'mysql_character_set' => 'utf8',
));

// Reset the SQL mode.
if ($connection->databaseType() === 'mysql') {
  $connection->query("SET sql_mode = '$sql_mode'");
}
