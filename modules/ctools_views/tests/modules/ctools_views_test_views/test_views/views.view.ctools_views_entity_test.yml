langcode: en
status: true
dependencies:
  config:
    - node.type.ctools_views
    - taxonomy.vocabulary.tags
  module:
    - datetime
    - node
    - options
    - taxonomy
    - user
id: ctools_views_entity_test
label: 'CTools Views Entity Test View'
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
core: 8.x
display:
  default:
    display_plugin: default
    id: default
    display_title: Master
    position: 0
    display_options:
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      query:
        type: views_query
        options:
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_comment: ''
          query_tags: {  }
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        type: none
        options:
          offset: 0
      style:
        type: table
      row:
        type: fields
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          entity_type: node
          entity_field: title
          alter:
            alter_text: false
            make_link: false
            absolute: false
            trim: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            html: false
          hide_empty: false
          empty_zero: false
          settings:
            link_to_entity: true
          plugin_id: field
          relationship: none
          group_type: group
          admin_label: ''
          label: Title
          exclude: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_alter_empty: true
          click_sort_column: value
          type: string
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            ctools_views: ctools_views
          entity_type: node
          entity_field: type
          plugin_id: bundle
      sorts:
        created:
          id: created
          table: node_field_data
          field: created
          order: DESC
          entity_type: node
          entity_field: created
          plugin_id: date
          relationship: none
          group_type: group
          admin_label: ''
          exposed: false
          expose:
            label: ''
          granularity: second
      title: 'CTools Views Entity Test View'
      header: {  }
      footer: {  }
      empty: {  }
      relationships: {  }
      arguments: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  block_filter_auto:
    display_plugin: block
    id: block_filter_auto
    display_title: 'Taxonomy autocomplete filter'
    position: 2
    display_options:
      display_extenders: {  }
      display_description: ''
      title: 'Taxonomy filter'
      defaults:
        title: false
        filters: false
        filter_groups: false
      filters:
        status:
          value: true
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            ctools_views: ctools_views
          entity_type: node
          entity_field: type
          plugin_id: bundle
          group: 1
        field_ctools_views_tags_target_id:
          id: field_ctools_views_tags_target_id
          table: node__field_ctools_views_tags
          field: field_ctools_views_tags_target_id
          relationship: none
          group_type: group
          admin_label: ''
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_ctools_views_tags_target_id_op
            label: 'Tags (field_ctools_views_tags)'
            description: ''
            use_operator: false
            operator: field_ctools_views_tags_target_id_op
            identifier: field_ctools_views_tags_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator1: '0'
              administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          type: textfield
          limit: true
          vid: tags
          hierarchy: false
          error_message: true
          plugin_id: taxonomy_index_tid
      filter_groups:
        operator: AND
        groups:
          1: AND
      allow:
        configure_filters: configure_filters
        items_per_page: false
        offset: '0'
        pager: '0'
        hide_fields: '0'
        sort_fields: '0'
        disable_filters: '0'
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - user
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  block_filter_date:
    display_plugin: block
    id: block_filter_date
    display_title: 'Date filter'
    position: 4
    display_options:
      display_extenders: {  }
      display_description: ''
      title: 'Date filter'
      defaults:
        title: false
        filters: false
        filter_groups: false
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            ctools_views: ctools_views
          entity_type: node
          entity_field: type
          plugin_id: bundle
          group: 1
        field_ctools_views_date_value:
          id: field_ctools_views_date_value
          table: node__field_ctools_views_date
          field: field_ctools_views_date_value
          relationship: none
          group_type: group
          admin_label: ''
          operator: between
          group: 1
          exposed: true
          expose:
            operator_id: field_ctools_views_date_value_op
            label: 'CTools Views Date (field_ctools_views_date)'
            description: ''
            use_operator: false
            operator: field_ctools_views_date_value_op
            identifier: field_ctools_views_date_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator1: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          plugin_id: datetime
        field_ctools_views_date_value_greater:
          id: field_ctools_views_date_value_greater
          table: node__field_ctools_views_date
          field: field_ctools_views_date_value
          relationship: none
          group_type: group
          admin_label: ''
          operator: '>'
          group: 1
          exposed: true
          expose:
            operator_id: field_ctools_views_date_value_greater_op
            label: 'CTools Views Date (field_ctools_views_date)'
            description: ''
            use_operator: false
            operator: field_ctools_views_date_value_greater_op
            identifier: field_ctools_views_date_value_greater
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator1: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          plugin_id: datetime
      filter_groups:
        operator: AND
        groups:
          1: AND
      allow:
        configure_filters: configure_filters
        items_per_page: false
        offset: '0'
        pager: '0'
        hide_fields: '0'
        sort_fields: '0'
        disable_filters: '0'
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  block_filter_tax_grouped:
    display_plugin: block
    id: block_filter_tax_grouped
    display_title: 'Taxonomy filter (grouped)'
    position: 2
    display_options:
      display_extenders: {  }
      display_description: ''
      title: 'Taxonomy filter (grouped)'
      defaults:
        title: false
        filters: false
        filter_groups: false
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            ctools_views: ctools_views
          entity_type: node
          entity_field: type
          plugin_id: bundle
          group: 1
        field_ctools_views_tags_target_id:
          id: field_ctools_views_tags_target_id
          table: node__field_ctools_views_tags
          field: field_ctools_views_tags_target_id
          relationship: none
          group_type: group
          admin_label: ''
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_ctools_views_tags_target_id_op
            label: 'Tags (field_ctools_views_tags)'
            description: ''
            use_operator: false
            operator: field_ctools_views_tags_target_id_op
            identifier: field_ctools_views_tags_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator1: '0'
              administrator: '0'
            reduce: false
          is_grouped: true
          group_info:
            label: 'Tags (field_ctools_views_tags)'
            description: ''
            identifier: field_ctools_views_tags_target_id
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items:
              1:
                title: 'Tag 2 or 3'
                operator: or
                value:
                  2: '2'
                  3: '3'
              2:
                title: 'Not tag 3'
                operator: not
                value:
                  3: '3'
              3:
                title: 'Tag 1'
                operator: or
                value:
                  1: '1'
          reduce_duplicates: false
          type: select
          limit: true
          vid: tags
          hierarchy: false
          error_message: true
          plugin_id: taxonomy_index_tid
      filter_groups:
        operator: AND
        groups:
          1: AND
      allow:
        configure_filters: configure_filters
        items_per_page: false
        offset: '0'
        pager: '0'
        hide_fields: '0'
        sort_fields: '0'
        disable_filters: '0'
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - user
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  block_filter_list:
    display_plugin: block
    id: block_filter_list
    display_title: 'List filter'
    position: 3
    display_options:
      display_extenders: {  }
      display_description: ''
      title: 'List filter'
      defaults:
        title: false
        filters: false
        filter_groups: false
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            ctools_views: ctools_views
          entity_type: node
          entity_field: type
          plugin_id: bundle
        field_ctools_views_list_value:
          id: field_ctools_views_list_value
          table: node__field_ctools_views_list
          field: field_ctools_views_list_value
          relationship: none
          group_type: group
          admin_label: ''
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_ctools_views_list_value_op
            label: 'Ctools Views List (field_ctools_views_list)'
            description: ''
            use_operator: false
            operator: field_ctools_views_list_value_op
            identifier: field_ctools_views_list_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator1: '0'
              administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          plugin_id: list_field
      filter_groups:
        operator: AND
        groups:
          1: AND
      allow:
        configure_filters: configure_filters
        items_per_page: false
        offset: '0'
        pager: '0'
        hide_fields: '0'
        sort_fields: '0'
        disable_filters: '0'
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  block_filter_tax:
    display_plugin: block
    id: block_filter_tax
    display_title: 'Taxonomy filter'
    position: 2
    display_options:
      display_extenders: {  }
      display_description: ''
      title: 'Taxonomy filter'
      defaults:
        title: false
        filters: false
        filter_groups: false
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            ctools_views: ctools_views
          entity_type: node
          entity_field: type
          plugin_id: bundle
          group: 1
        field_ctools_views_tags_target_id:
          id: field_ctools_views_tags_target_id
          table: node__field_ctools_views_tags
          field: field_ctools_views_tags_target_id
          relationship: none
          group_type: group
          admin_label: ''
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_ctools_views_tags_target_id_op
            label: 'Tags (field_ctools_views_tags)'
            description: ''
            use_operator: false
            operator: field_ctools_views_tags_target_id_op
            identifier: field_ctools_views_tags_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator1: '0'
              administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          type: select
          limit: true
          vid: tags
          hierarchy: false
          error_message: true
          plugin_id: taxonomy_index_tid
      filter_groups:
        operator: AND
        groups:
          1: AND
      allow:
        configure_filters: configure_filters
        items_per_page: false
        offset: '0'
        pager: '0'
        hide_fields: '0'
        sort_fields: '0'
        disable_filters: '0'
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - user
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  block_filter_text:
    display_plugin: block
    id: block_filter_text
    display_title: 'Textfield filter'
    position: 1
    display_options:
      display_extenders: {  }
      display_description: ''
      title: 'Textfield filter'
      defaults:
        title: false
        filters: false
        filter_groups: false
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            ctools_views: ctools_views
          entity_type: node
          entity_field: type
          plugin_id: bundle
        field_ctools_views_text_value:
          id: field_ctools_views_text_value
          table: node__field_ctools_views_text
          field: field_ctools_views_text_value
          relationship: none
          group_type: group
          admin_label: ''
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: field_ctools_views_text_value_op
            label: 'Text (field_ctools_views_text)'
            description: ''
            use_operator: true
            operator: field_ctools_views_text_value_op
            identifier: field_ctools_views_text_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator1: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          plugin_id: string
      filter_groups:
        operator: AND
        groups:
          1: AND
      allow:
        configure_filters: configure_filters
        items_per_page: false
        offset: '0'
        pager: '0'
        hide_fields: '0'
        sort_fields: '0'
        disable_filters: '0'
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  block_filter_text_grouped:
    display_plugin: block
    id: block_filter_text_grouped
    display_title: 'Textfield filter (grouped)'
    position: 1
    display_options:
      display_extenders: {  }
      display_description: ''
      title: 'Textfield filter (grouped)'
      defaults:
        title: false
        filters: false
        filter_groups: false
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            ctools_views: ctools_views
          entity_type: node
          entity_field: type
          plugin_id: bundle
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: title_op
            label: Title
            description: ''
            use_operator: false
            operator: title_op
            identifier: title
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator1: '0'
              administrator: '0'
          is_grouped: true
          group_info:
            label: Title
            description: ''
            identifier: title
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items:
              1:
                title: 'Contains "entity 2"'
                operator: contains
                value: 'entity 2'
              2:
                title: 'Does not contain "entity 2"'
                operator: not
                value: 'entity 3'
              3:
                title: 'Equals "Test entity 1"'
                operator: '='
                value: 'Test entity 1'
          plugin_id: string
      filter_groups:
        operator: AND
        groups:
          1: AND
      allow:
        configure_filters: configure_filters
        items_per_page: false
        offset: '0'
        pager: '0'
        hide_fields: '0'
        sort_fields: '0'
        disable_filters: '0'
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
