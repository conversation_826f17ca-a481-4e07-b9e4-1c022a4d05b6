<?php

/**
 * @file
 * Contains hook implementations for field_layout_test.
 */

/**
 * Implements hook_layout_alter().
 */
function field_layout_test_layout_alter(&$definitions) {
  /** @var \Drupal\Core\Layout\LayoutDefinition[] $definitions */
  if (\Drupal::state()->get('field_layout_test.alter_regions') && isset($definitions['layout_onecol'])) {
    $definitions['layout_onecol']->setRegions(['foo' => ['label' => 'Foo']]);
  }
}
