<?php

/**
 * @file
 * Provides a webform element for a selecting an image.
 */

use <PERSON><PERSON>al\Core\Url;

/**
 * Implements hook_webform_help_info().
 */
function webform_image_select_webform_help_info() {
  $help = [];
  $help['config_image_select_images'] = [
    'group' => 'configuration',
    'title' => t('Configuration: Images'),
    'content' => t('The <strong>Images configuration</strong> page lists reusable images for the image select element.'),
    'routes' => [
      // @see /admin/structure/webform/options/images
      'entity.webform_image_select_images.collection',
    ],
  ];
  return $help;
}

/**
 * Implements hook_webform_libraries_info().
 */
function webform_image_select_webform_libraries_info() {
  $libraries = [];
  $libraries['jquery.image-picker'] = [
    'title' => t('jQuery: Image Picker'),
    'description' => t('A simple jQuery plugin that transforms a select element into a more user friendly graphical interface.'),
    'notes' => t('Image Picker is used by the Image select element.'),
    'homepage_url' => Url::fromUri('https://rvera.github.io/image-picker/'),
    'download_url' => Url::fromUri('https://github.com/rvera/image-picker/archive/refs/tags/0.3.1.zip'),
    'version' => '0.3.1',
    'elements' => ['webform_image_select'],
    'optional' => FALSE,
    'license' => 'MIT',
  ];
  return $libraries;
}

/**
 * Implements hook_menu_local_tasks_alter().
 */
function webform_image_select_menu_local_tasks_alter(&$data, $route_name) {
  // Change config entities 'Translate *' tab to be just label 'Translate'.
  if (isset($data['tabs'][0]["config_translation.local_tasks:entity.webform_image_select_images.config_translation_overview"]['#link']['title'])) {
    $data['tabs'][0]["config_translation.local_tasks:entity.webform_image_select_images.config_translation_overview"]['#link']['title'] = t('Translate');
  }
}
