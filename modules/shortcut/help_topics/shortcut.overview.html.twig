---
label: 'Creating and using shortcut administrative links'
related:
  - core.ui_components
---
<h2>{% trans %}Goal{% endtrans %}</h2>
<p>{% trans %}Create, view, and use a set of shortcuts to access administrative pages.{% endtrans %}</p>
<h2>{% trans %}What are shortcuts?{% endtrans %}</h2>
<p>{% trans %}<em>Shortcuts</em> are quick links to administrative pages; they are managed by the core Shortcut module. A site can have one or more <em>shortcut sets</em>, which can be shared by one or more users (by default, there is only one set shared by all users); each set contains a limited number of shortcuts. Users need <em>Use shortcuts</em> permission to view shortcuts; <em>Edit current shortcut set</em> permission to add, delete, or edit the shortcuts in the set assigned to them; and <em>Select any shortcut set</em> permission to select a different shortcut set when editing their user profile. There is also an <em>Administer shortcuts</em> permission, which allows an administrator to do any of these actions, as well as select shortcut sets for other users.{% endtrans %}</p>
<h2>{% trans %}Steps{% endtrans %}</h2>
<ol>
  <li>{% trans %}Make sure that the core Shortcut module is installed, and that you have a role with <em>Edit current shortcut set</em> or <em>Administer shortcuts</em> permission. Also, make sure that a toolbar module is installed (either the core Toolbar module or a contributed module replacement).{% endtrans %}</li>
  <li>{% trans %}Navigate to an administrative page that you want in your shortcut list.{% endtrans %}</li>
  <li>{% trans %}Click the shortcut link to add the page to your shortcut list -- in the core Claro administrative theme, the link looks like a star, and is displayed next to the page title. However, if the page is already in your shortcut set, clicking the shortcut link will remove it from your shortcut set.{% endtrans %}</li>
  <li>{% trans %}Repeat until all the desired links have been added to your shortcut set.{% endtrans %}</li>
  <li>{% trans %}Click <em>Shortcuts</em> in the toolbar to display your shortcuts, and verify that the list is complete.{% endtrans %}</li>
  <li>{% trans %}Optionally, click <em>Edit shortcuts</em> at the right end of the shortcut list (left end in right-to-left languages), to remove links or change their order.{% endtrans %}</li>
  <li>{% trans %}Click any link in the shortcut bar to go directly to the administrative page.{% endtrans %}</li>
</ol>
