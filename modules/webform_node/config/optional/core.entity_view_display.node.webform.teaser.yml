langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.webform.body
    - field.field.node.webform.webform
    - node.type.webform
  module:
    - text
    - user
id: node.webform.teaser
targetEntityType: node
bundle: webform
mode: teaser
content:
  body:
    label: hidden
    type: text_summary_or_trimmed
    weight: 101
    settings:
      trim_length: 600
    third_party_settings: {  }
  links:
    weight: 100
    settings: {  }
    third_party_settings: {  }
hidden:
  webform: true
