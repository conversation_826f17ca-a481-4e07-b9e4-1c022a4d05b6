<?php

namespace Dr<PERSON>al\config_log_views\Plugin\views\field;

use <PERSON><PERSON><PERSON>\Core\Config\ConfigFactoryInterface;
use <PERSON><PERSON>al\views\Attribute\ViewsField;
use Dr<PERSON>al\views\Plugin\views\field\FieldPluginBase;
use <PERSON><PERSON>al\views\ResultRow;
use Dr<PERSON>al\views\Plugin\views\display\DisplayPluginBase;
use Drupal\views\ViewExecutable;
use Drupal\Core\Diff\DiffFormatter;
use Drupal\Component\Diff\Diff;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * A handler to provide a field that is completely custom by the administrator.
 *
 * @ingroup views_field_handlers
 */
#[ViewsField("config_log_diff")]
class ConfigLogDiff extends FieldPluginBase {

  /**
   * The current display.
   *
   * @var string
   *   The current display of the view.
   */
  protected $currentDisplay;

  /**
   * The constructor.
   *
   * @param array $configuration
   *   The configuration.
   * @param mixed $plugin_id
   *   The plugin id.
   * @param mixed $plugin_definition
   *   The plugin definition.
   * @param \Drupal\Core\Diff\DiffFormatter $diffFormatter
   *   The diff formatter service.
   * @param \Drupal\Core\Config\ConfigFactoryInterface $configFactory
   *   The configuration factory.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    protected DiffFormatter $diffFormatter,
    protected ConfigFactoryInterface $configFactory,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('diff.formatter'),
      $container->get('config.factory')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function init(ViewExecutable $view, DisplayPluginBase $display, ?array &$options = NULL) {
    parent::init($view, $display, $options);
    $this->currentDisplay = $view->current_display;
  }

  /**
   * {@inheritdoc}
   */
  public function clickSort($order) {}

  /**
   * {@inheritdoc}
   */
  public function usesGroupBy() {
    return FALSE;
  }

  /**
   * {@inheritdoc}
   */
  public function query() {
    // Do nothing -- to override the parent query.
  }

  /**
   * {@inheritdoc}
   */
  protected function defineOptions() {
    $options = parent::defineOptions();
    // First check whether the field should be hidden if the
    // value(hide_alter_empty = TRUE) /the rewrite is empty
    // (hide_alter_empty = FALSE).
    $options['hide_alter_empty'] = ['default' => FALSE];
    return $options;
  }

  /**
   * {@inheritdoc}
   */
  public function render(ResultRow $values) {
    $config = $this->configFactory->get('config_log.settings');
    $leadingContextLines = $config->get('leading_context_lines') ?: 0;
    $trailingContextLines = $config->get('trailing_context_lines') ?: 0;

    $length = 0;
    $newValue = ($values->config_log_data) ? explode("\n", $values->config_log_data) : [];
    $originalValue = ($values->config_log_originaldata) ? explode("\n", $values->config_log_originaldata) : [];

    $diff = new Diff($originalValue, $newValue);
    $this->diffFormatter->show_header = FALSE;
    $this->diffFormatter->leading_context_lines = $leadingContextLines;
    $this->diffFormatter->trailing_context_lines = $trailingContextLines;
    $output = $this->diffFormatter->format($diff);
    if ($output) {
      // Add the CSS for the inline diff.
      $form['#attached']['library'][] = 'system/diff';
      // Lets check the length of the difference.
      if (is_array($output) && is_countable($output)) {
        $length = count($output);
      }
      // Collapse the field(s) if length is more than x rows.
      if ($length > 7) {
        $form['diff'] = [
          '#type' => 'details',
          '#title' => t('Text too long to display, expand for a full view'),
          '#open' => FALSE,
        ];
        $form['diff']['details'] = [
          '#type' => 'table',
          '#attributes' => [
            'class' => ['diff'],
          ],
          '#header' => [
            ['data' => t('From'), 'colspan' => '2'],
            ['data' => t('To'), 'colspan' => '2'],
          ],
          '#rows' => $output,
        ];
      }
      else {
        $form['diff'] = [
          '#type' => 'table',
          '#attributes' => [
            'class' => ['diff'],
          ],
          '#header' => [
            ['data' => t('From'), 'colspan' => '2'],
            ['data' => t('To'), 'colspan' => '2'],
          ],
          '#rows' => $output,
        ];
      }

      return $form;
    }
    else {
      $output = t('No change');
    }

    return $output;
  }

}
