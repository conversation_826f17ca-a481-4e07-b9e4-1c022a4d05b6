/*
 * DO NOT EDIT THIS FILE.
 * See the following change record for more information,
 * https://www.drupal.org/node/3084859
 * @preserve
 */

/**
 * @file
 * Layout Builder styling for off-canvas UI.
 */

#drupal-off-canvas-wrapper .layout-selection {
  margin: 0;
  padding: 0;
  list-style: none;
}

#drupal-off-canvas-wrapper .layout-selection li {
  position: relative; /* Anchor throbber. */
  padding: calc(0.25 * var(--off-canvas-vertical-spacing-unit));
  border-bottom: 1px solid var(--off-canvas-border-color);
}

#drupal-off-canvas-wrapper .layout-selection li:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

/* Horizontally align icon and text. */

#drupal-off-canvas-wrapper .layout-selection a {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.625rem;
  padding: 0.625rem;
}

/*
   * This is the styling of the SVG within the layout selection list.
   */

#drupal-off-canvas-wrapper .layout-icon__region {
  fill: var(--off-canvas-text-color);
  stroke: transparent;
}

@media (forced-colors: active) {
  #drupal-off-canvas-wrapper .layout-icon__region {
    fill: canvastext;
  }
}

#drupal-off-canvas-wrapper .inline-block-create-button {
  --icon-size: 1rem;

  position: relative; /* Anchor pseudo-element. */
  display: block;
  padding: 1.5rem;
  padding-inline-start: calc(2 * var(--off-canvas-padding) + var(--icon-size) / 2); /* Room for icon */
  border-bottom: 1px solid #333;
  font-size: 1rem;

  /* Plus icon. */
}

#drupal-off-canvas-wrapper .inline-block-create-button::before {
  position: absolute;
  top: 50%;
  left: var(--off-canvas-padding);
  width: var(--icon-size);
  height: var(--icon-size);
  content: "";
  transform: translateY(-50%);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16px' height='16px'%3e%3cpath fill='%23bebebe' d='M0.656,9.023c0,0.274,0.224,0.5,0.499,0.5l4.853,0.001c0.274-0.001,0.501,0.226,0.5,0.5l0.001,4.853 c-0.001,0.273,0.227,0.5,0.501,0.5l1.995-0.009c0.273-0.003,0.497-0.229,0.5-0.503l0.002-4.806c0-0.272,0.228-0.5,0.499-0.502 l4.831-0.021c0.271-0.005,0.497-0.23,0.501-0.502l0.008-1.998c0-0.276-0.225-0.5-0.499-0.5l-4.852,0c-0.275,0-0.502-0.228-0.501-0.5 L9.493,1.184c0-0.275-0.225-0.499-0.5-0.499L6.997,0.693C6.722,0.694,6.496,0.92,6.495,1.195L6.476,6.026 c-0.001,0.274-0.227,0.5-0.501,0.5L1.167,6.525C0.892,6.526,0.665,6.752,0.665,7.026L0.656,9.023z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-size: contain;
}

@media (forced-colors: active) {
  #drupal-off-canvas-wrapper .inline-block-create-button::before {
    background: linktext;
    mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16px' height='16px'%3e%3cpath fill='%23bebebe' d='M0.656,9.023c0,0.274,0.224,0.5,0.499,0.5l4.853,0.001c0.274-0.001,0.501,0.226,0.5,0.5l0.001,4.853 c-0.001,0.273,0.227,0.5,0.501,0.5l1.995-0.009c0.273-0.003,0.497-0.229,0.5-0.503l0.002-4.806c0-0.272,0.228-0.5,0.499-0.502 l4.831-0.021c0.271-0.005,0.497-0.23,0.501-0.502l0.008-1.998c0-0.276-0.225-0.5-0.499-0.5l-4.852,0c-0.275,0-0.502-0.228-0.501-0.5 L9.493,1.184c0-0.275-0.225-0.499-0.5-0.499L6.997,0.693C6.722,0.694,6.496,0.92,6.495,1.195L6.476,6.026 c-0.001,0.274-0.227,0.5-0.501,0.5L1.167,6.525C0.892,6.526,0.665,6.752,0.665,7.026L0.656,9.023z'/%3e%3c/svg%3e");
    mask-repeat: no-repeat;
    mask-size: contain;
  }
}

#drupal-off-canvas-wrapper .inline-block-create-button,
#drupal-off-canvas-wrapper .inline-block-list__item {
  margin: 0 calc(-1 * var(--off-canvas-padding));
  color: var(--off-canvas-text-color);
}

#drupal-off-canvas-wrapper .inline-block-create-button:hover,
#drupal-off-canvas-wrapper .inline-block-list__item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

#drupal-off-canvas-wrapper .inline-block-create-button:focus,
#drupal-off-canvas-wrapper .inline-block-list__item:focus {
  outline-offset: -4px; /* Prevent outline from being cut off. */
}

#drupal-off-canvas-wrapper .inline-block-list {
  margin: 0 0 calc(2 * var(--off-canvas-vertical-spacing-unit));
  padding: 0;
  list-style: none;
}

#drupal-off-canvas-wrapper .inline-block-list li {
  position: relative; /* Anchor throbber. */
  margin: 0;
  padding: calc(0.25 * var(--off-canvas-vertical-spacing-unit)) 0;
}

#drupal-off-canvas-wrapper .inline-block-list li:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

/* This is the <a> tag. */

#drupal-off-canvas-wrapper .inline-block-list__item {
  display: block;
  flex-grow: 1;
  padding: calc(2 * var(--off-canvas-vertical-spacing-unit)) var(--off-canvas-padding);
  border-bottom: 1px solid var(--off-canvas-border-color);
}

/* Highlight the active block in the Move Block dialog. */

#drupal-off-canvas-wrapper .layout-builder-components-table__block-label--current {
  padding-left: 1.0625rem;
  border-left: solid 5px;
}
