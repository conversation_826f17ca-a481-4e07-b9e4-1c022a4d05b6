<?php

namespace Drupal\layout_builder\Attribute;

use <PERSON><PERSON><PERSON>\Component\Plugin\Attribute\Plugin;
use <PERSON><PERSON><PERSON>\layout_builder\SectionStorage\SectionStorageDefinition;

/**
 * Defines a SectionStorage attribute.
 *
 * Plugin Namespace: Plugin\SectionStorage
 *
 * @see \Drupal\layout_builder\SectionStorage\SectionStorageManager
 * @see plugin_api
 */
#[\Attribute(\Attribute::TARGET_CLASS)]
class SectionStorage extends Plugin {

  /**
   * Constructs a SectionStorage attribute.
   *
   * @param string $id
   *   The plugin ID.
   * @param int $weight
   *   (optional) The plugin weight.
   *   When an entity with layout is rendered, section storage plugins are
   *   checked, in order of their weight, to determine which one should be used
   *   to render the layout.
   * @param \Drupal\Component\Plugin\Context\ContextDefinitionInterface[] $context_definitions
   *   (optional) Any required context definitions.
   *   When an entity with layout is rendered, all section storage plugins which
   *   match a particular set of contexts are checked, in order of their weight,
   *   to determine which plugin should be used to render the layout.
   *   @see \Drupal\layout_builder\SectionStorage\SectionStorageManagerInterface::findByContext()
   * @param bool $handles_permission_check
   *   (optional) Indicates that this section storage handles its own
   *   permission checking. If FALSE, the 'configure any layout' permission
   *   will be required during routing access. If TRUE, Layout Builder will
   *   not enforce any access restrictions for the storage, so the section
   *   storage's implementation of access() must perform the access checking itself.
   * @param string|null $deriver
   *   (optional) The deriver class.
   */
  public function __construct(
    public readonly string $id,
    public readonly int $weight = 0,
    public readonly array $context_definitions = [],
    public readonly bool $handles_permission_check = FALSE,
    public readonly ?string $deriver = NULL,
  ) {}

  /**
   * {@inheritdoc}
   */
  public function get(): SectionStorageDefinition {
    return new SectionStorageDefinition([
      'id' => $this->id,
      'class' => $this->class,
      'weight' => $this->weight,
      'context_definitions' => $this->context_definitions,
      'handles_permission_check' => $this->handles_permission_check,
    ]);
  }

}
