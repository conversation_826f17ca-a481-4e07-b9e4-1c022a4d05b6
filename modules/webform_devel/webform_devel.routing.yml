entity.webform.fapi_export_form:
  path: '/admin/structure/webform/manage/{webform}/fapi'
  defaults:
    _entity_form: 'webform.fapi_export'
    _title_callback: '\Drupal\webform\Controller\WebformEntityController::title'
  requirements:
    _permission: 'access devel information'

entity.webform.fapi_test_form:
  path: '/admin/structure/webform/manage/{webform}/fapi/test'
  defaults:
    _entity_form: 'webform.fapi_test'
    _title_callback: '\Drupal\webform\Controller\WebformEntityController::title'
  requirements:
    _permission: 'access devel information'

entity.webform.api_form:
  path: '/webform/{webform}/api'
  defaults:
    _form: '\Drupal\webform_devel\Form\WebformDevelSubmissionApiForm'
    _title_callback: '\Drupal\webform\Controller\WebformTestController::title'
  options:
    parameters:
      webform:
        type: 'entity:webform'
    _admin_route: TRUE
  requirements:
    _entity_access: 'webform.update'
    _permission: 'access devel information'

entity.node.webform.api_form:
  path: '/node/{node}/webform/api'
  defaults:
    _form: '\Drupal\webform_devel\Form\WebformDevelSubmissionApiForm'
    _title_callback: '\Drupal\Core\Entity\Controller\EntityController::title'
    operation: webform_submission_view
    entity_access: 'webform.update'
  options:
    parameters:
      node:
        type: 'entity:node'
    _admin_route: TRUE
  requirements:
    _permission: 'access devel information'
    _custom_access: '\Drupal\webform_node\Access\WebformNodeAccess::checkWebformAccess'
