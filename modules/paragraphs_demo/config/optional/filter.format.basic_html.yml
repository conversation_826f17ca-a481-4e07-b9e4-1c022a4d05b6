langcode: en
status: true
dependencies: { }
name: 'Basic HTML'
format: basic_html
weight: 0
filters:
  filter_html:
    id: filter_html
    provider: filter
    status: true
    weight: -10
    settings:
      allowed_html: '<a href hreflang> <em> <strong> <cite> <blockquote cite> <code> <ul type> <ol start type> <li> <dl> <dt> <dd> <h2 id> <h3 id> <h4 id> <h5 id> <h6 id> <p> <br> <span> <img src alt height width data-entity-type data-entity-uuid data-align data-caption>'
      filter_html_help: false
      filter_html_nofollow: false
  filter_align:
    id: filter_align
    provider: filter
    status: true
    weight: 7
    settings: {  }
  filter_caption:
    id: filter_caption
    provider: filter
    status: true
    weight: 8
    settings: {  }
  filter_html_image_secure:
    id: filter_html_image_secure
    provider: filter
    status: true
    weight: 9
    settings: {  }
