langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.nested_paragraph.field_paragraphs_demo
    - paragraphs.paragraphs_type.nested_paragraph
  module:
    - paragraphs
id: paragraph.nested_paragraph.default
targetEntityType: paragraph
bundle: nested_paragraph
mode: default
content:
  field_paragraphs_demo:
    weight: 0
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: open
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
    third_party_settings: {  }
    type: paragraphs
hidden:
  created: true
  uid: true
