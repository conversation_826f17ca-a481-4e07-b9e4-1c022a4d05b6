langcode: en
status: true
dependencies:
  config:
    - field.field.node.paragraphed_content_demo.field_paragraphs_demo
    - node.type.paragraphed_content_demo
  module:
    - entity_reference_revisions
    - user
id: node.paragraphed_content_demo.default
targetEntityType: node
bundle: paragraphed_content_demo
mode: default
content:
  body:
    type: text_default
    weight: 101
    settings: {  }
    third_party_settings: {  }
    label: hidden
  field_paragraphs_demo:
    type: entity_reference_revisions_entity_view
    weight: 102
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    label: hidden
  links:
    weight: 100
    settings: {  }
    third_party_settings: {  }
hidden:
  langcode: true
