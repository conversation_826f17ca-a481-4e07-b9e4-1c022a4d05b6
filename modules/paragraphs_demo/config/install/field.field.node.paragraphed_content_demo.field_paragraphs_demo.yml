langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_paragraphs_demo
    - node.type.paragraphed_content_demo
  module:
    - entity_reference_revisions
id: node.paragraphed_content_demo.field_paragraphs_demo
field_name: field_paragraphs_demo
entity_type: node
bundle: paragraphed_content_demo
label: Paragraphs
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles: null
    target_bundles_drag_drop:
      image_text:
        enabled: false
        weight: -13
      images:
        enabled: false
        weight: -12
      nested_paragraph:
        enabled: false
        weight: -11
      text:
        enabled: false
        weight: -10
      text_image:
        enabled: false
        weight: -9
      user:
        enabled: false
        weight: -8
field_type: entity_reference_revisions
