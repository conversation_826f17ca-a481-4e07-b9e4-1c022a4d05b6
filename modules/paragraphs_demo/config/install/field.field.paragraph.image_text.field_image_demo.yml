langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_image_demo
    - paragraphs.paragraphs_type.image_text
  module:
    - image
id: paragraph.image_text.field_image_demo
field_name: field_image_demo
entity_type: paragraph
bundle: image_text
label: Image
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  file_directory: ''
  file_extensions: 'png gif jpg jpeg'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: false
  alt_field_required: false
  title_field: false
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
  handler: default
field_type: image
