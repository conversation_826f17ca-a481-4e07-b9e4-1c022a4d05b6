langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_text_demo
    - paragraphs.paragraphs_type.text_image
  module:
    - text
id: paragraph.text_image.field_text_demo
field_name: field_text_demo
entity_type: paragraph
bundle: text_image
label: Text
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: text_long
