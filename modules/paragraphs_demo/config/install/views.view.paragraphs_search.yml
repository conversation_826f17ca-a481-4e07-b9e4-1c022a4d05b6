langcode: en
status: true
dependencies:
  config:
    - search_api.index.paragraphs_demo_index
  module:
    - search_api
    - text
id: paragraphs_search
label: 'Paragraphs search'
module: views
description: ''
tag: ''
base_table: search_api_index_paragraphs_demo_index
base_field: search_api_id
core: 8.x
display:
  default:
    display_plugin: default
    id: default
    display_title: Master
    position: 0
    display_options:
      access:
        type: none
        options: {  }
      cache:
        type: tag
        options: {  }
      query:
        type: views_query
        options: {  }
      exposed_form:
        type: input_required
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
          text_input_required: 'Add a text and click on Apply to see results.'
          text_input_required_format: basic_html
      pager:
        type: mini
        options:
          items_per_page: 10
          offset: 0
          id: 0
          total_pages: null
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          tags:
            previous: ‹‹
            next: ››
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      row:
        type: search_api
        options:
          view_modes:
            'entity:node':
              article: teaser
              page: teaser
              paragraphed_content_demo: teaser
      fields:
        text:
          table: search_api_index_paragraphs_demo_index
          field: text
          id: text
          entity_type: null
          entity_field: null
          plugin_id: search_api_field
          relationship: none
          group_type: group
          admin_label: ''
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            multi_separator: ', '
      filters:
        search_api_fulltext:
          id: search_api_fulltext
          table: search_api_index_paragraphs_demo_index
          field: search_api_fulltext
          relationship: none
          group_type: group
          admin_label: ''
          operator: and
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: search_api_fulltext_op
            label: 'Fulltext Paragraphs demo search'
            description: ''
            use_operator: false
            operator: search_api_fulltext_op
            identifier: search_api_fulltext
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          min_length: null
          fields:
            field_text_demo: field_text_demo
            field_text_demo_1: field_text_demo_1
          plugin_id: search_api_fulltext
      sorts: {  }
      header: {  }
      footer: {  }
      empty: {  }
      relationships: {  }
      arguments: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_interface'
        - url.query_args
      tags: {  }
  page_1:
    display_plugin: page
    id: page_1
    display_title: Page
    position: 1
    display_options:
      display_extenders: {  }
      path: paragraphs_search
      menu:
        type: none
        title: Paragraphs_demo
        description: ''
        expanded: false
        parent: ''
        weight: 0
        context: '0'
        menu_name: main
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_interface'
        - url.query_args
      tags: {  }
