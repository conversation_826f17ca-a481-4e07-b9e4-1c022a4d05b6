langcode: en
status: true
dependencies:
  module:
    - image
    - paragraphs
id: paragraph.field_image_demo
field_name: field_image_demo
entity_type: paragraph
type: image
settings:
  uri_scheme: public
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
  target_type: file
  display_field: false
  display_default: false
module: image
locked: false
cardinality: 1
translatable: true
indexes: {  }
persist_with_no_fields: false
