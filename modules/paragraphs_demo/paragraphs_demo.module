<?php

/**
 * @file
 * Contains paragraphs_demo.module
 */

use Drupal\Core\Url;
use <PERSON>upal\Core\Routing\RouteMatchInterface;
use <PERSON><PERSON>al\node\Entity\Node;
use Drupal\paragraphs\Entity\Paragraph;
use Drupal\paragraphs_library\Entity\LibraryItem;
use Drupal\workflows\Entity\Workflow;

/**
 * Implements hook_help().
 */
function paragraphs_demo_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    // Help for the paragraphs demo module.
    case 'help.page.paragraphs_demo':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Paragraphs Demo module provides several <em>Paragraphs types</em> for the <a href=":paragraphs">Paragraphs module</a>, but no separate user interface. For more information, see the <a href=":online">online documentation for the Paragraphs module</a>.', [':online' => 'https://www.drupal.org/node/2444881', ':paragraphs' => Url::fromRoute('help.page', ['name' => 'paragraphs'])->toString()]) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dt>' . t('Changing demo Paragraphs types') . '</dt>';
      $output .= '<dd>' . t('Administrators can edit the provided <em>Paragraphs types</em> on the <a href=":paragraphs">Paragraphs types page</a> if the <a href=":field_ui">Field UI</a> module is enabled. For more information on fields and entities, see the <a href=":field">Field module help page</a>.', [':paragraphs' => Url::fromRoute('entity.paragraphs_type.collection')->toString(), ':field' => Url::fromRoute('help.page', ['name' => 'field'])->toString(), ':field_ui' => (\Drupal::moduleHandler()->moduleExists('field_ui')) ? Url::fromRoute('help.page', ['name' => 'field_ui'])->toString() : '#']) . '</dd>';
      $output .= '<dt>' . t('Deleting demo Paragraphs types') . '</dt>';
      $output .= '<dd>' . t('The provided <em>demo Paragraph types</em> stay available even when the Paragraphs Demo module is uninstalled. They can be deleted individually on the <a href=":paragraphs">Paragraphs types page</a>.', [':paragraphs' => Url::fromRoute('entity.paragraphs_type.collection')->toString()]) . '</dd>';
      return $output;
    break;
  }
}

/**
 * Implements hook_preprocess_node() for paragraph node templates.
 *
 * Attach css we need for paragraph demo content.
 */
function paragraphs_demo_preprocess_node(&$variables) {
  // If more general approach is needed then implement preprocessor for
  // paragraph.html.twig.
  if ($variables['node']->getType() === 'paragraphed_content_demo') {
    $variables['#attached']['library'][] = 'paragraphs_demo/drupal.paragraphs_demo';
  }
}

/**
 * Implements hook_modules_installed().
 */
function paragraphs_demo_modules_installed($modules, $is_syncing) {
  if (in_array('paragraphs_demo', $modules, TRUE)) {

    // Create three paragraphs to structure the content.
    $paragraph = Paragraph::create([
      'type' => 'text',
      'field_text_demo' => [
        'value' => '<h2>Paragraphs is the new way of content creation!</h2>
      <p>It allows you — Site Builders — to make things cleaner so that you can give more editing power to your end-users.
      Instead of putting all their content in one WYSIWYG body field including images and videos, end-users can now choose on-the-fly between pre-defined Paragraph Types independent from one another. Paragraph Types can be anything you want from a simple text block or image to a complex and configurable slideshow.</p>',
        'format' => 'basic_html',
      ],
    ]);
    $paragraph->save();

    $paragraph2 = Paragraph::create([
      'type' => 'text',
      'field_text_demo' => [
        'value' => '<p>This demo creates some default Paragraph types from which you can easily create some content (Nested Paragraph, Text, Image + Text, Text + Image, Image and User). It also includes some basic styling and assures that the content is responsive on any device.</p>',
        'format' => 'basic_html',
      ],
    ]);
    $paragraph2->save();

    $paragraph3 = Paragraph::create([
      'type' => 'text',
      'field_text_demo' => [
        'value' => '<p>Apart from the included Paragraph types, you can create your own simply by going to Structure -> Paragraphs types.</p>',
        'format' => 'basic_html',
      ],
    ]);
    $paragraph3->save();

    $paragraph4 = Paragraph::create([
      'type' => 'text',
      'field_text_demo' => [
        'value' => '<p>A search api example can be found <a href="/paragraphs_search">here</a></p>',
        'format' => 'basic_html',
      ],
    ]);
    $paragraph4->save();

    $paragraph5 = Paragraph::create([
      'type' => 'nested_paragraph',
      'field_paragraphs_demo' => $paragraph4,
    ]);
    $paragraph5->save();

    // PARAGRAPH DEMO ITEM: library items.
    $library_text_paragraph = Paragraph::create([
      'type' => 'text',
      'field_text_demo' => [
        'value' => 'This is content from the library. We can reuse it multiple times without duplicating it.',
        'format' => 'plain_text',
      ],
    ]);
    $library_text_paragraph->save();

    $library_item = LibraryItem::create([
      'label' => 'Library item',
      'paragraphs' => [
        $library_text_paragraph,
      ],
    ]);
    $library_item->save();

    $from_library = Paragraph::create([
      'type' => 'from_library',
      'field_reusable_paragraph' => [
        $library_item,
      ],
    ]);
    $from_library->save();

    // Add demo content with four paragraphs.
    $node = Node::create([
      'type' => 'paragraphed_content_demo',
      'title' => 'Welcome to the Paragraphs Demo module!',
      'langcode' => 'en',
      'uid' => '0',
      'status' => 1,
      'field_paragraphs_demo' => [
        $paragraph,
        $paragraph2,
        $paragraph3,
        $paragraph5,
        $from_library,
      ],
    ]);
    $node->save();
    // Set the node as the front page.
    \Drupal::configFactory()->getEditable('system.site')->set('page.front', '/node/' . $node->id())->save();

    if ($workflow = Workflow::load('editorial')) {
      $workflow->getTypePlugin()->addEntityTypeAndBundle('node', 'paragraphed_content_demo');
      $workflow->save();
    }

    if (\Drupal::getContainer()->has('search_api.post_request_indexing')) {
      \Drupal::service('search_api.post_request_indexing')->destruct();
    }
  }
}
