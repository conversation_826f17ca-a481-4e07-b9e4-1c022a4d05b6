<?php

namespace Drupal\user\Plugin\migrate\source\d6;

use <PERSON><PERSON>al\migrate_drupal\Plugin\migrate\source\DrupalSqlBase;
use <PERSON><PERSON><PERSON>\migrate\Row;

/**
 * Drupal 6 user picture source from database.
 *
 * Available configuration keys:
 * - site_path: (optional) The path to the site directory relative to <PERSON><PERSON><PERSON>
 *   root. Defaults to 'sites/default'.
 *
 * For additional configuration keys, refer to the parent classes.
 *
 * @see \Drupal\migrate\Plugin\migrate\source\SqlBase
 * @see \Drupal\migrate\Plugin\migrate\source\SourcePluginBase
 *
 * @MigrateSource(
 *   id = "d6_user_picture_file",
 *   source_module = "user"
 * )
 */
class UserPictureFile extends DrupalSqlBase {

  /**
   * The file directory path.
   *
   * @var string
   */
  protected $filePath;

  /**
   * The temporary file path.
   *
   * @var string
   */
  protected $tempFilePath;

  /**
   * {@inheritdoc}
   */
  public function query() {
    $query = $this->select('users', 'u')
      ->condition('u.picture', '', '<>')
      ->fields('u', ['uid', 'picture']);
    return $query;
  }

  /**
   * {@inheritdoc}
   */
  public function initializeIterator() {
    $site_path = $this->configuration['site_path'] ?? 'sites/default';
    $this->filePath = $this->variableGet('file_directory_path', $site_path . '/files') . '/';
    $this->tempFilePath = $this->variableGet('file_directory_temp', '/tmp') . '/';
    return parent::initializeIterator();
  }

  /**
   * {@inheritdoc}
   */
  public function prepareRow(Row $row) {
    $row->setSourceProperty('filename', basename($row->getSourceProperty('picture')));
    $row->setSourceProperty('file_directory_path', $this->filePath);
    $row->setSourceProperty('temp_directory_path', $this->tempFilePath);
    return parent::prepareRow($row);
  }

  /**
   * {@inheritdoc}
   */
  public function fields() {
    return [
      'picture' => "Path to the user's uploaded picture.",
      'filename' => 'The picture filename.',
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function getIds() {
    $ids['uid']['type'] = 'integer';
    return $ids;
  }

}
