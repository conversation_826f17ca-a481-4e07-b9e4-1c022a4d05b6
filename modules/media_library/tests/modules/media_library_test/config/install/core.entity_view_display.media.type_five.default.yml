langcode: en
status: true
dependencies:
  config:
    - field.field.media.type_five.field_media_test_oembed_video
    - media.type.type_five
  module:
    - media
id: media.type_five.default
targetEntityType: media
bundle: type_five
mode: default
content:
  created:
    type: timestamp
    label: hidden
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: long
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 0
    region: content
  field_media_test_oembed_video:
    type: oembed
    label: hidden
    settings:
      max_width: 0
      max_height: 0
      loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 6
    region: content
  thumbnail:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: thumbnail
    third_party_settings: {  }
    weight: 5
    region: content
  uid:
    type: author
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  name: true
