<?php

/**
 * @file
 * Display Suite devel integration.
 */

/**
 * Implements hook_entity_type_alter().
 */
function ds_devel_entity_type_alter(array &$entity_types) {
  /* @var $entity_types \Drupal\Core\Entity\EntityTypeInterface[] */
  foreach ($entity_types as $entity_type_id => $entity_type) {
    if ($entity_type->hasViewBuilderClass() && $entity_type->hasLinkTemplate('canonical')) {
      $entity_type->setLinkTemplate('devel-markup', $entity_type->getLinkTemplate('canonical') . '/devel/markup');
    }
  }
}
