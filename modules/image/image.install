<?php

/**
 * @file
 * Install, update and uninstall functions for the image module.
 */

use Drupal\Core\File\Exception\FileException;
use Drupal\Core\File\FileSystemInterface;

/**
 * Implements hook_install().
 */
function image_install() {
  // Create the styles directory and ensure it's writable.
  $directory = \Drupal::config('system.file')->get('default_scheme') . '://styles';
  \Drupal::service('file_system')->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
}

/**
 * Implements hook_uninstall().
 */
function image_uninstall() {
  // Remove the styles directory and generated images.
  /** @var \Drupal\Core\File\FileSystemInterface $file_system */
  $file_system = \Drupal::service('file_system');
  try {
    $file_system->deleteRecursive(\Drupal::config('system.file')->get('default_scheme') . '://styles');
  }
  catch (FileException $e) {
    // Ignore failed deletes.
  }
}

/**
 * Implements hook_requirements().
 */
function image_requirements($phase) {
  if ($phase != 'runtime') {
    return [];
  }

  $toolkit = \Drupal::service('image.toolkit.manager')->getDefaultToolkit();
  if ($toolkit) {
    $plugin_definition = $toolkit->getPluginDefinition();
    $requirements = [
      'image.toolkit' => [
        'title' => t('Image toolkit'),
        'value' => $toolkit->getPluginId(),
        'description' => $plugin_definition['title'],
      ],
    ];

    foreach ($toolkit->getRequirements() as $key => $requirement) {
      $namespaced_key = 'image.toolkit.' . $toolkit->getPluginId() . '.' . $key;
      $requirements[$namespaced_key] = $requirement;
    }
  }
  else {
    $requirements = [
      'image.toolkit' => [
        'title' => t('Image toolkit'),
        'value' => t('None'),
        'description' => t("No image toolkit is configured on the site. Check PHP installed extensions or add a contributed toolkit that doesn't require a PHP extension. Make sure that at least one valid image toolkit is installed."),
        'severity' => REQUIREMENT_ERROR,
      ],
    ];
  }

  return $requirements;
}

/**
 * Implements hook_update_last_removed().
 */
function image_update_last_removed() {
  return 8201;
}
