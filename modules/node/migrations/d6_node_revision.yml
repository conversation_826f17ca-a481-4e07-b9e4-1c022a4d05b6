# cspell:ignore tnid
id: d6_node_revision
label: Node revisions
audit: true
migration_tags:
  - Drupal 6
  - Content
deriver: <PERSON><PERSON>al\node\Plugin\migrate\D6NodeDeriver
source:
  plugin: d6_node_revision
process:
  # If you are using this file to build a custom migration consider removing
  # the nid and vid fields to allow incremental migrations.
  nid: nid
  vid: vid
  langcode:
    plugin: default_value
    source: language
    default_value: "und"
  title: title
  uid: node_uid
  status: status
  created: created
  changed: changed
  promote: promote
  sticky: sticky
  'body/format':
    plugin: migration_lookup
    migration: d6_filter_format
    source: format
  'body/value': body
  'body/summary': teaser
  revision_uid: revision_uid
  revision_log: log
  revision_timestamp: timestamp

#  unmapped d6 fields.
#  tnid
#  translate
#  moderate
#  comment

destination:
  plugin: entity_revision:node
