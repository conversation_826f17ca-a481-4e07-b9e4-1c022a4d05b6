<?php

namespace Dr<PERSON>al\node\Plugin\Action;

use <PERSON><PERSON>al\Core\Action\Attribute\Action;
use <PERSON>upal\Core\Field\FieldUpdateActionBase;
use Drupal\Core\StringTranslation\TranslatableMarkup;
use <PERSON><PERSON><PERSON>\node\NodeInterface;

/**
 * Makes a node sticky.
 */
#[Action(
  id: 'node_make_sticky_action',
  label: new TranslatableMarkup('Make selected content sticky'),
  type: 'node'
)]
class StickyNode extends FieldUpdateActionBase {

  /**
   * {@inheritdoc}
   */
  protected function getFieldsToUpdate() {
    return ['sticky' => NodeInterface::STICKY];
  }

}
