# cspell:ignore daycount totalcount
langcode: en
status: true
dependencies:
  module:
    - node
    - user
id: test_statistics_integration
label: 'Test statistics integration'
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    display_plugin: default
    id: default
    display_title: Default
    position: null
    display_options:
      access:
        type: perm
      cache:
        type: tag
      query:
        type: views_query
      exposed_form:
        type: basic
      pager:
        type: none
        options:
          offset: 0
      style:
        type: default
      row:
        type: fields
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          label: ''
          alter:
            alter_text: false
            make_link: false
            absolute: false
            trim: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            html: false
          hide_empty: false
          empty_zero: false
          plugin_id: field
          entity_type: node
          entity_field: title
        timestamp:
          id: timestamp
          table: node_counter
          field: timestamp
          relationship: none
          group_type: group
          admin_label: ''
          label: 'Most recent view'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          date_format: html_year
          custom_date_format: ''
          timezone: ''
          plugin_id: date
        totalcount:
          id: totalcount
          table: node_counter
          field: totalcount
          relationship: none
          group_type: group
          admin_label: ''
          label: 'Total views'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          set_precision: false
          precision: 0
          decimal: .
          separator: ''
          format_plural: false
          format_plural_string: "1\x03@count"
          prefix: ''
          suffix: ''
          plugin_id: numeric
        daycount:
          id: daycount
          table: node_counter
          field: daycount
          relationship: none
          group_type: group
          admin_label: ''
          label: 'Views today'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          set_precision: false
          precision: 0
          decimal: .
          separator: ''
          format_plural: false
          format_plural_string: "1\x03@count"
          prefix: ''
          suffix: ''
          plugin_id: numeric
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          id: status
          expose:
            operator: ''
          group: 1
          plugin_id: boolean
          entity_type: node
          entity_field: status
      sorts:
        created:
          id: created
          table: node_field_data
          field: created
          order: DESC
          entity_type: node
          entity_field: created
  page_1:
    display_plugin: page
    id: page_1
    display_title: Page
    position: null
    display_options:
      path: test_statistics_integration
