<?php

/**
 * @file
 * Defines a "managed_file" Form API field and a "file" field for Field module.
 */

use Drupal\Component\Utility\NestedArray;
use Drupal\Core\Datetime\Entity\DateFormat;
use Drupal\Core\Entity\EntityStorageInterface;
use <PERSON><PERSON>al\Core\Field\FieldDefinitionInterface;
use Drupal\Core\Field\FieldFilteredMarkup;
use Drupal\Core\File\Exception\FileException;
use Drupal\Core\File\Exception\FileExistsException;
use Drupal\Core\File\Exception\FileWriteException;
use Drupal\Core\File\Exception\InvalidStreamWrapperException;
use Drupal\Core\File\FileExists;
use Drupal\Core\File\FileSystemInterface;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Link;
use Drupal\Core\Lock\LockAcquiringException;
use Drupal\Core\Messenger\MessengerInterface;
use Drupal\Core\Render\BubbleableMetadata;
use Drupal\Core\Render\Element;
use Drupal\Core\Routing\RouteMatchInterface;
use Drupal\Core\StringTranslation\ByteSizeMarkup;
use Drupal\Core\Template\Attribute;
use Drupal\Core\Url;
use Drupal\file\Entity\File;
use Drupal\file\FileInterface;
use Drupal\file\IconMimeTypes;
use Drupal\file\Upload\FormUploadedFile;

// cspell:ignore widthx

/**
 * Implements hook_help().
 */
function file_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.file':
      $output = '';
      $output .= '<h2>' . t('About') . '</h2>';
      $output .= '<p>' . t('The File module allows you to create fields that contain files. See the <a href=":field">Field module help</a> and the <a href=":field_ui">Field UI help</a> pages for general information on fields and how to create and manage them. For more information, see the <a href=":file_documentation">online documentation for the File module</a>.', [':field' => Url::fromRoute('help.page', ['name' => 'field'])->toString(), ':field_ui' => (\Drupal::moduleHandler()->moduleExists('field_ui')) ? Url::fromRoute('help.page', ['name' => 'field_ui'])->toString() : '#', ':file_documentation' => 'https://www.drupal.org/documentation/modules/file']) . '</p>';
      $output .= '<h2>' . t('Uses') . '</h2>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Managing and displaying file fields') . '</dt>';
      $output .= '<dd>' . t('The <em>settings</em> and the <em>display</em> of the file field can be configured separately. See the <a href=":field_ui">Field UI help</a> for more information on how to manage fields and their display.', [':field_ui' => (\Drupal::moduleHandler()->moduleExists('field_ui')) ? Url::fromRoute('help.page', ['name' => 'field_ui'])->toString() : '#']) . '</dd>';
      $output .= '<dt>' . t('Allowing file extensions') . '</dt>';
      $output .= '<dd>' . t('In the field settings, you can define the allowed file extensions (for example <em>pdf docx psd</em>) for the files that will be uploaded with the file field.') . '</dd>';
      $output .= '<dt>' . t('Storing files') . '</dt>';
      $output .= '<dd>' . t('Uploaded files can either be stored as <em>public</em> or <em>private</em>, depending on the <a href=":file-system">File system settings</a>. For more information, see the <a href=":system-help">System module help page</a>.', [':file-system' => Url::fromRoute('system.file_system_settings')->toString(), ':system-help' => Url::fromRoute('help.page', ['name' => 'system'])->toString()]) . '</dd>';
      $output .= '<dt>' . t('Restricting the maximum file size') . '</dt>';
      $output .= '<dd>' . t('The maximum file size that users can upload is limited by PHP settings of the server, but you can restrict by entering the desired value as the <em>Maximum upload size</em> setting. The maximum file size is automatically displayed to users in the help text of the file field.') . '</dd>';
      $output .= '<dt>' . t('Displaying files and descriptions') . '<dt>';
      $output .= '<dd>' . t('In the field settings, you can allow users to toggle whether individual files are displayed. In the display settings, you can then choose one of the following formats: <ul><li><em>Generic file</em> displays links to the files and adds icons that symbolize the file extensions. If <em>descriptions</em> are enabled and have been submitted, then the description is displayed instead of the file name.</li><li><em>URL to file</em> displays the full path to the file as plain text.</li><li><em>Table of files</em> lists links to the files and the file sizes in a table.</li><li><em>RSS enclosure</em> only displays the first file, and only in a RSS feed, formatted according to the RSS 2.0 syntax for enclosures.</li></ul> A file can still be linked to directly by its URI even if it is not displayed.') . '</dd>';
      $output .= '</dl>';
      return $output;
  }
}

/**
 * Implements hook_field_widget_info_alter().
 */
function file_field_widget_info_alter(array &$info) {
  // Allows using the 'uri' widget for the 'file_uri' field type, which uses it
  // as the default widget.
  // @see \Drupal\file\Plugin\Field\FieldType\FileUriItem
  $info['uri']['field_types'][] = 'file_uri';
}

/**
 * Checks that a file meets the criteria specified by the validators.
 *
 * After executing the validator callbacks specified hook_file_validate() will
 * also be called to allow other modules to report errors about the file.
 *
 * @param \Drupal\file\FileInterface $file
 *   A file entity.
 * @param array $validators
 *   (optional) An associative array of callback functions used to validate
 *   the file. The keys are function names and the values arrays of callback
 *   parameters which will be passed in after the file entity. The functions
 *   should return an array of error messages; an empty array indicates that
 *   the file passed validation. The callback functions will be called in the
 *   order specified in the array, then the hook hook_file_validate()
 *   will be invoked so other modules can validate the new file.
 *
 * @return array
 *   An array containing validation error messages.
 *
 * @deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the
 *   'file.validator' service instead.
 *
 * @see https://www.drupal.org/node/3363700
 */
function file_validate(FileInterface $file, $validators = []) {
  @trigger_error(__FUNCTION__ . '() is deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the \'file.validator\' service instead. See https://www.drupal.org/node/3363700', E_USER_DEPRECATED);
  $violations = \Drupal::service('file.validator')->validate($file, $validators);
  $errors = [];
  foreach ($violations as $violation) {
    $errors[] = $violation->getMessage();
  }
  return $errors;
}

/**
 * Checks for files with names longer than can be stored in the database.
 *
 * @param \Drupal\file\FileInterface $file
 *   A file entity.
 *
 * @return array
 *   An empty array if the file name length is smaller than the limit or an
 *   array containing an error message if it's not or is empty.
 *
 * @deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the
 *   'file.validator' service instead.
 *
 * @see https://www.drupal.org/node/3363700
 */
function file_validate_name_length(FileInterface $file) {
  @trigger_error(__FUNCTION__ . '() is deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the \'file.validator\' service instead. See https://www.drupal.org/node/3363700', E_USER_DEPRECATED);
  $errors = [];

  if (!$file->getFilename()) {
    $errors[] = t("The file's name is empty. Enter a name for the file.");
  }
  if (strlen($file->getFilename()) > 240) {
    $errors[] = t("The file's name exceeds the 240 characters limit. Rename the file and try again.");
  }
  return $errors;
}

/**
 * Checks that the filename ends with an allowed extension.
 *
 * @param \Drupal\file\FileInterface $file
 *   A file entity.
 * @param string $extensions
 *   A string with a space separated list of allowed extensions.
 *
 * @return array
 *   An empty array if the file extension is allowed or an array containing an
 *   error message if it's not.
 *
 * @deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the
 *   'file.validator' service instead.
 *
 * @see https://www.drupal.org/node/3363700
 * @see hook_file_validate()
 */
function file_validate_extensions(FileInterface $file, $extensions) {
  @trigger_error(__FUNCTION__ . '() is deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the \'file.validator\' service instead. See https://www.drupal.org/node/3363700', E_USER_DEPRECATED);
  $errors = [];

  $regex = '/\.(' . preg_replace('/ +/', '|', preg_quote($extensions)) . ')$/i';
  // Filename may differ from the basename, for instance in case files migrated
  // from D7 file entities. Because of that new files are saved temporarily with
  // a generated file name, without the original extension, we will use the
  // generated filename property for extension validation only in case of
  // temporary files; and use the file system file name in case of permanent
  // files.
  $subject = $file->isTemporary() ? $file->getFilename() : $file->getFileUri();
  if (!preg_match($regex, $subject)) {
    $errors[] = t('Only files with the following extensions are allowed: %files-allowed.', ['%files-allowed' => $extensions]);
  }
  return $errors;
}

/**
 * Checks that the file's size is below certain limits.
 *
 * @param \Drupal\file\FileInterface $file
 *   A file entity.
 * @param int $file_limit
 *   (optional) The maximum file size in bytes. Zero (the default) indicates
 *   that no limit should be enforced.
 * @param int $user_limit
 *   (optional) The maximum number of bytes the user is allowed. Zero (the
 *   default) indicates that no limit should be enforced.
 *
 * @return array
 *   An empty array if the file size is below limits or an array containing an
 *   error message if it's not.
 *
 * @deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the
 *   'file.validator' service instead.
 *
 * @see https://www.drupal.org/node/3363700
 * @see hook_file_validate()
 */
function file_validate_size(FileInterface $file, $file_limit = 0, $user_limit = 0) {
  @trigger_error(__FUNCTION__ . '() is deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the \'file.validator\' service instead. See https://www.drupal.org/node/3363700', E_USER_DEPRECATED);
  $user = \Drupal::currentUser();
  $errors = [];

  if ($file_limit && $file->getSize() > $file_limit) {
    $errors[] = t('The file is %filesize exceeding the maximum file size of %maxsize.', [
      '%filesize' => ByteSizeMarkup::create($file->getSize()),
      '%maxsize' => ByteSizeMarkup::create($file_limit),
    ]);
  }

  // Save a query by only calling spaceUsed() when a limit is provided.
  if ($user_limit && (\Drupal::entityTypeManager()->getStorage('file')->spaceUsed($user->id()) + $file->getSize()) > $user_limit) {
    $errors[] = t('The file is %filesize which would exceed your disk quota of %quota.', [
      '%filesize' => ByteSizeMarkup::create($file->getSize()),
      '%quota' => ByteSizeMarkup::create($user_limit),
    ]);
  }

  return $errors;
}

/**
 * Checks that the file is recognized as a valid image.
 *
 * @param \Drupal\file\FileInterface $file
 *   A file entity.
 *
 * @return array
 *   An empty array if the file is a valid image or an array containing an error
 *   message if it's not.
 *
 * @deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the
 *   'file.validator' service instead.
 *
 * @see https://www.drupal.org/node/3363700
 * @see hook_file_validate()
 */
function file_validate_is_image(FileInterface $file) {
  @trigger_error(__FUNCTION__ . '() is deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the \'file.validator\' service instead. See https://www.drupal.org/node/3363700', E_USER_DEPRECATED);
  $errors = [];

  $image_factory = \Drupal::service('image.factory');
  $image = $image_factory->get($file->getFileUri());
  if (!$image->isValid()) {
    $supported_extensions = $image_factory->getSupportedExtensions();
    $errors[] = t('The image file is invalid or the image type is not allowed. Allowed types: %types', ['%types' => implode(', ', $supported_extensions)]);
  }

  return $errors;
}

/**
 * Verifies that image dimensions are within the specified maximum and minimum.
 *
 * Non-image files will be ignored. If an image toolkit is available the image
 * will be scaled to fit within the desired maximum dimensions.
 *
 * @param \Drupal\file\FileInterface $file
 *   A file entity. This function may resize the file affecting its size.
 * @param string|int $maximum_dimensions
 *   (optional) A string in the form WIDTHxHEIGHT; for example, '640x480' or
 *   '85x85'. If an image toolkit is installed, the image will be resized down
 *   to these dimensions. A value of zero (the default) indicates no restriction
 *   on size, so no resizing will be attempted.
 * @param string|int $minimum_dimensions
 *   (optional) A string in the form WIDTHxHEIGHT. This will check that the
 *   image meets a minimum size. A value of zero (the default) indicates that
 *   there is no restriction on size.
 *
 * @return array
 *   An empty array if the file meets the specified dimensions, was resized
 *   successfully to meet those requirements or is not an image. If the image
 *   does not meet the requirements or an attempt to resize it fails, an array
 *   containing the error message will be returned.
 *
 * @deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the
 *   'file.validator' service instead.
 *
 * @see https://www.drupal.org/node/3363700
 * @see hook_file_validate()
 */
function file_validate_image_resolution(FileInterface $file, $maximum_dimensions = 0, $minimum_dimensions = 0) {
  @trigger_error(__FUNCTION__ . '() is deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the \'file.validator\' service instead. See https://www.drupal.org/node/3363700', E_USER_DEPRECATED);
  $errors = [];

  // Check first that the file is an image.
  $image_factory = \Drupal::service('image.factory');
  $image = $image_factory->get($file->getFileUri());

  if ($image->isValid()) {
    $scaling = FALSE;
    if ($maximum_dimensions) {
      // Check that it is smaller than the given dimensions.
      [$width, $height] = explode('x', $maximum_dimensions);
      if ($image->getWidth() > $width || $image->getHeight() > $height) {
        // Try to resize the image to fit the dimensions.
        if ($image->scale($width, $height)) {
          $scaling = TRUE;
          $image->save();
          // Update the file size now that the image has been resized.
          $file->setSize($image->getFileSize());
          if (!empty($width) && !empty($height)) {
            $message = t('The image was resized to fit within the maximum allowed dimensions of %dimensions pixels. The new dimensions of the resized image are %new_widthx%new_height pixels.',
              [
                '%dimensions' => $maximum_dimensions,
                '%new_width' => $image->getWidth(),
                '%new_height' => $image->getHeight(),
              ]);
          }
          elseif (empty($width)) {
            $message = t('The image was resized to fit within the maximum allowed height of %height pixels. The new dimensions of the resized image are %new_widthx%new_height pixels.',
              [
                '%height' => $height,
                '%new_width' => $image->getWidth(),
                '%new_height' => $image->getHeight(),
              ]);
          }
          elseif (empty($height)) {
            $message = t('The image was resized to fit within the maximum allowed width of %width pixels. The new dimensions of the resized image are %new_widthx%new_height pixels.',
              [
                '%width' => $width,
                '%new_width' => $image->getWidth(),
                '%new_height' => $image->getHeight(),
              ]);
          }
          \Drupal::messenger()->addStatus($message);
        }
        else {
          $errors[] = t('The image exceeds the maximum allowed dimensions and an attempt to resize it failed.');
        }
      }
    }

    if ($minimum_dimensions) {
      // Check that it is larger than the given dimensions.
      [$width, $height] = explode('x', $minimum_dimensions);
      if ($image->getWidth() < $width || $image->getHeight() < $height) {
        if ($scaling) {
          $errors[] = t('The resized image is too small. The minimum dimensions are %dimensions pixels and after resizing, the image size will be %widthx%height pixels.',
            [
              '%dimensions' => $minimum_dimensions,
              '%width' => $image->getWidth(),
              '%height' => $image->getHeight(),
            ]);
        }
        else {
          $errors[] = t('The image is too small. The minimum dimensions are %dimensions pixels and the image size is %widthx%height pixels.',
            [
              '%dimensions' => $minimum_dimensions,
              '%width' => $image->getWidth(),
              '%height' => $image->getHeight(),
            ]);
        }
      }
    }
  }

  return $errors;
}

/**
 * Examines a file entity and returns appropriate content headers for download.
 *
 * @param \Drupal\file\FileInterface $file
 *   A file entity.
 *
 * @return array
 *   An associative array of headers, as expected by
 *   \Symfony\Component\HttpFoundation\StreamedResponse.
 */
function file_get_content_headers(FileInterface $file) {
  return [
    'Content-Type' => $file->getMimeType(),
    'Content-Length' => $file->getSize(),
    'Cache-Control' => 'private',
  ];
}

/**
 * Implements hook_theme().
 */
function file_theme() {
  return [
    // From file.module.
    'file_link' => [
      'variables' => ['file' => NULL, 'description' => NULL, 'attributes' => []],
    ],
    'file_managed_file' => [
      'render element' => 'element',
    ],
    'file_audio' => [
      'variables' => ['files' => [], 'attributes' => NULL],
    ],
    'file_video' => [
      'variables' => ['files' => [], 'attributes' => NULL],
    ],
    'file_widget_multiple' => [
      'render element' => 'element',
    ],
    'file_upload_help' => [
      'variables' => ['description' => NULL, 'upload_validators' => NULL, 'cardinality' => NULL],
    ],
  ];
}

/**
 * Implements hook_file_download().
 */
function file_file_download($uri) {
  // Get the file record based on the URI. If not in the database just return.
  /** @var \Drupal\file\FileRepositoryInterface $file_repository */
  $file_repository = \Drupal::service('file.repository');
  $file = $file_repository->loadByUri($uri);
  if (!$file) {
    return;
  }

  // Find out if a temporary file is still used in the system.
  if ($file->isTemporary()) {
    $usage = \Drupal::service('file.usage')->listUsage($file);
    if (empty($usage) && $file->getOwnerId() != \Drupal::currentUser()->id()) {
      // Deny access to temporary files without usage that are not owned by the
      // same user. This prevents the security issue that a private file that
      // was protected by field permissions becomes available after its usage
      // was removed and before it is actually deleted from the file system.
      // Modules that depend on this behavior should make the file permanent
      // instead.
      return -1;
    }
  }

  // Find out which (if any) fields of this type contain the file.
  $references = file_get_file_references($file, NULL, EntityStorageInterface::FIELD_LOAD_CURRENT, NULL);

  // Stop processing if there are no references in order to avoid returning
  // headers for files controlled by other modules. Make an exception for
  // temporary files where the host entity has not yet been saved (for example,
  // an image preview on a node/add form) in which case, allow download by the
  // file's owner.
  if (empty($references) && ($file->isPermanent() || $file->getOwnerId() != \Drupal::currentUser()->id())) {
    return;
  }

  if (!$file->access('download')) {
    return -1;
  }

  // Access is granted.
  $headers = file_get_content_headers($file);
  return $headers;
}

/**
 * Implements hook_cron().
 */
function file_cron() {
  $age = \Drupal::config('system.file')->get('temporary_maximum_age');
  $file_storage = \Drupal::entityTypeManager()->getStorage('file');

  /** @var \Drupal\Core\StreamWrapper\StreamWrapperManagerInterface $stream_wrapper_manager */
  $stream_wrapper_manager = \Drupal::service('stream_wrapper_manager');

  // Only delete temporary files if older than $age. Note that automatic cleanup
  // is disabled if $age set to 0.
  if ($age) {
    $fids = Drupal::entityQuery('file')
      ->accessCheck(FALSE)
      ->condition('status', FileInterface::STATUS_PERMANENT, '<>')
      ->condition('changed', \Drupal::time()->getRequestTime() - $age, '<')
      ->range(0, 100)
      ->execute();
    $files = $file_storage->loadMultiple($fids);
    foreach ($files as $file) {
      $references = \Drupal::service('file.usage')->listUsage($file);
      if (empty($references)) {
        if (!file_exists($file->getFileUri())) {
          if (!$stream_wrapper_manager->isValidUri($file->getFileUri())) {
            \Drupal::logger('file system')->warning('Temporary file "%path" that was deleted during garbage collection did not exist on the filesystem. This could be caused by a missing stream wrapper.', ['%path' => $file->getFileUri()]);
          }
          else {
            \Drupal::logger('file system')->warning('Temporary file "%path" that was deleted during garbage collection did not exist on the filesystem.', ['%path' => $file->getFileUri()]);
          }
        }
        // Delete the file entity. If the file does not exist, this will
        // generate a second notice in the watchdog.
        $file->delete();
      }
      else {
        \Drupal::logger('file system')->info('Did not delete temporary file "%path" during garbage collection because it is in use by the following modules: %modules.', ['%path' => $file->getFileUri(), '%modules' => implode(', ', array_keys($references))]);
      }
    }
  }
}

/**
 * Saves form file uploads.
 *
 * The files will be added to the {file_managed} table as temporary files.
 * Temporary files are periodically cleaned. Use the 'file.usage' service to
 * register the usage of the file which will automatically mark it as permanent.
 *
 * @param array $element
 *   The FAPI element whose values are being saved.
 * @param \Drupal\Core\Form\FormStateInterface $form_state
 *   The current state of the form.
 * @param null|int $delta
 *   (optional) The delta of the file to return the file entity.
 *   Defaults to NULL.
 * @param \Drupal\Core\File\FileExists|int $fileExists
 *   (optional) The replace behavior when the destination file already exists.
 *
 * @return array|\Drupal\file\FileInterface|null|false
 *   An array of file entities or a single file entity if $delta != NULL. Each
 *   array element contains the file entity if the upload succeeded or FALSE if
 *   there was an error. Function returns NULL if no file was uploaded.
 *
 * @throws \ValueError
 *    Thrown if $fileExists is a legacy int and not a valid value.
 *
 * @internal
 *   This function is internal, and may be removed in a minor version release.
 *   It wraps file_save_upload() to allow correct error handling in forms.
 *   Contrib and custom code should not call this function, they should use the
 *   managed file upload widgets in core.
 *
 * @see https://www.drupal.org/project/drupal/issues/3069020
 * @see https://www.drupal.org/project/drupal/issues/2482783
 */
function _file_save_upload_from_form(array $element, FormStateInterface $form_state, $delta = NULL, FileExists|int $fileExists = FileExists::Rename) {
  if (!$fileExists instanceof FileExists) {
    // @phpstan-ignore-next-line
    $fileExists = FileExists::fromLegacyInt($fileExists, __METHOD__);
  }
  // Get all errors set before calling this method. This will also clear them
  // from the messenger service.
  $errors_before = \Drupal::messenger()->deleteByType(MessengerInterface::TYPE_ERROR);

  $upload_location = $element['#upload_location'] ?? FALSE;
  $upload_name = implode('_', $element['#parents']);
  $upload_validators = $element['#upload_validators'] ?? [];

  $result = file_save_upload($upload_name, $upload_validators, $upload_location, $delta, $fileExists);

  // Get new errors that are generated while trying to save the upload. This
  // will also clear them from the messenger service.
  $errors_new = \Drupal::messenger()->deleteByType(MessengerInterface::TYPE_ERROR);
  if (!empty($errors_new)) {

    if (count($errors_new) > 1) {
      // Render multiple errors into a single message.
      // This is needed because only one error per element is supported.
      $render_array = [
        'error' => [
          '#markup' => t('One or more files could not be uploaded.'),
        ],
        'item_list' => [
          '#theme' => 'item_list',
          '#items' => $errors_new,
        ],
      ];
      $error_message = \Drupal::service('renderer')->renderInIsolation($render_array);
    }
    else {
      $error_message = reset($errors_new);
    }

    $form_state->setError($element, $error_message);
  }

  // Ensure that errors set prior to calling this method are still shown to the
  // user.
  if (!empty($errors_before)) {
    foreach ($errors_before as $error) {
      \Drupal::messenger()->addError($error);
    }
  }

  return $result;
}

/**
 * Saves file uploads to a new location.
 *
 * The files will be added to the {file_managed} table as temporary files.
 * Temporary files are periodically cleaned. Use the 'file.usage' service to
 * register the usage of the file which will automatically mark it as permanent.
 *
 * Note that this function does not support correct form error handling. The
 * file upload widgets in core do support this. It is advised to use these in
 * any custom form, instead of calling this function.
 *
 * @param string $form_field_name
 *   A string that is the associative array key of the upload form element in
 *   the form array.
 * @param array $validators
 *   (optional) An associative array of callback functions used to validate the
 *   file. See file_validate() for a full discussion of the array format.
 *   If the array is empty, it will be set up to call file_validate_extensions()
 *   with a safe list of extensions, as follows: "jpg jpeg gif png txt doc
 *   xls pdf ppt pps odt ods odp". To allow all extensions, you must explicitly
 *   set this array to ['file_validate_extensions' => '']. (Beware: this is not
 *   safe and should only be allowed for trusted users, if at all.)
 * @param string|false $destination
 *   (optional) A string containing the URI that the file should be copied to.
 *   This must be a stream wrapper URI. If this value is omitted or set to
 *   FALSE, Drupal's temporary files scheme will be used ("temporary://").
 * @param null|int $delta
 *   (optional) The delta of the file to return the file entity.
 *   Defaults to NULL.
 * @param \Drupal\Core\File\FileExists|int $fileExists
 *   (optional) The replace behavior when the destination file already exists.
 *
 * @return array|\Drupal\file\FileInterface|null|false
 *   An array of file entities or a single file entity if $delta != NULL. Each
 *   array element contains the file entity if the upload succeeded or FALSE if
 *   there was an error. Function returns NULL if no file was uploaded.
 *
 * @throws \ValueError
 *   Thrown if $fileExists is a legacy int and not a valid value.
 *
 * @see _file_save_upload_from_form()
 */
function file_save_upload($form_field_name, $validators = [], $destination = FALSE, $delta = NULL, FileExists|int $fileExists = FileExists::Rename) {
  if (!$fileExists instanceof FileExists) {
    // @phpstan-ignore-next-line
    $fileExists = FileExists::fromLegacyInt($fileExists, __METHOD__);
  }
  static $upload_cache;

  $all_files = \Drupal::request()->files->get('files', []);
  // Make sure there's an upload to process.
  if (empty($all_files[$form_field_name])) {
    return NULL;
  }
  $file_upload = $all_files[$form_field_name];

  // Return cached objects without processing since the file will have
  // already been processed and the paths in $_FILES will be invalid.
  if (isset($upload_cache[$form_field_name])) {
    if (isset($delta)) {
      return $upload_cache[$form_field_name][$delta];
    }
    return $upload_cache[$form_field_name];
  }

  // Prepare uploaded files info. Representation is slightly different
  // for multiple uploads and we fix that here.
  $uploaded_files = $file_upload;
  if (!is_array($file_upload)) {
    $uploaded_files = [$file_upload];
  }

  if ($destination === FALSE || $destination === NULL) {
    $destination = 'temporary://';
  }

  /** @var \Drupal\file\Upload\FileUploadHandler $file_upload_handler */
  $file_upload_handler = \Drupal::service('file.upload_handler');
  /** @var \Drupal\Core\Render\RendererInterface $renderer */
  $renderer = \Drupal::service('renderer');
  $files = [];
  /** @var \Symfony\Component\HttpFoundation\File\UploadedFile $uploaded_file */
  foreach ($uploaded_files as $i => $uploaded_file) {
    try {
      $form_uploaded_file = new FormUploadedFile($uploaded_file);
      $result = $file_upload_handler->handleFileUpload($form_uploaded_file, $validators, $destination, $fileExists, FALSE);
      if ($result->hasViolations()) {
        $errors = [];
        foreach ($result->getViolations() as $violation) {
          $errors[] = $violation->getMessage();
        }
        $message = [
          'error' => [
            '#markup' => t('The specified file %name could not be uploaded.', ['%name' => $uploaded_file->getClientOriginalName()]),
          ],
          'item_list' => [
            '#theme' => 'item_list',
            '#items' => $errors,
          ],
        ];
        // @todo Add support for render arrays in
        // \Drupal\Core\Messenger\MessengerInterface::addMessage()?
        // @see https://www.drupal.org/node/2505497.
        \Drupal::messenger()->addError($renderer->renderInIsolation($message));
        $files[$i] = FALSE;
        continue;
      }
      $file = $result->getFile();
      // If the filename has been modified, let the user know.
      if ($result->isRenamed()) {
        if ($result->isSecurityRename()) {
          $message = t('For security reasons, your upload has been renamed to %filename.', ['%filename' => $file->getFilename()]);
        }
        else {
          $message = t('Your upload has been renamed to %filename.', ['%filename' => $file->getFilename()]);
        }
        \Drupal::messenger()->addStatus($message);
      }
      $files[$i] = $file;
    }
    catch (FileExistsException $e) {
      \Drupal::messenger()->addError(t('Destination file "%file" exists', ['%file' => $destination . $uploaded_file->getFilename()]));
      $files[$i] = FALSE;
    }
    catch (InvalidStreamWrapperException $e) {
      \Drupal::messenger()->addError(t('The file could not be uploaded because the destination "%destination" is invalid.', ['%destination' => $destination]));
      $files[$i] = FALSE;
    }
    catch (FileWriteException $e) {
      \Drupal::messenger()->addError(t('File upload error. Could not move uploaded file.'));
      \Drupal::logger('file')->notice('Upload error. Could not move uploaded file %file to destination %destination.', ['%file' => $uploaded_file->getClientOriginalName(), '%destination' => $destination . '/' . $uploaded_file->getClientOriginalName()]);
      $files[$i] = FALSE;
    }
    catch (FileException $e) {
      \Drupal::messenger()->addError(t('The file %filename could not be uploaded because the name is invalid.', ['%filename' => $uploaded_file->getClientOriginalName()]));
      $files[$i] = FALSE;
    }
    catch (LockAcquiringException $e) {
      \Drupal::messenger()->addError(t('File already locked for writing.'));
      $files[$i] = FALSE;
    }
  }

  // Add files to the cache.
  $upload_cache[$form_field_name] = $files;

  return isset($delta) ? $files[$delta] : $files;
}

/**
 * Determines the preferred upload progress implementation.
 *
 * @return string|false
 *   A string indicating which upload progress system is available. Either "apc"
 *   or "uploadprogress". If neither are available, returns FALSE.
 *
 * @deprecated in drupal:10.3.0 and is removed from drupal:11.0.0. Use
 *    extension_loaded('uploadprogress') instead.
 *
 * @see https://www.drupal.org/node/3397577
 */
function file_progress_implementation() {
  @trigger_error(__FUNCTION__ . '() is deprecated in drupal:10.3.0 and is removed from drupal:11.0.0. Use extension_loaded(\'uploadprogress\') instead. See https://www.drupal.org/node/3397577', E_USER_DEPRECATED);
  static $implementation;
  if (!isset($implementation)) {
    $implementation = FALSE;

    // We prefer the PECL extension uploadprogress because it supports multiple
    // simultaneous uploads. APCu only supports one at a time.
    if (extension_loaded('uploadprogress')) {
      $implementation = 'uploadprogress';
    }
  }
  return $implementation;
}

/**
 * Implements hook_ENTITY_TYPE_predelete() for file entities.
 */
function file_file_predelete(File $file) {
  // @todo Remove references to a file that is in-use.
}

/**
 * Implements hook_tokens().
 */
function file_tokens($type, $tokens, array $data, array $options, BubbleableMetadata $bubbleable_metadata) {
  $token_service = \Drupal::token();

  $url_options = ['absolute' => TRUE];
  if (isset($options['langcode'])) {
    $url_options['language'] = \Drupal::languageManager()->getLanguage($options['langcode']);
    $langcode = $options['langcode'];
  }
  else {
    $langcode = NULL;
  }

  $replacements = [];

  if ($type == 'file' && !empty($data['file'])) {
    /** @var \Drupal\file\FileInterface $file */
    $file = $data['file'];

    foreach ($tokens as $name => $original) {
      switch ($name) {
        // Basic keys and values.
        case 'fid':
          $replacements[$original] = $file->id();
          break;

        // Essential file data
        case 'name':
          $replacements[$original] = $file->getFilename();
          break;

        case 'path':
          $replacements[$original] = $file->getFileUri();
          break;

        case 'mime':
          $replacements[$original] = $file->getMimeType();
          break;

        case 'size':
          $replacements[$original] = ByteSizeMarkup::create($file->getSize());
          break;

        case 'url':
          // Ideally, this would use return a relative URL, but because tokens
          // are also often used in emails, it's better to keep absolute file
          // URLs. The 'url.site' cache context is associated to ensure the
          // correct absolute URL is used in case of a multisite setup.
          $replacements[$original] = $file->createFileUrl(FALSE);
          $bubbleable_metadata->addCacheContexts(['url.site']);
          break;

        // These tokens are default variations on the chained tokens handled below.
        case 'created':
          $date_format = DateFormat::load('medium');
          $bubbleable_metadata->addCacheableDependency($date_format);
          $replacements[$original] = \Drupal::service('date.formatter')->format($file->getCreatedTime(), 'medium', '', NULL, $langcode);
          break;

        case 'changed':
          $date_format = DateFormat::load('medium');
          $bubbleable_metadata = $bubbleable_metadata->addCacheableDependency($date_format);
          $replacements[$original] = \Drupal::service('date.formatter')->format($file->getChangedTime(), 'medium', '', NULL, $langcode);
          break;

        case 'owner':
          $owner = $file->getOwner();
          $bubbleable_metadata->addCacheableDependency($owner);
          $name = $owner->label();
          $replacements[$original] = $name;
          break;
      }
    }

    if ($date_tokens = $token_service->findWithPrefix($tokens, 'created')) {
      $replacements += $token_service->generate('date', $date_tokens, ['date' => $file->getCreatedTime()], $options, $bubbleable_metadata);
    }

    if ($date_tokens = $token_service->findWithPrefix($tokens, 'changed')) {
      $replacements += $token_service->generate('date', $date_tokens, ['date' => $file->getChangedTime()], $options, $bubbleable_metadata);
    }

    if (($owner_tokens = $token_service->findWithPrefix($tokens, 'owner')) && $file->getOwner()) {
      $replacements += $token_service->generate('user', $owner_tokens, ['user' => $file->getOwner()], $options, $bubbleable_metadata);
    }
  }

  return $replacements;
}

/**
 * Implements hook_token_info().
 */
function file_token_info() {
  $types['file'] = [
    'name' => t("Files"),
    'description' => t("Tokens related to uploaded files."),
    'needs-data' => 'file',
  ];

  // File related tokens.
  $file['fid'] = [
    'name' => t("File ID"),
    'description' => t("The unique ID of the uploaded file."),
  ];
  $file['name'] = [
    'name' => t("File name"),
    'description' => t("The name of the file on disk."),
  ];
  $file['path'] = [
    'name' => t("Path"),
    'description' => t("The location of the file relative to Drupal root."),
  ];
  $file['mime'] = [
    'name' => t("MIME type"),
    'description' => t("The MIME type of the file."),
  ];
  $file['size'] = [
    'name' => t("File size"),
    'description' => t("The size of the file."),
  ];
  $file['url'] = [
    'name' => t("URL"),
    'description' => t("The web-accessible URL for the file."),
  ];
  $file['created'] = [
    'name' => t("Created"),
    'description' => t("The date the file created."),
    'type' => 'date',
  ];
  $file['changed'] = [
    'name' => t("Changed"),
    'description' => t("The date the file was most recently changed."),
    'type' => 'date',
  ];
  $file['owner'] = [
    'name' => t("Owner"),
    'description' => t("The user who originally uploaded the file."),
    'type' => 'user',
  ];

  return [
    'types' => $types,
    'tokens' => [
      'file' => $file,
    ],
  ];
}

/**
 * Form submission handler for upload / remove buttons of managed_file elements.
 *
 * @see \Drupal\file\Element\ManagedFile::processManagedFile()
 */
function file_managed_file_submit($form, FormStateInterface $form_state) {
  // Determine whether it was the upload or the remove button that was clicked,
  // and set $element to the managed_file element that contains that button.
  $parents = $form_state->getTriggeringElement()['#array_parents'];
  $button_key = array_pop($parents);
  $element = NestedArray::getValue($form, $parents);

  // No action is needed here for the upload button, because all file uploads on
  // the form are processed by \Drupal\file\Element\ManagedFile::valueCallback()
  // regardless of which button was clicked. Action is needed here for the
  // remove button, because we only remove a file in response to its remove
  // button being clicked.
  if ($button_key == 'remove_button') {
    $fids = array_keys($element['#files']);
    // Get files that will be removed.
    if ($element['#multiple']) {
      $remove_fids = [];
      foreach (Element::children($element) as $name) {
        if (str_starts_with($name, 'file_') && $element[$name]['selected']['#value']) {
          $remove_fids[] = (int) substr($name, 5);
        }
      }
      $fids = array_diff($fids, $remove_fids);
    }
    else {
      // If we deal with single upload element remove the file and set
      // element's value to empty array (file could not be removed from
      // element if we don't do that).
      $remove_fids = $fids;
      $fids = [];
    }

    foreach ($remove_fids as $fid) {
      // If it's a temporary file we can safely remove it immediately, otherwise
      // it's up to the implementing module to remove usages of files to have them
      // removed.
      if ($element['#files'][$fid] && $element['#files'][$fid]->isTemporary()) {
        $element['#files'][$fid]->delete();
      }
    }
    // Update both $form_state->getValues() and FormState::$input to reflect
    // that the file has been removed, so that the form is rebuilt correctly.
    // $form_state->getValues() must be updated in case additional submit
    // handlers run, and for form building functions that run during the
    // rebuild, such as when the managed_file element is part of a field widget.
    // FormState::$input must be updated so that
    // \Drupal\file\Element\ManagedFile::valueCallback() has correct information
    // during the rebuild.
    $form_state->setValueForElement($element['fids'], implode(' ', $fids));
    NestedArray::setValue($form_state->getUserInput(), $element['fids']['#parents'], implode(' ', $fids));
  }

  // Set the form to rebuild so that $form is correctly updated in response to
  // processing the file removal. Since this function did not change $form_state
  // if the upload button was clicked, a rebuild isn't necessary in that
  // situation and calling $form_state->disableRedirect() would suffice.
  // However, we choose to always rebuild, to keep the form processing workflow
  // consistent between the two buttons.
  $form_state->setRebuild();
}

/**
 * Saves any files that have been uploaded into a managed_file element.
 *
 * @param array $element
 *   The FAPI element whose values are being saved.
 * @param \Drupal\Core\Form\FormStateInterface $form_state
 *   The current state of the form.
 *
 * @return array|false
 *   An array of file entities for each file that was saved, keyed by its file
 *   ID. Each array element contains a file entity. Function returns FALSE if
 *   upload directory could not be created or no files were uploaded.
 */
function file_managed_file_save_upload($element, FormStateInterface $form_state) {
  $upload_name = implode('_', $element['#parents']);
  $all_files = \Drupal::request()->files->get('files', []);
  if (empty($all_files[$upload_name])) {
    return FALSE;
  }
  $file_upload = $all_files[$upload_name];

  $destination = $element['#upload_location'] ?? NULL;
  if (isset($destination) && !\Drupal::service('file_system')->prepareDirectory($destination, FileSystemInterface::CREATE_DIRECTORY)) {
    \Drupal::logger('file')->notice('The upload directory %directory for the file field %name could not be created or is not accessible. A newly uploaded file could not be saved in this directory as a consequence, and the upload was canceled.', ['%directory' => $destination, '%name' => $element['#field_name']]);
    $form_state->setError($element, t('The file could not be uploaded.'));
    return FALSE;
  }

  // Save attached files to the database.
  $files_uploaded = $element['#multiple'] && count(array_filter($file_upload)) > 0;
  $files_uploaded |= !$element['#multiple'] && !empty($file_upload);
  if ($files_uploaded) {
    if (!$files = _file_save_upload_from_form($element, $form_state)) {
      \Drupal::logger('file')->notice('The file upload failed. %upload', ['%upload' => $upload_name]);
      return [];
    }

    // Value callback expects FIDs to be keys.
    $files = array_filter($files);
    $fids = array_map(function ($file) {
      return $file->id();
    }, $files);

    return empty($files) ? [] : array_combine($fids, $files);
  }

  return [];
}

/**
 * Prepares variables for file form widget templates.
 *
 * Default template: file-managed-file.html.twig.
 *
 * @param array $variables
 *   An associative array containing:
 *   - element: A render element representing the file.
 */
function template_preprocess_file_managed_file(&$variables) {
  $element = $variables['element'];

  $variables['attributes'] = [];
  if (isset($element['#id'])) {
    $variables['attributes']['id'] = $element['#id'];
  }
  if (!empty($element['#attributes']['class'])) {
    $variables['attributes']['class'] = (array) $element['#attributes']['class'];
  }
}

/**
 * Prepares variables for file link templates.
 *
 * Default template: file-link.html.twig.
 *
 * @param array $variables
 *   An associative array containing:
 *   - file: A File entity to which the link will be created.
 *   - icon_directory: (optional) A path to a directory of icons to be used for
 *     files. Defaults to the value of the "icon.directory" variable.
 *   - description: A description to be displayed instead of the filename.
 *   - attributes: An associative array of attributes to be placed in the a tag.
 */
function template_preprocess_file_link(&$variables) {
  $file = $variables['file'];
  $options = [];

  /** @var \Drupal\Core\File\FileUrlGeneratorInterface $file_url_generator */
  $file_url_generator = \Drupal::service('file_url_generator');
  $url = $file_url_generator->generate($file->getFileUri());

  $mime_type = $file->getMimeType();
  $options['attributes']['type'] = $mime_type;

  // Use the description as the link text if available.
  if (empty($variables['description'])) {
    $link_text = $file->getFilename();
  }
  else {
    $link_text = $variables['description'];
    $options['attributes']['title'] = $file->getFilename();
  }

  // Classes to add to the file field for icons.
  $classes = [
    'file',
    // Add a specific class for each and every mime type.
    'file--mime-' . strtr($mime_type, ['/' => '-', '.' => '-']),
    // Add a more general class for groups of well known MIME types.
    'file--' . IconMimeTypes::getIconClass($mime_type),
  ];

  // Set file classes to the options array.
  $variables['attributes'] = new Attribute($variables['attributes']);
  $variables['attributes']->addClass($classes);
  $variables['file_size'] = $file->getSize() !== NULL ? ByteSizeMarkup::create($file->getSize()) : '';

  $variables['link'] = Link::fromTextAndUrl($link_text, $url->mergeOptions($options))->toRenderable();
}

/**
 * Prepares variables for multi file form widget templates.
 *
 * Default template: file-widget-multiple.html.twig.
 *
 * @param array $variables
 *   An associative array containing:
 *   - element: A render element representing the widgets.
 */
function template_preprocess_file_widget_multiple(&$variables) {
  $element = $variables['element'];
  // Special ID and classes for draggable tables.
  $weight_class = $element['#id'] . '-weight';
  $table_id = $element['#id'] . '-table';

  // Build up a table of applicable fields.
  $headers = [];
  $headers[] = t('File information');
  if ($element['#display_field']) {
    $headers[] = [
      'data' => t('Display'),
      'class' => ['checkbox'],
    ];
  }
  $headers[] = t('Weight');
  $headers[] = t('Operations');

  // Get our list of widgets in order (needed when the form comes back after
  // preview or failed validation).
  $widgets = [];
  foreach (Element::children($element) as $key) {
    $widgets[] = &$element[$key];
  }
  usort($widgets, '_field_multiple_value_form_sort_helper');

  $rows = [];
  foreach ($widgets as &$widget) {
    // Save the uploading row for last.
    if (empty($widget['#files'])) {
      $widget['#title'] = $element['#file_upload_title'];
      $widget['#description'] = \Drupal::service('renderer')->renderInIsolation($element['#file_upload_description']);
      continue;
    }

    // Delay rendering of the buttons, so that they can be rendered later in the
    // "operations" column.
    $operations_elements = [];
    foreach (Element::children($widget) as $key) {
      if (isset($widget[$key]['#type']) && $widget[$key]['#type'] == 'submit') {
        hide($widget[$key]);
        $operations_elements[] = &$widget[$key];
      }
    }

    // Delay rendering of the "Display" option and the weight selector, so that
    // each can be rendered later in its own column.
    if ($element['#display_field']) {
      hide($widget['display']);
    }
    hide($widget['_weight']);
    $widget['_weight']['#attributes']['class'] = [$weight_class];

    // Render everything else together in a column, without the normal wrappers.
    $row = [];
    $widget['#theme_wrappers'] = [];
    $row[] = \Drupal::service('renderer')->render($widget);

    // Arrange the row with the rest of the rendered columns.
    if ($element['#display_field']) {
      unset($widget['display']['#title']);
      $row[] = [
        'data' => $widget['display'],
        'class' => ['checkbox'],
      ];
    }
    $row[] = [
      'data' => $widget['_weight'],
    ];

    // Show the buttons that had previously been marked as hidden in this
    // preprocess function. We use show() to undo the earlier hide().
    foreach (Element::children($operations_elements) as $key) {
      show($operations_elements[$key]);
    }
    $row[] = [
      'data' => $operations_elements,
    ];
    $rows[] = [
      'data' => $row,
      'class' => isset($widget['#attributes']['class']) ? array_merge($widget['#attributes']['class'], ['draggable']) : ['draggable'],
    ];
  }

  $variables['table'] = [
    '#type' => 'table',
    '#header' => $headers,
    '#rows' => $rows,
    '#attributes' => [
      'id' => $table_id,
    ],
    '#tabledrag' => [
      [
        'action' => 'order',
        'relationship' => 'sibling',
        'group' => $weight_class,
      ],
    ],
    '#access' => !empty($rows),
  ];

  $variables['element'] = $element;
}

/**
 * Prepares variables for file upload help text templates.
 *
 * Default template: file-upload-help.html.twig.
 *
 * @param array $variables
 *   An associative array containing:
 *   - description: The normal description for this field, specified by the
 *     user.
 *   - upload_validators: An array of upload validators as used in
 *     $element['#upload_validators'].
 */
function template_preprocess_file_upload_help(&$variables) {
  $description = $variables['description'];
  $upload_validators = $variables['upload_validators'];
  $cardinality = $variables['cardinality'];

  $descriptions = [];

  if (!empty($description)) {
    $descriptions[] = FieldFilteredMarkup::create($description);
  }
  if (isset($cardinality)) {
    if ($cardinality == -1) {
      $descriptions[] = t('Unlimited number of files can be uploaded to this field.');
    }
    else {
      $descriptions[] = \Drupal::translation()->formatPlural($cardinality, 'One file only.', 'Maximum @count files.');
    }
  }
  if (isset($upload_validators['file_validate_size'])) {
    @trigger_error('\'file_validate_size\' is deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the \'FileSizeLimit\' constraint instead. See https://www.drupal.org/node/3363700', E_USER_DEPRECATED);
    $descriptions[] = t('@size limit.', ['@size' => ByteSizeMarkup::create($upload_validators['file_validate_size'][0])]);
  }
  if (isset($upload_validators['FileSizeLimit'])) {
    $descriptions[] = t('@size limit.', ['@size' => ByteSizeMarkup::create($upload_validators['FileSizeLimit']['fileLimit'])]);
  }

  if (isset($upload_validators['file_validate_extensions'])) {
    @trigger_error('\'file_validate_extensions\' is deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the \'FileExtension\' constraint instead. See https://www.drupal.org/node/3363700', E_USER_DEPRECATED);
    $descriptions[] = t('Allowed types: @extensions.', ['@extensions' => $upload_validators['file_validate_extensions'][0]]);
  }
  if (isset($upload_validators['FileExtension'])) {
    $descriptions[] = t('Allowed types: @extensions.', ['@extensions' => $upload_validators['FileExtension']['extensions']]);
  }

  if (isset($upload_validators['file_validate_image_resolution']) || isset($upload_validators['FileImageDimensions'])) {
    if (isset($upload_validators['file_validate_image_resolution'])) {
      @trigger_error('\'file_validate_image_resolution\' is deprecated in drupal:10.2.0 and is removed from drupal:11.0.0. Use the \'FileImageDimensions\' constraint instead. See https://www.drupal.org/node/3363700', E_USER_DEPRECATED);
      $max = $upload_validators['file_validate_image_resolution'][0];
      $min = $upload_validators['file_validate_image_resolution'][1];
    }
    else {
      $max = $upload_validators['FileImageDimensions']['maxDimensions'];
      $min = $upload_validators['FileImageDimensions']['minDimensions'];
    }
    if ($min && $max && $min == $max) {
      $descriptions[] = t('Images must be exactly <strong>@size</strong> pixels.', ['@size' => $max]);
    }
    elseif ($min && $max) {
      $descriptions[] = t('Images must be larger than <strong>@min</strong> pixels. Images larger than <strong>@max</strong> pixels will be resized.', [
        '@min' => $min,
        '@max' => $max,
      ]);
    }
    elseif ($min) {
      $descriptions[] = t('Images must be larger than <strong>@min</strong> pixels.', ['@min' => $min]);
    }
    elseif ($max) {
      $descriptions[] = t('Images larger than <strong>@max</strong> pixels will be resized.', ['@max' => $max]);
    }
  }

  $variables['descriptions'] = $descriptions;
}

/**
 * Gets a class for the icon for a MIME type.
 *
 * @param string $mime_type
 *   A MIME type.
 *
 * @return string
 *   A class associated with the file.
 *
 * @deprecated in drupal:10.3.0 and is removed from drupal:11.0.0. Use
 *   \Drupal\file\IconMimeTypes::getIconClass() instead.
 *
 * @see https://www.drupal.org/node/3411269
 */
function file_icon_class($mime_type) {
  @trigger_error(__FUNCTION__ . '() is deprecated in drupal:10.3.0 and is removed from drupal:11.0.0. Use \Drupal\file\IconMimeTypes::getIconClass() instead. See https://www.drupal.org/node/3411269', E_USER_DEPRECATED);
  return IconMimeTypes::getIconClass($mime_type);
}

/**
 * Determines the generic icon MIME package based on a file's MIME type.
 *
 * @param string $mime_type
 *   A MIME type.
 *
 * @return string|false
 *   The generic icon MIME package expected for this file.
 *
 *  @deprecated in drupal:10.3.0 and is removed from drupal:11.0.0. Use
 *    \Drupal\file\IconMimeTypes::getGenericMimeType() instead.
 *
 * @see https://www.drupal.org/node/3411269
 */
function file_icon_map($mime_type) {
  @trigger_error(__FUNCTION__ . '() is deprecated in drupal:10.3.0 and is removed from drupal:11.0.0. Use \Drupal\file\IconMimeTypes::getGenericMimeType() instead. See https://www.drupal.org/node/3411269', E_USER_DEPRECATED);
  return IconMimeTypes::getGenericMimeType($mime_type);
}

/**
 * Retrieves a list of references to a file.
 *
 * @param \Drupal\file\FileInterface $file
 *   A file entity.
 * @param \Drupal\Core\Field\FieldDefinitionInterface|null $field
 *   (optional) A field definition to be used for this check. If given,
 *   limits the reference check to the given field. Defaults to NULL.
 * @param int $age
 *   (optional) A constant that specifies which references to count. Use
 *   EntityStorageInterface::FIELD_LOAD_REVISION (the default) to retrieve all
 *   references within all revisions or
 *   EntityStorageInterface::FIELD_LOAD_CURRENT to retrieve references only in
 *   the current revisions of all entities that have references to this file.
 * @param string $field_type
 *   (optional) The name of a field type. If given, limits the reference check
 *   to fields of the given type. If both $field and $field_type are given but
 *   $field is not the same type as $field_type, an empty array will be
 *   returned. Defaults to 'file'.
 *
 * @return array
 *   A multidimensional array. The keys are field_name, entity_type,
 *   entity_id and the value is an entity referencing this file.
 *
 * @ingroup file
 */
function file_get_file_references(FileInterface $file, ?FieldDefinitionInterface $field = NULL, $age = EntityStorageInterface::FIELD_LOAD_REVISION, $field_type = 'file') {
  $references = &drupal_static(__FUNCTION__, []);
  $field_columns = &drupal_static(__FUNCTION__ . ':field_columns', []);

  // Fill the static cache, disregard $field and $field_type for now.
  if (!isset($references[$file->id()][$age])) {
    $references[$file->id()][$age] = [];
    $usage_list = \Drupal::service('file.usage')->listUsage($file);
    $file_usage_list = $usage_list['file'] ?? [];
    foreach ($file_usage_list as $entity_type_id => $entity_ids) {
      $entities = \Drupal::entityTypeManager()
        ->getStorage($entity_type_id)->loadMultiple(array_keys($entity_ids));
      foreach ($entities as $entity) {
        $bundle = $entity->bundle();
        // We need to find file fields for this entity type and bundle.
        if (!isset($file_fields[$entity_type_id][$bundle])) {
          $file_fields[$entity_type_id][$bundle] = [];
          // This contains the possible field names.
          foreach ($entity->getFieldDefinitions() as $field_name => $field_definition) {
            // If this is the first time this field type is seen, check
            // whether it references files.
            if (!isset($field_columns[$field_definition->getType()])) {
              $field_columns[$field_definition->getType()] = file_field_find_file_reference_column($field_definition);
            }
            // If the field type does reference files then record it.
            if ($field_columns[$field_definition->getType()]) {
              $file_fields[$entity_type_id][$bundle][$field_name] = $field_columns[$field_definition->getType()];
            }
          }
        }
        foreach ($file_fields[$entity_type_id][$bundle] as $field_name => $field_column) {
          // Iterate over the field items to find the referenced file and field
          // name. This will fail if the usage checked is in a non-current
          // revision because field items are from the current
          // revision.
          // We also iterate over all translations because a file can be linked
          // to a language other than the default.
          foreach ($entity->getTranslationLanguages() as $langcode => $language) {
            foreach ($entity->getTranslation($langcode)->get($field_name) as $item) {
              if ($file->id() == $item->{$field_column}) {
                $references[$file->id()][$age][$field_name][$entity_type_id][$entity->id()] = $entity;
                break;
              }
            }
          }
        }
      }
    }
  }
  $return = $references[$file->id()][$age];
  // Filter the static cache down to the requested entries. The usual static
  // cache is very small so this will be very fast.
  $entity_field_manager = \Drupal::service('entity_field.manager');
  if ($field || $field_type) {
    foreach ($return as $field_name => $data) {
      foreach (array_keys($data) as $entity_type_id) {
        $field_storage_definitions = $entity_field_manager->getFieldStorageDefinitions($entity_type_id);
        $current_field = $field_storage_definitions[$field_name];
        if (($field_type && $current_field->getType() != $field_type) || ($field && $field->uuid() != $current_field->uuid())) {
          unset($return[$field_name][$entity_type_id]);
        }
      }
    }
  }
  return $return;
}

/**
 * Determine whether a field references files stored in {file_managed}.
 *
 * @param \Drupal\Core\Field\FieldDefinitionInterface $field
 *   A field definition.
 *
 * @return bool
 *   The field column if the field references {file_managed}.fid, typically
 *   fid, FALSE if it does not.
 */
function file_field_find_file_reference_column(FieldDefinitionInterface $field) {
  $schema = $field->getFieldStorageDefinition()->getSchema();
  foreach ($schema['foreign keys'] as $data) {
    if ($data['table'] == 'file_managed') {
      foreach ($data['columns'] as $field_column => $column) {
        if ($column == 'fid') {
          return $field_column;
        }
      }
    }
  }
  return FALSE;
}

/**
 * Implements hook_form_FORM_ID_alter().
 *
 * Injects the file sanitization options into /admin/config/media/file-system.
 *
 * These settings are enforced during upload by the FileEventSubscriber that
 * listens to the FileUploadSanitizeNameEvent event.
 *
 * @see \Drupal\system\Form\FileSystemForm
 * @see \Drupal\Core\File\Event\FileUploadSanitizeNameEvent
 * @see \Drupal\file\EventSubscriber\FileEventSubscriber
 */
function file_form_system_file_system_settings_alter(array &$form, FormStateInterface $form_state) {
  $config = \Drupal::config('file.settings');
  $form['filename_sanitization'] = [
    '#type' => 'details',
    '#title' => t('Sanitize filenames'),
    '#description' => t('These settings only apply to new files as they are uploaded. Changes here do not affect existing file names.'),
    '#open' => TRUE,
    '#tree' => TRUE,
  ];

  $form['filename_sanitization']['replacement_character'] = [
    '#type' => 'select',
    '#title' => t('Replacement character'),
    '#default_value' => $config->get('filename_sanitization.replacement_character'),
    '#options' => [
      '-' => t('Dash (-)'),
      '_' => t('Underscore (_)'),
    ],
    '#description' => t('Used when replacing whitespace, replacing non-alphanumeric characters or transliterating unknown characters.'),
  ];

  $form['filename_sanitization']['transliterate'] = [
    '#type' => 'checkbox',
    '#title' => t('Transliterate'),
    '#default_value' => $config->get('filename_sanitization.transliterate'),
    '#description' => t('Transliteration replaces any characters that are not alphanumeric, underscores, periods or hyphens with the replacement character. It ensures filenames only contain ASCII characters. It is recommended to keep transliteration enabled.'),
  ];

  $form['filename_sanitization']['replace_whitespace'] = [
    '#type' => 'checkbox',
    '#title' => t('Replace whitespace with the replacement character'),
    '#default_value' => $config->get('filename_sanitization.replace_whitespace'),
  ];

  $form['filename_sanitization']['replace_non_alphanumeric'] = [
    '#type' => 'checkbox',
    '#title' => t('Replace non-alphanumeric characters with the replacement character'),
    '#default_value' => $config->get('filename_sanitization.replace_non_alphanumeric'),
    '#description' => t('Alphanumeric characters, dots <span aria-hidden="true">(.)</span>, underscores <span aria-hidden="true">(_)</span> and dashes <span aria-hidden="true">(-)</span> are preserved.'),
  ];

  $form['filename_sanitization']['deduplicate_separators'] = [
    '#type' => 'checkbox',
    '#title' => t('Replace sequences of dots, underscores and/or dashes with the replacement character'),
    '#default_value' => $config->get('filename_sanitization.deduplicate_separators'),
  ];

  $form['filename_sanitization']['lowercase'] = [
    '#type' => 'checkbox',
    '#title' => t('Convert to lowercase'),
    '#default_value' => $config->get('filename_sanitization.lowercase'),
  ];

  $form['#submit'][] = 'file_system_settings_submit';
}

/**
 * Form submission handler for file system settings form.
 */
function file_system_settings_submit(array &$form, FormStateInterface $form_state) {
  $config = \Drupal::configFactory()->getEditable('file.settings')
    ->set('filename_sanitization', $form_state->getValue('filename_sanitization'));
  $config->save();
}
