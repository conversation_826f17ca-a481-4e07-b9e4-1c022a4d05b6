/*
 * DO NOT EDIT THIS FILE.
 * See the following change record for more information,
 * https://www.drupal.org/node/3084859
 * @preserve
 */
/* cspell:ignore csvg cpath wght */
/**
 * @file
 * Toolbar button styles.
 */
:root {
  --toolbar-button-outline-offset: 0;
}
.toolbar-button {
  z-index: 1;
  align-items: center;
  padding-inline: var(--admin-toolbar-space-16);
  padding-block: var(--admin-toolbar-space-10);
  min-height: var(--admin-toolbar-space-40);
  cursor: pointer;
  text-align: start;
  -webkit-text-decoration: none;
  text-decoration: none;
  word-break: break-word;
  color: var(--admin-toolbar-color-gray-800);
  border: 0;
  border-radius: var(--admin-toolbar-space-8);
  background-color: transparent;
  font-size: var(--admin-toolbar-font-size-info-sm);
  font-variation-settings: "wght" 700;
  line-height: var(--admin-toolbar-line-height-info-sm);
  gap: calc(0.5 * var(--admin-toolbar-rem));
}
.toolbar-button:has(+ .toolbar-popover__wrapper .is-active) {
  color: var(--admin-toolbar-color-gray-950);
  background-color: var(--admin-toolbar-color-gray-050);
}
.toolbar-button:hover {
  z-index: 20;
  color: var(--admin-toolbar-color-gray-990);
  outline: 2px solid var(--admin-toolbar-color-blue-200);
  outline-offset: var(--toolbar-button-outline-offset);
  background-color: var(--admin-toolbar-color-gray-050);
}
.toolbar-button:focus {
  z-index: 10;
  color: var(--admin-toolbar-color-blue-700);
  outline: 2px solid var(--admin-toolbar-color-focus);
  outline-offset: var(--toolbar-button-outline-offset);
}
.toolbar-button.current {
  color: var(--admin-toolbar-color-blue-700);
  background-color: var(--admin-toolbar-color-gray-050);
}
/* Dark color modifier for submenus title */
.toolbar-button--dark {
  color: var(--admin-toolbar-color-gray-990);
}
.toolbar-button--large {
  padding: var(--admin-toolbar-space-8) var(--admin-toolbar-space-16);
  font-size: var(--admin-toolbar-font-size-info-lg);
  line-height: var(--admin-toolbar-line-height-info-lg);
}
.toolbar-button--non-interactive:hover,
.toolbar-button--non-interactive:focus,
.toolbar-button--non-interactive:hover:focus {
  z-index: 1;
  cursor: auto;
  color: var(--admin-toolbar-color-gray-800);
  outline: 0;
  background-color: transparent;
}
.toolbar-button--small-offset {
  --toolbar-button-outline-offset: calc(-1 * var(--admin-toolbar-space-4));
}
/* Class starts with `toolbar-button--icon`  */
[class*="toolbar-button--icon"] {
  padding-inline: var(--admin-toolbar-space-10);
}
[class*="toolbar-button--icon"]::before {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  content: attr(data-icon-text);
  color: currentColor;
  background-image: linear-gradient(currentColor, currentColor 50%, transparent 50%);
  background-position-y: calc(100% - (100% * var(--icon, 0)));
  background-size: 100% 200%;
  font-size: calc(0.75 * var(--admin-toolbar-rem));
  inline-size: var(--admin-toolbar-space-20);
  block-size: var(--admin-toolbar-space-20);
  mask-repeat: no-repeat;
  mask-position: center center;
  mask-size: 100% auto;
  mask-image: var(--icon);
}
[class*="toolbar-button--icon"]:hover::before {
  background-color: linear-gradient(var(--admin-toolbar-color-blue-600), var(--admin-toolbar-color-blue-600) 50%, transparent 50%);
}
@media (forced-colors: active) {
  [class*="toolbar-button--icon"]::before,
  [class*="toolbar-button--icon"]:hover::before {
    background: canvastext;
  }
  a[class*="toolbar-button--icon"]::before,
  a[class*="toolbar-button--icon"]:hover::before {
    background: linktext;
  }
}
.toolbar-button--weight--400 {
  font-variation-settings: "wght" 400;
}
/* Set 0 specificity */
:where(.toolbar-button) {
  display: flex;
  flex-grow: 1;
}
[class*="toolbar-button--expand"]::after {
  flex-shrink: 0;
  margin-inline-start: auto;
  content: "";
  transition: transform var(--admin-toolbar-transition);
  background-color: currentColor;
  block-size: var(--admin-toolbar-space-16);
  inline-size: var(--admin-toolbar-space-16);
  mask-size: var(--admin-toolbar-space-16);
  mask-repeat: no-repeat;
  mask-position: center center;
  mask-image: url("data:image/svg+xml,%3csvg viewBox='0 0 8 8' xmlns='http://www.w3.org/2000/svg'%3e  %3cpath d='M5.7653 4.2653L3.2653 6.7653C3.19485 6.83575 3.0993 6.87532 2.99967 6.87532C2.90005 6.87532 2.8045 6.83575 2.73405 6.7653C2.6636 6.69485 2.62402 6.5993 2.62402 6.49967C2.62402 6.40005 2.6636 6.3045 2.73405 6.23405L4.96874 3.99999L2.73467 1.7653C2.69979 1.73042 2.67212 1.68901 2.65324 1.64343C2.63436 1.59785 2.62465 1.54901 2.62465 1.49967C2.62465 1.45034 2.63436 1.40149 2.65324 1.35592C2.67212 1.31034 2.69979 1.26893 2.73467 1.23405C2.76956 1.19917 2.81097 1.1715 2.85654 1.15262C2.90212 1.13374 2.95097 1.12402 3.0003 1.12402C3.04963 1.12402 3.09848 1.13374 3.14405 1.15262C3.18963 1.1715 3.23104 1.19917 3.26592 1.23405L5.76592 3.73405C5.80084 3.76893 5.82853 3.81036 5.84741 3.85596C5.86628 3.90157 5.87597 3.95045 5.87591 3.9998C5.87585 4.04916 5.86605 4.09802 5.84707 4.14358C5.82809 4.18914 5.8003 4.2305 5.7653 4.2653Z'/%3e%3c/svg%3e");
}
:where([dir="rtl"]) [class*="toolbar-button--expand"]::after {
  transform: rotate(180deg);
}
@media (forced-colors: active) {
  [class*="toolbar-button--expand"]::after {
    background: canvastext;
  }
}
.toolbar-button--expand--down::after {
  transform: rotate(90deg);
}
@media (forced-colors: active) {
  .toolbar-button--expand--down::after {
    background: canvastext;
  }
}
.toolbar-button--expand--down[aria-expanded="true"]::after {
  transform: rotate(-90deg);
}
.toolbar-button--expand--down[aria-expanded="true"]:focus,
.toolbar-button--expand--down[aria-expanded="true"]:hover {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.toolbar-button--icon--announcements-feed-announcement {
  --icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 256 256'%3e  %3cpath fill='none' d='M0 0h256v256H0z'/%3e  %3cpath fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='16' d='M160 80v120.67a8 8 0 0 0 3.56 6.65l11 7.33a8 8 0 0 0 12.2-4.72L200 160'/%3e  %3cpath fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='16' d='M40 200a8 8 0 0 0 13.15 6.12C105.55 162.16 160 160 160 160h40a40 40 0 0 0 0-80h-40s-54.45-2.16-106.85-46.11A8 8 0 0 0 40 40Z'/%3e%3c/svg%3e");
}
.toolbar-button--icon--announcements-feed-announcement::before {
  transform: scaleX(-1);
}
[dir="rtl"] .toolbar-button--icon--announcements-feed-announcement::before {
  transform: scaleX(1);
}
.toolbar-button--icon--back {
  --icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e  %3cpath d='M14.2501 7.99994c0 .19892-.079.38968-.2197.53033-.1406.14066-.3314.21967-.5303.21967H4.3126l3.22 3.21936c.1409.1409.22005.332.22005.5313 0 .1992-.07915.3903-.22005.5312-.14089.1409-.33199.2201-.53125.2201-.19925 0-.39035-.0792-.53125-.2201l-4.5-4.49998c-.06992-.06968-.12539-.15247-.16325-.24364-.03785-.09116-.05734-.1889-.05734-.28761 0-.09871.01949-.19645.05734-.28762.03786-.09116.09333-.17395.16325-.24363l4.5-4.5c.06977-.06977.15259-.12511.24374-.16286.09115-.03776.18885-.05719.28751-.05719.09867 0 .19636.01943.28751.05719.09116.03775.17398.09309.24374.16286.06977.06976.12511.15259.16286.24374.03776.09115.05719.18885.05719.28751s-.01943.19636-.05719.28751c-.03775.09115-.09309.17397-.16286.24374l-3.22 3.21812h9.1875c.1989 0 .3897.07902.5303.21967.1407.14066.2197.33142.2197.53033Z'/%3e%3c/svg%3e");
}
.toolbar-button--icon--burger {
  --icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 18 14'%3e  %3cpath stroke='%2355565B' stroke-width='2' d='M0 1h18M0 7h18M0 13h18'/%3e%3c/svg%3e");
}
.toolbar-button--icon--cross {
  --icon: url("data:image/svg+xml,%3csvg role='img' xmlns='http://www.w3.org/2000/svg' fill='none'  viewbox='0 0 20 20'%3e  %3cpath fill='%2355565B'    d='M16.2883 14.9617c.1761.1762.275.415.275.6641 0 .2491-.0989.4879-.275.6641-.1761.1761-.415.275-.6641.275-.2491 0-.4879-.0989-.664-.275L10 11.3281l-4.96172 4.9602c-.17612.1761-.41499.2751-.66406.2751-.24908 0-.48795-.099-.66407-.2751-.17612-.1761-.27506-.415-.27506-.6641 0-.249.09894-.4879.27506-.664L8.67187 10 3.71172 5.0383c-.17613-.17612-.27507-.41499-.27507-.66407 0-.24907.09894-.48794.27507-.66406.17612-.17612.41499-.27506.66406-.27506.24907 0 .48794.09894.66406.27506L10 8.67189l4.9617-4.9625c.1761-.17612.415-.27506.6641-.27506.249 0 .4879.09894.664.27506.1762.17612.2751.41499.2751.66406 0 .24907-.0989.48794-.2751.66407L11.3281 10l4.9602 4.9617Z'%3e  %3c/path%3e%3c/svg%3e");
}
.toolbar-button--icon--entity-user-collection {
  --icon: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e  %3cpath d='M10.9922 14.805C11.9974 14.1358 12.7606 13.1609 13.1688 12.0244C13.5771 10.8879 13.6088 9.65024 13.2592 8.49437C12.9096 7.3385 12.1974 6.32581 11.2278 5.60605C10.2581 4.88629 9.08262 4.49768 7.87505 4.49768C6.66747 4.49768 5.49196 4.88629 4.52233 5.60605C3.55271 6.32581 2.84045 7.3385 2.49088 8.49437C2.14132 9.65024 2.17301 10.8879 2.58126 12.0244C2.98952 13.1609 3.75267 14.1358 4.75786 14.805C2.93957 15.4751 1.38671 16.7152 0.330984 18.3403C0.275499 18.4227 0.236959 18.5154 0.217604 18.6129C0.198249 18.7104 0.198466 18.8108 0.218241 18.9082C0.238017 19.0056 0.276957 19.0981 0.332798 19.1803C0.388639 19.2625 0.460266 19.3328 0.543518 19.3871C0.626769 19.4414 0.719983 19.4786 0.817742 19.4966C0.9155 19.5146 1.01585 19.5129 1.11297 19.4917C1.21008 19.4706 1.30202 19.4303 1.38344 19.3733C1.46486 19.3163 1.53413 19.2437 1.58723 19.1597C2.26822 18.1123 3.20007 17.2516 4.29814 16.6558C5.39622 16.0599 6.62574 15.7479 7.87505 15.7479C9.12435 15.7479 10.3539 16.0599 11.4519 16.6558C12.55 17.2516 13.4819 18.1123 14.1629 19.1597C14.2729 19.3231 14.4428 19.4368 14.6359 19.476C14.829 19.5152 15.0298 19.4768 15.1949 19.3692C15.3599 19.2615 15.476 19.0932 15.5179 18.9007C15.5599 18.7082 15.5244 18.5068 15.4191 18.3403C14.3634 16.7152 12.8105 15.4751 10.9922 14.805ZM3.75005 10.125C3.75005 9.30912 3.99197 8.51159 4.44523 7.83324C4.8985 7.15488 5.54273 6.62617 6.29648 6.31396C7.05022 6.00175 7.87962 5.92006 8.67979 6.07922C9.47997 6.23839 10.215 6.63126 10.7919 7.20815C11.3688 7.78504 11.7616 8.52004 11.9208 9.32022C12.0799 10.1204 11.9983 10.9498 11.686 11.7035C11.3738 12.4573 10.8451 13.1015 10.1668 13.5548C9.48842 14.008 8.69089 14.25 7.87505 14.25C6.78141 14.2487 5.73292 13.8137 4.9596 13.0404C4.18628 12.2671 3.75129 11.2186 3.75005 10.125ZM23.4507 19.3781C23.2841 19.4867 23.0812 19.5247 22.8865 19.4838C22.6919 19.4428 22.5215 19.3262 22.4129 19.1597C21.7327 18.1116 20.801 17.2506 19.7027 16.655C18.6044 16.0594 17.3744 15.7483 16.125 15.75C15.9261 15.75 15.7354 15.6709 15.5947 15.5303C15.4541 15.3896 15.375 15.1989 15.375 15C15.375 14.8011 15.4541 14.6103 15.5947 14.4696C15.7354 14.329 15.9261 14.25 16.125 14.25C16.7325 14.2494 17.3324 14.1147 17.8817 13.8554C18.4311 13.5961 18.9164 13.2187 19.303 12.7501C19.6896 12.2815 19.9679 11.7334 20.1181 11.1448C20.2683 10.5561 20.2866 9.94162 20.1718 9.3451C20.0569 8.74859 19.8117 8.1848 19.4537 7.69402C19.0958 7.20325 18.6338 6.7976 18.1008 6.50606C17.5679 6.21452 16.9771 6.04429 16.3708 6.00752C15.7644 5.97076 15.1574 6.06838 14.5932 6.2934C14.5012 6.33317 14.4021 6.3541 14.3019 6.35495C14.2017 6.35579 14.1023 6.33654 14.0096 6.29833C13.917 6.26012 13.8329 6.20373 13.7624 6.13248C13.6919 6.06123 13.6364 5.97658 13.5992 5.88352C13.562 5.79046 13.5438 5.69088 13.5457 5.59067C13.5476 5.49046 13.5696 5.39165 13.6103 5.30007C13.6511 5.2085 13.7098 5.12602 13.7829 5.05752C13.8561 4.98901 13.9422 4.93586 14.0363 4.90121C15.3277 4.3862 16.764 4.36767 18.0682 4.84919C19.3725 5.33071 20.4522 6.27817 21.099 7.50879C21.7459 8.73941 21.9141 10.166 21.5713 11.5133C21.2284 12.8607 20.3987 14.0333 19.2422 14.805C21.0605 15.4751 22.6134 16.7152 23.6691 18.3403C23.7777 18.5069 23.8158 18.7098 23.7748 18.9044C23.7338 19.099 23.6172 19.2694 23.4507 19.3781Z' fill='currentColor'/%3e%3c/svg%3e");
}
.toolbar-button--icon--help {
  --icon: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e  %3cpath d='M13.125 16.875C13.125 17.0975 13.059 17.315 12.9354 17.5C12.8118 17.685 12.6361 17.8292 12.4305 17.9144C12.225 17.9995 11.9988 18.0218 11.7805 17.9784C11.5623 17.935 11.3618 17.8278 11.2045 17.6705C11.0472 17.5132 10.94 17.3127 10.8966 17.0945C10.8532 16.8762 10.8755 16.65 10.9606 16.4445C11.0458 16.2389 11.19 16.0632 11.375 15.9396C11.56 15.816 11.7775 15.75 12 15.75C12.2984 15.75 12.5845 15.8685 12.7955 16.0795C13.0065 16.2905 13.125 16.5766 13.125 16.875ZM12 6.75C9.93188 6.75 8.25 8.26406 8.25 10.125V10.5C8.25 10.6989 8.32902 10.8897 8.46967 11.0303C8.61033 11.171 8.80109 11.25 9 11.25C9.19892 11.25 9.38968 11.171 9.53033 11.0303C9.67099 10.8897 9.75 10.6989 9.75 10.5V10.125C9.75 9.09375 10.7597 8.25 12 8.25C13.2403 8.25 14.25 9.09375 14.25 10.125C14.25 11.1562 13.2403 12 12 12C11.8011 12 11.6103 12.079 11.4697 12.2197C11.329 12.3603 11.25 12.5511 11.25 12.75V13.5C11.25 13.6989 11.329 13.8897 11.4697 14.0303C11.6103 14.171 11.8011 14.25 12 14.25C12.1989 14.25 12.3897 14.171 12.5303 14.0303C12.671 13.8897 12.75 13.6989 12.75 13.5V13.4325C14.46 13.1184 15.75 11.7544 15.75 10.125C15.75 8.26406 14.0681 6.75 12 6.75ZM21.75 12C21.75 13.9284 21.1782 15.8134 20.1068 17.4168C19.0355 19.0202 17.5127 20.2699 15.7312 21.0078C13.9496 21.7458 11.9892 21.9389 10.0979 21.5627C8.20656 21.1865 6.46928 20.2579 5.10571 18.8943C3.74215 17.5307 2.81355 15.7934 2.43735 13.9021C2.06114 12.0108 2.25422 10.0504 2.99218 8.26884C3.73013 6.48726 4.97982 4.96452 6.58319 3.89317C8.18657 2.82183 10.0716 2.25 12 2.25C14.585 2.25273 17.0634 3.28084 18.8913 5.10872C20.7192 6.93661 21.7473 9.41498 21.75 12ZM20.25 12C20.25 10.3683 19.7662 8.77325 18.8596 7.41655C17.9531 6.05984 16.6646 5.00242 15.1571 4.37799C13.6497 3.75357 11.9909 3.59019 10.3905 3.90852C8.79017 4.22685 7.32016 5.01259 6.16637 6.16637C5.01259 7.32015 4.22685 8.79016 3.90853 10.3905C3.5902 11.9908 3.75358 13.6496 4.378 15.1571C5.00242 16.6646 6.05984 17.9531 7.41655 18.8596C8.77326 19.7661 10.3683 20.25 12 20.25C14.1873 20.2475 16.2843 19.3775 17.8309 17.8309C19.3775 16.2843 20.2475 14.1873 20.25 12Z' fill='currentColor'/%3e%3c/svg%3e");
}
.toolbar-button--icon--navigation-blocks {
  --icon: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e  %3cpath d='M9.75 3.75H5.25C4.85218 3.75 4.47064 3.90804 4.18934 4.18934C3.90804 4.47064 3.75 4.85218 3.75 5.25V9.75C3.75 10.1478 3.90804 10.5294 4.18934 10.8107C4.47064 11.092 4.85218 11.25 5.25 11.25H9.75C10.1478 11.25 10.5294 11.092 10.8107 10.8107C11.092 10.5294 11.25 10.1478 11.25 9.75V5.25C11.25 4.85218 11.092 4.47064 10.8107 4.18934C10.5294 3.90804 10.1478 3.75 9.75 3.75ZM9.75 9.75H5.25V5.25H9.75V9.75ZM18.75 3.75H14.25C13.8522 3.75 13.4706 3.90804 13.1893 4.18934C12.908 4.47064 12.75 4.85218 12.75 5.25V9.75C12.75 10.1478 12.908 10.5294 13.1893 10.8107C13.4706 11.092 13.8522 11.25 14.25 11.25H18.75C19.1478 11.25 19.5294 11.092 19.8107 10.8107C20.092 10.5294 20.25 10.1478 20.25 9.75V5.25C20.25 4.85218 20.092 4.47064 19.8107 4.18934C19.5294 3.90804 19.1478 3.75 18.75 3.75ZM18.75 9.75H14.25V5.25H18.75V9.75ZM9.75 12.75H5.25C4.85218 12.75 4.47064 12.908 4.18934 13.1893C3.90804 13.4706 3.75 13.8522 3.75 14.25V18.75C3.75 19.1478 3.90804 19.5294 4.18934 19.8107C4.47064 20.092 4.85218 20.25 5.25 20.25H9.75C10.1478 20.25 10.5294 20.092 10.8107 19.8107C11.092 19.5294 11.25 19.1478 11.25 18.75V14.25C11.25 13.8522 11.092 13.4706 10.8107 13.1893C10.5294 12.908 10.1478 12.75 9.75 12.75ZM9.75 18.75H5.25V14.25H9.75V18.75ZM18.75 12.75H14.25C13.8522 12.75 13.4706 12.908 13.1893 13.1893C12.908 13.4706 12.75 13.8522 12.75 14.25V18.75C12.75 19.1478 12.908 19.5294 13.1893 19.8107C13.4706 20.092 13.8522 20.25 14.25 20.25H18.75C19.1478 20.25 19.5294 20.092 19.8107 19.8107C20.092 19.5294 20.25 19.1478 20.25 18.75V14.25C20.25 13.8522 20.092 13.4706 19.8107 13.1893C19.5294 12.908 19.1478 12.75 18.75 12.75ZM18.75 18.75H14.25V14.25H18.75V18.75Z' fill='currentColor'/%3e%3c/svg%3e");
}
.toolbar-button--icon--navigation-content {
  --icon: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e  %3cpath d='M20.25 3.75H3.75C3.35218 3.75 2.97064 3.90804 2.68934 4.18934C2.40804 4.47064 2.25 4.85218 2.25 5.25V18.75C2.25 19.1478 2.40804 19.5294 2.68934 19.8107C2.97064 20.092 3.35218 20.25 3.75 20.25H20.25C20.6478 20.25 21.0294 20.092 21.3107 19.8107C21.592 19.5294 21.75 19.1478 21.75 18.75V5.25C21.75 4.85218 21.592 4.47064 21.3107 4.18934C21.0294 3.90804 20.6478 3.75 20.25 3.75ZM20.25 18.75H3.75V5.25H20.25V18.75ZM17.25 9C17.25 9.19891 17.171 9.38968 17.0303 9.53033C16.8897 9.67098 16.6989 9.75 16.5 9.75H7.5C7.30109 9.75 7.11032 9.67098 6.96967 9.53033C6.82902 9.38968 6.75 9.19891 6.75 9C6.75 8.80109 6.82902 8.61032 6.96967 8.46967C7.11032 8.32902 7.30109 8.25 7.5 8.25H16.5C16.6989 8.25 16.8897 8.32902 17.0303 8.46967C17.171 8.61032 17.25 8.80109 17.25 9ZM17.25 12C17.25 12.1989 17.171 12.3897 17.0303 12.5303C16.8897 12.671 16.6989 12.75 16.5 12.75H7.5C7.30109 12.75 7.11032 12.671 6.96967 12.5303C6.82902 12.3897 6.75 12.1989 6.75 12C6.75 11.8011 6.82902 11.6103 6.96967 11.4697C7.11032 11.329 7.30109 11.25 7.5 11.25H16.5C16.6989 11.25 16.8897 11.329 17.0303 11.4697C17.171 11.6103 17.25 11.8011 17.25 12ZM17.25 15C17.25 15.1989 17.171 15.3897 17.0303 15.5303C16.8897 15.671 16.6989 15.75 16.5 15.75H7.5C7.30109 15.75 7.11032 15.671 6.96967 15.5303C6.82902 15.3897 6.75 15.1989 6.75 15C6.75 14.8011 6.82902 14.6103 6.96967 14.4697C7.11032 14.329 7.30109 14.25 7.5 14.25H16.5C16.6989 14.25 16.8897 14.329 17.0303 14.4697C17.171 14.6103 17.25 14.8011 17.25 15Z' fill='currentColor'/%3e%3c/svg%3e");
}
.toolbar-button--icon--navigation-create {
  --icon: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e  %3cpath d='M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96452 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM12 20.25C10.3683 20.25 8.77326 19.7661 7.41655 18.8596C6.05984 17.9531 5.00242 16.6646 4.378 15.1571C3.75358 13.6496 3.5902 11.9908 3.90853 10.3905C4.22685 8.79016 5.01259 7.32015 6.16637 6.16637C7.32016 5.01259 8.79017 4.22685 10.3905 3.90852C11.9909 3.59019 13.6497 3.75357 15.1571 4.37799C16.6646 5.00242 17.9531 6.05984 18.8596 7.41655C19.7662 8.77325 20.25 10.3683 20.25 12C20.2475 14.1873 19.3775 16.2843 17.8309 17.8309C16.2843 19.3775 14.1873 20.2475 12 20.25ZM16.5 12C16.5 12.1989 16.421 12.3897 16.2803 12.5303C16.1397 12.671 15.9489 12.75 15.75 12.75H12.75V15.75C12.75 15.9489 12.671 16.1397 12.5303 16.2803C12.3897 16.421 12.1989 16.5 12 16.5C11.8011 16.5 11.6103 16.421 11.4697 16.2803C11.329 16.1397 11.25 15.9489 11.25 15.75V12.75H8.25C8.05109 12.75 7.86033 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86033 11.329 8.05109 11.25 8.25 11.25H11.25V8.25C11.25 8.05109 11.329 7.86032 11.4697 7.71967C11.6103 7.57902 11.8011 7.5 12 7.5C12.1989 7.5 12.3897 7.57902 12.5303 7.71967C12.671 7.86032 12.75 8.05109 12.75 8.25V11.25H15.75C15.9489 11.25 16.1397 11.329 16.2803 11.4697C16.421 11.6103 16.5 11.8011 16.5 12Z' fill='currentColor'/%3e%3c/svg%3e");
}
.toolbar-button--icon--navigation-files {
  --icon: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e  %3cpath d='M20.0306 6.21938L16.2806 2.46938C16.2109 2.39975 16.1282 2.34454 16.0371 2.3069C15.9461 2.26926 15.8485 2.24992 15.75 2.25H8.25C7.85218 2.25 7.47064 2.40804 7.18934 2.68934C6.90804 2.97064 6.75 3.35218 6.75 3.75V5.25H5.25C4.85218 5.25 4.47064 5.40804 4.18934 5.68934C3.90804 5.97064 3.75 6.35218 3.75 6.75V20.25C3.75 20.6478 3.90804 21.0294 4.18934 21.3107C4.47064 21.592 4.85218 21.75 5.25 21.75H15.75C16.1478 21.75 16.5294 21.592 16.8107 21.3107C17.092 21.0294 17.25 20.6478 17.25 20.25V18.75H18.75C19.1478 18.75 19.5294 18.592 19.8107 18.3107C20.092 18.0294 20.25 17.6478 20.25 17.25V6.75C20.2501 6.65148 20.2307 6.55391 20.1931 6.46286C20.1555 6.37182 20.1003 6.28908 20.0306 6.21938ZM15.75 20.25H5.25V6.75H12.4397L15.75 10.0603V17.985C15.75 17.9906 15.75 17.9953 15.75 18C15.75 18.0047 15.75 18.0094 15.75 18.015V20.25ZM18.75 17.25H17.25V9.75C17.2501 9.65148 17.2307 9.55391 17.1931 9.46286C17.1555 9.37182 17.1003 9.28908 17.0306 9.21937L13.2806 5.46938C13.2109 5.39975 13.1282 5.34454 13.0371 5.3069C12.9461 5.26926 12.8485 5.24992 12.75 5.25H8.25V3.75H15.4397L18.75 7.06031V17.25ZM13.5 14.25C13.5 14.4489 13.421 14.6397 13.2803 14.7803C13.1397 14.921 12.9489 15 12.75 15H8.25C8.05109 15 7.86032 14.921 7.71967 14.7803C7.57902 14.6397 7.5 14.4489 7.5 14.25C7.5 14.0511 7.57902 13.8603 7.71967 13.7197C7.86032 13.579 8.05109 13.5 8.25 13.5H12.75C12.9489 13.5 13.1397 13.579 13.2803 13.7197C13.421 13.8603 13.5 14.0511 13.5 14.25ZM13.5 17.25C13.5 17.4489 13.421 17.6397 13.2803 17.7803C13.1397 17.921 12.9489 18 12.75 18H8.25C8.05109 18 7.86032 17.921 7.71967 17.7803C7.57902 17.6397 7.5 17.4489 7.5 17.25C7.5 17.0511 7.57902 16.8603 7.71967 16.7197C7.86032 16.579 8.05109 16.5 8.25 16.5H12.75C12.9489 16.5 13.1397 16.579 13.2803 16.7197C13.421 16.8603 13.5 17.0511 13.5 17.25Z' fill='currentColor'/%3e%3c/svg%3e");
}
.toolbar-button--icon--navigation-media {
  --icon: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e  %3cpath d='M19.5 3H7.5C7.10218 3 6.72064 3.15804 6.43934 3.43934C6.15804 3.72064 6 4.10218 6 4.5V6H4.5C4.10218 6 3.72064 6.15804 3.43934 6.43934C3.15804 6.72064 3 7.10218 3 7.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H16.5C16.8978 21 17.2794 20.842 17.5607 20.5607C17.842 20.2794 18 19.8978 18 19.5V18H19.5C19.8978 18 20.2794 17.842 20.5607 17.5607C20.842 17.2794 21 16.8978 21 16.5V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM7.5 4.5H19.5V11.0044L17.9344 9.43875C17.6531 9.15766 17.2717 8.99976 16.8741 8.99976C16.4764 8.99976 16.095 9.15766 15.8137 9.43875L8.75344 16.5H7.5V4.5ZM16.5 19.5H4.5V7.5H6V16.5C6 16.8978 6.15804 17.2794 6.43934 17.5607C6.72064 17.842 7.10218 18 7.5 18H16.5V19.5ZM19.5 16.5H10.875L16.875 10.5L19.5 13.125V16.5ZM11.25 10.5C11.695 10.5 12.13 10.368 12.5 10.1208C12.87 9.87357 13.1584 9.52217 13.3287 9.11104C13.499 8.6999 13.5436 8.2475 13.4568 7.81105C13.37 7.37459 13.1557 6.97368 12.841 6.65901C12.5263 6.34434 12.1254 6.13005 11.689 6.04323C11.2525 5.95642 10.8001 6.00097 10.389 6.17127C9.97783 6.34157 9.62643 6.62996 9.37919 6.99997C9.13196 7.36998 9 7.80499 9 8.25C9 8.84674 9.23705 9.41903 9.65901 9.84099C10.081 10.2629 10.6533 10.5 11.25 10.5ZM11.25 7.5C11.3983 7.5 11.5433 7.54399 11.6667 7.6264C11.79 7.70881 11.8861 7.82594 11.9429 7.96299C11.9997 8.10003 12.0145 8.25083 11.9856 8.39632C11.9567 8.5418 11.8852 8.67544 11.7803 8.78033C11.6754 8.88522 11.5418 8.95665 11.3963 8.98559C11.2508 9.01453 11.1 8.99968 10.963 8.94291C10.8259 8.88614 10.7088 8.79001 10.6264 8.66668C10.544 8.54334 10.5 8.39834 10.5 8.25C10.5 8.05109 10.579 7.86032 10.7197 7.71967C10.8603 7.57902 11.0511 7.5 11.25 7.5Z' fill='currentColor'/%3e%3c/svg%3e");
}
.toolbar-button--icon--pencil {
  --icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e  %3cpath d='M17.7586 5.73214 14.268 2.24073c-.1161-.1161-.2539-.2082-.4056-.27104-.1517-.06284-.3142-.09518-.4784-.09518s-.3268.03234-.4784.09518c-.1517.06284-.2895.15494-.4056.27104L2.86641 11.8751c-.11658.1157-.209.2533-.2719.405-.06291.1517-.09503.3144-.0945.4786v3.4914c0 .3315.13169.6495.36611.8839.23442.2344.55237.3661.88389.3661h3.4914c.16422.0005.32689-.0316.47858-.0945s.28936-.1553.40502-.2719l9.63359-9.6336c.1161-.11607.2082-.25388.271-.40556.0629-.15168.0952-.31424.0952-.47842s-.0323-.32675-.0952-.47842c-.0628-.15168-.1549-.28949-.271-.40556Zm-13.75 6.76796 6.6164-6.6164 1.3039 1.3039-6.61639 6.6156-1.30391-1.3031Zm-.25859 1.5086 2.2414 2.2414h-2.2414v-2.2414Zm3.75 1.9828L6.1961 14.6876l6.6164-6.6164 1.3039 1.3039-6.61639 6.6164ZM15 8.49151 11.5086 5.0001l1.875-1.875 3.4914 3.49063L15 8.49151Z'/%3e%3c/svg%3e");
}
.toolbar-button--icon--preview {
  --icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e  %3cpath fill='%2355565B' d='M19.3211 9.74688c-.0274-.06172-.6891-1.52969-2.1602-3.00079C15.2008 4.78594 12.725 3.75 9.99999 3.75c-2.725 0-5.20078 1.03594-7.16094 2.99609C1.36796 8.21719.703118 9.6875.678899 9.74688.643362 9.82681.625 9.91331.625 10.0008c0 .0875.018362.174.053899.2539.027344.0617.689061 1.5289 2.160151 3C4.79921 15.2141 7.27499 16.25 9.99999 16.25c2.72501 0 5.20081-1.0359 7.16091-2.9953 1.4711-1.4711 2.1328-2.9383 2.1602-3 .0355-.0799.0539-.1664.0539-.2539 0-.08749-.0184-.17399-.0539-.25392ZM9.99999 15c-2.40469 0-4.50547-.8742-6.24453-2.5977C3.0419 11.6927 2.43483 10.8836 1.95312 10c.48158-.88364 1.08867-1.69283 1.80234-2.40234C5.49452 5.87422 7.5953 5 9.99999 5c2.40471 0 4.50551.87422 6.24451 2.59766.715.70934 1.3234 1.51853 1.8063 2.40234-.5633 1.0516-3.0172 5-8.05081 5Zm0-8.75c-.74168 0-1.4667.21993-2.08339.63199-.61668.41205-1.09733.99772-1.38116 1.68295-.28382.68522-.35809 1.43926-.21339 2.16666.14469.7274.50184 1.3956 1.02629 1.9201.52445.5244 1.19263.8816 1.92006 1.0262.72743.1447 1.4814.0705 2.1667-.2134.6852-.2838 1.2708-.7644 1.6829-1.3811.4121-.6167.632-1.3417.632-2.0834-.001-.99424-.3965-1.94747-1.0995-2.65051-.703-.70304-1.6563-1.09846-2.65051-1.09949Zm0 6.25c-.49445 0-.9778-.1466-1.38892-.4213-.41113-.2747-.73156-.6652-.92078-1.122-.18922-.4568-.23872-.95947-.14226-1.44443.09646-.48495.33456-.93041.68419-1.28004.34964-.34963.79509-.58773 1.28005-.68419.48495-.09647.98763-.04696 1.44443.14226.4568.18922.8473.50965 1.122.92077.2747.41113.4213.89448.4213 1.38893 0 .663-.2634 1.2989-.7322 1.7678-.4689.4688-1.1048.7322-1.76781.7322Z'/%3e%3c/svg%3e");
}
.toolbar-button--icon--shortcuts {
  --icon: url("data:image/svg+xml,%3csvg width='22' height='21' viewBox='0 0 22 21' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M21.4251 8.121C21.3342 7.84104 21.1631 7.594 20.9328 7.41063C20.7026 7.22726 20.4236 7.11566 20.1304 7.08975L14.5626 6.60974L12.3801 1.41974C12.2664 1.14742 12.0748 0.914795 11.8292 0.751174C11.5836 0.587552 11.2952 0.500244 11.0001 0.500244C10.705 0.500244 10.4165 0.587552 10.1709 0.751174C9.92537 0.914795 9.7337 1.14742 9.62007 1.41974L7.44413 6.60974L1.86976 7.09256C1.57542 7.11729 1.29493 7.22838 1.06347 7.41188C0.832008 7.59539 0.65988 7.84315 0.568668 8.1241C0.477457 8.40504 0.471222 8.70666 0.550747 8.99113C0.630272 9.2756 0.792015 9.53027 1.01569 9.72318L5.24476 13.4188L3.97726 18.9069C3.91023 19.1941 3.92936 19.4947 4.03224 19.7711C4.13512 20.0475 4.31719 20.2874 4.55569 20.4609C4.79419 20.6344 5.07853 20.7337 5.37317 20.7464C5.66781 20.7592 5.95967 20.6848 6.21226 20.5326L10.9935 17.6263L15.7851 20.5326C16.0377 20.6848 16.3295 20.7592 16.6242 20.7464C16.9188 20.7337 17.2031 20.6344 17.4416 20.4609C17.6801 20.2874 17.8622 20.0475 17.9651 19.7711C18.068 19.4947 18.0871 19.1941 18.0201 18.9069L16.7535 13.4132L20.9816 9.72318C21.2053 9.5296 21.3667 9.27421 21.4456 8.98914C21.5245 8.70406 21.5174 8.40202 21.4251 8.121ZM19.9982 8.58975L15.7701 12.2797C15.5643 12.4587 15.4112 12.6905 15.3273 12.95C15.2434 13.2095 15.2318 13.487 15.2938 13.7526L16.5641 19.2501L11.7763 16.3438C11.5427 16.2016 11.2745 16.1263 11.001 16.1263C10.7275 16.1263 10.4593 16.2016 10.2257 16.3438L5.44444 19.2501L6.70632 13.7563C6.76834 13.4907 6.75676 13.2132 6.67285 12.9537C6.58893 12.6942 6.43585 12.4625 6.23007 12.2835L2.00007 8.59537C1.99973 8.59257 1.99973 8.58973 2.00007 8.58693L7.57257 8.10506C7.84463 8.08108 8.10499 7.98327 8.32555 7.82219C8.54611 7.6611 8.7185 7.44286 8.82413 7.191L11.0001 2.00756L13.1751 7.191C13.2807 7.44286 13.4531 7.6611 13.6737 7.82219C13.8942 7.98327 14.1546 8.08108 14.4266 8.10506L20.0001 8.58693C20.0001 8.58693 20.0001 8.59256 20.0001 8.59349L19.9982 8.58975Z' fill='currentColor'/%3e%3c/svg%3e");
}
.toolbar-button--icon--system-admin-config {
  --icon: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e  %3cpath d='M6.00002 9.84375V3.75C6.00002 3.55109 5.92101 3.36032 5.78035 3.21967C5.6397 3.07902 5.44894 3 5.25002 3C5.05111 3 4.86035 3.07902 4.71969 3.21967C4.57904 3.36032 4.50002 3.55109 4.50002 3.75V9.84375C3.85471 10.009 3.28274 10.3843 2.87429 10.9105C2.46584 11.4367 2.24414 12.0839 2.24414 12.75C2.24414 13.4161 2.46584 14.0633 2.87429 14.5895C3.28274 15.1157 3.85471 15.491 4.50002 15.6562V20.25C4.50002 20.4489 4.57904 20.6397 4.71969 20.7803C4.86035 20.921 5.05111 21 5.25002 21C5.44894 21 5.6397 20.921 5.78035 20.7803C5.92101 20.6397 6.00002 20.4489 6.00002 20.25V15.6562C6.64533 15.491 7.2173 15.1157 7.62575 14.5895C8.0342 14.0633 8.25591 13.4161 8.25591 12.75C8.25591 12.0839 8.0342 11.4367 7.62575 10.9105C7.2173 10.3843 6.64533 10.009 6.00002 9.84375ZM5.25002 14.25C4.95335 14.25 4.66334 14.162 4.41667 13.9972C4.16999 13.8324 3.97774 13.5981 3.8642 13.324C3.75067 13.0499 3.72097 12.7483 3.77885 12.4574C3.83672 12.1664 3.97958 11.8991 4.18936 11.6893C4.39914 11.4796 4.66642 11.3367 4.95739 11.2788C5.24836 11.2209 5.54996 11.2506 5.82405 11.3642C6.09814 11.4777 6.33241 11.67 6.49723 11.9166C6.66205 12.1633 6.75002 12.4533 6.75002 12.75C6.75002 13.1478 6.59199 13.5294 6.31068 13.8107C6.02938 14.092 5.64785 14.25 5.25002 14.25ZM12.75 5.34375V3.75C12.75 3.55109 12.671 3.36032 12.5304 3.21967C12.3897 3.07902 12.1989 3 12 3C11.8011 3 11.6103 3.07902 11.4697 3.21967C11.329 3.36032 11.25 3.55109 11.25 3.75V5.34375C10.6047 5.50898 10.0327 5.88428 9.62429 6.41048C9.21584 6.93669 8.99414 7.58387 8.99414 8.25C8.99414 8.91613 9.21584 9.56331 9.62429 10.0895C10.0327 10.6157 10.6047 10.991 11.25 11.1562V20.25C11.25 20.4489 11.329 20.6397 11.4697 20.7803C11.6103 20.921 11.8011 21 12 21C12.1989 21 12.3897 20.921 12.5304 20.7803C12.671 20.6397 12.75 20.4489 12.75 20.25V11.1562C13.3953 10.991 13.9673 10.6157 14.3758 10.0895C14.7842 9.56331 15.0059 8.91613 15.0059 8.25C15.0059 7.58387 14.7842 6.93669 14.3758 6.41048C13.9673 5.88428 13.3953 5.50898 12.75 5.34375ZM12 9.75C11.7034 9.75 11.4133 9.66203 11.1667 9.4972C10.92 9.33238 10.7277 9.09811 10.6142 8.82402C10.5007 8.54994 10.471 8.24834 10.5288 7.95736C10.5867 7.66639 10.7296 7.39912 10.9394 7.18934C11.1491 6.97956 11.4164 6.8367 11.7074 6.77882C11.9984 6.72094 12.3 6.75065 12.574 6.86418C12.8481 6.97771 13.0824 7.16997 13.2472 7.41664C13.412 7.66332 13.5 7.95333 13.5 8.25C13.5 8.64782 13.342 9.02936 13.0607 9.31066C12.7794 9.59196 12.3978 9.75 12 9.75ZM21.75 15.75C21.7494 15.0849 21.5282 14.4388 21.121 13.9129C20.7139 13.387 20.1438 13.011 19.5 12.8438V3.75C19.5 3.55109 19.421 3.36032 19.2804 3.21967C19.1397 3.07902 18.9489 3 18.75 3C18.5511 3 18.3603 3.07902 18.2197 3.21967C18.079 3.36032 18 3.55109 18 3.75V12.8438C17.3547 13.009 16.7827 13.3843 16.3743 13.9105C15.9658 14.4367 15.7441 15.0839 15.7441 15.75C15.7441 16.4161 15.9658 17.0633 16.3743 17.5895C16.7827 18.1157 17.3547 18.491 18 18.6562V20.25C18 20.4489 18.079 20.6397 18.2197 20.7803C18.3603 20.921 18.5511 21 18.75 21C18.9489 21 19.1397 20.921 19.2804 20.7803C19.421 20.6397 19.5 20.4489 19.5 20.25V18.6562C20.1438 18.489 20.7139 18.113 21.121 17.5871C21.5282 17.0612 21.7494 16.4151 21.75 15.75ZM18.75 17.25C18.4534 17.25 18.1633 17.162 17.9167 16.9972C17.67 16.8324 17.4777 16.5981 17.3642 16.324C17.2507 16.0499 17.221 15.7483 17.2788 15.4574C17.3367 15.1664 17.4796 14.8991 17.6894 14.6893C17.8991 14.4796 18.1664 14.3367 18.4574 14.2788C18.7484 14.2209 19.05 14.2506 19.324 14.3642C19.5981 14.4777 19.8324 14.67 19.9972 14.9166C20.162 15.1633 20.25 15.4533 20.25 15.75C20.25 16.1478 20.092 16.5294 19.8107 16.8107C19.5294 17.092 19.1478 17.25 18.75 17.25Z' fill='currentColor'/%3e%3c/svg%3e");
}
.toolbar-button--icon--system-admin-reports {
  --icon: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e  %3cpath d='M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96452 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM18.7378 7.24406L12.75 10.7006V3.78469C13.9389 3.89369 15.0901 4.25945 16.1239 4.85672C17.1577 5.45398 18.0495 6.26851 18.7378 7.24406ZM11.25 3.78469V11.5659L4.51032 15.4566C3.95704 14.2574 3.69975 12.9428 3.76008 11.6235C3.82041 10.3042 4.19659 9.01868 4.85701 7.87497C5.51742 6.73126 6.44276 5.76281 7.55523 5.05104C8.66771 4.33927 9.93481 3.90498 11.25 3.78469ZM12 20.25C10.6792 20.2495 9.37792 19.9318 8.20543 19.3238C7.03295 18.7158 6.02358 17.8351 5.26219 16.7559L19.4897 8.5425C20.0699 9.79946 20.3244 11.1822 20.2299 12.5634C20.1354 13.9445 19.6948 15.2797 18.9487 16.4459C18.2027 17.6121 17.1752 18.5717 15.9608 19.2365C14.7465 19.9013 13.3844 20.2499 12 20.25Z' fill='currentColor'/%3e%3c/svg%3e");
}
.toolbar-button--icon--system-admin-structure {
  --icon: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e  %3cpath d='M15.75 10.5H20.25C20.6478 10.5 21.0294 10.342 21.3107 10.0607C21.592 9.77936 21.75 9.39782 21.75 9V4.5C21.75 4.10218 21.592 3.72064 21.3107 3.43934C21.0294 3.15804 20.6478 3 20.25 3H15.75C15.3522 3 14.9706 3.15804 14.6893 3.43934C14.408 3.72064 14.25 4.10218 14.25 4.5V6H13.5C12.7044 6 11.9413 6.31607 11.3787 6.87868C10.8161 7.44129 10.5 8.20435 10.5 9V11.25H7.5V10.5C7.5 10.1022 7.34196 9.72064 7.06066 9.43934C6.77936 9.15804 6.39782 9 6 9H3C2.60218 9 2.22064 9.15804 1.93934 9.43934C1.65804 9.72064 1.5 10.1022 1.5 10.5V13.5C1.5 13.8978 1.65804 14.2794 1.93934 14.5607C2.22064 14.842 2.60218 15 3 15H6C6.39782 15 6.77936 14.842 7.06066 14.5607C7.34196 14.2794 7.5 13.8978 7.5 13.5V12.75H10.5V15C10.5 15.7956 10.8161 16.5587 11.3787 17.1213C11.9413 17.6839 12.7044 18 13.5 18H14.25V19.5C14.25 19.8978 14.408 20.2794 14.6893 20.5607C14.9706 20.842 15.3522 21 15.75 21H20.25C20.6478 21 21.0294 20.842 21.3107 20.5607C21.592 20.2794 21.75 19.8978 21.75 19.5V15C21.75 14.6022 21.592 14.2206 21.3107 13.9393C21.0294 13.658 20.6478 13.5 20.25 13.5H15.75C15.3522 13.5 14.9706 13.658 14.6893 13.9393C14.408 14.2206 14.25 14.6022 14.25 15V16.5H13.5C13.1022 16.5 12.7206 16.342 12.4393 16.0607C12.158 15.7794 12 15.3978 12 15V9C12 8.60218 12.158 8.22064 12.4393 7.93934C12.7206 7.65804 13.1022 7.5 13.5 7.5H14.25V9C14.25 9.39782 14.408 9.77936 14.6893 10.0607C14.9706 10.342 15.3522 10.5 15.75 10.5ZM6 13.5H3V10.5H6V13.5ZM15.75 15H20.25V19.5H15.75V15ZM15.75 4.5H20.25V9H15.75V4.5Z' fill='currentColor'/%3e%3c/svg%3e");
}
.toolbar-button--icon--system-modules-list {
  --icon: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e  %3cpath d='M20.6503 14.8631C20.5434 14.7957 20.4212 14.7564 20.2949 14.7488C20.1687 14.7413 20.0427 14.7658 19.9285 14.82C19.6427 14.9552 19.3274 15.0159 19.0118 14.9965C18.6963 14.9771 18.3908 14.8782 18.1237 14.7091C17.8566 14.54 17.6366 14.3061 17.4842 14.0291C17.3318 13.7521 17.2518 13.4411 17.2518 13.125C17.2518 12.8089 17.3318 12.4979 17.4842 12.2209C17.6366 11.944 17.8566 11.7101 18.1237 11.5409C18.3908 11.3718 18.6963 11.2729 19.0118 11.2535C19.3274 11.2342 19.6427 11.2949 19.9285 11.43C20.0428 11.4843 20.169 11.5087 20.2953 11.5011C20.4217 11.4934 20.544 11.4539 20.651 11.3863C20.7579 11.3186 20.846 11.225 20.9071 11.1141C20.9681 11.0033 21.0001 10.8788 21 10.7522V6.75002C21 6.35219 20.842 5.97066 20.5607 5.68936C20.2794 5.40805 19.8979 5.25002 19.5 5.25002H16.1044C16.1184 5.12551 16.1252 5.00031 16.125 4.87502C16.1243 4.41407 16.0295 3.95812 15.8464 3.53511C15.6633 3.1121 15.3957 2.73095 15.06 2.41502C14.5953 1.97853 14.0164 1.68261 13.3904 1.56154C12.7644 1.44046 12.117 1.49918 11.523 1.73089C10.929 1.9626 10.4128 2.35782 10.0341 2.87079C9.65549 3.38376 9.42989 3.9935 9.38347 4.62939C9.36906 4.83624 9.37345 5.04396 9.39659 5.25002H6.00003C5.60221 5.25002 5.22067 5.40805 4.93937 5.68936C4.65807 5.97066 4.50003 6.35219 4.50003 6.75002V9.77064C4.37552 9.75669 4.25032 9.7498 4.12503 9.75002C3.66409 9.75076 3.20816 9.8456 2.78516 10.0287C2.36216 10.2119 1.981 10.4794 1.66503 10.815C1.34679 11.1512 1.10175 11.5497 0.945386 11.9854C0.789021 12.4211 0.724736 12.8845 0.756593 13.3463C0.810338 14.1594 1.15585 14.9259 1.7296 15.5046C2.30334 16.0833 3.06674 16.4355 3.87941 16.4963C4.08624 16.5111 4.29401 16.5067 4.50003 16.4831V19.5C4.50003 19.8978 4.65807 20.2794 4.93937 20.5607C5.22067 20.842 5.60221 21 6.00003 21H19.5C19.8979 21 20.2794 20.842 20.5607 20.5607C20.842 20.2794 21 19.8978 21 19.5V15.4978C21.0001 15.3711 20.9681 15.2464 20.9069 15.1355C20.8458 15.0245 20.7575 14.9308 20.6503 14.8631ZM19.5 19.5H6.00003V15.4978C6.00007 15.3713 5.96809 15.2468 5.90705 15.1359C5.84602 15.025 5.75792 14.9314 5.65096 14.8637C5.54401 14.7961 5.42167 14.7566 5.29534 14.749C5.16901 14.7413 5.0428 14.7658 4.92847 14.82C4.64269 14.9552 4.32737 15.0159 4.01183 14.9965C3.6963 14.9771 3.39079 14.8782 3.1237 14.7091C2.85662 14.54 2.63664 14.3061 2.48421 14.0291C2.33178 13.7521 2.25184 13.4411 2.25184 13.125C2.25184 12.8089 2.33178 12.4979 2.48421 12.2209C2.63664 11.944 2.85662 11.7101 3.1237 11.5409C3.39079 11.3718 3.6963 11.2729 4.01183 11.2535C4.32737 11.2342 4.64269 11.2949 4.92847 11.43C5.0428 11.4843 5.16901 11.5087 5.29534 11.5011C5.42167 11.4934 5.54401 11.4539 5.65096 11.3863C5.75792 11.3186 5.84602 11.225 5.90705 11.1141C5.96809 11.0033 6.00007 10.8788 6.00003 10.7522V6.75002H10.3772C10.5038 6.75006 10.6283 6.71807 10.7392 6.65704C10.85 6.596 10.9436 6.50791 11.0113 6.40095C11.079 6.29399 11.1184 6.17165 11.1261 6.04533C11.1337 5.919 11.1093 5.79279 11.055 5.67845C10.9199 5.39267 10.8592 5.07736 10.8786 4.76182C10.898 4.44628 10.9968 4.14077 11.1659 3.87369C11.3351 3.60661 11.569 3.38663 11.8459 3.23419C12.1229 3.08176 12.4339 3.00183 12.75 3.00183C13.0662 3.00183 13.3772 3.08176 13.6541 3.23419C13.9311 3.38663 14.165 3.60661 14.3341 3.87369C14.5033 4.14077 14.6021 4.44628 14.6215 4.76182C14.6409 5.07736 14.5802 5.39267 14.445 5.67845C14.3908 5.79279 14.3663 5.919 14.374 6.04533C14.3816 6.17165 14.4211 6.29399 14.4888 6.40095C14.5564 6.50791 14.65 6.596 14.7609 6.65704C14.8718 6.71807 14.9963 6.75006 15.1228 6.75002H19.5V9.77158C19.294 9.74798 19.0862 9.74358 18.8794 9.75845C18.0065 9.82026 17.1917 10.2187 16.607 10.8697C16.0222 11.5207 15.7132 12.3734 15.745 13.2479C15.7769 14.1224 16.1472 14.9503 16.7778 15.5571C17.4084 16.1638 18.25 16.5019 19.125 16.5C19.2503 16.5002 19.3755 16.4933 19.5 16.4794V19.5Z' fill='currentColor'/%3e%3c/svg%3e");
}
.toolbar-button--icon--system-themes-page {
  --icon: url("data:image/svg+xml,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e  %3cpath d='M21.75 3C21.75 2.80109 21.6709 2.61032 21.5303 2.46967C21.3896 2.32902 21.1989 2.25 21 2.25C16.8675 2.25 12.6271 6.91031 10.2721 9.99656C9.43109 9.73898 8.54134 9.68224 7.67437 9.83089C6.8074 9.97954 5.98738 10.3294 5.28017 10.8525C4.57295 11.3755 3.99826 12.0571 3.60225 12.8426C3.20624 13.628 2.99996 14.4954 2.99996 15.375C2.99996 18.27 1.16808 19.5684 1.0809 19.6284C0.947855 19.7183 0.847149 19.8484 0.793587 19.9998C0.740026 20.1511 0.736435 20.3156 0.783343 20.4692C0.830251 20.6227 0.925182 20.7571 1.05418 20.8527C1.18317 20.9482 1.33943 20.9999 1.49996 21H8.62496C9.50458 21 10.3719 20.7937 11.1574 20.3977C11.9428 20.0017 12.6244 19.427 13.1475 18.7198C13.6705 18.0126 14.0204 17.1926 14.1691 16.3256C14.3177 15.4586 14.261 14.5689 14.0034 13.7278C17.0906 11.3728 21.75 7.1325 21.75 3ZM8.62496 19.5H3.24652C3.87933 18.6009 4.49996 17.2425 4.49996 15.375C4.49996 14.5592 4.74189 13.7616 5.19515 13.0833C5.64841 12.4049 6.29264 11.8762 7.04639 11.564C7.80013 11.2518 8.62953 11.1701 9.42971 11.3293C10.2299 11.4884 10.9649 11.8813 11.5418 12.4582C12.1187 13.0351 12.5115 13.7701 12.6707 14.5703C12.8299 15.3704 12.7482 16.1998 12.436 16.9536C12.1237 17.7073 11.595 18.3516 10.9167 18.8048C10.2383 19.2581 9.44081 19.5 8.62496 19.5ZM11.6643 10.6453C11.9856 10.2291 12.3009 9.83688 12.6103 9.46875C13.3675 9.98035 14.0196 10.6324 14.5312 11.3897C14.1625 11.6984 13.7703 12.0138 13.3546 12.3356C12.9176 11.6586 12.3414 11.0824 11.6643 10.6453ZM15.6675 10.3941C15.1012 9.59512 14.4039 8.89778 13.605 8.33156C16.5843 5.09438 18.8315 4.11281 20.1581 3.84C19.8909 5.1675 18.9046 7.41469 15.6675 10.3941Z' fill='currentColor'/%3e%3c/svg%3e");
}
.toolbar-button--icon--user {
  --icon: url("data:image/svg+xml,%3csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e  %3cpath d='M10 1.875C8.39303 1.875 6.82214 2.35152 5.486 3.24431C4.14985 4.1371 3.10844 5.40605 2.49348 6.8907C1.87852 8.37535 1.71762 10.009 2.03112 11.5851C2.34463 13.1612 3.11846 14.6089 4.25476 15.7452C5.39106 16.8815 6.8388 17.6554 8.4149 17.9689C9.99099 18.2824 11.6247 18.1215 13.1093 17.5065C14.594 16.8916 15.8629 15.8502 16.7557 14.514C17.6485 13.1779 18.125 11.607 18.125 10C18.1227 7.84581 17.266 5.78051 15.7427 4.25727C14.2195 2.73403 12.1542 1.87727 10 1.875ZM5.7875 15.4297C6.23964 14.7226 6.86251 14.1406 7.59869 13.7375C8.33488 13.3345 9.16069 13.1232 10 13.1232C10.8393 13.1232 11.6651 13.3345 12.4013 13.7375C13.1375 14.1406 13.7604 14.7226 14.2125 15.4297C13.0081 16.3664 11.5258 16.8749 10 16.8749C8.4742 16.8749 6.99193 16.3664 5.7875 15.4297ZM7.5 9.375C7.5 8.88055 7.64663 8.3972 7.92133 7.98607C8.19603 7.57495 8.58648 7.25452 9.04329 7.0653C9.50011 6.87608 10.0028 6.82657 10.4877 6.92304C10.9727 7.0195 11.4181 7.2576 11.7678 7.60723C12.1174 7.95686 12.3555 8.40232 12.452 8.88727C12.5484 9.37223 12.4989 9.87489 12.3097 10.3317C12.1205 10.7885 11.8001 11.179 11.3889 11.4537C10.9778 11.7284 10.4945 11.875 10 11.875C9.33696 11.875 8.70108 11.6116 8.23224 11.1428C7.7634 10.6739 7.5 10.038 7.5 9.375ZM15.1375 14.5633C14.4404 13.5532 13.4603 12.7717 12.3203 12.3172C12.9327 11.8349 13.3795 11.1737 13.5987 10.4257C13.8179 9.67766 13.7985 8.87992 13.5433 8.1434C13.2881 7.40687 12.8097 6.76819 12.1746 6.31616C11.5396 5.86414 10.7795 5.62123 10 5.62123C9.22052 5.62123 8.4604 5.86414 7.82536 6.31616C7.19033 6.76819 6.71193 7.40687 6.45671 8.1434C6.20149 8.87992 6.18212 9.67766 6.40131 10.4257C6.62049 11.1737 7.06734 11.8349 7.67969 12.3172C6.5397 12.7717 5.55956 13.5532 4.8625 14.5633C3.9817 13.5728 3.406 12.3488 3.20473 11.0387C3.00346 9.72856 3.1852 8.3882 3.72806 7.17898C4.27093 5.96977 5.15178 4.94326 6.26454 4.22308C7.3773 3.50289 8.67452 3.11972 10 3.11972C11.3255 3.11972 12.6227 3.50289 13.7355 4.22308C14.8482 4.94326 15.7291 5.96977 16.2719 7.17898C16.8148 8.3882 16.9966 9.72856 16.7953 11.0387C16.594 12.3488 16.0183 13.5728 15.1375 14.5633Z' fill='currentColor'/%3e%3c/svg%3e");
}
.toolbar-button--collapsible::after {
  display: none;
}
.toolbar-button--collapsible .toolbar-button__label {
  position: absolute;
  overflow: hidden;
  clip: rect(0 0 0 0);
  width: 1px;
  height: 1px;
  white-space: nowrap;
  clip-path: inset(50%);
  opacity: 0;
}
[data-admin-toolbar="expanded"] .toolbar-button--collapsible::after {
  display: block;
}
[data-admin-toolbar="expanded"] .toolbar-button--collapsible .toolbar-button__label {
  position: relative;
  clip: revert;
  width: auto;
  height: auto;
  white-space: wrap;
  clip-path: none;
  opacity: 1;
}
[data-admin-toolbar-animating] .toolbar-button--collapsible .toolbar-button__label {
  display: none;
}
