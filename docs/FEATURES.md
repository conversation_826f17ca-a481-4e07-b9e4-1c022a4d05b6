Features
--------

<table class="views-view-grid" width="100%">
<tr>
<td>
<blockquote>The Webform module for Drupal provides all the features expected from an enterprise proprietary form builder combined with the flexibility and openness of <PERSON><PERSON><PERSON>.</blockquote>
</td>
<td width="100"><img src="/files/webform_stacked-logo_256.png" width="256" alt="Webform logo"></td>
</tr>
</table>

The Webform module allows you to build any type of form that can collect any
type of data, which can be submitted to any application or system.
Every single behavior and aspect of your forms and its inputs are customizable.
Whether you need a multi-page form containing a multi-column input layout with
conditional logic or a simple contact form that pushes data to a SalesForce/CRM,
 it is all possible using the Webform module for Drupal 8.

Drupal and the Webform module strives to be fully accessible to all users and
site builders. Assistive technologies, including screen readers and
keyboard access, are fully supported.

Besides being a feature rich form builder, the Webform module is part of the
Drupal project's ecosystem and community.

<blockquote>
The <a href="https://www.drupal.org/about">Drupal project</a> is open source software.
Anyone can download, use, work on, and share it with others.
It's built on principles like collaboration, globalism, and innovation.
It's distributed under the terms of the <a href="https://www.gnu.org/copyleft/gpl.html">GNU General Public License</a> (GPL).
There are <a href="https://www.drupal.org/about/licensing">no licensing fees</a>, ever. Drupal (and Webform) will always be free.
</blockquote>

<div align="center">
<table class="views-view-grid">
  <tr>
<td><a class="action-button" href="https://youtu.be/VncMRSwjVto">▶ Watch video</a></td>
<td><a class="action-button" href="https://simplytest.me/project/webform/6.x">Try Webform</a></td>
  </tr>
</table>
</div>

<hr/>

## Form manager

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--form-manager.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--form-manager.png" alt="Form manager" /><br/>
    <strong>Form manager</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--form-manager-add.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--form-manager-add.png" alt="Form manager: Add webform" /><br/>
    <strong>Add webform</strong>
    </a>
    </div></td>
  </tr>
</table>

The form manager provides a list of all available webforms.

Form manager features include:

- Filtering by keyword, category, and status
- Sorting by total number of submissions
- Archiving of old forms

## Form builder

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--form-builder.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--form-builder.png" alt="Form builder" /><br/>
    <strong>Form builder</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--form-builder.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--form-builder.png" alt="Edit element" /><br/>
    <strong>Edit element</strong>
    </a>
    </div></td>
  </tr>
</table>

The Webform module provides an intuitive form builder based upon Drupal 8's
best practices for user interface design and user experience.
The form builder allows non-technical users to easily build
and maintain webforms.

Form builder features include:

- Drag-n-drop form element management
- Multi-column layout management
- Conditional logic overview
- Element duplication

## Configuration settings

Form behaviors, features, submission handling, messaging, and confirmations are completely customizable using global settings and/or form-specific settings.

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--settings-general.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--settings-general.png" alt="Configuration settings: General" /><br/>
    <strong>General</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--settings-form.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--settings-form.png" alt="Configuration settings: Form" /><br/>
    <strong>Form</strong>
    </a>
    </div></td>
  </tr>
</table>

### General settings

Allow a webform's administrative information, paths, behaviors, and third-party settings to be customized.

General settings include:

- Categorization
- Customizable paths
- Disable saving of results
- Ajax support

### Form settings

Allow a form's status, attributes, behaviors, labels, messages, wizard settings,
and preview to be customized.

Form settings include:

- Open and close date/time scheduling
- Login redirection with custom messaging.
- Multiple step wizard forms
- Submission preview
- Input prepopulation using query string parameters.

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--settings-submissions.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--settings-submissions.png" alt="Configuration settings: Submissions" /><br/>
    <strong>Submissions</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--settings-confirmation.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--settings-confirmation.png" alt="Configuration settings: Confirmation" /><br/>
    <strong>Confirmation</strong>
    </a>
    </div></td>
  </tr>
</table>

### Submissions settings

Allows a submission's labels, behaviors, limits, and draft settings to be
customized.

Submission settings include:

- Saving of drafts
- Automatic purging of submissions
- Submission limits per user and/or per form
- Autofilling form using previously submitted values

### Confirmation settings

Allows the form's confirmation type, message, and URL to be customized.

Confirmation types include:

- Dedicated page
- Redirect to internal or external URL
- Displaying of a custom status message
- Opening a modal dialog

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--settings-handlers.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--settings-handlers.png" alt="Configuration settings: Handlers" /><br/>
    <strong>Handlers</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--settings-handlers-email.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--settings-handlers-email.png" alt="Configuration settings: Email handler" /><br/>
    <strong>Email handler</strong>
    </a>
    </div></td>
  </tr>
</table>


### Emails / Handlers

Allows additional actions and behaviors to be processed when a webform or
submission is created, updated, or deleted. Handlers are used to route
submitted data to external applications and send notifications & confirmations.

Email support features include:

- Previewing and resending emails
- Sending HTML emails
- File attachments (requires the Mail System and Swift Mailer module.)
- HTML and plain-text email-friendly Twig templates
- Customizable display formats for individual form elements

Remote post features include:
- Posting selected elements to a remote server
- Adding custom parameters to remote post requests

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--settings-assets.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--settings-assets.png" alt="Configuration settings: CSS/JS assets" /><br/>
    <strong>CSS/JS assets</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--settings-access.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--settings-access.png" alt="Configuration settings: Access" /><br/>
    <strong>Access</strong>
    </a>
    </div></td>
  </tr>
</table>

### CSS/JS assets

The CSS/JS assets page allows site builders to attach custom CSS and JavaScript
to a webform.  Custom CSS can be used to make simple layout or design tweaks
to a form. Custom JavaScript allows additional conditional logic and
behaviors to be added to a form.

### Access settings

Allows an administrator to determine who can administer a webform and/or create,
update, delete, and purge webform submissions.

## Elements

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--elements.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--elements.png" alt="Elements" /><br/>
    <strong>Elements</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--elements-settings.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--elements-settings.png" alt="Element settings" /><br/>
    <strong>Element settings</strong>
    </a>
    </div></td>
  </tr>
</table>

The Webform module is built directly on top of Drupal 8's Form API.
Every form element available in Drupal 8 is supported by the Webform module.

Form elements include:

- **Basic HTML**: Textfield, Textareas, Checkboxes, Radios, Select menu, Password, and more...
- **Advanced HTML5**: Email, Url, Number, Telephone, Date, Number, Range, and more...
- **Advanced Drupal**: File uploads, Entity References, Table select, Date list, and more...
- **Widgets**: Likert scale, Star rating, Buttons, Geolocation, Terms of service, Select/Checkboxes/Radios with other, and more...
- **Markup**: Dismissible messages, Basic HTML, Advanced HTML, Details, and Fieldsets.
- **Composites**: Name, Address, Contact, Credit Card, and event custom composites
- **Computed**: Calculated values using Tokens and Twig with Ajax support.

### Element settings

All of Drupal 8's default form element properties and behaviors are supported.
There are also several custom webform element properties and settings available
to enhance a form element's behavior.

Standard and custom properties allow for:

- Customizable error validation messages
- Conditional logic using [FAPI States API](https://api.drupal.org/api/examples/form_example%21form_example_states.inc/function/form_example_states_form/7)
- Input masks (using [jquery.inputmask](https://github.com/RobinHerbots/jquery.inputmask))
- [Select2](https://select2.github.io/) or [Chosen](https://harvesthq.github.io/chosen/) replacement of select boxes
- Word and character counting for text elements
- Help tooltips
- More information slideouts
- Regular expression pattern validation
- Private elements, visible only to administrators
- Unique values per element

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--elements-conditional.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--elements-conditional.png" alt="Conditional logic" /><br/>
    <strong>Conditional logic</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--elements-source.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--elements-source.png" alt="View source" /><br/>
    <strong>View source</strong>
    </a>
    </div></td>
  </tr>
</table>

### States/Conditional logic

Drupal's State API can be used by developers to provide conditional logic
to hide and show form elements.

Drupal's State API supports:

- Show/Hide
- Required/Optional
- Open/Close
- Enable/Disable

### Viewing source

At the heart of a Webform module's form elements is a Drupal
[render array](https://www.drupal.org/docs/8/api/render-api/render-arrays),
which can be edited and managed by developers. The Drupal render array
gives developers complete control over a webform's elements, layout,
and look-and-feel by allowing developers to make bulk updates
to a webform's label, descriptions, and behaviors.

## Forms

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--forms-accessiblity.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--forms-accessiblity.png" alt="Accessibility" /><br/>
    <strong>Accessibility</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--forms-wizard.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--forms-wizard.png" alt="Multistep form" /><br/>
    <strong>Multistep form</strong>
    </a>
    </div></td>
  </tr>
</table>

### Accessibility

The outputted forms and even the Webform module's administrative interface
(i.e. form builder) are accessible using keyboard navigation and screen readers.
The Webform module complies with [WCAG 2.0](http://www.w3.org/TR/WCAG20/#contents)
and [ATAG 2.0](http://www.w3.org/TR/ATAG20/#contents) guidelines.

### Multistep form

Forms can be broken up into multiple pages using a progress bar.
Authenticated users can save drafts and/or have their changes automatically
saved as they progress through a long form.

Multistep form features include:

- Customizable progress bar
- Customizable previous and next button labels and styles
- Saving drafts between steps

### Drupal integration​

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--forms-block.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--forms-block.png" alt="Block" /><br/>
    <strong>Block</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--forms-node.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--forms-node.png" alt="Node" /><br/>
    <strong>Node</strong>
    </a>
    </div></td>
  </tr>
</table>

Webforms can be attached to nodes or displayed as blocks.
Webforms can also have dedicated SEO-friendly URLs.
Form elements are render arrays that can easily be altered using
custom hooks and/or plugins.

## Results management

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--results.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--results.png" alt="Results" /><br/>
    <strong>Results</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--results-customize.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--results-customize.png" alt="Customize results" /><br/>
    <strong>Customize results </strong>
    </a>
    </div></td>
  </tr>
</table>

Form submissions can optionally be stored in the database, reviewed,
and downloaded. Submissions can also be flagged with administrative notes.

Results management features include:

- Submission flagging, locking, and notes
- Viewing submissions as HTML, plain text, and YAML
- Customizable reports
- Downloading results as a CSV to Google Sheets or MS Excel
- Saving of download preferences per form
- [Drupal Views](https://www.drupal.org/docs/8/core/modules/views) integration
for advanced reporting.

## Access controls

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--access-permissions.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--access-permissions.png" alt="Permissions" /><br/>
    <strong>Permissions</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--access-element.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--access-element.png" alt="Element access" /><br/>
    <strong>Element access</strong>
    </a>
    </div></td>
  </tr>
</table>

The Webform module provides full access controls and permissions for managing
who can create forms, post submissions, and access a webform's results.
Access controls can be applied to roles and/or specific users.
The Webform access submodule allows you to even setup reusable permission
groups which can be applied to multiple instances of the same webform.

Access controls allow users to:

- Create new forms
- Update forms
- Delete forms
- View submissions
- Update submissions
- Delete submissions
- View selected elements
- Update selected elements

## Reusable templates

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--templates.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--templates.png" alt="Templates" /><br/>
    <strong>Templates</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--templates-preview.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--templates-preview.png" alt="Templates preview" /><br/>
    <strong>Templates preview</strong>
    </a>
    </div></td>
  </tr>
</table>

The Webform module provides a few starter templates and multiple example
forms which webform administrators can update or use to create new
reusable templates for their organization.

Starter templates include:

- Contact Us
- Donation
- Employee Evaluation
- Issue
- Job Application
- Job Seeker Profile
- Registration
- Session Evaluation
- Subscribe
- User Profile

## Reusable options

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--options.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--options.png" alt="Options" /><br/>
    <strong>Options</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--options-likert.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--options-likert.png" alt="Likert" /><br/>
    <strong>Likert</strong>
    </a>
    </div></td>
  </tr>
</table>


Administrators can define reusable global options for select menus, checkboxes,
and radio buttons. The Webform module includes default options for states,
countries, demographics, likert answers, and more.

Reusable options include:

- **Geographic**: Languages, country, and states
- **Date and time**: Days, months, and time zones
- **Demographic**: Education, employment status, ethnicity, Industry,
languages, marital status, relationship, size, and job titles
- **Likert**: Agreement, comparison, importance, quality, satisfaction,
ten scale, and would you


## Internationalization

Forms and configuration can be translated into multiple languages using
Drupal's [configuration translation system](https://www.drupal.org/docs/8/core/modules/config-translation.

## Add-ons

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--addons.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--addons.png" alt="Add-ons" /><br/>
    <strong>Add-ons</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--addons-webform-analysis.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--addons-webform-analysis.png" alt="Analysis" /><br/>
    <strong>Analysis</strong>
    </a>
    </div></td>
  </tr>
</table>

There are [dozens of add-ons available](https://www.drupal.org/node/2837065)
that extend and/or provide additional functionality to the Webform module
and Drupal's Form API.

Add-ons include:

- Analysis for creating graphs and charts
- CRM integration including SalesForce, HubSpot, MyEmma, SugarCRM, more…
- SPAM protection
- Advanced workflows
- Data encryption
- GDPR compliance

## Development tools

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--devel-test.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--devel-test.png" alt="Test" /><br/>
    <strong>Test</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--devel-api.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--devel-api.png" alt="API" /><br/>
    <strong>API</strong>
    </a>
    </div></td>
  </tr>
</table>

Examples and tools are provided to help developers get started customizing
existing features and adding new features to the Webform module.

Development tools include:

- Test forms using customizable default values
- Easy to export configuration files
- Debugging tools for all handlers
- Examples for external API integration using remotes posts or custom code
- Example modules for creating custom elements and handlers
- Demos for building event registration and application evaluation system

## Drush & Composer integration

<table class="views-view-grid" width="100%">
  <tr>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--drush.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--drush.png" alt="Drush" /><br/>
    <strong>Drush</strong>
    </a>
    </div></td>
    <td width="50%"><div class="note">
    <a href="https://www.drupal.org/files/webform-8.x.5.x-features--drush-composer.png">
    <img src="https://www.drupal.org/files/webform-8.x.5.x-features--drush-composer.png" alt="Composer" /><br/>
    <strong>Composer</strong>
    </a>
    </div></td>
  </tr>
</table>

Drush commands are provided to:

- Generate multiple submissions
- Export submissions
- Purge submissions
- Download and manage third-party libraries
- Generate a composer.js file for third-party libraries
- Tidy YAML configuration files
