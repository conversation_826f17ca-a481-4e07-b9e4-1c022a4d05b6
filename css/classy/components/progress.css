/**
 * @file
 * Visual styles for progress bar.
 *
 * @see progress.js
 */

.progress__track {
  border-color: #b3b3b3;
  border-radius: 10em;
  background-color: #f2f1eb;
  background-image: -webkit-linear-gradient(#e7e7df, #f0f0f0);
  background-image: linear-gradient(#e7e7df, #f0f0f0);
  box-shadow: inset 0 1px 3px hsla(0, 0%, 0%, 0.16);
}
.progress__bar {
  height: 16px;
  margin-top: -1px;
  margin-left: -1px; /* LTR */
  padding: 0 1px;
  -webkit-transition: width 0.5s ease-out;
  transition: width 0.5s ease-out;
  -webkit-animation: animate-stripes 3s linear infinite;
  -moz-animation: animate-stripes 3s linear infinite;
  border: 1px #07629a solid;
  border-radius: 10em;
  background: #057ec9;
  background-image:
    -webkit-linear-gradient(top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.15)),
    -webkit-linear-gradient(left top, #0094f0 0%, #0094f0 25%, #007ecc 25%, #007ecc 50%, #0094f0 50%, #0094f0 75%, #0094f0 100%);
  background-image:
    linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.15)),
    linear-gradient(to right bottom, #0094f0 0%, #0094f0 25%, #007ecc 25%, #007ecc 50%, #0094f0 50%, #0094f0 75%, #0094f0 100%);
  background-size: 40px 40px;
}
[dir="rtl"] .progress__bar {
  margin-right: -1px;
  margin-left: 0;
  -webkit-animation-direction: reverse;
  -moz-animation-direction: reverse;
  animation-direction: reverse;
}

@media screen and (prefers-reduced-motion: reduce) {
  .progress__bar {
    -webkit-transition: none;
    transition: none;
    -webkit-animation: none;
    -moz-animation: none;
  }
}

/**
 * Progress bar animations.
 */
@-webkit-keyframes animate-stripes {
  0% { background-position: 0 0, 0 0; }
  100% { background-position: 0 0, -80px 0; }
}

@-ms-keyframes animate-stripes {
  0% { background-position: 0 0, 0 0; }
  100% { background-position: 0 0, -80px 0; }
}

@keyframes animate-stripes {
  0% { background-position: 0 0, 0 0; }
  100% { background-position: 0 0, -80px 0; }
}
