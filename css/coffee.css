/**
 * @file
 * Stylesheet for the Coffee module.
 */
[id^="coffee"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

#coffee-bg {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
}

.coffee-form-wrapper {
  pointer-events: none;
  left: 0;
  width: 100%;
  position: fixed;
  top: 20%;
  z-index: 9999;
}
#coffee-form {
  pointer-events: auto;
  position: relative;
  margin: 0 auto;
  max-width: 500px;
  width: 100%;
  padding: 10px;
  font: 16px/1.5 sans-serif;
  background: black;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 15px;
  opacity: 1;
  -webkit-transition: opacity 0.35s;
  -moz-transition: opacity 0.35s;
  -o-transition: opacity 0.35s;
  transition: opacity 0.35s false;
}

#coffee-form,
#coffee-form input,
#coffee-form a {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.coffee-form-wrapper.hide-form {
  position: absolute;
  top: -100%;
  opacity: 0;
}

#coffee-form-inner {
  background: #fff;
  color: #444;
  padding: 10px 10px 1px;
  border-radius: 5px;
  -webkit-box-shadow: 0 10px 40px rgba(0, 0, 0, 0.6);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.6);
}

#coffee-q {
  background: #d1d1d1;
  color: #000;
  border: 0;
  font: 36px sans-serif;
  line-height: 52px;
  padding: 5px 10px;
  width: 100%;
  height: 52px;
  outline: none;
  display: block;
  margin: 0 0 10px;
  border-radius: 0;
}

#coffee-q:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}

#coffee-results ul {
  position: static;
  float: none;
  list-style: none;
  margin: 0;
  padding: 10px 0 0;
  border: 0;
}

#coffee-results li {
  margin: 0;
  padding: 0;
  float: none;
  clear: none;
  width: auto;
}

#coffee-results a {
  display: block;
  border: 0;
  outline: none;
  color: #444;
  padding: 6px 10px 4px;
  line-height: normal;
  font-size: 16px;
  text-decoration: none;
  height: auto;
  -webkit-transition: none;
  -moz-transition: none;
  -o-transition: none;
  transition: none;
  border-radius: 0;
}

#coffee-results .ui-widget-content .ui-state-active,
#coffee-results a.ui-state-hover,
#coffee-results a:focus,
#coffee-results a:hover,
#coffee-results .ui-state-active {
  background: #d1d1d1;
  color: #000;
  margin: 0;
  cursor: pointer;
}

#coffee-results .description {
  display: block;
  font-size: 11px;
  color: #888;
}

#coffee-results a.ui-state-hover .description {
  color: #666;
}

#coffee-results .ui-widget {
  font-family: sans-serif;
}

.toolbar .toolbar-bar .toolbar-icon-coffee:before,
.toolbar .toolbar-bar .toolbar-icon-coffee.active:before {
  background-image: url("../images/icons/bebebe/coffee.svg");
}

#coffee-q {
  background-image: url("../images/icons/bebebe/coffee.svg");
  background-position: right 10px center;
  background-repeat: no-repeat;
  background-size: 40px 40px;
}

[dir="rtl"] #coffee-q {
  background-position: left 20px center;
}
