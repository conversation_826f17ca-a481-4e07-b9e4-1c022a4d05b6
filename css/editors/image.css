/**
 * @file
 * Functional styles for the Quick Edit image in-place editor.
 */

/**
 * A minimum width/height is required so that users can drag and drop files
 * onto small images.
 */
.quickedit-image-element {
  min-width: 200px;
  min-height: 200px;
}

.quickedit-image-dropzone {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.quickedit-image-icon {
  display: block;
  width: 50px;
  height: 50px;
  background-repeat: no-repeat;
  background-size: cover;
}

.quickedit-image-field-info {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.quickedit-image-text {
  display: block;
}

/**
 * If we do not prevent pointer-events for child elements, our drag+drop events
 * will not fire properly. This can lead to unintentional redirects if a file
 * is dropped on a child element when a user intended to upload it.
 */
.quickedit-image-dropzone * {
  pointer-events: none;
}
