services:
  quick_node_clone.entity.form_builder:
    class: <PERSON><PERSON><PERSON>\quick_node_clone\Entity\QuickNodeCloneEntityFormBuilder
    arguments:
      - '@form_builder'
      - '@entity_type.bundle.info'
      - '@config.factory'
      - '@module_handler'
      - '@entity_type.manager'
      - '@current_user'
      - '@tempstore.private'
      - '@string_translation'

  quick_node_clone.address_event_subscriber:
    class: <PERSON><PERSON>al\quick_node_clone\EventSubscriber\AddressEventSubscriber
    arguments:
      - '@tempstore.private'
      - '@quick_node_clone.node_finder'
    tags:
      - { name: event_subscriber }

  quick_node_clone.node_finder:
    class: <PERSON><PERSON>al\quick_node_clone\QuickNodeCloneNodeFinder
    arguments:
      - '@request_stack'
      - '@entity_type.manager'
      - '@?path_alias.manager'
