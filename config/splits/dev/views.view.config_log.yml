uuid: b3dcd389-a823-43af-9793-507c19f8ab73
langcode: pl
status: true
dependencies:
  config:
    - system.menu.admin
  module:
    - config_log_views
    - user
_core:
  default_config_hash: LFoFY7Bl5AytyzhPtREQGau-VbJYuUezEvI-sWiy5vM
id: config_log
label: 'Config log'
module: views
description: ''
tag: ''
base_table: config_log
base_field: ''
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: 'Configuration Log'
      fields:
        clid:
          id: clid
          table: config_log
          field: clid
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: numeric
          label: clid
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          set_precision: false
          precision: 0
          decimal: .
          separator: ''
          format_plural: false
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
        created:
          id: created
          table: config_log
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: date
          label: 'Entry date'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          date_format: custom
          custom_date_format: 'Y-m-d H:i:s'
          timezone: ''
        name_1:
          id: name_1
          table: users_field_data
          field: name
          relationship: uid
          group_type: group
          admin_label: ''
          entity_type: user
          entity_field: name
          plugin_id: field
          label: 'Nazwa użytkownika'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: user_name
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        uid:
          id: uid
          table: config_log
          field: uid
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: numeric
          label: 'ID użytkownika'
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          set_precision: false
          precision: 0
          decimal: .
          separator: ''
          format_plural: false
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
        name:
          id: name
          table: config_log
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          label: 'Configuration Name'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        old_name:
          id: old_name
          table: config_log
          field: old_name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          label: 'Configuration object old name'
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        data:
          id: data
          table: config_log
          field: data
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          label: Data
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        originaldata:
          id: originaldata
          table: config_log
          field: originaldata
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          label: 'Original Data'
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        diff_field:
          id: diff_field
          table: config_log
          field: diff_field
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: config_log_diff
          label: Różnica
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
      pager:
        type: full
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 25
          total_pages: null
          id: 0
          tags:
            next: 'Następna ›'
            previous: '‹ Poprzednia'
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: true
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: false
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'administer site configuration'
      cache:
        type: none
        options: {  }
      empty: {  }
      sorts:
        clid:
          id: clid
          table: config_log
          field: clid
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: DESC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
      arguments: {  }
      filters:
        data:
          id: data
          table: config_log
          field: data
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: data_op
            label: Data
            description: ''
            use_operator: false
            operator: data_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: data
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              active_member: '0'
              admin_user: '0'
              administrator: '0'
              anonymous: '0'
              chapter_leader: '0'
              clhub: '0'
              contributor: '0'
              feeds_admin: '0'
              help_desk: '0'
              mhpl: '0'
              nonunion_member: '0'
              peer_intervention: '0'
              publisher: '0'
              retiree: '0'
              visiting_nurse: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        name:
          id: name
          table: config_log
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: name_op
            label: 'Configuration Name'
            description: ''
            use_operator: false
            operator: name_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: name
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              active_member: '0'
              admin_user: '0'
              administrator: '0'
              anonymous: '0'
              chapter_leader: '0'
              clhub: '0'
              contributor: '0'
              feeds_admin: '0'
              help_desk: '0'
              mhpl: '0'
              nonunion_member: '0'
              peer_intervention: '0'
              publisher: '0'
              retiree: '0'
              visiting_nurse: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        uid:
          id: uid
          table: config_log
          field: uid
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: uid_op
            label: 'User id'
            description: ''
            use_operator: false
            operator: uid_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: uid
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              active_member: '0'
              admin_user: '0'
              administrator: '0'
              anonymous: '0'
              chapter_leader: '0'
              clhub: '0'
              contributor: '0'
              feeds_admin: '0'
              help_desk: '0'
              mhpl: '0'
              nonunion_member: '0'
              peer_intervention: '0'
              publisher: '0'
              retiree: '0'
              visiting_nurse: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        created:
          id: created
          table: config_log
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: date
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 1
          exposed: true
          expose:
            operator_id: created_op
            label: 'Entry date'
            description: 'A date in any machine readable format, like YYYY-MM-DD HH:MM:SS'
            use_operator: true
            operator: created_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: created
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              applicant: '0'
              intranet_user: '0'
              external: '0'
              president: '0'
              board_member: '0'
              general_secretary: '0'
              treasurer: '0'
              data_protection_officer: '0'
              community_team_lead: '0'
              community_team_member: '0'
              tech_roles: '0'
              communication_lead: '0'
              translator: '0'
              civicrm_admin: '0'
              administrator: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            clid: clid
            data: data
          default: '-1'
          info:
            clid:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            data:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: false
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        uid:
          id: uid
          table: config_log
          field: uid
          relationship: none
          group_type: group
          admin_label: uid
          plugin_id: standard
          required: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
  log_page:
    id: log_page
    display_title: Page
    display_plugin: page
    position: 2
    display_options:
      display_description: 'Shows a log of configuration changes'
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
      path: admin/reports/config-log
      menu:
        type: normal
        title: 'Config Log'
        description: 'Shows a log of configuration changes'
        weight: 0
        expanded: false
        menu_name: admin
        parent: system.admin_reports
        context: '0'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
