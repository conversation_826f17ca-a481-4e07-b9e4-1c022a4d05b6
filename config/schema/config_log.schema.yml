config_log.settings:
  type: config_object
  label: 'Config Log Settings'
  mapping:
    log_ignored_config:
      type: sequence
      label: 'List of ignored configuration names'
      sequence:
        type: string
    log_ignored_config_negate:
      type: boolean
      label: 'Negate the condition of the list of ignored configuration names'
    log_destination:
      type: sequence
      label: 'Log destination'
      sequence:
        type: string
    log_email_address:
      type: string
      label: 'Notification email address'
    ignore_config_import:
      type: boolean
      label: 'Whether the config import should be logged'
    ignore_no_changes:
      type: boolean
      label: 'Ignore logging if there are no changes to the configuration'
    leading_context_lines:
      type: number
      label: 'This governs the number of unchanged leading context lines to show'
    trailing_context_lines:
      type: number
      label: 'This governs the number of unchanged trailing context lines to show'
