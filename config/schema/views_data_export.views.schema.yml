# Schema for the views plugins of the Views Data Export module.

views.display.data_export:
  type: views.display.rest_export
  label: 'Data export display options'
  mapping:
    displays:
      type: sequence
      label: 'Attach to'
      sequence:
        type: string
        label: 'Display'
    filename:
      type: string
      label: 'Downloaded filename'
    automatic_download:
      type: boolean
      label: 'Download immediately'
    redirect_path:
      type: string
      label: 'Redirect path'
    export_method:
      type: string
      label: 'Export method'
    export_batch_size:
      type: integer
      label: 'Batch size'
    export_limit:
      type: integer
      label: 'Limit'
    facet_settings:
      type: string
      label: 'Facet sources'
    store_in_public_file_directory:
      type: boolean
      label: 'Allow anonymous users to download this file'
    custom_redirect_path:
      type: boolean
      label: 'Custom redirect path'
    redirect_to_display:
      type: string
      label: 'Redirect to this display'
    include_query_params:
      type: boolean
      label: 'Include query string parameters on redirect'

views.style.data_export:
  type: views_style
  label: 'Data export output format'
  mapping:
    formats:
      type: sequence
      label: 'Formats'
      sequence:
        type: string
        label: 'Format'
    csv_settings:
      type: mapping
      label: 'CSV settings'
      mapping:
        delimiter:
          type: string
          label: 'Delimiter'
        enclosure:
          type: string
          label: 'Enclosure'
        escape_char:
          type: string
          label: 'Escape character'
        strip_tags:
          type: boolean
          label: 'Strip HTML'
        trim:
          type: boolean
          label: 'Trim whitespace'
        encoding:
          type: string
          label: 'Encoding'
        utf8_bom:
          type: string
          label: 'Include unicode signature (BOM)'
        use_serializer_encode_only:
          type: boolean
          label: 'Only use serializer->encode method'
        output_header:
          type: boolean
          label: 'Show header row'
    xls_settings:
      type: mapping
      label: 'XLS settings'
      mapping:
        xls_format:
          type: string
          label: 'Format'
        metadata:
          type: mapping
          label: 'Metadata'
          mapping:
            creator:
              type: string
              label: 'Creator'
            last_modified_by:
              type: string
              label: 'Last modified by'
            title:
              type: string
              label: 'Title'
            description:
              type: string
              label: 'Description'
            subject:
              type: string
              label: 'Subject'
            keywords:
              type: string
              label: 'Keywords'
            category:
              type: string
              label: 'Category'
            manager:
              type: string
              label: 'Manager'
            company:
              type: string
              label: 'Company'
    xml_settings:
      type: mapping
      label: 'XML settings'
      mapping:
        encoding:
          type: string
          label: 'Encoding'
        root_node_name:
          type: string
          label: 'Root node name'
        item_node_name:
          type: string
          label: 'Item node name'
        format_output:
          type: boolean
          label: 'Format output'
