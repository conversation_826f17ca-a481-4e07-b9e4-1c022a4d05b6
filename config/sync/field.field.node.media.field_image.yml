uuid: b624a49c-d7ea-4db7-8bb5-d9ae2e523bad
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_image
    - node.type.media
  module:
    - image
id: node.media.field_image
field_name: field_image
entity_type: node
bundle: media
label: '<PERSON><PERSON>z do skrótu'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: '[date:custom:Y]-[date:custom:m]'
  file_extensions: 'png gif jpg jpeg'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: true
  alt_field_required: false
  title_field: true
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
