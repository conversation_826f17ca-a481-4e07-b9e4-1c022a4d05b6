uuid: 5dccbd79-fd97-43ad-9f01-1d525de5ab27
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxmessage.npxmessage
    - field.storage.npxmessage.field_npxmessage_interval_type
  module:
    - options
id: npxmessage.npxmessage.field_npxmessage_interval_type
field_name: field_npxmessage_interval_type
entity_type: npxmessage
bundle: npxmessage
label: 'Typ interwału'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: list_string
