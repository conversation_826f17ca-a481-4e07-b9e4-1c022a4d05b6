uuid: 632b7593-0abf-4386-9801-************
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_quote_category
    - node.type.npx_quote
    - taxonomy.vocabulary.quotes_categories
id: node.npx_quote.field_quote_category
field_name: field_quote_category
entity_type: node
bundle: npx_quote
label: Kategoria
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      quotes_categories: quotes_categories
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
