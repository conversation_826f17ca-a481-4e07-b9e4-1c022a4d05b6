uuid: 6be8c2c8-1640-405e-b000-aa58c1831e37
langcode: pl
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.npxtraining_date.body
    - field.field.node.npxtraining_date.field_addcost_info
    - field.field.node.npxtraining_date.field_historic_minor_discount
    - field.field.node.npxtraining_date.field_historic_minor_price
    - field.field.node.npxtraining_date.field_npx_calendar_hide
    - field.field.node.npxtraining_date.field_npxtraining_date_addcost
    - field.field.node.npxtraining_date.field_npxtraining_date_available
    - field.field.node.npxtraining_date.field_npxtraining_date_cal_title
    - field.field.node.npxtraining_date.field_npxtraining_date_city
    - field.field.node.npxtraining_date.field_npxtraining_date_constcost
    - field.field.node.npxtraining_date.field_npxtraining_date_dis_fxd
    - field.field.node.npxtraining_date.field_npxtraining_date_dis_per
    - field.field.node.npxtraining_date.field_npxtraining_date_dis_ref
    - field.field.node.npxtraining_date.field_npxtraining_date_end
    - field.field.node.npxtraining_date.field_npxtraining_date_form
    - field.field.node.npxtraining_date.field_npxtraining_date_guarant
    - field.field.node.npxtraining_date.field_npxtraining_date_hours
    - field.field.node.npxtraining_date.field_npxtraining_date_ldis_ref
    - field.field.node.npxtraining_date.field_npxtraining_date_lim_dis
    - field.field.node.npxtraining_date.field_npxtraining_date_min
    - field.field.node.npxtraining_date.field_npxtraining_date_price
    - field.field.node.npxtraining_date.field_npxtraining_date_room
    - field.field.node.npxtraining_date.field_npxtraining_date_start
    - field.field.node.npxtraining_date.field_npxtraining_date_trainer
    - field.field.node.npxtraining_date.field_npxtraining_date_trcost
    - field.field.node.npxtraining_date.field_npxtraining_hours
    - field.field.node.npxtraining_date.field_zoom_link
    - field.field.node.npxtraining_date.field_zoom_message
    - node.type.npxtraining_date
  module:
    - text
    - user
id: node.npxtraining_date.teaser
targetEntityType: node
bundle: npxtraining_date
mode: teaser
content:
  body:
    type: text_summary_or_trimmed
    label: hidden
    settings:
      trim_length: 600
    third_party_settings: {  }
    weight: 101
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  field_addcost_info: true
  field_historic_minor_discount: true
  field_historic_minor_price: true
  field_npx_calendar_hide: true
  field_npxtraining_date_addcost: true
  field_npxtraining_date_available: true
  field_npxtraining_date_cal_title: true
  field_npxtraining_date_city: true
  field_npxtraining_date_constcost: true
  field_npxtraining_date_dis_fxd: true
  field_npxtraining_date_dis_per: true
  field_npxtraining_date_dis_ref: true
  field_npxtraining_date_end: true
  field_npxtraining_date_form: true
  field_npxtraining_date_guarant: true
  field_npxtraining_date_hours: true
  field_npxtraining_date_ldis_ref: true
  field_npxtraining_date_lim_dis: true
  field_npxtraining_date_min: true
  field_npxtraining_date_price: true
  field_npxtraining_date_room: true
  field_npxtraining_date_start: true
  field_npxtraining_date_trainer: true
  field_npxtraining_date_trcost: true
  field_npxtraining_hours: true
  field_zoom_link: true
  field_zoom_message: true
  langcode: true
