uuid: 5ef2fee2-d181-4b03-9f4f-b7fe59d343cf
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.advantage.body
    - field.field.node.advantage.field_c
    - field.field.node.advantage.field_circle_
    - field.field.node.advantage.field_colorc
    - node.type.advantage
  module:
    - path
    - text
id: node.advantage.default
targetEntityType: node
bundle: advantage
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 31
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_c:
    type: string_textfield
    weight: 33
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_circle_:
    type: string_textfield
    weight: 32
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_colorc:
    type: string_textfield
    weight: 34
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 15
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 121
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 16
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 50
    region: content
    settings: {  }
    third_party_settings: {  }
hidden: {  }
