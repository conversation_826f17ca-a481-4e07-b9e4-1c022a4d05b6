uuid: bbf1de20-5c4a-41d2-be47-5c559e7bbdd3
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxsubmission.npxsubmission
    - field.storage.node.field_npxtraining_date_end
    - field.storage.node.field_npxtraining_date_trainer
    - field.storage.npxsubmission.field_npxsubmission_amount
    - field.storage.npxsubmission.field_npxsubmission_date_nid
    - field.storage.npxsubmission.field_npxsubmission_gen_certs
    - field.storage.npxsubmission.field_npxsubmission_nid
    - field.storage.npxsubmission.field_npxsubmission_online
    - field.storage.npxsubmission.field_npxsubmission_paper
    - field.storage.npxsubmission.field_npxsubmission_participants
    - field.storage.npxsubmission.field_npxsubmission_pdf
    - field.storage.npxsubmission.field_npxsubmission_send_notify
    - field.storage.npxsubmission.field_npxsubmission_trainer
    - user.role.administrator
    - user.role.redaktor
  module:
    - datetime
    - ds
    - eck
    - file
    - node
    - user
id: duplikat_faktury_zgloszenia_z_formularza_szkoleniowego
label: 'Duplikat Faktury: Zgłoszenia z formularza szkoleniowego'
module: views
description: ''
tag: ''
base_table: npxsubmission_field_data
base_field: id
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: 'Zgłoszenia z formularza szkoleniowego'
      fields:
        id:
          id: id
          table: npxsubmission_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxsubmission
          entity_field: id
          plugin_id: field
          label: ID
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        created:
          id: created
          table: npxsubmission_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxsubmission
          entity_field: created
          plugin_id: field
          label: 'Data dodania'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: custom
            custom_date_format: 'Y.m.d H:i'
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        uid:
          id: uid
          table: npxsubmission_field_data
          field: uid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxsubmission
          entity_field: uid
          plugin_id: field
          label: Autor
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_nid:
          id: field_npxsubmission_nid
          table: npxsubmission__field_npxsubmission_nid
          field: field_npxsubmission_nid
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Szkolenie
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_date_nid:
          id: field_npxsubmission_date_nid
          table: npxsubmission__field_npxsubmission_date_nid
          field: field_npxsubmission_date_nid
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Termin szkolenia'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxtraining_date_end:
          id: field_npxtraining_date_end
          table: node__field_npxtraining_date_end
          field: field_npxtraining_date_end
          relationship: field_npxsubmission_date_nid
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Data zakończenia szkolenia'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: datetime_default
          settings:
            timezone_override: Europe/Warsaw
            format_type: html_date
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 1
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_online:
          id: field_npxsubmission_online
          table: npxsubmission__field_npxsubmission_online
          field: field_npxsubmission_online
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Online
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: yes-no
            format_custom_false: STACJONARNE
            format_custom_true: 'LIVE ONLINE'
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_paper:
          id: field_npxsubmission_paper
          table: npxsubmission__field_npxsubmission_paper
          field: field_npxsubmission_paper
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Papier
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: yes-no
            format_custom_false: ''
            format_custom_true: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_amount:
          id: field_npxsubmission_amount
          table: npxsubmission__field_npxsubmission_amount
          field: field_npxsubmission_amount
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Liczba uczest.'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_participants:
          id: field_npxsubmission_participants
          table: npxsubmission__field_npxsubmission_participants
          field: field_npxsubmission_participants
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Uczestnicy
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_trainer:
          id: field_npxsubmission_trainer
          table: npxsubmission__field_npxsubmission_trainer
          field: field_npxsubmission_trainer
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Trener zgł.'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxtraining_date_trainer:
          id: field_npxtraining_date_trainer
          table: node__field_npxtraining_date_trainer
          field: field_npxtraining_date_trainer
          relationship: field_npxsubmission_date_nid
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Trener
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_pdf:
          id: field_npxsubmission_pdf
          table: npxsubmission__field_npxsubmission_pdf
          field: field_npxsubmission_pdf
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: PDF
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: file_default
          settings: {  }
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_send_notify:
          id: field_npxsubmission_send_notify
          table: npxsubmission__field_npxsubmission_send_notify
          field: field_npxsubmission_send_notify
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Maile
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: unicode-yes-no
            format_custom_false: ''
            format_custom_true: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_gen_certs:
          id: field_npxsubmission_gen_certs
          table: npxsubmission__field_npxsubmission_gen_certs
          field: field_npxsubmission_gen_certs
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Certyfikaty
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: unicode-yes-no
            format_custom_false: ''
            format_custom_true: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        view_npxsubmission:
          id: view_npxsubmission
          table: npxsubmission
          field: view_npxsubmission
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxsubmission
          plugin_id: entity_link
          label: Podgląd
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          text: przeglądaj
          output_url_as_text: false
          absolute: false
        nothing:
          id: nothing
          table: views
          field: nothing
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: custom
          label: Powiadomienia
          exclude: false
          alter:
            alter_text: true
            text: '<a href="/admin/npx/powiadomienia?submission_id={{ id }}&field_npxsubmission_date_nid_target_id=">Powiadomienia</a>'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
        field_npxsubmission_nid_1:
          id: field_npxsubmission_nid_1
          table: npxsubmission__field_npxsubmission_nid
          field: field_npxsubmission_nid
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Szkolenie ID'
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_entity_id
          settings: {  }
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_date_nid_1:
          id: field_npxsubmission_date_nid_1
          table: npxsubmission__field_npxsubmission_date_nid
          field: field_npxsubmission_date_nid
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Termin szkolenia ID'
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_entity_id
          settings: {  }
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        nothing_1:
          id: nothing_1
          table: views
          field: nothing
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: custom
          label: Ankieta
          exclude: false
          alter:
            alter_text: true
            text: "<a href=\"https://4grow.pl/ankieta/form/{{ field_npxsubmission_nid_1 }}\" target=\"_blank\">Ankieta&nbsp;temat</a>\r\n<a href=\"https://4grow.pl/ankieta/form/{{ field_npxsubmission_nid_1 }}/{{ field_npxsubmission_date_nid_1 }}\" target=\"_blank\">Ankieta&nbsp;termin</a>"
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
        operations:
          id: operations
          table: npxsubmission
          field: operations
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxsubmission
          plugin_id: entity_operations
          label: Akcje
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          destination: true
      pager:
        type: mini
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 100
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: basic
        options:
          submit_button: Zastosuj
          reset_button: false
          reset_button_label: Resetuj
          exposed_sorts_label: 'Sortuj po'
          expose_sort_order: true
          sort_asc_label: Rosnąco
          sort_desc_label: Malejąco
      access:
        type: role
        options:
          role:
            administrator: administrator
            redaktor: redaktor
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        field_npxtraining_date_end_value:
          id: field_npxtraining_date_end_value
          table: node__field_npxtraining_date_end
          field: field_npxtraining_date_end_value
          relationship: field_npxsubmission_date_nid
          group_type: group
          admin_label: ''
          plugin_id: datetime
          order: DESC
          expose:
            label: 'Data zakończenia (field_npxtraining_date_end)'
            field_identifier: field_npxtraining_date_end_value
          exposed: false
          granularity: day
      arguments:
        field_npxsubmission_date_nid_target_id:
          id: field_npxsubmission_date_nid_target_id
          table: npxsubmission__field_npxsubmission_date_nid
          field: field_npxsubmission_date_nid_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: entity_target_id
          default_action: ignore
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
          target_entity_type_id: node
      filters:
        type:
          id: type
          table: npxsubmission_field_data
          field: type
          entity_type: npxsubmission
          entity_field: type
          plugin_id: bundle
          value:
            npxsubmission: npxsubmission
          expose:
            operator_limit_selection: false
            operator_list: {  }
        id:
          id: id
          table: npxsubmission_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxsubmission
          entity_field: id
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: id_op
            label: 'Numer zgłoszenia'
            description: ''
            use_operator: false
            operator: id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            id: id
            created: created
            uid: uid
            field_npxsubmission_nid: field_npxsubmission_nid
            field_npxsubmission_date_nid: field_npxsubmission_date_nid
            field_npxtraining_date_end: field_npxtraining_date_end
            field_npxsubmission_online: field_npxsubmission_online
            field_npxsubmission_paper: field_npxsubmission_paper
            field_npxsubmission_amount: field_npxsubmission_amount
            field_npxsubmission_participants: field_npxsubmission_participants
            field_npxsubmission_trainer: field_npxsubmission_trainer
            field_npxtraining_date_trainer: field_npxtraining_date_trainer
            field_npxsubmission_pdf: field_npxsubmission_pdf
            field_npxsubmission_send_notify: field_npxsubmission_send_notify
            view_npxsubmission: view_npxsubmission
            nothing: nothing
            operations: operations
          default: '-1'
          info:
            id:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            created:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            uid:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_nid:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_date_nid:
              sortable: false
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxtraining_date_end:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_online:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_paper:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_amount:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_participants:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_trainer:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxtraining_date_trainer:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_pdf:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_send_notify:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            view_npxsubmission:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            nothing:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            operations:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: true
          summary: ''
          empty_table: false
          caption: ''
          description: ''
      row:
        type: 'ds_entity:npxsubmission'
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        field_npxsubmission_date_nid:
          id: field_npxsubmission_date_nid
          table: npxsubmission__field_npxsubmission_date_nid
          field: field_npxsubmission_date_nid
          relationship: none
          group_type: group
          admin_label: 'field_npxsubmission_date_nid: Content'
          plugin_id: standard
          required: false
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.roles
      tags:
        - 'config:field.storage.node.field_npxtraining_date_end'
        - 'config:field.storage.node.field_npxtraining_date_trainer'
        - 'config:field.storage.npxsubmission.field_npxsubmission_amount'
        - 'config:field.storage.npxsubmission.field_npxsubmission_date_nid'
        - 'config:field.storage.npxsubmission.field_npxsubmission_gen_certs'
        - 'config:field.storage.npxsubmission.field_npxsubmission_nid'
        - 'config:field.storage.npxsubmission.field_npxsubmission_online'
        - 'config:field.storage.npxsubmission.field_npxsubmission_paper'
        - 'config:field.storage.npxsubmission.field_npxsubmission_participants'
        - 'config:field.storage.npxsubmission.field_npxsubmission_pdf'
        - 'config:field.storage.npxsubmission.field_npxsubmission_send_notify'
        - 'config:field.storage.npxsubmission.field_npxsubmission_trainer'
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders:
        metatag_display_extender: {  }
      path: admin/npx/zgloszenia-z-formularza-szkoleniowego
      menu:
        enabled: true
        expanded: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.roles
      tags:
        - 'config:field.storage.node.field_npxtraining_date_end'
        - 'config:field.storage.node.field_npxtraining_date_trainer'
        - 'config:field.storage.npxsubmission.field_npxsubmission_amount'
        - 'config:field.storage.npxsubmission.field_npxsubmission_date_nid'
        - 'config:field.storage.npxsubmission.field_npxsubmission_gen_certs'
        - 'config:field.storage.npxsubmission.field_npxsubmission_nid'
        - 'config:field.storage.npxsubmission.field_npxsubmission_online'
        - 'config:field.storage.npxsubmission.field_npxsubmission_paper'
        - 'config:field.storage.npxsubmission.field_npxsubmission_participants'
        - 'config:field.storage.npxsubmission.field_npxsubmission_pdf'
        - 'config:field.storage.npxsubmission.field_npxsubmission_send_notify'
        - 'config:field.storage.npxsubmission.field_npxsubmission_trainer'
