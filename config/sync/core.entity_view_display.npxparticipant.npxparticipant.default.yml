uuid: bd23a60b-8af4-456e-9d02-2f0b0d970ac1
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxparticipant.npxparticipant
    - field.field.npxparticipant.npxparticipant.field_npxparticipant_cert
    - field.field.npxparticipant.npxparticipant.field_npxparticipant_email
    - field.field.npxparticipant.npxparticipant.field_npxparticipant_firstname
    - field.field.npxparticipant.npxparticipant.field_npxparticipant_gender
    - field.field.npxparticipant.npxparticipant.field_npxparticipant_name
    - field.field.npxparticipant.npxparticipant.field_npxparticipant_phone
    - field.field.npxparticipant.npxparticipant.field_npxparticipant_position
id: npxparticipant.npxparticipant.default
targetEntityType: npxparticipant
bundle: npxparticipant
mode: default
content:
  created:
    type: timestamp
    label: hidden
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: ''
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 1
    region: content
  field_npxparticipant_email:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 5
    region: content
  field_npxparticipant_firstname:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 7
    region: content
  field_npxparticipant_gender:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 2
    region: content
  field_npxparticipant_name:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 3
    region: content
  field_npxparticipant_phone:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 6
    region: content
  field_npxparticipant_position:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 4
    region: content
  title:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  changed: true
  field_npxparticipant_cert: true
  id: true
  langcode: true
