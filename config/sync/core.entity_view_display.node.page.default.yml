uuid: c1e6945f-226d-4ab5-a563-9912c75c4f8b
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.page.body
    - field.field.node.page.field_contact_section
    - field.field.node.page.field_metatags
    - field.field.node.page.field_npx_basic_paragraphs
    - node.type.page
  module:
    - ds
    - entity_reference_revisions
    - metatag
    - text
    - user
third_party_settings:
  ds:
    layout:
      id: ds_1col
      library: null
      disable_css: false
      entity_classes: old_view_mode
      settings:
        classes:
          layout_class: {  }
          ds_content: {  }
          ds_hidden: {  }
        wrappers:
          ds_content: div
          ds_hidden: div
        outer_wrapper: div
        attributes: ''
        link_attribute: ''
        link_custom: ''
    regions:
      ds_content:
        - body
        - field_npx_basic_paragraphs
        - field_metatags
id: node.page.default
targetEntityType: node
bundle: page
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: ds_content
  field_contact_section:
    type: entity_reference_revisions_entity_view
    label: above
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 3
    region: content
  field_metatags:
    type: metatag_empty_formatter
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: ds_content
  field_npx_basic_paragraphs:
    type: entity_reference_revisions_entity_view
    label: hidden
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 1
    region: ds_content
hidden:
  langcode: true
  links: true
