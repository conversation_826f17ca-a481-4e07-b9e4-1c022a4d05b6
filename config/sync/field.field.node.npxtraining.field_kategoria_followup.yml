uuid: fa6a52b4-7a50-44be-9660-4345f7a5bf54
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_kategoria_followup
    - node.type.npxtraining
id: node.npxtraining.field_kategoria_followup
field_name: field_kategoria_followup
entity_type: node
bundle: npxtraining
label: 'Kategoria followup'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: views
  handler_settings:
    view:
      view_name: filtr_kategoria_followup
      display_name: entity_reference_1
      arguments: {  }
field_type: entity_reference
