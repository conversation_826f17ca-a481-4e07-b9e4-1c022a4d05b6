uuid: e5bbbc10-2d6b-4911-8000-ecfc6888336b
langcode: pl
status: true
dependencies:
  module:
    - blazy
    - collapse_text
    - npx
    - slick
name: 'Full HTML no p no br'
format: full_html_no_p_no_br
weight: -9
filters:
  blazy_filter:
    id: blazy_filter
    provider: blazy
    status: false
    weight: 3
    settings:
      media_switch: ''
      hybrid_style: ''
      box_style: ''
      box_media_style: ''
      box_caption: ''
      filter_tags:
        img: img
        iframe: iframe
      use_data_uri: '0'
  filter_collapse_text:
    id: filter_collapse_text
    provider: collapse_text
    status: false
    weight: 0
    settings:
      default_title: 'Click here to expand or collapse this section'
      form: '1'
  filter_htmlcorrector:
    id: filter_htmlcorrector
    provider: filter
    status: true
    weight: 10
    settings: {  }
  npx_filter_lazyload:
    id: npx_filter_lazyload
    provider: npx
    status: true
    weight: 20
    settings: {  }
  slick_filter:
    id: slick_filter
    provider: slick
    status: false
    weight: 4
    settings:
      caption:
        alt: '0'
        title: '0'
      optionset: default
      layout: ''
      background: false
      box_caption: ''
      box_caption_custom: ''
      box_media_style: ''
      loading: ''
      responsive_image_style: ''
      box_style: ''
      image_style: ''
      media_switch: ''
      ratio: ''
      thumbnail_style: ''
      grid: ''
      grid_medium: ''
      grid_small: ''
      style: ''
      skin: ''
      overridables:
        arrows: '0'
        autoplay: '0'
        dots: '0'
        draggable: '0'
        infinite: '0'
        mouseWheel: '0'
        randomize: '0'
        variableWidth: '0'
      optionset_thumbnail: ''
      skin_thumbnail: ''
      thumbnail_caption: ''
      thumbnail_effect: ''
      thumbnail_position: ''
      override: false
      preserve_keys: false
      visible_items: null
