uuid: e9308659-f085-4e6e-bdd4-0c3da653a055
langcode: pl
status: true
dependencies:
  config:
    - field.field.taxonomy_term.training_date_room.field_npx_feeding
    - field.field.taxonomy_term.training_date_room.field_npx_information
    - field.field.taxonomy_term.training_date_room.field_npx_map_link
    - field.field.taxonomy_term.training_date_room.field_npx_map_link_not_embed
    - field.field.taxonomy_term.training_date_room.field_npx_virtual
    - field.field.taxonomy_term.training_date_room.field_room_page_desc
    - taxonomy.vocabulary.training_date_room
  module:
    - link
    - text
id: taxonomy_term.training_date_room.default
targetEntityType: taxonomy_term
bundle: training_date_room
mode: default
content:
  description:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  field_npx_feeding:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 4
    region: content
  field_npx_information:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 3
    region: content
  field_npx_map_link:
    type: link
    label: above
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: '0'
      target: '0'
    third_party_settings:
      ds:
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
    weight: 1
    region: content
  field_npx_map_link_not_embed:
    type: link
    label: above
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 2
    region: content
  field_npx_virtual:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 6
    region: content
  field_room_page_desc:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 5
    region: content
hidden:
  langcode: true
