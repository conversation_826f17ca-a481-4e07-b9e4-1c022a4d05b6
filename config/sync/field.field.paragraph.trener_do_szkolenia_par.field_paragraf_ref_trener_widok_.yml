uuid: 9ea87e00-ffea-4591-8515-21c74e48e3ed
langcode: pl
status: true
dependencies:
  config:
    - field.storage.paragraph.field_paragraf_ref_trener_widok_
    - node.type.npxtrainer
    - paragraphs.paragraphs_type.trener_do_szkolenia_par
id: paragraph.trener_do_szkolenia_par.field_paragraf_ref_trener_widok_
field_name: field_paragraf_ref_trener_widok_
entity_type: paragraph
bundle: trener_do_szkolenia_par
label: 'Trener - nagłówek'
description: 'Pole zaciąga pierwsze 600 znaków z opisu sylwetki trenera wybranego z listy oraz jego zdjęcie. Zawartość pola w połączeniu z polem "Dodatkowe informacje o trenerze" stanowić będzie opis postaci trenera na stronie szkolenia.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      npxtrainer: npxtrainer
    sort:
      field: _none
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
