uuid: e8e44261-a7e8-4099-9953-83a3645eeadc
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.book.body
    - field.field.node.book.field_metatags
    - node.type.book
  module:
    - metatag
    - path
    - text
id: node.book.default
targetEntityType: node
bundle: book
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 6
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  field_metatags:
    type: metatag_firehose
    weight: 7
    region: content
    settings:
      sidebar: true
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 1
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  path:
    type: path
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 10
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 5
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 2
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
hidden: {  }
