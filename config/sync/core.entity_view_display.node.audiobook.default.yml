uuid: fe89bb7b-6bf4-47a3-ac56-eacf4ea72b90
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.audiobook.body
    - field.field.node.audiobook.field_mp3
    - node.type.audiobook
  module:
    - ds
    - file
    - text
    - user
id: node.audiobook.default
targetEntityType: node
bundle: audiobook
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 101
    region: content
  field_mp3:
    type: file_audio
    label: hidden
    settings:
      controls: true
      autoplay: false
      loop: false
      multiple_file_display_type: tags
    third_party_settings:
      ds:
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
    weight: 102
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  langcode: true
