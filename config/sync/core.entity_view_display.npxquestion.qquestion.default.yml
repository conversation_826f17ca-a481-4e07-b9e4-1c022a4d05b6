uuid: 11739212-fc90-4fd8-8e55-be342f495890
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxquestion.qquestion
    - field.field.npxquestion.qquestion.field_answers
    - field.field.npxquestion.qquestion.field_category
    - field.field.npxquestion.qquestion.field_multiple
    - field.field.npxquestion.qquestion.field_question_text
  module:
    - text
id: npxquestion.qquestion.default
targetEntityType: npxquestion
bundle: qquestion
mode: default
content:
  field_answers:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: -3
    region: content
  field_category:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: -2
    region: content
  field_multiple:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: -1
    region: content
  field_question_text:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: -4
    region: content
  title:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: -5
    region: content
hidden:
  id: true
  langcode: true
