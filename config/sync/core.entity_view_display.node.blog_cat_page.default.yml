uuid: 3572da85-3926-41c7-8ca7-0e6dbf9dd6a4
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.blog_cat_page.body
    - field.field.node.blog_cat_page.field_kategoria_tag_wpisow
    - field.field.node.blog_cat_page.field_metatags
    - node.type.blog_cat_page
  module:
    - metatag
    - text
    - user
id: node.blog_cat_page.default
targetEntityType: node
bundle: blog_cat_page
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 101
    region: content
  field_kategoria_tag_wpisow:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 102
    region: content
  field_metatags:
    type: metatag_empty_formatter
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: ds_content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  langcode: true
