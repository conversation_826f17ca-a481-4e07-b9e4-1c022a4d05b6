uuid: a3faa0aa-1791-4627-8af4-14345be34078
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_faq
    - node.type.landing_page
    - paragraphs.paragraphs_type.faq_question_answer
  module:
    - entity_reference_revisions
id: node.landing_page.field_faq
field_name: field_faq
entity_type: node
bundle: landing_page
label: FAQ
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      faq_question_answer: faq_question_answer
    negate: 0
    target_bundles_drag_drop:
      arguments:
        weight: 18
        enabled: false
      cecha_par:
        weight: 19
        enabled: false
      contact_section:
        weight: 20
        enabled: false
      faq_question_answer:
        weight: 21
        enabled: true
      logo_x_2:
        weight: 22
        enabled: false
      naglowek_par:
        weight: 23
        enabled: false
      npx_par_teaser:
        weight: 24
        enabled: false
      par_block:
        weight: 25
        enabled: false
      par_colorbox_gallery:
        weight: 26
        enabled: false
      par_quiz_review:
        weight: 27
        enabled: false
      par_text:
        weight: 28
        enabled: false
      par_training_from_category:
        weight: 29
        enabled: false
      title_with_description:
        weight: 30
        enabled: false
      training_contact:
        weight: 31
        enabled: false
      trener_do_szkolenia_par:
        weight: 32
        enabled: false
      tytul_szkolenia:
        weight: 33
        enabled: false
      tytul_szkolenia_ii:
        weight: 34
        enabled: false
field_type: entity_reference_revisions
