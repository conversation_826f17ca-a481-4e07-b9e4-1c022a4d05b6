uuid: d6ec5cc0-c5cf-4986-b37d-c570159a99b7
langcode: pl
status: false
dependencies:
  config:
    - views.view.wstep_do_tresci
  module:
    - node
    - views
  theme:
    - bootstrap4grow
id: bootstrap4grow_views_block__wstep_do_tresci_block_2
theme: bootstrap4grow
region: featured_top
weight: -40
provider: null
plugin: 'views_block:wstep_do_tresci-block_2'
settings:
  id: 'views_block:wstep_do_tresci-block_2'
  label: ''
  label_display: '0'
  provider: views
  context_mapping: {  }
  views_label: ''
  items_per_page: none
visibility:
  'entity_bundle:node':
    id: 'entity_bundle:node'
    negate: false
    context_mapping:
      node: '@node.node_route_context:node'
    bundles:
      npxtraining: npxtraining
      npxtraining_closed: npxtraining_closed
