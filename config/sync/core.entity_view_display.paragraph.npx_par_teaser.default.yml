uuid: 03d41c62-b809-4449-8028-479570d1d3f3
langcode: pl
status: true
dependencies:
  config:
    - field.field.paragraph.npx_par_teaser.field_teaser_body
    - field.field.paragraph.npx_par_teaser.field_teaser_image
    - field.field.paragraph.npx_par_teaser.field_teaser_link
    - field.field.paragraph.npx_par_teaser.field_teaser_link_text
    - field.field.paragraph.npx_par_teaser.field_teaser_title
    - paragraphs.paragraphs_type.npx_par_teaser
  module:
    - image
    - link
    - text
id: paragraph.npx_par_teaser.default
targetEntityType: paragraph
bundle: npx_par_teaser
mode: default
content:
  field_teaser_body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  field_teaser_image:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 1
    region: content
  field_teaser_link:
    type: link
    label: hidden
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 2
    region: content
  field_teaser_link_text:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings:
      ds:
        ft:
          id: reset
          settings:
            lb: ''
    weight: 4
    region: content
  field_teaser_title:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 3
    region: content
hidden: {  }
