uuid: 0d90cb0c-0ca6-4627-9e08-73efb3f230ac
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_npxtraining_date_start
    - field.storage.node.field_npxtraining_date_trainer
    - field.storage.npxparticipant.field_npxparticipant_email
    - field.storage.npxparticipant.field_npxparticipant_firstname
    - field.storage.npxparticipant.field_npxparticipant_name
    - field.storage.npxsubmission.field_npxsubmission_firm
    - field.storage.npxsubmission.field_npxsubmission_nid
    - field.storage.npxsubmission.field_npxsubmission_trainer
    - system.menu.admin
    - user.role.administrator
    - user.role.redaktor
  module:
    - csv_serialization
    - datetime
    - eck
    - node
    - rest
    - serialization
    - user
    - views_data_export
id: lista_uczestnikow_rozbudowana
label: 'Lista uczestników rozbudowana'
module: views
description: ''
tag: ''
base_table: npxparticipant_field_data
base_field: id
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: 'Lista uczestników rozbudowana'
      fields:
        field_npxsubmission_firm:
          id: field_npxsubmission_firm
          table: npxsubmission__field_npxsubmission_firm
          field: field_npxsubmission_firm
          relationship: reverse__npxsubmission__field_npxsubmission_participants
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Firma
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxparticipant_firstname:
          id: field_npxparticipant_firstname
          table: npxparticipant__field_npxparticipant_firstname
          field: field_npxparticipant_firstname
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Imię
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxparticipant_name:
          id: field_npxparticipant_name
          table: npxparticipant__field_npxparticipant_name
          field: field_npxparticipant_name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Nazwisko
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxparticipant_email:
          id: field_npxparticipant_email
          table: npxparticipant__field_npxparticipant_email
          field: field_npxparticipant_email
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Email
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: basic_string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_nid:
          id: field_npxsubmission_nid
          table: npxsubmission__field_npxsubmission_nid
          field: field_npxsubmission_nid
          relationship: reverse__npxsubmission__field_npxsubmission_participants
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Szkolenie
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxtraining_date_start:
          id: field_npxtraining_date_start
          table: node__field_npxtraining_date_start
          field: field_npxtraining_date_start
          relationship: field_npxsubmission_date_nid
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Data szkolenia'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: datetime_default
          settings:
            timezone_override: ''
            format_type: html_date
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_trainer:
          id: field_npxsubmission_trainer
          table: npxsubmission__field_npxsubmission_trainer
          field: field_npxsubmission_trainer
          relationship: reverse__npxsubmission__field_npxsubmission_participants
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Trener planowany'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxtraining_date_trainer:
          id: field_npxtraining_date_trainer
          table: node__field_npxtraining_date_trainer
          field: field_npxtraining_date_trainer
          relationship: field_npxsubmission_date_nid
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Trener prowadzący'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 100
          total_pages: null
          id: 0
          tags:
            next: 'Następna ›'
            previous: '‹ Poprzednia'
            first: Pierwsza
            last: 'Ostatnia >>'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
          pagination_heading_level: h4
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: role
        options:
          role:
            administrator: administrator
            redaktor: redaktor
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts: {  }
      arguments: {  }
      filters:
        field_npxsubmission_firm_value:
          id: field_npxsubmission_firm_value
          table: npxsubmission__field_npxsubmission_firm
          field: field_npxsubmission_firm_value
          relationship: reverse__npxsubmission__field_npxsubmission_participants
          group_type: group
          admin_label: ''
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: field_npxsubmission_firm_value_op
            label: Firma
            description: ''
            use_operator: false
            operator: field_npxsubmission_firm_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_npxsubmission_firm_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
              tester_grow3: '0'
              redaktor_lvl2: '0'
              manager: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_npxparticipant_name_value:
          id: field_npxparticipant_name_value
          table: npxparticipant__field_npxparticipant_name
          field: field_npxparticipant_name_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: field_npxparticipant_name_value_op
            label: Nazwisko
            description: ''
            use_operator: false
            operator: field_npxparticipant_name_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_npxparticipant_name_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
              tester_grow3: '0'
              redaktor_lvl2: '0'
              manager: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_npxparticipant_email_value:
          id: field_npxparticipant_email_value
          table: npxparticipant__field_npxparticipant_email
          field: field_npxparticipant_email_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: field_npxparticipant_email_value_op
            label: Email
            description: ''
            use_operator: false
            operator: field_npxparticipant_email_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_npxparticipant_email_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
              tester_grow3: '0'
              redaktor_lvl2: '0'
              manager: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            field_npxsubmission_firm: field_npxsubmission_firm
            field_npxparticipant_firstname: field_npxparticipant_firstname
            field_npxparticipant_name: field_npxparticipant_name
            field_npxparticipant_email: field_npxparticipant_email
            field_npxsubmission_nid: field_npxsubmission_nid
            field_npxtraining_date_start: field_npxtraining_date_start
            field_npxsubmission_trainer: field_npxsubmission_trainer
            field_npxtraining_date_trainer: field_npxtraining_date_trainer
          default: '-1'
          info:
            field_npxsubmission_firm:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxparticipant_firstname:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxparticipant_name:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxparticipant_email:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_nid:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxtraining_date_start:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_trainer:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxtraining_date_trainer:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: false
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        reverse__npxsubmission__field_npxsubmission_participants:
          id: reverse__npxsubmission__field_npxsubmission_participants
          table: npxparticipant_field_data
          field: reverse__npxsubmission__field_npxsubmission_participants
          relationship: none
          group_type: group
          admin_label: field_npxsubmission_participants
          entity_type: npxparticipant
          plugin_id: entity_reverse
          required: true
        field_npxsubmission_nid:
          id: field_npxsubmission_nid
          table: npxsubmission__field_npxsubmission_nid
          field: field_npxsubmission_nid
          relationship: reverse__npxsubmission__field_npxsubmission_participants
          group_type: group
          admin_label: 'field_npxsubmission_nid: Content'
          plugin_id: standard
          required: true
        field_npxsubmission_date_nid:
          id: field_npxsubmission_date_nid
          table: npxsubmission__field_npxsubmission_date_nid
          field: field_npxsubmission_date_nid
          relationship: reverse__npxsubmission__field_npxsubmission_participants
          group_type: group
          admin_label: 'field_npxsubmission_date_nid: Content'
          plugin_id: standard
          required: false
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: '<strong>Widok z listą uczestników, zgłoszony przez Mateusza.</strong>'
            format: full_html
          tokenize: false
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.roles
      tags:
        - 'config:field.storage.node.field_npxtraining_date_start'
        - 'config:field.storage.node.field_npxtraining_date_trainer'
        - 'config:field.storage.npxparticipant.field_npxparticipant_email'
        - 'config:field.storage.npxparticipant.field_npxparticipant_firstname'
        - 'config:field.storage.npxparticipant.field_npxparticipant_name'
        - 'config:field.storage.npxsubmission.field_npxsubmission_firm'
        - 'config:field.storage.npxsubmission.field_npxsubmission_nid'
        - 'config:field.storage.npxsubmission.field_npxsubmission_trainer'
  data_export_1:
    id: data_export_1
    display_title: 'Eksport danych'
    display_plugin: data_export
    position: 2
    display_options:
      style:
        type: data_export
        options:
          formats:
            csv: csv
          csv_settings:
            delimiter: ','
            enclosure: '"'
            escape_char: \
            strip_tags: true
            trim: true
            encoding: utf8
            utf8_bom: '1'
            use_serializer_encode_only: false
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
      path: admin/npx/lista-uczestnikow-rozbudowana-csv
      auth:
        - cookie
      displays:
        page_1: page_1
        default: '0'
      filename: ''
      automatic_download: false
      store_in_public_file_directory: false
      custom_redirect_path: false
      redirect_to_display: none
      include_query_params: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - url
        - user.roles
      tags:
        - 'config:field.storage.node.field_npxtraining_date_start'
        - 'config:field.storage.node.field_npxtraining_date_trainer'
        - 'config:field.storage.npxparticipant.field_npxparticipant_email'
        - 'config:field.storage.npxparticipant.field_npxparticipant_firstname'
        - 'config:field.storage.npxparticipant.field_npxparticipant_name'
        - 'config:field.storage.npxsubmission.field_npxsubmission_firm'
        - 'config:field.storage.npxsubmission.field_npxsubmission_nid'
        - 'config:field.storage.npxsubmission.field_npxsubmission_trainer'
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders:
        metatag_display_extender: {  }
      path: admin/npx/lista-uczestnikow-rozbudowana
      menu:
        type: normal
        title: 'Lista uczestników rozbudowana'
        description: ''
        weight: 0
        enabled: true
        expanded: false
        menu_name: admin
        parent: 'menu_link_content:ac30b9fc-f31c-4b05-9b24-d26c833f629e'
        context: '0'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.roles
      tags:
        - 'config:field.storage.node.field_npxtraining_date_start'
        - 'config:field.storage.node.field_npxtraining_date_trainer'
        - 'config:field.storage.npxparticipant.field_npxparticipant_email'
        - 'config:field.storage.npxparticipant.field_npxparticipant_firstname'
        - 'config:field.storage.npxparticipant.field_npxparticipant_name'
        - 'config:field.storage.npxsubmission.field_npxsubmission_firm'
        - 'config:field.storage.npxsubmission.field_npxsubmission_nid'
        - 'config:field.storage.npxsubmission.field_npxsubmission_trainer'
