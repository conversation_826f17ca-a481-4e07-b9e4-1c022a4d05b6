uuid: 8aade5f2-fcd6-4861-88ff-c4880dcd8a7b
langcode: pl
status: true
dependencies:
  config:
    - core.entity_view_mode.node.bootstrap_carousel
    - field.field.node.media.body
    - field.field.node.media.field_audio_wideo
    - field.field.node.media.field_image
    - field.field.node.media.field_media_kat
    - field.field.node.media.field_metatags
    - field.field.node.media.field_miniaturka
    - field.field.node.media.field_mp3
    - field.field.node.media.field_video_embed
    - field.field.node.media.field_zajawka
    - node.type.media
  module:
    - bg_image_formatter
    - ds
    - field_group
    - metatag
    - text
    - user
    - video_embed_field
third_party_settings:
  ds:
    layout:
      id: ds_1col
      library: null
      disable_css: false
      entity_classes: old_view_mode
      settings:
        classes:
          layout_class: {  }
          ds_content: {  }
          ds_hidden: {  }
        wrappers:
          ds_content: div
          ds_hidden: div
        outer_wrapper: div
        attributes: ''
        link_attribute: ''
        link_custom: ''
    regions:
      ds_content:
        - group_inner
        - field_image
        - field_video_embed
        - field_zajawka
      ds_hidden:
        - field_metatags
  field_group:
    group_inner:
      children:
        - field_image
        - field_video_embed
        - field_zajawka
      label: inner
      parent_name: ''
      region: content
      weight: 0
      format_type: html_element
      format_settings:
        classes: inner
        id: ''
        element: div
        show_label: false
        label_element: h3
        attributes: ''
        effect: none
        speed: fast
id: node.media.bootstrap_carousel
targetEntityType: node
bundle: media
mode: bootstrap_carousel
content:
  field_image:
    type: bg_image_formatter
    label: hidden
    settings:
      image_style: ''
      css_settings:
        bg_image_selector: body
        bg_image_z_index: auto
        bg_image_color: '#FFFFFF'
        bg_image_x: left
        bg_image_y: top
        bg_image_attachment: scroll
        bg_image_repeat: no-repeat
        bg_image_background_size: ''
        bg_image_media_query: all
        bg_image_important: 1
        bg_image_background_size_ie8: 0
    third_party_settings: {  }
    weight: 1
    region: ds_content
  field_metatags:
    type: metatag_empty_formatter
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 4
    region: ds_hidden
  field_video_embed:
    type: video_embed_field_thumbnail
    label: hidden
    settings:
      image_style: ''
      link_image_to: ''
    third_party_settings:
      ds:
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
    weight: 2
    region: ds_content
  field_zajawka:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 3
    region: ds_content
hidden:
  body: true
  field_audio_wideo: true
  field_media_kat: true
  field_miniaturka: true
  field_mp3: true
  langcode: true
  links: true
