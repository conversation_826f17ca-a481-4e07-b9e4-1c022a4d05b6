uuid: 987561b8-72e1-4b58-ab2e-8eea637aa4bd
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.body
    - field.storage.node.field_additional_info
    - field.storage.node.field_bonus_email
    - field.storage.node.field_name
    - field.storage.node.field_npxtraining_date_trainer
    - field.storage.node.field_promotions
    - field.storage.node.field_surname
    - system.menu.admin
    - taxonomy.vocabulary.training_date_room
  module:
    - node
    - npx_survey
    - options
    - taxonomy
    - text
    - user
_core:
  default_config_hash: _15VZjLPsaWTB364NdX-k1jlbMSSHPSb5U8GmkWo9Pg
id: npx_survey_entity_list
label: 'Ankieta poszkoleniowa lista'
module: views
description: ''
tag: ''
base_table: npx_survey_entity
base_field: id
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: 'An<PERSON><PERSON> poszkoleniowa lista'
      fields:
        id:
          id: id
          table: npx_survey_entity
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: id
          plugin_id: field
          label: ID
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        training_id:
          id: training_id
          table: npx_survey_entity
          field: training_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: training_id
          plugin_id: field
          label: 'Training node ID'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        training_date_id:
          id: training_date_id
          table: npx_survey_entity
          field: training_date_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: training_date_id
          plugin_id: field
          label: 'TrainingDate node ID'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        overall_rating:
          id: overall_rating
          table: npx_survey_entity
          field: overall_rating
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: overall_rating
          plugin_id: field
          label: 'Overall rating'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        training_rating:
          id: training_rating
          table: npx_survey_entity
          field: training_rating
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: training_rating
          plugin_id: field
          label: 'Training rating'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        trainer_rating:
          id: trainer_rating
          table: npx_survey_entity
          field: trainer_rating
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: trainer_rating
          plugin_id: field
          label: 'Trainer rating'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        positive_comment__value:
          id: positive_comment__value
          table: npx_survey_entity
          field: positive_comment__value
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: positive_comment
          plugin_id: field
          label: 'What was positive'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        negative_comment__value:
          id: negative_comment__value
          table: npx_survey_entity
          field: negative_comment__value
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: negative_comment
          plugin_id: field
          label: 'What was negative'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        not_perfect_comment__value:
          id: not_perfect_comment__value
          table: npx_survey_entity
          field: not_perfect_comment__value
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: not_perfect_comment
          plugin_id: field
          label: 'What was not perfect'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        useful_rating:
          id: useful_rating
          table: npx_survey_entity
          field: useful_rating
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: useful_rating
          plugin_id: field
          label: 'Useful rating'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        food_rating:
          id: food_rating
          table: npx_survey_entity
          field: food_rating
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: food_rating
          plugin_id: field
          label: 'Food rating'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        found:
          id: found
          table: npx_survey_entity
          field: found
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: found
          plugin_id: field
          label: 'Where found'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        decision_comment__value:
          id: decision_comment__value
          table: npx_survey_entity
          field: decision_comment__value
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: decision_comment
          plugin_id: field
          label: 'Decision comment'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        improve_comment__value:
          id: improve_comment__value
          table: npx_survey_entity
          field: improve_comment__value
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: improve_comment
          plugin_id: field
          label: 'Improve comment'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_name:
          id: field_name
          table: node__field_name
          field: field_name
          relationship: reverse__node__field_survey
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Imię
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_surname:
          id: field_surname
          table: node__field_surname
          field: field_surname
          relationship: reverse__node__field_survey
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Nazwisko
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        body:
          id: body
          table: node__body
          field: body
          relationship: reverse__node__field_survey
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Opinia
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_additional_info:
          id: field_additional_info
          table: node__field_additional_info
          field: field_additional_info
          relationship: reverse__node__field_survey
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Dodatkowe informacje'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: list_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: ul
          separator: ', '
          field_api_classes: false
        field_bonus_email:
          id: field_bonus_email
          table: node__field_bonus_email
          field: field_bonus_email
          relationship: reverse__node__field_survey
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Bonus email'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: basic_string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_promotions:
          id: field_promotions
          table: node__field_promotions
          field: field_promotions
          relationship: reverse__node__field_survey
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Promocje
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: default
            format_custom_false: ''
            format_custom_true: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        operations:
          id: operations
          table: npx_survey_entity
          field: operations
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          plugin_id: entity_operations
          label: 'Odnośniki do czynności'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          destination: false
        created_2:
          id: created_2
          table: npx_survey_entity
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: created
          plugin_id: field
          label: Utworzono
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: medium
            custom_date_format: ''
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxtraining_date_trainer:
          id: field_npxtraining_date_trainer
          table: node__field_npxtraining_date_trainer
          field: field_npxtraining_date_trainer
          relationship: training_id
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Trener
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxtraining_date_trainer_1:
          id: field_npxtraining_date_trainer_1
          table: node__field_npxtraining_date_trainer
          field: field_npxtraining_date_trainer
          relationship: training_date_id
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Trener
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        training_form_type:
          id: training_form_type
          table: npx_survey_entity
          field: training_form_type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: training_form_type
          plugin_id: field
          label: 'Forma szkolenia'
          exclude: false
          alter:
            alter_text: true
            text: '{% if training_form_type__value == 1 %}online{% elseif training_form_type__value == 2 %}stacjonarne{% else %}brak danych{% endif %}'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        individual_training_use:
          id: individual_training_use
          table: npx_survey_entity
          field: individual_training_use
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: individual_training_use
          plugin_id: field
          label: 'Trening indywidualny'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: yes-no
            format_custom_false: ''
            format_custom_true: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 500
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
          pagination_heading_level: h4
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access administration pages'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        id:
          id: id
          table: npx_survey_entity
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npx_survey_entity
          entity_field: id
          plugin_id: standard
          order: DESC
          expose:
            label: ''
            field_identifier: id
          exposed: false
      arguments: {  }
      filters:
        title:
          id: title
          table: node_field_data
          field: title
          relationship: training_id
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: title_op
            label: 'Tytuł szkolenia'
            description: ''
            use_operator: false
            operator: title_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: title
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
              tester_grow3: '0'
              redaktor_lvl2: '0'
              manager: '0'
              webinar200: '0'
              moderator: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_name_value:
          id: field_name_value
          table: node__field_name
          field: field_name_value
          relationship: reverse__node__field_survey
          group_type: group
          admin_label: ''
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: field_name_value_op
            label: Imię
            description: ''
            use_operator: false
            operator: field_name_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_name_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
              tester_grow3: '0'
              redaktor_lvl2: '0'
              manager: '0'
              webinar200: '0'
              moderator: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_surname_value:
          id: field_surname_value
          table: node__field_surname
          field: field_surname_value
          relationship: reverse__node__field_survey
          group_type: group
          admin_label: ''
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: field_surname_value_op
            label: Nazwisko
            description: ''
            use_operator: false
            operator: field_surname_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_surname_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
              tester_grow3: '0'
              redaktor_lvl2: '0'
              manager: '0'
              webinar200: '0'
              moderator: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_npxtraining_closed_cstmr_value:
          id: field_npxtraining_closed_cstmr_value
          table: node__field_npxtraining_closed_cstmr
          field: field_npxtraining_closed_cstmr_value
          relationship: training_id
          group_type: group
          admin_label: ''
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: field_npxtraining_closed_cstmr_value_op
            label: Klient
            description: ''
            use_operator: false
            operator: field_npxtraining_closed_cstmr_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_npxtraining_closed_cstmr_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
              tester_grow3: '0'
              redaktor_lvl2: '0'
              manager: '0'
              webinar200: '0'
              moderator: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        title_1:
          id: title_1
          table: node_field_data
          field: title
          relationship: field_npxtraining_date_trainer
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: title_1_op
            label: 'Trener zamknięte'
            description: ''
            use_operator: false
            operator: title_1_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: title_1
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              redaktor_ze_zmiana_sekcji: '0'
              edytor_plus_plus: '0'
              tester_grow3: '0'
              redaktor_lvl2: '0'
              manager: '0'
              webinar200: '0'
              moderator: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        title_2:
          id: title_2
          table: node_field_data
          field: title
          relationship: field_npxtraining_date_trainer_1
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: title_2_op
            label: 'Trener szkolenie otwarte'
            description: ''
            use_operator: false
            operator: title_2_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: title_2
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              redaktor_ze_zmiana_sekcji: '0'
              edytor_plus_plus: '0'
              tester_grow3: '0'
              redaktor_lvl2: '0'
              manager: '0'
              webinar200: '0'
              moderator: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_npxtraining_date_room_target_id:
          id: field_npxtraining_date_room_target_id
          table: node__field_npxtraining_date_room
          field: field_npxtraining_date_room_target_id
          relationship: training_id
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_npxtraining_date_room_target_id_op
            label: Sala
            description: ''
            use_operator: false
            operator: field_npxtraining_date_room_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_npxtraining_date_room_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
              tester_grow3: '0'
              redaktor_lvl2: '0'
              manager: '0'
              webinar200: '0'
              moderator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: training_date_room
          type: select
          hierarchy: false
          limit: true
          error_message: true
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            id: id
            training_id: training_id
            training_date_id: training_date_id
            overall_rating: overall_rating
            training_rating: training_rating
            trainer_rating: trainer_rating
            positive_comment__value: positive_comment__value
            negative_comment__value: negative_comment__value
            not_perfect_comment__value: not_perfect_comment__value
            useful_rating: useful_rating
            food_rating: food_rating
            found: found
            decision_comment__value: decision_comment__value
            improve_comment__value: improve_comment__value
            operations: operations
          default: '-1'
          info:
            id:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            training_id:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            training_date_id:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            overall_rating:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            training_rating:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            trainer_rating:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            positive_comment__value:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            negative_comment__value:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            not_perfect_comment__value:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            useful_rating:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            food_rating:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            found:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            decision_comment__value:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            improve_comment__value:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            operations:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: false
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        reverse__node__field_survey:
          id: reverse__node__field_survey
          table: npx_survey_entity
          field: reverse__node__field_survey
          relationship: none
          group_type: group
          admin_label: field_survey
          entity_type: npx_survey_entity
          plugin_id: entity_reverse
          required: false
        training_id:
          id: training_id
          table: npx_survey_entity
          field: training_id
          relationship: none
          group_type: group
          admin_label: Szkolenie
          entity_type: npx_survey_entity
          entity_field: training_id
          plugin_id: standard
          required: false
        training_date_id:
          id: training_date_id
          table: npx_survey_entity
          field: training_date_id
          relationship: none
          group_type: group
          admin_label: 'Termin szkolenia'
          entity_type: npx_survey_entity
          entity_field: training_date_id
          plugin_id: standard
          required: false
        field_npxtraining_date_trainer:
          id: field_npxtraining_date_trainer
          table: node__field_npxtraining_date_trainer
          field: field_npxtraining_date_trainer
          relationship: training_id
          group_type: group
          admin_label: 'field_npxtraining_date_trainer: Content'
          plugin_id: standard
          required: false
        field_npxtraining_date_trainer_1:
          id: field_npxtraining_date_trainer_1
          table: node__field_npxtraining_date_trainer
          field: field_npxtraining_date_trainer
          relationship: training_date_id
          group_type: group
          admin_label: 'field_npxtraining_date_trainer: Content'
          plugin_id: standard
          required: false
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags:
        - 'config:field.storage.node.body'
        - 'config:field.storage.node.field_additional_info'
        - 'config:field.storage.node.field_bonus_email'
        - 'config:field.storage.node.field_name'
        - 'config:field.storage.node.field_npxtraining_date_trainer'
        - 'config:field.storage.node.field_promotions'
        - 'config:field.storage.node.field_surname'
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
      path: admin/npx-survey-list-view
      menu:
        type: normal
        title: 'Ankieta poszkoleniowa lista'
        description: ''
        weight: -1
        expanded: false
        menu_name: admin
        parent: npx_survey.admin_config_entities
        context: '0'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags:
        - 'config:field.storage.node.body'
        - 'config:field.storage.node.field_additional_info'
        - 'config:field.storage.node.field_bonus_email'
        - 'config:field.storage.node.field_name'
        - 'config:field.storage.node.field_npxtraining_date_trainer'
        - 'config:field.storage.node.field_promotions'
        - 'config:field.storage.node.field_surname'
