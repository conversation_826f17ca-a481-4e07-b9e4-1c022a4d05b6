uuid: 4bc2b188-0d2d-4165-bd42-41b48d007c8e
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.blog_cat_page.body
    - field.field.node.blog_cat_page.field_kategoria_tag_wpisow
    - field.field.node.blog_cat_page.field_metatags
    - node.type.blog_cat_page
  module:
    - metatag
    - path
    - text
id: node.blog_cat_page.default
targetEntityType: node
bundle: blog_cat_page
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 121
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_kategoria_tag_wpisow:
    type: options_buttons
    weight: 122
    region: content
    settings: {  }
    third_party_settings: {  }
  field_metatags:
    type: metatag_firehose
    weight: 7
    region: content
    settings:
      sidebar: true
      use_details: true
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 15
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  simple_sitemap:
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 120
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 16
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 50
    region: content
    settings: {  }
    third_party_settings: {  }
hidden: {  }
