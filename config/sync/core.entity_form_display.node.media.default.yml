uuid: a1583a85-3a0b-4ccb-a81c-28a3cd958c68
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.media.body
    - field.field.node.media.field_audio_wideo
    - field.field.node.media.field_image
    - field.field.node.media.field_media_kat
    - field.field.node.media.field_metatags
    - field.field.node.media.field_miniaturka
    - field.field.node.media.field_mp3
    - field.field.node.media.field_video_embed
    - field.field.node.media.field_zajawka
    - image.style.thumbnail
    - node.type.media
  module:
    - file
    - image
    - metatag
    - path
    - text
    - video_embed_field
id: node.media.default
targetEntityType: node
bundle: media
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 6
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  field_audio_wideo:
    type: file_generic
    weight: 13
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings:
      imce:
        enabled: true
  field_image:
    type: image_image
    weight: 4
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_media_kat:
    type: options_select
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  field_metatags:
    type: metatag_firehose
    weight: 12
    region: content
    settings: {  }
    third_party_settings: {  }
  field_miniaturka:
    type: image_image
    weight: 3
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_mp3:
    type: file_generic
    weight: 14
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  field_video_embed:
    type: video_embed_field_textfield
    weight: 17
    region: content
    settings: {  }
    third_party_settings: {  }
  field_zajawka:
    type: text_textarea
    weight: 5
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 1
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  path:
    type: path
    weight: 11
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 9
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 16
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 10
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 7
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 15
    region: content
    settings: {  }
    third_party_settings: {  }
hidden: {  }
