uuid: bbaa43d1-957b-4949-a08f-dceb38e9625d
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_mp3
    - node.type.media
  module:
    - file
id: node.media.field_mp3
field_name: field_mp3
entity_type: node
bundle: media
label: Audio
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: '[date:custom:Y]-[date:custom:m]'
  file_extensions: mp3
  max_filesize: ''
  description_field: false
field_type: file
