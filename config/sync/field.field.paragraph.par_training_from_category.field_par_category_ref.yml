uuid: cc33818a-d986-46f1-a71e-65cf84403183
langcode: pl
status: true
dependencies:
  config:
    - field.storage.paragraph.field_par_category_ref
    - paragraphs.paragraphs_type.par_training_from_category
    - taxonomy.vocabulary.training_category
id: paragraph.par_training_from_category.field_par_category_ref
field_name: field_par_category_ref
entity_type: paragraph
bundle: par_training_from_category
label: Kategoria
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      training_category: training_category
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
