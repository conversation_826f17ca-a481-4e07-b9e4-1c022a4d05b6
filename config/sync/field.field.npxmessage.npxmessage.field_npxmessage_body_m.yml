uuid: 3e2ed810-3bbb-4fbc-8e07-7dd3ebf4418b
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxmessage.npxmessage
    - field.storage.npxmessage.field_npxmessage_body_m
  module:
    - text
id: npxmessage.npxmessage.field_npxmessage_body_m
field_name: field_npxmessage_body_m
entity_type: npxmessage
bundle: npxmessage
label: 'Treść do szkolenia live-online'
description: "<a href=\"#\" class=\"npx-token-toggle\">Pokaż/ukryj tokeny</a>\r\n<code class=\"hidden\"><br/>\r\n%training:title% - tytuł szkolenia<br/>\r\n%training:url% - URL szkolenia,<br/>\r\n%date:day% - data szkolenia<br/>\r\n%date:day-5% - data szkolenia -5 dni<br/>\r\n%date:hours% - godziny<br/>\r\n%trainer% - podpis trenera ze stopką<br/>\r\n%trainer:name% - imię i nazwisko trenera<br/>\r\n%trainer:email% - email trenera<br/>\r\n%trainer:phone% - telefon trenera<br/>\r\n%trainer:inslnk% - kwestionariusz do Insights<br/>\r\n%room:name% - nazwa sali szkoleniowej<br/>\r\n%room:desc% - adres sali szkoleniowej i kod do budynku<br/>\r\n%room:link% - link do mapy<br/>\r\n%room:info% - kod do budynku, informacja o parkowaniu i jak dojechać komunikacją miejską<br/>\r\n%room:food% - informacje o wyżywieniu<br/>\r\n%zoom:link% - link do zooma<br/>\r\n%zoom:message% - opis z linkami do zooma<br/>\r\n%user:name% - imię uczestnika, wołacz<br/>\r\n%user:rawname% - imię uczestnika bez zmian<br/>\r\n%user:phrasename% - imię uczestnika, zwrot Pan/Pani i wołacz<br/>\r\n%user:phrase% - zwrot Pan/Pani i wołacz<br/>\r\n%user:phrase-mian% - zwrot Pan/Pani<br/>\r\n%user:phrase-dop% - zwrot Pana/Pani<br/>\r\n%user:phrase-cel% - zwrot Panu/Pani<br/>\r\n%user:phrase-bier% - zwrot Pana/Panią<br/>\r\n%user:phrase-narz% - zwrot Panem/Panią<br/>\r\n%attach-N% - gdzie N to numer załącznika np: %attach-1%, %attach-2%<br/>\r\n</code>"
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: text_long
