uuid: 74b12c19-61c6-4fc3-8807-fe14dc15c2d6
langcode: pl
status: true
dependencies:
  config:
    - field.field.paragraph.contact_section.field_contacts
    - field.field.paragraph.contact_section.field_left_section
    - field.field.paragraph.contact_section.field_title
    - paragraphs.paragraphs_type.contact_section
  module:
    - paragraphs
id: paragraph.contact_section.default
targetEntityType: paragraph
bundle: contact_section
mode: default
content:
  field_contacts:
    type: entity_reference_autocomplete
    weight: 2
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_left_section:
    type: entity_reference_paragraphs
    weight: 1
    region: content
    settings:
      title: Akapit
      title_plural: Akapity
      edit_mode: open
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
    third_party_settings: {  }
  field_title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  created: true
  status: true
