uuid: fc1536b4-a1d7-4c82-922d-453833a66aa1
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_npxtraining_date_room
    - node.type.npxtraining_date
    - taxonomy.vocabulary.training_date_room
  content:
    - 'taxonomy_term:training_date_room:3a3e4f08-7f71-4b89-83de-d308441608f0'
id: node.npxtraining_date.field_npxtraining_date_room
field_name: field_npxtraining_date_room
entity_type: node
bundle: npxtraining_date
label: Sala
description: ''
required: false
translatable: false
default_value:
  -
    target_uuid: 3a3e4f08-7f71-4b89-83de-d308441608f0
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      training_date_room: training_date_room
    sort:
      field: _none
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
