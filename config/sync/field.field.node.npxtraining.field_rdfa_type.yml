uuid: 0989f3d1-87ec-45b2-a9ca-abed51c610cc
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_rdfa_type
    - node.type.npxtraining
  module:
    - options
id: node.npxtraining.field_rdfa_type
field_name: field_rdfa_type
entity_type: node
bundle: npxtraining
label: 'Typ rdfa'
description: ''
required: false
translatable: false
default_value:
  -
    value: mixed
default_value_callback: ''
settings: {  }
field_type: list_string
