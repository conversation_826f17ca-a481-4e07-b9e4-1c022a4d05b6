uuid: 9c344047-9f7b-4a8d-ba7c-aee62b3b4519
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_arguments_par
    - node.type.npxtraining
    - paragraphs.paragraphs_type.arguments
id: node.npxtraining.field_arguments_par
field_name: field_arguments_par
entity_type: node
bundle: npxtraining
label: Argumenty
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      arguments: arguments
    negate: 0
    target_bundles_drag_drop:
      arguments:
        weight: 9
        enabled: true
      cecha_par:
        weight: 10
        enabled: false
      logo_x_2:
        weight: 11
        enabled: false
      naglowek_par:
        weight: 12
        enabled: false
      par_quiz_review:
        weight: 13
        enabled: false
      trener_do_szkolenia_par:
        weight: 14
        enabled: false
      tytul_szkolenia:
        weight: 15
        enabled: false
      tytul_szkolenia_ii:
        weight: 16
        enabled: false
field_type: entity_reference
