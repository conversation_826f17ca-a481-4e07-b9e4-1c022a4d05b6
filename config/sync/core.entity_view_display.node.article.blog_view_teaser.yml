uuid: 647e28b0-e814-4ec7-bb56-a99f204595a3
langcode: pl
status: true
dependencies:
  config:
    - core.entity_view_mode.node.blog_view_teaser
    - field.field.node.article.body
    - field.field.node.article.comment
    - field.field.node.article.field_artykul_wprowadzenie
    - field.field.node.article.field_freshmail_form
    - field.field.node.article.field_image
    - field.field.node.article.field_kategoria
    - field.field.node.article.field_meta_tagi
    - field.field.node.article.field_npx_basic_paragraphs
    - field.field.node.article.field_pokaz_na_liscie
    - field.field.node.article.field_tags
    - field.field.node.article.field_trailer
    - field.field.node.article.field_zalacznik
    - image.style.blog_page_image
    - node.type.article
  module:
    - ds
    - field_group
    - image
    - smart_trim
    - user
third_party_settings:
  ds:
    layout:
      id: ds_1col
      library: null
      disable_css: false
      entity_classes: all_classes
      settings:
        classes:
          layout_class: {  }
          ds_content: {  }
          ds_hidden: {  }
        wrappers:
          ds_content: div
          ds_hidden: div
        outer_wrapper: div
        attributes: ''
        link_attribute: ''
        link_custom: ''
        label: ''
    regions:
      ds_content:
        - node_title
        - group_div
        - field_image
        - field_trailer
        - node_link
    fields:
      node_link:
        plugin_id: node_link
        weight: 4
        label: hidden
        formatter: default
        settings:
          'link text': 'Read more'
          'link class': ' npx-program-button mt-1 text-black'
          wrapper: ''
          class: ''
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
      node_title:
        plugin_id: node_title
        weight: 0
        label: hidden
        formatter: default
        settings:
          link: true
          'link class': text-black
          wrapper: h3
          class: ''
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
  field_group:
    group_div:
      children:
        - field_image
        - field_trailer
      label: 'div flex'
      parent_name: ''
      region: ds_content
      weight: 1
      format_type: html_element
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        element: div
        show_label: false
        label_element: h3
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
id: node.article.blog_view_teaser
targetEntityType: node
bundle: article
mode: blog_view_teaser
content:
  field_image:
    type: image
    label: hidden
    settings:
      image_link: content
      image_style: blog_page_image
      image_loading:
        attribute: lazy
    third_party_settings:
      ds:
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
    weight: 2
    region: ds_content
  field_trailer:
    type: smart_trim
    label: hidden
    settings:
      trim_length: 12
      trim_type: words
      trim_suffix: ...
      wrap_output: false
      wrap_class: trimmed
      more_link: false
      more_class: more-link
      more_text: More
      more_aria_label: 'Read more about [node:title]'
      summary_handler: full
      trim_options:
        text: false
        trim_zero: false
        replace_tokens: false
    third_party_settings:
      ds:
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
    weight: 3
    region: ds_content
hidden:
  body: true
  comment: true
  field_artykul_wprowadzenie: true
  field_freshmail_form: true
  field_kategoria: true
  field_meta_tagi: true
  field_npx_basic_paragraphs: true
  field_pokaz_na_liscie: true
  field_tags: true
  field_zalacznik: true
  langcode: true
  links: true
