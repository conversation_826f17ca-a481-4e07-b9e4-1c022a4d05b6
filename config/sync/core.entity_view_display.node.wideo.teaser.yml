uuid: 71fe062e-d624-47e2-8c81-4815e9db0e84
langcode: pl
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.wideo.body
    - field.field.node.wideo.field_podpis_wideo
    - field.field.node.wideo.field_video_embed
    - field.field.node.wideo.field_zaslepka
    - image.style.max_width_600
    - node.type.wideo
  module:
    - ds
    - user
    - video_embed_field
third_party_settings:
  ds:
    layout:
      id: ds_1col
      library: null
      disable_css: false
      entity_classes: all_classes
      settings:
        classes:
          layout_class: {  }
          ds_content: {  }
          ds_hidden: {  }
        wrappers:
          ds_content: div
          ds_hidden: div
        outer_wrapper: div
        attributes: ''
        link_attribute: ''
        link_custom: ''
        label: ''
    regions:
      ds_content:
        - field_video_embed
        - field_podpis_wideo
      ds_hidden:
        - links
id: node.wideo.teaser
targetEntityType: node
bundle: wideo
mode: teaser
content:
  field_podpis_wideo:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: ds_content
  field_video_embed:
    type: video_embed_field_colorbox
    label: hidden
    settings:
      autoplay: true
      responsive: true
      width: 854
      height: 480
      image_style: max_width_600
      link_image_to: ''
      modal_max_width: 854
    third_party_settings:
      ds:
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
    weight: 0
    region: ds_content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: ds_hidden
hidden:
  body: true
  field_zaslepka: true
  langcode: true
