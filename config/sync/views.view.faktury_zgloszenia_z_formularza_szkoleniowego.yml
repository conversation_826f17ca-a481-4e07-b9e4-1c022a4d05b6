uuid: d54ef05a-9502-467f-abfc-f2d8a6579a42
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxsubmission.npxsubmission
    - field.field.npxsubmission.npxsubmission.field_fra_type
    - field.storage.node.field_npxtraining_date_end
    - field.storage.npxsubmission.field_npxsubmission_address
    - field.storage.npxsubmission.field_npxsubmission_calc_td
    - field.storage.npxsubmission.field_npxsubmission_city
    - field.storage.npxsubmission.field_npxsubmission_comment
    - field.storage.npxsubmission.field_npxsubmission_date_nid
    - field.storage.npxsubmission.field_npxsubmission_firm
    - field.storage.npxsubmission.field_npxsubmission_nid
    - field.storage.npxsubmission.field_npxsubmission_nip
    - field.storage.npxsubmission.field_npxsubmission_pdf
    - field.storage.npxsubmission.field_npxsubmission_post_code
    - field.storage.npxsubmission.field_npxsubmission_send_notify
    - user.role.administrator
    - user.role.redaktor
  module:
    - datetime
    - ds
    - eck
    - file
    - node
    - user
    - views_entity_form_field
id: faktury_zgloszenia_z_formularza_szkoleniowego
label: 'Faktury: Zgłoszenia z formularza szkoleniowego'
module: views
description: ''
tag: ''
base_table: npxsubmission_field_data
base_field: id
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: 'Wystawianie faktur'
      fields:
        id:
          id: id
          table: npxsubmission_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxsubmission
          entity_field: id
          plugin_id: field
          label: ID
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_date_nid:
          id: field_npxsubmission_date_nid
          table: npxsubmission__field_npxsubmission_date_nid
          field: field_npxsubmission_date_nid
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Termin szkolenia'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxtraining_date_end:
          id: field_npxtraining_date_end
          table: node__field_npxtraining_date_end
          field: field_npxtraining_date_end
          relationship: field_npxsubmission_date_nid
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Data zakończenia szkolenia'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: datetime_default
          settings:
            timezone_override: ''
            format_type: html_date
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 1
          delta_offset: 0
          delta_reversed: true
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_nid:
          id: field_npxsubmission_nid
          table: npxsubmission__field_npxsubmission_nid
          field: field_npxsubmission_nid
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Szkolenie
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_firm:
          id: field_npxsubmission_firm
          table: npxsubmission__field_npxsubmission_firm
          field: field_npxsubmission_firm
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Firma
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_address:
          id: field_npxsubmission_address
          table: npxsubmission__field_npxsubmission_address
          field: field_npxsubmission_address
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Adres
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_post_code:
          id: field_npxsubmission_post_code
          table: npxsubmission__field_npxsubmission_post_code
          field: field_npxsubmission_post_code
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Kod pocztowy'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_city:
          id: field_npxsubmission_city
          table: npxsubmission__field_npxsubmission_city
          field: field_npxsubmission_city
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Miasto
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_nip:
          id: field_npxsubmission_nip
          table: npxsubmission__field_npxsubmission_nip
          field: field_npxsubmission_nip
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: NIP
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_calc_td:
          id: field_npxsubmission_calc_td
          table: npxsubmission__field_npxsubmission_calc_td
          field: field_npxsubmission_calc_td
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Łączny koszt z rabatem netto'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: .
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_comment:
          id: field_npxsubmission_comment
          table: npxsubmission__field_npxsubmission_comment
          field: field_npxsubmission_comment
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Uwagi
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: basic_string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_pdf:
          id: field_npxsubmission_pdf
          table: npxsubmission__field_npxsubmission_pdf
          field: field_npxsubmission_pdf
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: PDF
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: file_default
          settings: {  }
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        view_npxsubmission:
          id: view_npxsubmission
          table: npxsubmission
          field: view_npxsubmission
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxsubmission
          plugin_id: entity_link
          label: Podgląd
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          text: przeglądaj
          output_url_as_text: false
          absolute: false
        operations:
          id: operations
          table: npxsubmission
          field: operations
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxsubmission
          plugin_id: entity_operations
          label: Akcje
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          destination: true
        field_npxsubmission_send_notify:
          id: field_npxsubmission_send_notify
          table: npxsubmission__field_npxsubmission_send_notify
          field: field_npxsubmission_send_notify
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Maile
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: unicode-yes-no
            format_custom_false: ''
            format_custom_true: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        form_field_field_fra_type:
          id: form_field_field_fra_type
          table: npxsubmission_field_data
          field: form_field_field_fra_type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxsubmission
          plugin_id: entity_form_field
          label: Fra
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          plugin:
            hide_title: 1
            hide_description: 1
            type: options_select
            settings: {  }
            third_party_settings: {  }
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 100
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          pagination_heading_level: h4
      exposed_form:
        type: basic
        options:
          submit_button: Zastosuj
          reset_button: false
          reset_button_label: Resetuj
          exposed_sorts_label: 'Sortuj po'
          expose_sort_order: true
          sort_asc_label: Rosnąco
          sort_desc_label: Malejąco
      access:
        type: role
        options:
          role:
            administrator: administrator
            redaktor: redaktor
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        field_npxtraining_date_end_value:
          id: field_npxtraining_date_end_value
          table: node__field_npxtraining_date_end
          field: field_npxtraining_date_end_value
          relationship: field_npxsubmission_date_nid
          group_type: group
          admin_label: ''
          plugin_id: datetime
          order: DESC
          expose:
            label: ''
            field_identifier: field_npxtraining_date_end_value
          exposed: false
          granularity: day
      arguments:
        field_npxsubmission_date_nid_target_id:
          id: field_npxsubmission_date_nid_target_id
          table: npxsubmission__field_npxsubmission_date_nid
          field: field_npxsubmission_date_nid_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: entity_target_id
          default_action: ignore
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
          target_entity_type_id: node
      filters:
        type:
          id: type
          table: npxsubmission_field_data
          field: type
          entity_type: npxsubmission
          entity_field: type
          plugin_id: bundle
          value:
            npxsubmission: npxsubmission
          expose:
            operator_limit_selection: false
            operator_list: {  }
        id:
          id: id
          table: npxsubmission_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxsubmission
          entity_field: id
          plugin_id: numeric
          operator: '='
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: true
          expose:
            operator_id: id_op
            label: 'Numer zgłoszenia'
            description: ''
            use_operator: false
            operator: id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_npxtraining_date_start_value:
          id: field_npxtraining_date_start_value
          table: node__field_npxtraining_date_start
          field: field_npxtraining_date_start_value
          relationship: field_npxsubmission_date_nid
          group_type: group
          admin_label: ''
          plugin_id: datetime
          operator: '>='
          value:
            min: ''
            max: ''
            value: today
            type: offset
          group: 1
          exposed: true
          expose:
            operator_id: field_npxtraining_date_start_value_op
            label: 'Data rozpoczęcia'
            description: ''
            use_operator: false
            operator: field_npxtraining_date_start_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_npxtraining_date_start_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
              tester_grow3: '0'
              redaktor_lvl2: '0'
              manager: '0'
              webinar200: '0'
              moderator: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: 01-02-2021
          is_grouped: false
          group_info:
            label: 'Data rozpoczęcia (field_npxtraining_date_start)'
            description: null
            identifier: field_npxtraining_date_start_value
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items:
              1: {  }
              2: {  }
              3: {  }
        field_npxtraining_date_end_value:
          id: field_npxtraining_date_end_value
          table: node__field_npxtraining_date_end
          field: field_npxtraining_date_end_value
          relationship: field_npxsubmission_date_nid
          group_type: group
          admin_label: ''
          plugin_id: datetime
          operator: '<='
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 1
          exposed: true
          expose:
            operator_id: field_npxtraining_date_end_value_op
            label: 'Data zakończenia'
            description: ''
            use_operator: false
            operator: field_npxtraining_date_end_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_npxtraining_date_end_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
              tester_grow3: '0'
              redaktor_lvl2: '0'
              manager: '0'
              webinar200: '0'
              moderator: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: 01-02-2021
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: table
        options:
          grouping:
            -
              field: field_npxsubmission_nid
              rendered: true
              rendered_strip: false
          row_class: ''
          default_row_class: true
          columns:
            id: id
            field_npxsubmission_date_nid: field_npxsubmission_date_nid
            field_npxtraining_date_end: field_npxtraining_date_end
            field_npxsubmission_nid: field_npxsubmission_nid
            field_npxsubmission_firm: field_npxsubmission_firm
            field_npxsubmission_address: field_npxsubmission_address
            field_npxsubmission_post_code: field_npxsubmission_post_code
            field_npxsubmission_city: field_npxsubmission_city
            field_npxsubmission_nip: field_npxsubmission_nip
            field_npxsubmission_calc_td: field_npxsubmission_calc_td
            field_npxsubmission_comment: field_npxsubmission_comment
            field_npxsubmission_pdf: field_npxsubmission_pdf
            view_npxsubmission: view_npxsubmission
            operations: operations
          default: '-1'
          info:
            id:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_date_nid:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxtraining_date_end:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_nid:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_firm:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_address:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_post_code:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_city:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_nip:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_calc_td:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_comment:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_pdf:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            view_npxsubmission:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            operations:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: true
          summary: ''
          empty_table: false
          caption: ''
          description: ''
      row:
        type: 'ds_entity:npxsubmission'
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        field_npxsubmission_date_nid:
          id: field_npxsubmission_date_nid
          table: npxsubmission__field_npxsubmission_date_nid
          field: field_npxsubmission_date_nid
          relationship: none
          group_type: group
          admin_label: 'field_npxsubmission_date_nid: Content'
          plugin_id: standard
          required: false
      use_ajax: false
      group_by: false
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user
        - user.roles
      tags:
        - 'config:field.field.npxsubmission.npxsubmission.field_fra_type'
        - 'config:field.storage.node.field_npxtraining_date_end'
        - 'config:field.storage.npxsubmission.field_fra_type'
        - 'config:field.storage.npxsubmission.field_npxsubmission_address'
        - 'config:field.storage.npxsubmission.field_npxsubmission_calc_td'
        - 'config:field.storage.npxsubmission.field_npxsubmission_city'
        - 'config:field.storage.npxsubmission.field_npxsubmission_comment'
        - 'config:field.storage.npxsubmission.field_npxsubmission_date_nid'
        - 'config:field.storage.npxsubmission.field_npxsubmission_firm'
        - 'config:field.storage.npxsubmission.field_npxsubmission_nid'
        - 'config:field.storage.npxsubmission.field_npxsubmission_nip'
        - 'config:field.storage.npxsubmission.field_npxsubmission_pdf'
        - 'config:field.storage.npxsubmission.field_npxsubmission_post_code'
        - 'config:field.storage.npxsubmission.field_npxsubmission_send_notify'
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders:
        metatag_display_extender: {  }
      path: admin/npx/fra-zgloszenia-z-formularza-szkoleniowego
      menu:
        enabled: true
        expanded: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user
        - user.roles
      tags:
        - 'config:field.field.npxsubmission.npxsubmission.field_fra_type'
        - 'config:field.storage.node.field_npxtraining_date_end'
        - 'config:field.storage.npxsubmission.field_fra_type'
        - 'config:field.storage.npxsubmission.field_npxsubmission_address'
        - 'config:field.storage.npxsubmission.field_npxsubmission_calc_td'
        - 'config:field.storage.npxsubmission.field_npxsubmission_city'
        - 'config:field.storage.npxsubmission.field_npxsubmission_comment'
        - 'config:field.storage.npxsubmission.field_npxsubmission_date_nid'
        - 'config:field.storage.npxsubmission.field_npxsubmission_firm'
        - 'config:field.storage.npxsubmission.field_npxsubmission_nid'
        - 'config:field.storage.npxsubmission.field_npxsubmission_nip'
        - 'config:field.storage.npxsubmission.field_npxsubmission_pdf'
        - 'config:field.storage.npxsubmission.field_npxsubmission_post_code'
        - 'config:field.storage.npxsubmission.field_npxsubmission_send_notify'
