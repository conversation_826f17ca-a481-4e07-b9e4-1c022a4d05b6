uuid: 0ff1adbf-92bf-4926-aa49-2b4a573a5aac
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxdiscount.npxdiscount
    - field.storage.npxdiscount.field_npxdiscount_fixed
id: npxdiscount.npxdiscount.field_npxdiscount_fixed
field_name: field_npxdiscount_fixed
entity_type: npxdiscount
bundle: npxdiscount
label: '<PERSON><PERSON><PERSON><PERSON> k<PERSON>'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  min: null
  max: null
  prefix: ''
  suffix: zł
field_type: integer
