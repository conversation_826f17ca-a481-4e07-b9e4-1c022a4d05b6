uuid: c0ee41cb-f623-426e-b60d-dc1a51175759
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxdiscount.npxdiscount
    - field.field.npxdiscount.npxdiscount.field_npxdiscount_amount
    - field.field.npxdiscount.npxdiscount.field_npxdiscount_daterange
    - field.field.npxdiscount.npxdiscount.field_npxdiscount_fixed
    - field.field.npxdiscount.npxdiscount.field_npxdiscount_percent
    - field.field.npxdiscount.npxdiscount.field_npxdiscount_renew
    - field.field.npxdiscount.npxdiscount.field_npxdiscount_type
  module:
    - datetime_range
    - options
    - user
id: npxdiscount.npxdiscount.default
targetEntityType: npxdiscount
bundle: npxdiscount
mode: default
content:
  created:
    type: timestamp
    label: hidden
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: ''
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 0
    region: content
  field_npxdiscount_amount:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 6
    region: content
  field_npxdiscount_daterange:
    type: daterange_default
    label: above
    settings:
      timezone_override: ''
      format_type: medium
      from_to: both
      separator: '-'
    third_party_settings: {  }
    weight: 4
    region: content
  field_npxdiscount_fixed:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 2
    region: content
  field_npxdiscount_percent:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 3
    region: content
  field_npxdiscount_renew:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 7
    region: content
  field_npxdiscount_type:
    type: list_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  title:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: -5
    region: content
  uid:
    type: author
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  changed: true
  langcode: true
