uuid: e3d81f08-f3bf-405a-8775-d9c84db5b739
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxdiscount.npxdiscount
    - field.field.npxdiscount.npxdiscount.field_npxdiscount_amount
    - field.field.npxdiscount.npxdiscount.field_npxdiscount_daterange
    - field.field.npxdiscount.npxdiscount.field_npxdiscount_fixed
    - field.field.npxdiscount.npxdiscount.field_npxdiscount_percent
    - field.field.npxdiscount.npxdiscount.field_npxdiscount_renew
    - field.field.npxdiscount.npxdiscount.field_npxdiscount_type
  module:
    - datetime_range
id: npxdiscount.npxdiscount.default
targetEntityType: npxdiscount
bundle: npxdiscount
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npxdiscount_amount:
    type: number
    weight: 16
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_npxdiscount_daterange:
    type: daterange_default
    weight: 14
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npxdiscount_fixed:
    type: number
    weight: 12
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_npxdiscount_percent:
    type: number
    weight: 13
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_npxdiscount_renew:
    type: boolean_checkbox
    weight: 17
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_npxdiscount_type:
    type: options_select
    weight: 11
    region: content
    settings: {  }
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
