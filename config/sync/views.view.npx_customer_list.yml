uuid: 966e4330-ac77-4fd7-a97f-4fd696ca487f
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxparticipant.npxparticipant
    - field.storage.npxparticipant.field_npxparticipant_email
    - field.storage.npxparticipant.field_npxparticipant_gender
    - field.storage.npxparticipant.field_npxparticipant_name
    - field.storage.npxparticipant.field_npxparticipant_phone
    - field.storage.npxparticipant.field_npxparticipant_position
    - field.storage.npxsubmission.field_npxsubmission_firm
    - system.menu.admin
    - user.role.administrator
    - user.role.manager
    - user.role.redaktor
  module:
    - csv_serialization
    - eck
    - rest
    - serialization
    - user
    - views_data_export
id: npx_customer_list
label: 'Baza klientów'
module: views
description: ''
tag: ''
base_table: npxparticipant_field_data
base_field: id
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: '<PERSON><PERSON> klientów'
      fields:
        created:
          id: created
          table: npxparticipant_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxparticipant
          entity_field: created
          plugin_id: field
          label: 'Data dodania'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: html_date
            custom_date_format: ''
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxparticipant_gender:
          id: field_npxparticipant_gender
          table: npxparticipant__field_npxparticipant_gender
          field: field_npxparticipant_gender
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Zwrot
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxparticipant_name:
          id: field_npxparticipant_name
          table: npxparticipant__field_npxparticipant_name
          field: field_npxparticipant_name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Imię i nazwisko'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxparticipant_position:
          id: field_npxparticipant_position
          table: npxparticipant__field_npxparticipant_position
          field: field_npxparticipant_position
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Stanowisko
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxparticipant_email:
          id: field_npxparticipant_email
          table: npxparticipant__field_npxparticipant_email
          field: field_npxparticipant_email
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Email
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: basic_string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxparticipant_phone:
          id: field_npxparticipant_phone
          table: npxparticipant__field_npxparticipant_phone
          field: field_npxparticipant_phone
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Telefon
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_firm:
          id: field_npxsubmission_firm
          table: npxsubmission__field_npxsubmission_firm
          field: field_npxsubmission_firm
          relationship: reverse__npxsubmission__field_npxsubmission_participants
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: '-- BRAK FIRMY --'
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 30
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
          pagination_heading_level: h4
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: role
        options:
          role:
            administrator: administrator
            redaktor: redaktor
            manager: manager
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        field_npxsubmission_firm_value:
          id: field_npxsubmission_firm_value
          table: npxsubmission__field_npxsubmission_firm
          field: field_npxsubmission_firm_value
          relationship: reverse__npxsubmission__field_npxsubmission_participants
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: ASC
          expose:
            label: 'Firma (field_npxsubmission_firm)'
            field_identifier: field_npxsubmission_firm_value
          exposed: true
        field_npxparticipant_name_value:
          id: field_npxparticipant_name_value
          table: npxparticipant__field_npxparticipant_name
          field: field_npxparticipant_name_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: ASC
          expose:
            label: 'Nazwisko (field_npxparticipant_name)'
            field_identifier: field_npxparticipant_name_value
          exposed: true
        created:
          id: created
          table: npxparticipant_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxparticipant
          entity_field: created
          plugin_id: date
          order: ASC
          expose:
            label: 'Data dodania'
            field_identifier: created
          exposed: true
          granularity: day
      arguments: {  }
      filters:
        type:
          id: type
          table: npxparticipant_field_data
          field: type
          entity_type: npxparticipant
          entity_field: type
          plugin_id: bundle
          value:
            npxparticipant: npxparticipant
          group: 1
          expose:
            operator_limit_selection: false
            operator_list: {  }
        field_npxsubmission_firm_value:
          id: field_npxsubmission_firm_value
          table: npxsubmission__field_npxsubmission_firm
          field: field_npxsubmission_firm_value
          relationship: reverse__npxsubmission__field_npxsubmission_participants
          group_type: group
          admin_label: ''
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: field_npxsubmission_firm_value_op
            label: Firma
            description: ''
            use_operator: false
            operator: field_npxsubmission_firm_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_npxsubmission_firm_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_npxparticipant_name_value:
          id: field_npxparticipant_name_value
          table: npxparticipant__field_npxparticipant_name
          field: field_npxparticipant_name_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: field_npxparticipant_name_value_op
            label: 'Imię i nazwisko'
            description: ''
            use_operator: false
            operator: field_npxparticipant_name_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_npxparticipant_name_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            created: created
            field_npxparticipant_gender: field_npxparticipant_gender
            field_npxparticipant_name: field_npxparticipant_name
            field_npxparticipant_position: field_npxparticipant_position
            field_npxparticipant_email: field_npxparticipant_email
            field_npxparticipant_phone: field_npxparticipant_phone
            field_npxsubmission_firm: field_npxsubmission_firm
          default: '-1'
          info:
            created:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxparticipant_gender:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxparticipant_name:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxparticipant_position:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxparticipant_email:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxparticipant_phone:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_npxsubmission_firm:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: false
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        reverse__npxsubmission__field_npxsubmission_participants:
          id: reverse__npxsubmission__field_npxsubmission_participants
          table: npxparticipant_field_data
          field: reverse__npxsubmission__field_npxsubmission_participants
          relationship: none
          group_type: group
          admin_label: field_npxsubmission_participants
          entity_type: npxparticipant
          plugin_id: entity_reverse
          required: true
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: '<strong>Widok zawiera listę klientów z agregacją po nazwie firmy.</strong>'
            format: full_html
          tokenize: false
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'url.query_args:sort_by'
        - 'url.query_args:sort_order'
        - user.roles
      tags:
        - 'config:field.storage.npxparticipant.field_npxparticipant_email'
        - 'config:field.storage.npxparticipant.field_npxparticipant_gender'
        - 'config:field.storage.npxparticipant.field_npxparticipant_name'
        - 'config:field.storage.npxparticipant.field_npxparticipant_phone'
        - 'config:field.storage.npxparticipant.field_npxparticipant_position'
        - 'config:field.storage.npxsubmission.field_npxsubmission_firm'
  data_export_1:
    id: data_export_1
    display_title: 'Eksport danych'
    display_plugin: data_export
    position: 2
    display_options:
      style:
        type: data_export
        options:
          formats:
            csv: csv
          csv_settings:
            delimiter: ','
            enclosure: '"'
            escape_char: \
            strip_tags: true
            trim: true
            encoding: utf8
            utf8_bom: '0'
            use_serializer_encode_only: false
      display_extenders:
        metatag_display_extender: {  }
      path: admin/npx/zgloszenia-z-formularza-szkoleniowego-plec
      filename: ''
      automatic_download: false
      store_in_public_file_directory: false
      custom_redirect_path: false
      redirect_to_display: none
      include_query_params: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - url
        - 'url.query_args:sort_by'
        - 'url.query_args:sort_order'
        - user.roles
      tags:
        - 'config:field.storage.npxparticipant.field_npxparticipant_email'
        - 'config:field.storage.npxparticipant.field_npxparticipant_gender'
        - 'config:field.storage.npxparticipant.field_npxparticipant_name'
        - 'config:field.storage.npxparticipant.field_npxparticipant_phone'
        - 'config:field.storage.npxparticipant.field_npxparticipant_position'
        - 'config:field.storage.npxsubmission.field_npxsubmission_firm'
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders:
        metatag_display_extender: {  }
      path: admin/npx/baza-klientow
      menu:
        type: normal
        title: 'Baza klientów'
        description: ''
        weight: 0
        expanded: false
        menu_name: admin
        parent: 'menu_link_content:795ce09a-4909-4963-b705-5ea3c1e34dbd'
        context: '0'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'url.query_args:sort_by'
        - 'url.query_args:sort_order'
        - user.roles
      tags:
        - 'config:field.storage.npxparticipant.field_npxparticipant_email'
        - 'config:field.storage.npxparticipant.field_npxparticipant_gender'
        - 'config:field.storage.npxparticipant.field_npxparticipant_name'
        - 'config:field.storage.npxparticipant.field_npxparticipant_phone'
        - 'config:field.storage.npxparticipant.field_npxparticipant_position'
        - 'config:field.storage.npxsubmission.field_npxsubmission_firm'
