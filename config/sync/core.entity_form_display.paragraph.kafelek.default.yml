uuid: 9cff15f1-39dd-4569-96a2-d9b663c87be1
langcode: pl
status: true
dependencies:
  config:
    - field.field.paragraph.kafelek.field_naglowek
    - field.field.paragraph.kafelek.field_szkolenie
    - paragraphs.paragraphs_type.kafelek
  module:
    - paragraphs
id: paragraph.kafelek.default
targetEntityType: paragraph
bundle: kafelek
mode: default
content:
  field_naglowek:
    type: paragraphs
    weight: 0
    region: content
    settings:
      title: Akapit
      title_plural: Akapity
      edit_mode: open
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
      features:
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_szkolenie:
    type: paragraphs
    weight: 1
    region: content
    settings:
      title: Akapit
      title_plural: Akapity
      edit_mode: open
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
      features:
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
hidden:
  created: true
  status: true
