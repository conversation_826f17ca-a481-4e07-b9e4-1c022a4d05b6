uuid: e87ff977-07d3-48c0-9503-3c9b0f63b7ff
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.npxtraining_date.body
    - field.field.node.npxtraining_date.field_addcost_info
    - field.field.node.npxtraining_date.field_historic_minor_discount
    - field.field.node.npxtraining_date.field_historic_minor_price
    - field.field.node.npxtraining_date.field_npx_calendar_hide
    - field.field.node.npxtraining_date.field_npxtraining_date_addcost
    - field.field.node.npxtraining_date.field_npxtraining_date_available
    - field.field.node.npxtraining_date.field_npxtraining_date_cal_title
    - field.field.node.npxtraining_date.field_npxtraining_date_city
    - field.field.node.npxtraining_date.field_npxtraining_date_constcost
    - field.field.node.npxtraining_date.field_npxtraining_date_dis_fxd
    - field.field.node.npxtraining_date.field_npxtraining_date_dis_per
    - field.field.node.npxtraining_date.field_npxtraining_date_dis_ref
    - field.field.node.npxtraining_date.field_npxtraining_date_end
    - field.field.node.npxtraining_date.field_npxtraining_date_form
    - field.field.node.npxtraining_date.field_npxtraining_date_guarant
    - field.field.node.npxtraining_date.field_npxtraining_date_hours
    - field.field.node.npxtraining_date.field_npxtraining_date_ldis_ref
    - field.field.node.npxtraining_date.field_npxtraining_date_lim_dis
    - field.field.node.npxtraining_date.field_npxtraining_date_min
    - field.field.node.npxtraining_date.field_npxtraining_date_price
    - field.field.node.npxtraining_date.field_npxtraining_date_room
    - field.field.node.npxtraining_date.field_npxtraining_date_start
    - field.field.node.npxtraining_date.field_npxtraining_date_trainer
    - field.field.node.npxtraining_date.field_npxtraining_date_trcost
    - field.field.node.npxtraining_date.field_npxtraining_hours
    - field.field.node.npxtraining_date.field_zoom_link
    - field.field.node.npxtraining_date.field_zoom_message
    - node.type.npxtraining_date
  module:
    - datetime
    - field_group
    - inline_entity_form
    - text
third_party_settings:
  field_group:
    group_report_fields:
      children:
        - field_npxtraining_hours
        - field_npxtraining_date_constcost
        - field_npxtraining_date_trcost
        - field_npxtraining_date_addcost
        - field_addcost_info
      label: 'Pola do raportu'
      region: content
      parent_name: ''
      weight: 16
      format_type: fieldset
      format_settings:
        classes: ''
        id: ''
        description: ''
        required_fields: true
id: node.npxtraining_date.default
targetEntityType: node
bundle: npxtraining_date
mode: default
content:
  field_addcost_info:
    type: string_textarea
    weight: 34
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_historic_minor_discount:
    type: number
    weight: 36
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_historic_minor_price:
    type: number
    weight: 35
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_npx_calendar_hide:
    type: boolean_checkbox
    weight: 19
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_npxtraining_date_addcost:
    type: string_textfield
    weight: 33
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_npxtraining_date_available:
    type: options_select
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npxtraining_date_cal_title:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_npxtraining_date_city:
    type: entity_reference_autocomplete
    weight: 9
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_npxtraining_date_constcost:
    type: string_textfield
    weight: 31
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_npxtraining_date_dis_fxd:
    type: number
    weight: 11
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_npxtraining_date_dis_per:
    type: number
    weight: 12
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_npxtraining_date_dis_ref:
    type: inline_entity_form_complex
    weight: 14
    region: content
    settings:
      form_mode: default
      override_labels: false
      label_singular: ''
      label_plural: ''
      allow_new: true
      allow_existing: false
      match_operator: CONTAINS
      allow_duplicate: false
      collapsible: false
      collapsed: false
      revision: false
    third_party_settings: {  }
  field_npxtraining_date_end:
    type: datetime_default
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npxtraining_date_form:
    type: options_buttons
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npxtraining_date_guarant:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: false
    third_party_settings: {  }
  field_npxtraining_date_hours:
    type: string_textfield
    weight: 21
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_npxtraining_date_ldis_ref:
    type: inline_entity_form_complex
    weight: 15
    region: content
    settings:
      form_mode: default
      override_labels: false
      label_singular: ''
      label_plural: ''
      allow_new: true
      allow_existing: false
      match_operator: CONTAINS
      allow_duplicate: false
      collapsible: false
      collapsed: false
      revision: false
    third_party_settings: {  }
  field_npxtraining_date_lim_dis:
    type: boolean_checkbox
    weight: 8
    region: content
    settings:
      display_label: false
    third_party_settings: {  }
  field_npxtraining_date_min:
    type: string_textfield
    weight: 20
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_npxtraining_date_price:
    type: number
    weight: 3
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_npxtraining_date_room:
    type: entity_reference_autocomplete
    weight: 13
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_npxtraining_date_start:
    type: datetime_default
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npxtraining_date_trainer:
    type: entity_reference_autocomplete
    weight: 10
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_npxtraining_date_trcost:
    type: string_textfield
    weight: 32
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_npxtraining_hours:
    type: number
    weight: 30
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_zoom_link:
    type: string_textfield
    weight: 22
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_zoom_message:
    type: text_textarea
    weight: 23
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  simple_sitemap:
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 18
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 17
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  body: true
  created: true
  langcode: true
  path: true
  promote: true
  sticky: true
  uid: true
