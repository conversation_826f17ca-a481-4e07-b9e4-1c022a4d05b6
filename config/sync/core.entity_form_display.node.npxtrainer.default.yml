uuid: 2fda8a04-15be-46c6-a300-a9dc76b99d1f
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.npxtrainer.body
    - field.field.node.npxtrainer.field_aktywny
    - field.field.node.npxtrainer.field_image
    - field.field.node.npxtrainer.field_image_alt
    - field.field.node.npxtrainer.field_image_big
    - field.field.node.npxtrainer.field_kolejnosc
    - field.field.node.npxtrainer.field_link_1
    - field.field.node.npxtrainer.field_link_2
    - field.field.node.npxtrainer.field_link_do_profilu_trenera
    - field.field.node.npxtrainer.field_npxtrainer_email
    - field.field.node.npxtrainer.field_npxtrainer_inslnk
    - field.field.node.npxtrainer.field_npxtrainer_phone
    - field.field.node.npxtrainer.field_npxtrainer_position
    - field.field.node.npxtrainer.field_npxtrainer_signature
    - field.field.node.npxtrainer.field_ref_opinia
    - field.field.node.npxtrainer.field_ref_referencja
    - field.field.node.npxtrainer.field_tekst_dodatkowy
    - field.field.node.npxtrainer.field_tekstplus
    - field.field.node.npxtrainer.field_ukryj_na_zesp
    - field.field.node.npxtrainer.field_zajawka
    - image.style.thumbnail
    - node.type.npxtrainer
  module:
    - image
    - imce
    - link
    - path
    - text
id: node.npxtrainer.default
targetEntityType: node
bundle: npxtrainer
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 9
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  field_aktywny:
    type: boolean_checkbox
    weight: 1
    region: content
    settings:
      display_label: false
    third_party_settings: {  }
  field_image:
    type: image_image
    weight: 15
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings:
      imce:
        enabled: true
  field_image_alt:
    type: image_image
    weight: 17
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings:
      imce:
        enabled: true
  field_image_big:
    type: image_image
    weight: 16
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings:
      imce:
        enabled: true
  field_kolejnosc:
    type: number
    weight: 19
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_link_1:
    type: link_default
    weight: 22
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  field_link_2:
    type: link_default
    weight: 24
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  field_link_do_profilu_trenera:
    type: link_default
    weight: 27
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  field_npxtrainer_email:
    type: email_default
    weight: 11
    region: content
    settings:
      placeholder: ''
      size: 60
    third_party_settings: {  }
  field_npxtrainer_inslnk:
    type: string_textfield
    weight: 28
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
    rows: 6
  field_npxtrainer_phone:
    type: string_textfield
    weight: 12
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_npxtrainer_position:
    type: string_textfield
    weight: 14
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_npxtrainer_signature:
    type: text_textarea
    weight: 13
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_ref_opinia:
    type: entity_reference_autocomplete
    weight: 23
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_ref_referencja:
    type: entity_reference_autocomplete
    weight: 21
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_tekst_dodatkowy:
    type: text_textarea
    weight: 20
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_tekstplus:
    type: text_textarea
    weight: 10
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_ukryj_na_zesp:
    type: boolean_checkbox
    weight: 2
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_zajawka:
    type: text_textarea
    weight: 18
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 3
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  path:
    type: path
    weight: 8
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 26
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 7
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 4
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 25
    region: content
    settings: {  }
    third_party_settings: {  }
hidden: {  }
