uuid: 1075926a-cee4-4e51-9308-0883e6ffa6fd
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.npxtrainer.body
    - field.field.node.npxtrainer.field_aktywny
    - field.field.node.npxtrainer.field_image
    - field.field.node.npxtrainer.field_image_alt
    - field.field.node.npxtrainer.field_image_big
    - field.field.node.npxtrainer.field_kolejnosc
    - field.field.node.npxtrainer.field_link_1
    - field.field.node.npxtrainer.field_link_2
    - field.field.node.npxtrainer.field_link_do_profilu_trenera
    - field.field.node.npxtrainer.field_npxtrainer_email
    - field.field.node.npxtrainer.field_npxtrainer_inslnk
    - field.field.node.npxtrainer.field_npxtrainer_phone
    - field.field.node.npxtrainer.field_npxtrainer_position
    - field.field.node.npxtrainer.field_npxtrainer_signature
    - field.field.node.npxtrainer.field_ref_opinia
    - field.field.node.npxtrainer.field_ref_referencja
    - field.field.node.npxtrainer.field_tekst_dodatkowy
    - field.field.node.npxtrainer.field_tekstplus
    - field.field.node.npxtrainer.field_ukryj_na_zesp
    - field.field.node.npxtrainer.field_zajawka
    - node.type.npxtrainer
  module:
    - ds
    - image
    - link
    - text
    - user
third_party_settings:
  ds:
    layout:
      id: ds_1col
      library: null
      disable_css: false
      entity_classes: old_view_mode
      settings:
        classes:
          layout_class:
            trener: trener
          ds_content: {  }
          ds_hidden: {  }
        wrappers:
          ds_content: div
          ds_hidden: div
        outer_wrapper: div
        attributes: ''
        link_attribute: ''
        link_custom: ''
    regions:
      ds_content:
        - field_image
        - node_title
        - field_npxtrainer_position
        - field_image_big
        - body
        - field_tekstplus
        - field_ref_referencja
        - field_link_1
        - field_ref_opinia
        - field_link_2
        - langcode
        - links
        - field_npxtrainer_email
        - field_npxtrainer_phone
        - field_npxtrainer_signature
        - field_zajawka
        - field_kolejnosc
        - field_tekst_dodatkowy
        - field_aktywny
        - field_ukryj_na_zesp
        - field_npxtrainer_inslnk
        - field_image_alt
      ds_hidden:
        - node_link
    fields:
      node_title:
        plugin_id: node_title
        weight: 1
        label: hidden
        formatter: default
      node_link:
        plugin_id: node_link
        weight: 19
        label: hidden
        formatter: default
        settings:
          'link text': 'Czytaj więcej'
          'link class': ''
          wrapper: ''
          class: ''
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
id: node.npxtrainer.default
targetEntityType: node
bundle: npxtrainer
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 4
    region: ds_content
  field_aktywny:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 18
    region: ds_content
  field_image:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 0
    region: ds_content
  field_image_alt:
    type: image
    label: above
    settings:
      image_link: ''
      image_style: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 21
    region: ds_content
  field_image_big:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 3
    region: ds_content
  field_kolejnosc:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 16
    region: ds_content
  field_link_1:
    type: link
    label: hidden
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 7
    region: ds_content
  field_link_2:
    type: link
    label: hidden
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 9
    region: ds_content
  field_npxtrainer_email:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 12
    region: ds_content
  field_npxtrainer_inslnk:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 20
    region: ds_content
  field_npxtrainer_phone:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 13
    region: ds_content
  field_npxtrainer_position:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 2
    region: ds_content
  field_npxtrainer_signature:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 14
    region: ds_content
  field_ref_opinia:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: full
      link: false
    third_party_settings:
      ds:
        ds_limit: ''
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
    weight: 8
    region: ds_content
  field_ref_referencja:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: full
      link: false
    third_party_settings:
      ds:
        ds_limit: ''
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
    weight: 6
    region: ds_content
  field_tekst_dodatkowy:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 17
    region: ds_content
  field_tekstplus:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 5
    region: ds_content
  field_ukryj_na_zesp:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 19
    region: ds_content
  field_zajawka:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 15
    region: ds_content
  langcode:
    type: language
    label: above
    settings:
      link_to_entity: false
      native_language: false
    third_party_settings: {  }
    weight: 10
    region: ds_content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 11
    region: ds_content
hidden:
  field_link_do_profilu_trenera: true
