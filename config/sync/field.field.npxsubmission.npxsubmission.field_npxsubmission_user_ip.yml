uuid: 653fc2a3-f8ee-4520-9b00-c0a8be484475
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxsubmission.npxsubmission
    - field.storage.npxsubmission.field_npxsubmission_user_ip
id: npxsubmission.npxsubmission.field_npxsubmission_user_ip
field_name: field_npxsubmission_user_ip
entity_type: npxsubmission
bundle: npxsubmission
label: 'Adres IP'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
