uuid: 65460245-5ea3-4f89-8f00-fd2b7e801532
langcode: pl
status: true
dependencies:
  config:
    - block_content.type.blok_szkolenie
    - core.entity_view_mode.block_content.full
    - field.field.block_content.blok_szkolenie.body
    - field.field.block_content.blok_szkolenie.field_tekst_dodatkowy
  module:
    - ds
    - text
third_party_settings:
  ds:
    layout:
      id: ds_2col_stacked
      library: layout_plugin/ds_2col_stacked
      disable_css: false
      entity_classes: old_view_mode
      settings:
        classes:
          layout_class: {  }
          header: {  }
          left: {  }
          right: {  }
          footer: {  }
          ds_hidden: {  }
        wrappers:
          header: div
          left: div
          right: div
          footer: div
          ds_hidden: div
        outer_wrapper: div
        attributes: ''
        link_attribute: ''
        link_custom: ''
      path: modules/ds
    regions:
      header:
        - body
      footer:
        - field_tekst_dodatkowy
id: block_content.blok_szkolenie.full
targetEntityType: block_content
bundle: blok_szkolenie
mode: full
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: header
  field_tekst_dodatkowy:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: footer
  langcode:
    type: language
    label: above
    settings:
      link_to_entity: false
      native_language: false
    third_party_settings: {  }
    weight: 0
    region: content
hidden: {  }
