uuid: 7787d37a-f2a3-4ae9-839e-089ef61b65a9
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxsubmission.npxsubmission
    - field.storage.node.field_npxtraining_tytul_formalny
    - field.storage.npxsubmission.field_npxsubmission_address
    - field.storage.npxsubmission.field_npxsubmission_city
    - field.storage.npxsubmission.field_npxsubmission_email
    - field.storage.npxsubmission.field_npxsubmission_firm
    - field.storage.npxsubmission.field_npxsubmission_firstname
    - field.storage.npxsubmission.field_npxsubmission_name
    - field.storage.npxsubmission.field_npxsubmission_nip
    - field.storage.npxsubmission.field_npxsubmission_phone
    - field.storage.npxsubmission.field_npxsubmission_position
    - field.storage.npxsubmission.field_npxsubmission_post_code
    - system.menu.admin
    - user.role.administrator
    - user.role.manager
    - user.role.redaktor
    - user.role.redaktor_lvl2
  module:
    - csv_serialization
    - eck
    - node
    - rest
    - serialization
    - text
    - user
    - views_data_export
id: lista_zglaszajacych
label: 'Lista zgłaszających'
module: views
description: ''
tag: ''
base_table: npxsubmission_field_data
base_field: id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: 'Lista zgłaszających'
      fields:
        created:
          id: created
          table: npxsubmission_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxsubmission
          entity_field: created
          plugin_id: field
          label: 'Data dodania'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: kalendarzowa
            custom_date_format: ''
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_firm:
          id: field_npxsubmission_firm
          table: npxsubmission__field_npxsubmission_firm
          field: field_npxsubmission_firm
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Firma
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_address:
          id: field_npxsubmission_address
          table: npxsubmission__field_npxsubmission_address
          field: field_npxsubmission_address
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Adres
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_post_code:
          id: field_npxsubmission_post_code
          table: npxsubmission__field_npxsubmission_post_code
          field: field_npxsubmission_post_code
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Kod pocztowy'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_city:
          id: field_npxsubmission_city
          table: npxsubmission__field_npxsubmission_city
          field: field_npxsubmission_city
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Miasto
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_nip:
          id: field_npxsubmission_nip
          table: npxsubmission__field_npxsubmission_nip
          field: field_npxsubmission_nip
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: NIP
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_firstname:
          id: field_npxsubmission_firstname
          table: npxsubmission__field_npxsubmission_firstname
          field: field_npxsubmission_firstname
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Imię osoby zgłaszającej'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_name:
          id: field_npxsubmission_name
          table: npxsubmission__field_npxsubmission_name
          field: field_npxsubmission_name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Osoba zgłaszająca'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_position:
          id: field_npxsubmission_position
          table: npxsubmission__field_npxsubmission_position
          field: field_npxsubmission_position
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Stanowisko zgłaszającego'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_phone:
          id: field_npxsubmission_phone
          table: npxsubmission__field_npxsubmission_phone
          field: field_npxsubmission_phone
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Telefon kontaktowy'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxsubmission_email:
          id: field_npxsubmission_email
          table: npxsubmission__field_npxsubmission_email
          field: field_npxsubmission_email
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Email zgłaszającego'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: basic_string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_npxtraining_tytul_formalny:
          id: field_npxtraining_tytul_formalny
          table: node__field_npxtraining_tytul_formalny
          field: field_npxtraining_tytul_formalny
          relationship: field_npxsubmission_nid
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Nazwa szkolenia'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title:
          id: title
          table: node_field_data
          field: title
          relationship: field_npxsubmission_date_nid
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: 'Termin szkolenia'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 100
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
          pagination_heading_level: h4
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: role
        options:
          role:
            administrator: administrator
            redaktor: redaktor
            redaktor_lvl2: redaktor_lvl2
            manager: manager
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        created:
          id: created
          table: npxsubmission_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxsubmission
          entity_field: created
          plugin_id: date
          order: ASC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
          granularity: second
      arguments: {  }
      filters:
        type:
          id: type
          table: npxsubmission_field_data
          field: type
          entity_type: npxsubmission
          entity_field: type
          plugin_id: bundle
          value:
            npxsubmission: npxsubmission
          expose:
            operator_limit_selection: false
            operator_list: {  }
        created:
          id: created
          table: npxsubmission_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: npxsubmission
          entity_field: created
          plugin_id: date
          operator: '>='
          value:
            min: ''
            max: ''
            value: '2018-01-01 00:00:00'
            type: date
          group: 1
          exposed: false
          expose:
            operator_id: created_op
            label: 'Wyświetl od dnia'
            description: 'Domyślnie 01.01.2018'
            use_operator: false
            operator: created_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: created
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              redaktor: '0'
              edytor_plus_plus: '0'
              tester_grow3: '0'
              redaktor_lvl2: '0'
              manager: '0'
              webinar200: '0'
              moderator: '0'
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            title: title
            created: created
          default: '-1'
          info:
            title:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            created:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: false
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        field_npxsubmission_date_nid:
          id: field_npxsubmission_date_nid
          table: npxsubmission__field_npxsubmission_date_nid
          field: field_npxsubmission_date_nid
          relationship: none
          group_type: group
          admin_label: 'field_npxsubmission_date_nid: Termin szkolenia'
          plugin_id: standard
          required: true
        field_npxsubmission_nid:
          id: field_npxsubmission_nid
          table: npxsubmission__field_npxsubmission_nid
          field: field_npxsubmission_nid
          relationship: none
          group_type: group
          admin_label: 'field_npxsubmission_nid: Szkolenie'
          plugin_id: standard
          required: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url.query_args
        - user.roles
      tags:
        - 'config:field.storage.node.field_npxtraining_tytul_formalny'
        - 'config:field.storage.npxsubmission.field_npxsubmission_address'
        - 'config:field.storage.npxsubmission.field_npxsubmission_city'
        - 'config:field.storage.npxsubmission.field_npxsubmission_email'
        - 'config:field.storage.npxsubmission.field_npxsubmission_firm'
        - 'config:field.storage.npxsubmission.field_npxsubmission_firstname'
        - 'config:field.storage.npxsubmission.field_npxsubmission_name'
        - 'config:field.storage.npxsubmission.field_npxsubmission_nip'
        - 'config:field.storage.npxsubmission.field_npxsubmission_phone'
        - 'config:field.storage.npxsubmission.field_npxsubmission_position'
        - 'config:field.storage.npxsubmission.field_npxsubmission_post_code'
  data_export_1:
    id: data_export_1
    display_title: 'Eksport danych'
    display_plugin: data_export
    position: 2
    display_options:
      style:
        type: data_export
        options:
          formats:
            csv: csv
          csv_settings:
            delimiter: ','
            enclosure: '"'
            escape_char: \
            strip_tags: true
            trim: true
            encoding: utf8
            utf8_bom: '0'
            use_serializer_encode_only: false
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
      path: admin/npx/lista_zglaszaszajacych/export
      auth:
        - cookie
      displays:
        page_1: page_1
        default: '0'
      filename: ''
      automatic_download: true
      store_in_public_file_directory: false
      custom_redirect_path: false
      redirect_to_display: none
      include_query_params: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - request_format
        - user.roles
      tags:
        - 'config:field.storage.node.field_npxtraining_tytul_formalny'
        - 'config:field.storage.npxsubmission.field_npxsubmission_address'
        - 'config:field.storage.npxsubmission.field_npxsubmission_city'
        - 'config:field.storage.npxsubmission.field_npxsubmission_email'
        - 'config:field.storage.npxsubmission.field_npxsubmission_firm'
        - 'config:field.storage.npxsubmission.field_npxsubmission_firstname'
        - 'config:field.storage.npxsubmission.field_npxsubmission_name'
        - 'config:field.storage.npxsubmission.field_npxsubmission_nip'
        - 'config:field.storage.npxsubmission.field_npxsubmission_phone'
        - 'config:field.storage.npxsubmission.field_npxsubmission_position'
        - 'config:field.storage.npxsubmission.field_npxsubmission_post_code'
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
      path: admin/npx/lista-zglaszajacych
      menu:
        type: normal
        title: 'Lista zgłaszających'
        menu_name: admin
        parent: 'menu_link_content:795ce09a-4909-4963-b705-5ea3c1e34dbd'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url.query_args
        - user.roles
      tags:
        - 'config:field.storage.node.field_npxtraining_tytul_formalny'
        - 'config:field.storage.npxsubmission.field_npxsubmission_address'
        - 'config:field.storage.npxsubmission.field_npxsubmission_city'
        - 'config:field.storage.npxsubmission.field_npxsubmission_email'
        - 'config:field.storage.npxsubmission.field_npxsubmission_firm'
        - 'config:field.storage.npxsubmission.field_npxsubmission_firstname'
        - 'config:field.storage.npxsubmission.field_npxsubmission_name'
        - 'config:field.storage.npxsubmission.field_npxsubmission_nip'
        - 'config:field.storage.npxsubmission.field_npxsubmission_phone'
        - 'config:field.storage.npxsubmission.field_npxsubmission_position'
        - 'config:field.storage.npxsubmission.field_npxsubmission_post_code'
