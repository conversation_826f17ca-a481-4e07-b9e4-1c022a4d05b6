uuid: 2445f48b-5f5d-485b-9700-0d64c3107917
langcode: pl
status: true
dependencies:
  config:
    - core.entity_view_mode.node.full
    - field.field.node.npxtrainer.body
    - field.field.node.npxtrainer.field_aktywny
    - field.field.node.npxtrainer.field_image
    - field.field.node.npxtrainer.field_image_alt
    - field.field.node.npxtrainer.field_image_big
    - field.field.node.npxtrainer.field_kolejnosc
    - field.field.node.npxtrainer.field_link_1
    - field.field.node.npxtrainer.field_link_2
    - field.field.node.npxtrainer.field_link_do_profilu_trenera
    - field.field.node.npxtrainer.field_npxtrainer_email
    - field.field.node.npxtrainer.field_npxtrainer_inslnk
    - field.field.node.npxtrainer.field_npxtrainer_phone
    - field.field.node.npxtrainer.field_npxtrainer_position
    - field.field.node.npxtrainer.field_npxtrainer_signature
    - field.field.node.npxtrainer.field_ref_opinia
    - field.field.node.npxtrainer.field_ref_referencja
    - field.field.node.npxtrainer.field_tekst_dodatkowy
    - field.field.node.npxtrainer.field_tekstplus
    - field.field.node.npxtrainer.field_ukryj_na_zesp
    - field.field.node.npxtrainer.field_zajawka
    - node.type.npxtrainer
  module:
    - ds
    - image
    - link
    - text
    - user
third_party_settings:
  ds:
    layout:
      id: ds_3col_stacked_fluid
      library: layout_plugin/ds_3col_stacked_fluid
      disable_css: false
      entity_classes: old_view_mode
      settings:
        classes:
          layout_class:
            trener-profil: trener-profil
          header: {  }
          left: {  }
          middle: {  }
          right: {  }
          footer: {  }
          ds_hidden: {  }
        wrappers:
          header: div
          left: div
          middle: div
          right: div
          footer: div
          ds_hidden: div
        outer_wrapper: div
        attributes: ''
        link_attribute: ''
        link_custom: ''
      path: modules/ds
    regions:
      middle:
        - field_image_big
        - body
        - field_tekstplus
        - field_ref_referencja
        - field_link_1
        - field_ref_opinia
        - field_link_2
      header:
        - field_npxtrainer_position
      right:
        - field_tekst_dodatkowy
id: node.npxtrainer.full
targetEntityType: node
bundle: npxtrainer
mode: full
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 4
    region: middle
  field_aktywny:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 19
    region: content
  field_image:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 1
    region: content
  field_image_big:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 0
    region: middle
  field_kolejnosc:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 26
    region: content
  field_link_1:
    type: link
    label: hidden
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 7
    region: middle
  field_link_2:
    type: link
    label: hidden
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 9
    region: middle
  field_npxtrainer_email:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 22
    region: content
  field_npxtrainer_phone:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 23
    region: content
  field_npxtrainer_position:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 3
    region: header
  field_npxtrainer_signature:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 24
    region: content
  field_ref_opinia:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: trener
      link: false
    third_party_settings:
      ds:
        ds_limit: ''
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
    weight: 8
    region: middle
  field_ref_referencja:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: trener
      link: false
    third_party_settings:
      ds:
        ds_limit: ''
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
    weight: 6
    region: middle
  field_tekst_dodatkowy:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 10
    region: right
  field_tekstplus:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 5
    region: middle
  field_zajawka:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 25
    region: content
  langcode:
    type: language
    label: above
    settings:
      link_to_entity: false
      native_language: false
    third_party_settings: {  }
    weight: 20
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 21
    region: content
hidden:
  field_image_alt: true
  field_link_do_profilu_trenera: true
  field_npxtrainer_inslnk: true
  field_ukryj_na_zesp: true
