uuid: 72a57267-4c03-444f-bb60-a4dc317c485f
langcode: pl
status: true
dependencies:
  config:
    - block_content.type.npx_freshmail_newsletter
    - field.storage.block_content.field_npx_freshmail_image
  module:
    - image
_core:
  default_config_hash: rnHAa-VmdF_QH4oLaChI4ohUw6nydk8Y24IFaMuR-VQ
id: block_content.npx_freshmail_newsletter.field_npx_freshmail_image
field_name: field_npx_freshmail_image
entity_type: block_content
bundle: npx_freshmail_newsletter
label: Ikona
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: '[date:custom:Y]-[date:custom:m]'
  file_extensions: 'png gif jpg jpeg'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: true
  alt_field_required: true
  title_field: false
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
