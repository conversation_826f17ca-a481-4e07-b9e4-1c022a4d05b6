uuid: 2611d795-81fe-438c-a55e-1b742499e4c6
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_kategoria
    - node.type.opinia
    - taxonomy.vocabulary.kategoria_wpisu
id: node.opinia.field_kategoria
field_name: field_kategoria
entity_type: node
bundle: opinia
label: 'Kategoria wpisu'
description: ''
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      kategoria_wpisu: kategoria_wpisu
    sort:
      field: _none
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
