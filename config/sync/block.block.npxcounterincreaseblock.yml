uuid: 66f865eb-2b0e-4b65-b77d-2dfa742a1ee5
langcode: pl
status: true
dependencies:
  module:
    - node
    - npx_blocks
  theme:
    - bootstrap4grow
id: npxcounterincreaseblock
theme: bootstrap4grow
region: content
weight: -20
provider: null
plugin: npx_counter_increase_block
settings:
  id: npx_counter_increase_block
  label: 'Npx counter increase block'
  label_display: '0'
  provider: npx_blocks
visibility:
  'entity_bundle:node':
    id: 'entity_bundle:node'
    negate: false
    context_mapping:
      node: '@node.node_route_context:node'
    bundles:
      npxtraining: npxtraining
      npxtraining_closed: npxtraining_closed
