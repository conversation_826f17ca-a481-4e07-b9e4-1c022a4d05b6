uuid: 46d608a7-02aa-4abb-b027-39455ae9530d
langcode: pl
status: true
dependencies:
  config:
    - block_content.type.blok_szkolenie
    - field.storage.node.field_npxtrainer_block_ref_
    - node.type.npxtraining
id: node.npxtraining.field_npxtrainer_block_ref_
field_name: field_npxtrainer_block_ref_
entity_type: node
bundle: npxtraining
label: 'npxtrainer block ref voucher'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:block_content'
  handler_settings:
    target_bundles:
      blok_szkolenie: blok_szkolenie
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
