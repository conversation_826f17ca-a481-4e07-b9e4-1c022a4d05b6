uuid: bce0340b-6746-4ff1-8f92-703441e8dd81
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxcollect.npxcollect
    - field.storage.npxcollect.field_npxcollect_hash
id: npxcollect.npxcollect.field_npxcollect_hash
field_name: field_npxcollect_hash
entity_type: npxcollect
bundle: npxcollect
label: Hasz
description: 'Generowane automatycznie po zapisaniu, nie wypeł<PERSON>.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
