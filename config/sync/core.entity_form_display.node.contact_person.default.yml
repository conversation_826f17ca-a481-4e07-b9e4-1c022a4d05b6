uuid: c041ef74-bcd6-47af-810a-e5466a263fe9
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.contact_person.field_training_contact
    - node.type.contact_person
  module:
    - paragraphs
    - path
id: node.contact_person.default
targetEntityType: node
bundle: contact_person
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_training_contact:
    type: entity_reference_paragraphs
    weight: 122
    region: content
    settings:
      title: Akapit
      title_plural: Akapity
      edit_mode: open
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: _none
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 15
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 120
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 16
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 50
    region: content
    settings: {  }
    third_party_settings: {  }
hidden: {  }
