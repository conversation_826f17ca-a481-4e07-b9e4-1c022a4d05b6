uuid: 486f5422-cbd3-47a5-a800-cd84c00b6cd2
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxmail.npxmail
    - field.field.npxmail.npxmail.field_npxmail_date_to_send
    - field.field.npxmail.npxmail.field_npxmail_message
    - field.field.npxmail.npxmail.field_npxmail_node
    - field.field.npxmail.npxmail.field_npxmail_sended_emails
    - field.field.npxmail.npxmail.field_npxmail_sent
    - field.field.npxmail.npxmail.field_npxmail_submission
  module:
    - datetime
id: npxmail.npxmail.default
targetEntityType: npxmail
bundle: npxmail
mode: default
content:
  created:
    type: timestamp
    label: hidden
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: ''
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 0
    region: content
  field_npxmail_date_to_send:
    type: datetime_default
    label: above
    settings:
      timezone_override: ''
      format_type: medium
    third_party_settings: {  }
    weight: 4
    region: content
  field_npxmail_message:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 3
    region: content
  field_npxmail_node:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 7
    region: content
  field_npxmail_sended_emails:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 6
    region: content
  field_npxmail_sent:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 5
    region: content
  field_npxmail_submission:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 1
    region: content
  title:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: -5
    region: content
hidden:
  changed: true
  id: true
  langcode: true
