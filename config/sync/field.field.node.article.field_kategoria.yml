uuid: fd8816fa-c7d3-40cb-9bba-64ec10a757ef
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_kategoria
    - node.type.article
    - taxonomy.vocabulary.kategoria_wpisu
  content:
    - 'taxonomy_term:kategoria_wpisu:a8bc86ed-62e6-4309-af0b-11e23d1cae7c'
id: node.article.field_kategoria
field_name: field_kategoria
entity_type: node
bundle: article
label: 'Kategoria wpisu'
description: ''
required: true
translatable: true
default_value:
  -
    target_uuid: a8bc86ed-62e6-4309-af0b-11e23d1cae7c
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      kategoria_wpisu: kategoria_wpisu
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
