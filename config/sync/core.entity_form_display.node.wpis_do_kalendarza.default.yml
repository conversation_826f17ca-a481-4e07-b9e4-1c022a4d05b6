uuid: b6eb2a17-3fd8-4ffc-8a33-************
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.wpis_do_kalendarza.body
    - field.field.node.wpis_do_kalendarza.field_npx_cal_event_type
    - field.field.node.wpis_do_kalendarza.field_npxtraining_date_end
    - field.field.node.wpis_do_kalendarza.field_npxtraining_date_start
    - node.type.wpis_do_kalendarza
  module:
    - datetime
    - path
    - text
id: node.wpis_do_kalendarza.default
targetEntityType: node
bundle: wpis_do_kalendarza
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 31
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npx_cal_event_type:
    type: options_select
    weight: 34
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npxtraining_date_end:
    type: datetime_default
    weight: 33
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npxtraining_date_start:
    type: datetime_default
    weight: 32
    region: content
    settings: {  }
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 15
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 121
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 16
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 50
    region: content
    settings: {  }
    third_party_settings: {  }
hidden: {  }
