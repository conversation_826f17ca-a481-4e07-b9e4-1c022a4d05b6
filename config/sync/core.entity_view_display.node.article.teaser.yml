uuid: 747c915f-c36e-4bc9-8600-c97ab04adc79
langcode: pl
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.article.body
    - field.field.node.article.comment
    - field.field.node.article.field_artykul_wprowadzenie
    - field.field.node.article.field_freshmail_form
    - field.field.node.article.field_image
    - field.field.node.article.field_kategoria
    - field.field.node.article.field_meta_tagi
    - field.field.node.article.field_npx_basic_paragraphs
    - field.field.node.article.field_pokaz_na_liscie
    - field.field.node.article.field_tags
    - field.field.node.article.field_trailer
    - field.field.node.article.field_zalacznik
    - image.style.medium
    - node.type.article
  module:
    - ds
    - image
    - text
    - user
third_party_settings:
  ds:
    layout:
      id: ds_2col_fluid
      library: ds/ds_2col_fluid
      disable_css: false
      entity_classes: all_classes
      settings:
        classes:
          layout_class: {  }
          left: {  }
          right: {  }
          ds_hidden: {  }
        wrappers:
          left: div
          right: div
          ds_hidden: div
        outer_wrapper: div
        attributes: ''
        link_attribute: ''
        link_custom: ''
    regions:
      left:
        - field_image
      right:
        - node_title
        - field_artykul_wprowadzenie
        - node_link
    fields:
      node_title:
        plugin_id: node_title
        weight: 1
        label: hidden
        formatter: default
        settings:
          link: true
          wrapper: h2
          class: ''
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
      node_link:
        plugin_id: node_link
        weight: 3
        label: hidden
        formatter: default
id: node.article.teaser
targetEntityType: node
bundle: article
mode: teaser
content:
  field_artykul_wprowadzenie:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: right
  field_image:
    type: image
    label: hidden
    settings:
      image_link: content
      image_style: medium
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 0
    region: left
hidden:
  body: true
  comment: true
  field_freshmail_form: true
  field_kategoria: true
  field_meta_tagi: true
  field_npx_basic_paragraphs: true
  field_pokaz_na_liscie: true
  field_tags: true
  field_trailer: true
  field_zalacznik: true
  langcode: true
  links: true
