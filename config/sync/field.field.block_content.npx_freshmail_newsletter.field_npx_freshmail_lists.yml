uuid: f9b84975-6f00-4d00-8d85-341f320b1a89
langcode: pl
status: true
dependencies:
  config:
    - block_content.type.npx_freshmail_newsletter
    - field.storage.block_content.field_npx_freshmail_lists
    - taxonomy.vocabulary.npx_freshmail_lists
_core:
  default_config_hash: '-629zGkvwL3PtERVVkWG3B0krwnwZ0fSKH9GSa3J9HE'
id: block_content.npx_freshmail_newsletter.field_npx_freshmail_lists
field_name: field_npx_freshmail_lists
entity_type: block_content
bundle: npx_freshmail_newsletter
label: Listy
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      npx_freshmail_lists: npx_freshmail_lists
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
