uuid: 08596c62-f89d-41ef-a900-b15a952cc7f6
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxmessage.npxmessage
    - field.field.npxmessage.npxmessage.field_npxmessage_attach_cert
    - field.field.npxmessage.npxmessage.field_npxmessage_body_f
    - field.field.npxmessage.npxmessage.field_npxmessage_body_m
    - field.field.npxmessage.npxmessage.field_npxmessage_from
    - field.field.npxmessage.npxmessage.field_npxmessage_interval_type
    - field.field.npxmessage.npxmessage.field_npxmessage_interval_value
    - field.field.npxmessage.npxmessage.field_npxmessage_subject
  module:
    - text
id: npxmessage.npxmessage.default
targetEntityType: npxmessage
bundle: npxmessage
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npxmessage_attach_cert:
    type: boolean_checkbox
    weight: 17
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_npxmessage_body_f:
    type: text_textarea
    weight: 13
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_npxmessage_body_m:
    type: text_textarea
    weight: 12
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_npxmessage_from:
    type: string_textfield
    weight: 16
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_npxmessage_interval_type:
    type: options_select
    weight: 15
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npxmessage_interval_value:
    type: number
    weight: 14
    region: content
    settings:
      placeholder: ''
    third_party_settings: {  }
  field_npxmessage_subject:
    type: string_textarea
    weight: 11
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
