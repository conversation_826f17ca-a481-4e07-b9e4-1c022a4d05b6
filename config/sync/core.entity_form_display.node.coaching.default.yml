uuid: 08e86adc-1f96-4428-bf00-f94ec517f1a7
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.coaching.body
    - field.field.node.coaching.field_coaching_teksty
    - field.field.node.coaching.field_metatags
    - field.field.node.coaching.field_npxtrainer_block_ref_
    - field.field.node.coaching.field_npxtraining_block_img
    - field.field.node.coaching.field_npxtraining_paragraf_trene
    - field.field.node.coaching.field_npxtraining_reviews
    - image.style.thumbnail
    - node.type.coaching
  module:
    - focal_point
    - metatag
    - paragraphs
    - path
    - text
id: node.coaching.default
targetEntityType: node
bundle: coaching
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 7
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  field_coaching_teksty:
    type: text_textarea
    weight: 12
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_metatags:
    type: metatag_firehose
    weight: 13
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npxtrainer_block_ref_:
    type: entity_reference_autocomplete
    weight: 10
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_npxtraining_block_img:
    type: image_focal_point
    weight: 8
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
      preview_link: true
      offsets: '50,50'
    third_party_settings: {  }
  field_npxtraining_paragraf_trene:
    type: entity_reference_paragraphs
    weight: 11
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: open
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
    third_party_settings: {  }
  field_npxtraining_reviews:
    type: text_textarea
    weight: 9
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 1
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  path:
    type: path
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 14
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 5
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 2
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 50
    region: content
    settings: {  }
    third_party_settings: {  }
hidden: {  }
