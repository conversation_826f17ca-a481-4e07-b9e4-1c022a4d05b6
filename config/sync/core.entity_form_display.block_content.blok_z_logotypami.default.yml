uuid: 53cdfc3f-450e-45da-8d19-178c15fc9a56
langcode: pl
status: true
dependencies:
  config:
    - block_content.type.blok_z_logotypami
    - field.field.block_content.blok_z_logotypami.field_loga_firm
  module:
    - inline_entity_form
id: block_content.blok_z_logotypami.default
targetEntityType: block_content
bundle: blok_z_logotypami
mode: default
content:
  field_loga_firm:
    type: inline_entity_form_simple
    weight: 26
    region: content
    settings:
      form_mode: default
      override_labels: false
      label_singular: ''
      label_plural: ''
    third_party_settings: {  }
  info:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
hidden: {  }
