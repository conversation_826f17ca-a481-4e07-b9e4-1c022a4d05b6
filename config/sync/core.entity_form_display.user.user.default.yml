uuid: 1c8783f1-bd5c-4263-bd00-b9c11b4c7524
langcode: pl
status: true
dependencies:
  config:
    - field.field.user.user.field_npxhash
    - field.field.user.user.user_picture
    - image.style.thumbnail
  module:
    - image
    - path
    - user
id: user.user.default
targetEntityType: user
bundle: user
mode: default
content:
  account:
    weight: -10
    region: content
    settings: {  }
    third_party_settings: {  }
  contact:
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npxhash:
    type: string_textfield
    weight: 31
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  google_analytics:
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  language:
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  timezone:
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  user_picture:
    type: image_image
    weight: -1
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
hidden:
  langcode: true
