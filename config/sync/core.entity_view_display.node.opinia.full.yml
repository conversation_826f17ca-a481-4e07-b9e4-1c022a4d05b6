uuid: 9c1bbe29-f50d-4b38-8bf2-1adc5889ef20
langcode: pl
status: true
dependencies:
  config:
    - core.entity_view_mode.node.full
    - field.field.node.opinia.body
    - field.field.node.opinia.field_additional_info
    - field.field.node.opinia.field_bonus_email
    - field.field.node.opinia.field_goldenline_link
    - field.field.node.opinia.field_image
    - field.field.node.opinia.field_kat_szkolenie
    - field.field.node.opinia.field_kategoria
    - field.field.node.opinia.field_linkedin_link
    - field.field.node.opinia.field_metatags
    - field.field.node.opinia.field_name
    - field.field.node.opinia.field_ocena_wartosci_opinii
    - field.field.node.opinia.field_promotions
    - field.field.node.opinia.field_surname
    - field.field.node.opinia.field_survey
    - field.field.node.opinia.field_tagi
    - field.field.node.opinia.field_trener
    - field.field.node.opinia.field_zajawka
    - image.style.mini_scale_crop_72_x_72
    - node.type.opinia
  module:
    - ds
    - field_group
    - image
    - link
    - metatag
    - taxonomy
    - text
    - user
third_party_settings:
  ds:
    layout:
      id: ds_3col_stacked_fluid
      library: ds/ds_3col_stacked_fluid
      disable_css: false
      entity_classes: old_view_mode
      settings:
        classes:
          layout_class:
            opinia: opinia
          header: {  }
          left: {  }
          middle: {  }
          right: {  }
          footer: {  }
          ds_hidden: {  }
        wrappers:
          header: div
          left: div
          middle: div
          right: div
          footer: div
          ds_hidden: div
        outer_wrapper: div
        attributes: ''
        link_attribute: ''
        link_custom: ''
        label: ''
    regions:
      header:
        - 'dynamic_block_field:node-ds_npx_breadcrumb'
        - node_title
      middle:
        - body
      footer:
        - group_signature_wrapper
        - field_zajawka
        - field_goldenline_link
        - field_image
        - field_linkedin_link
      ds_hidden:
        - field_ocena_wartosci_opinii
        - field_metatags
        - field_kategoria
        - npx_training_title_extra
        - node_link
        - links
        - langcode
        - field_trener
        - field_tagi
        - node_post_date
    fields:
      'dynamic_block_field:node-ds_npx_breadcrumb':
        plugin_id: 'dynamic_block_field:node-ds_npx_breadcrumb'
        weight: 0
        label: hidden
        formatter: default
      node_post_date:
        plugin_id: node_post_date
        weight: 14
        label: hidden
        formatter: ds_post_date_long
      node_link:
        plugin_id: node_link
        weight: 9
        label: hidden
        formatter: default
        settings:
          'link text': 'Strona opinii'
          'link class': ''
          wrapper: ''
          class: ''
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
      node_title:
        plugin_id: node_title
        weight: 1
        label: hidden
        formatter: default
        settings:
          link: false
          'link class': ''
          link_target: ''
          wrapper: ''
          class: ''
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
  field_group:
    group_signature_wrapper:
      children:
        - field_zajawka
        - field_goldenline_link
        - field_linkedin_link
      label: 'Signature wrapper'
      parent_name: ''
      region: footer
      weight: 3
      format_type: html_element
      format_settings:
        classes: n-signature-wrapper
        id: ''
        element: div
        show_label: false
        label_element: h3
        attributes: ''
        effect: none
        speed: fast
id: node.opinia.full
targetEntityType: node
bundle: opinia
mode: full
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: middle
  field_goldenline_link:
    type: link
    label: hidden
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 4
    region: footer
  field_image:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: mini_scale_crop_72_x_72
      image_loading:
        attribute: lazy
    third_party_settings:
      ds:
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
    weight: 4
    region: footer
  field_kategoria:
    type: entity_reference_rss_category
    label: inline
    settings: {  }
    third_party_settings:
      ds:
        ft:
          id: expert
          settings:
            lb: 'Szkolenie:'
            lbw: false
            lbw-el: ''
            lbw-cl: ''
            lbw-at: ''
            lb-col: false
            ow: false
            ow-el: ''
            ow-cl: ''
            ow-at: ''
            ow-def-at: false
            ow-def-cl: false
            fis: false
            fis-el: ''
            fis-cl: ''
            fis-at: ''
            fis-def-at: false
            fi: false
            fi-el: ''
            fi-cl: ''
            fi-at: ''
            fi-def-at: false
            prefix: ''
            suffix: ''
    weight: 7
    region: ds_hidden
  field_linkedin_link:
    type: link
    label: hidden
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 5
    region: footer
  field_metatags:
    type: metatag_empty_formatter
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 6
    region: ds_hidden
  field_ocena_wartosci_opinii:
    type: entity_reference_rss_category
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 5
    region: ds_hidden
  field_tagi:
    type: entity_reference_entity_id
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 13
    region: ds_hidden
  field_trener:
    type: entity_reference_entity_id
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 12
    region: ds_hidden
  field_zajawka:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 3
    region: footer
  langcode:
    type: language
    label: above
    settings:
      link_to_entity: false
      native_language: false
    third_party_settings: {  }
    weight: 11
    region: ds_hidden
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 10
    region: ds_hidden
  npx_training_title_extra:
    settings: {  }
    third_party_settings: {  }
    weight: 8
    region: ds_hidden
hidden:
  field_additional_info: true
  field_bonus_email: true
  field_kat_szkolenie: true
  field_name: true
  field_promotions: true
  field_surname: true
  field_survey: true
