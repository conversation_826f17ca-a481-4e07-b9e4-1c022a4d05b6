uuid: 4b3d86db-e270-4a3d-9011-f8893d479351
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_historic_minor_price
    - node.type.npxtraining_date
id: node.npxtraining_date.field_historic_minor_price
field_name: field_historic_minor_price
entity_type: node
bundle: npxtraining_date
label: 'Najniższa cena 30 dni'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  min: null
  max: null
  prefix: ''
  suffix: ''
field_type: integer
