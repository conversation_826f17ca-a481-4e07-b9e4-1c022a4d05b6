uuid: 65c44730-8fa4-4f22-af8a-6aa59dce3656
langcode: pl
status: true
dependencies:
  config:
    - block_content.type.npx_freshmail_newsletter
    - field.field.block_content.npx_freshmail_newsletter.field_npx_freshmail_btn
    - field.field.block_content.npx_freshmail_newsletter.field_npx_freshmail_header
    - field.field.block_content.npx_freshmail_newsletter.field_npx_freshmail_image
    - field.field.block_content.npx_freshmail_newsletter.field_npx_freshmail_layout_type
    - field.field.block_content.npx_freshmail_newsletter.field_npx_freshmail_lists
    - field.field.block_content.npx_freshmail_newsletter.field_npx_freshmail_msg
    - image.style.thumbnail
  module:
    - image
    - text
_core:
  default_config_hash: KoSkixjNhRccwh7PHxD8SHova5XDuOJEX1uTA73XPm4
id: block_content.npx_freshmail_newsletter.default
targetEntityType: block_content
bundle: npx_freshmail_newsletter
mode: default
content:
  field_npx_freshmail_btn:
    type: string_textfield
    weight: 6
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_npx_freshmail_header:
    type: text_textarea
    weight: 4
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_npx_freshmail_image:
    type: image_image
    weight: 5
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_npx_freshmail_layout_type:
    type: options_select
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npx_freshmail_lists:
    type: options_buttons
    weight: 3
    region: content
    settings: {  }
    third_party_settings: {  }
  field_npx_freshmail_msg:
    type: text_textarea
    weight: 7
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  info:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 1
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
hidden: {  }
