uuid: 3e06d0e5-f3bd-467a-956f-6005a4060bc8
langcode: pl
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.npx_quote.body
    - field.field.node.npx_quote.field_quote_author
    - field.field.node.npx_quote.field_quote_category
    - node.type.npx_quote
  module:
    - text
    - user
id: node.npx_quote.teaser
targetEntityType: node
bundle: npx_quote
mode: teaser
content:
  body:
    type: text_summary_or_trimmed
    label: hidden
    settings:
      trim_length: 600
    third_party_settings: {  }
    weight: 101
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  field_quote_author: true
  field_quote_category: true
  langcode: true
