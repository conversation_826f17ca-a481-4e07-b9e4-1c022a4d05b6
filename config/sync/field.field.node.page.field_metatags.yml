uuid: fa2f6f60-d7e5-46c4-af96-4d05b7db4452
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_metatags
    - node.type.page
  module:
    - metatag
id: node.page.field_metatags
field_name: field_metatags
entity_type: node
bundle: page
label: Metatags
description: ''
required: false
translatable: true
default_value:
  -
    value: 'a:0:{}'
default_value_callback: ''
settings: {  }
field_type: metatag
