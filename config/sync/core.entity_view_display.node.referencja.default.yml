uuid: 5abd21aa-6ffc-4cd9-bb45-697df31caa2a
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.referencja.body
    - field.field.node.referencja.field_image
    - field.field.node.referencja.field_kat_szkolenie
    - field.field.node.referencja.field_kategoria
    - field.field.node.referencja.field_kolejnosc
    - field.field.node.referencja.field_metatags
    - field.field.node.referencja.field_plik
    - field.field.node.referencja.field_referencja_zajawka
    - field.field.node.referencja.field_trener
    - field.field.node.referencja.field_zajawka
    - node.type.referencja
  module:
    - ds
    - field_group
    - metatag
    - text
    - user
third_party_settings:
  ds:
    layout:
      id: ds_3col_stacked_fluid
      library: ds/ds_3col_stacked_fluid
      disable_css: false
      entity_classes: old_view_mode
      settings:
        classes:
          layout_class:
            referencja: referencja
          header: {  }
          left: {  }
          middle: {  }
          right: {  }
          footer: {  }
          ds_hidden: {  }
        wrappers:
          header: div
          left: div
          middle: div
          right: div
          footer: div
          ds_hidden: div
        outer_wrapper: div
        attributes: ''
        link_attribute: ''
        link_custom: ''
    regions:
      middle:
        - field_referencja_zajawka
      footer:
        - field_zajawka
      ds_hidden:
        - field_metatags
  field_group:
    group_wiecej:
      children: {  }
      label: Więcej
      parent_name: ''
      region: content
      weight: 24
      format_type: fieldset
      format_settings:
        classes: ''
        id: ''
        description: ''
id: node.referencja.default
targetEntityType: node
bundle: referencja
mode: default
content:
  field_metatags:
    type: metatag_empty_formatter
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: ds_hidden
  field_referencja_zajawka:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: middle
  field_zajawka:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: footer
hidden:
  body: true
  field_image: true
  field_kat_szkolenie: true
  field_kategoria: true
  field_kolejnosc: true
  field_plik: true
  field_trener: true
  langcode: true
  links: true
