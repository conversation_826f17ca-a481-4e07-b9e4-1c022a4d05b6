uuid: edcd771c-8c12-4572-bd30-5a08a56ee7ec
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.webform.body
    - field.field.node.webform.webform
    - node.type.webform
  module:
    - ds
    - text
    - user
    - webform
third_party_settings:
  ds:
    layout:
      id: layout_onecol
      library: layout_discovery/onecol
      disable_css: false
      entity_classes: all_classes
      settings:
        label: ''
    regions:
      content:
        - links
        - body
        - webform
_core:
  default_config_hash: 3r1i0UMUbeNHhvmiqHDYznoS4G9XR1jq6lQ49r80lDE
id: node.webform.default
targetEntityType: node
bundle: webform
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  webform:
    type: webform_entity_reference_entity_view
    label: hidden
    settings:
      source_entity: true
    third_party_settings:
      ds:
        ft:
          id: default
          settings:
            lb: ''
            lb-col: false
            classes: {  }
    weight: 2
    region: content
hidden:
  langcode: true
