uuid: 86505692-b56f-4074-a718-24e903cbfbfc
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxmessage.npxmessage
    - field.field.npxmessage.npxmessage.field_npxmessage_attach_cert
    - field.field.npxmessage.npxmessage.field_npxmessage_body_f
    - field.field.npxmessage.npxmessage.field_npxmessage_body_m
    - field.field.npxmessage.npxmessage.field_npxmessage_from
    - field.field.npxmessage.npxmessage.field_npxmessage_interval_type
    - field.field.npxmessage.npxmessage.field_npxmessage_interval_value
    - field.field.npxmessage.npxmessage.field_npxmessage_subject
  module:
    - options
    - text
    - user
id: npxmessage.npxmessage.default
targetEntityType: npxmessage
bundle: npxmessage
mode: default
content:
  created:
    type: timestamp
    label: hidden
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: ''
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 0
    region: content
  field_npxmessage_attach_cert:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 7
    region: content
  field_npxmessage_body_f:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 3
    region: content
  field_npxmessage_body_m:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
  field_npxmessage_from:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 6
    region: content
  field_npxmessage_interval_type:
    type: list_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 5
    region: content
  field_npxmessage_interval_value:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 4
    region: content
  field_npxmessage_subject:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  title:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: -5
    region: content
  uid:
    type: author
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  changed: true
  id: true
  langcode: true
