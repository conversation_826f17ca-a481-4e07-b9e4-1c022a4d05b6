uuid: fabbf61e-e7da-4951-ac42-6637b17f304c
langcode: pl
status: true
dependencies:
  config:
    - field.field.node.book.body
    - field.field.node.book.field_metatags
    - node.type.book
  module:
    - ds
    - metatag
    - text
    - user
third_party_settings:
  ds:
    layout:
      id: ds_1col
      library: null
      disable_css: false
      entity_classes: old_view_mode
      settings:
        classes:
          layout_class: {  }
          ds_content: {  }
          ds_hidden: {  }
        wrappers:
          ds_content: div
          ds_hidden: div
        outer_wrapper: div
        attributes: ''
        link_attribute: ''
        link_custom: ''
        label: ''
    regions:
      ds_content:
        - body
        - field_metatags
id: node.book.default
targetEntityType: node
bundle: book
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: ds_content
  field_metatags:
    type: metatag_empty_formatter
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: ds_content
hidden:
  langcode: true
  links: true
