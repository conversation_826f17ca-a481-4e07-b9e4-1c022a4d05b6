uuid: 786a87fb-a17d-4eed-b063-0ede01abcf00
langcode: pl
status: true
dependencies:
  config:
    - eck.eck_type.npxsubmission.npxsubmission
    - field.storage.npxsubmission.field_fra_type
    - taxonomy.vocabulary.fra_types
  content:
    - 'taxonomy_term:fra_types:31df87b8-dc48-4213-8698-ecdc2db7f995'
id: npxsubmission.npxsubmission.field_fra_type
field_name: field_fra_type
entity_type: npxsubmission
bundle: npxsubmission
label: Fra
description: ''
required: false
translatable: false
default_value:
  -
    target_uuid: 31df87b8-dc48-4213-8698-ecdc2db7f995
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      fra_types: fra_types
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
