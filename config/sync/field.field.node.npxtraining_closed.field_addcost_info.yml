uuid: b69fa528-5142-42ac-a721-913b85093efb
langcode: pl
status: true
dependencies:
  config:
    - field.storage.node.field_addcost_info
    - node.type.npxtraining_closed
id: node.npxtraining_closed.field_addcost_info
field_name: field_addcost_info
entity_type: node
bundle: npxtraining_closed
label: 'Opis kosztów dodatkowych'
description: 'To pole służy do rozliczenia trenerów, potrzebuj<PERSON> wiedzieć co powinienem im dodać do stawki szkoleniowej.'
required: false
translatable: false
default_value:
  -
    value: "Opisujcie wg tego wzoru - to przykład:\r\n\r\nPo stronie trenera (NETTO):\r\n- logistyka 950 - 300 zł netto hotel/pociąg (4grow płaciło)= 650\r\n- angielska wersja 2000\r\n- +30% za połówkę dnia\r\n- weekend 1000 zł"
default_value_callback: ''
settings: {  }
field_type: string_long
