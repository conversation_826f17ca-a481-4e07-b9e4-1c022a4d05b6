uuid: 38ea4d7d-41cd-4484-893f-2437bd0cee42
langcode: pl
status: true
dependencies:
  config:
    - core.entity_view_mode.node.bootstrap_carousel
    - field.field.node.opinia.body
    - field.field.node.opinia.field_additional_info
    - field.field.node.opinia.field_bonus_email
    - field.field.node.opinia.field_goldenline_link
    - field.field.node.opinia.field_image
    - field.field.node.opinia.field_kat_szkolenie
    - field.field.node.opinia.field_kategoria
    - field.field.node.opinia.field_linkedin_link
    - field.field.node.opinia.field_metatags
    - field.field.node.opinia.field_name
    - field.field.node.opinia.field_ocena_wartosci_opinii
    - field.field.node.opinia.field_promotions
    - field.field.node.opinia.field_surname
    - field.field.node.opinia.field_survey
    - field.field.node.opinia.field_tagi
    - field.field.node.opinia.field_trener
    - field.field.node.opinia.field_zajawka
    - node.type.opinia
  module:
    - ds
    - metatag
    - taxonomy
    - text
    - user
third_party_settings:
  ds:
    layout:
      id: ds_1col
      library: null
      disable_css: false
      entity_classes: old_view_mode
      settings:
        classes:
          layout_class:
            opinia: opinia
          ds_content: {  }
          ds_hidden: {  }
        wrappers:
          ds_content: div
          ds_hidden: div
        outer_wrapper: div
        attributes: ''
        link_attribute: ''
        link_custom: ''
    regions:
      ds_content:
        - body
        - field_zajawka
      ds_hidden:
        - field_kategoria
        - field_metatags
        - links
        - langcode
        - field_trener
        - field_tagi
        - node_post_date
    fields:
      node_post_date:
        plugin_id: node_post_date
        weight: 8
        label: hidden
        formatter: ds_post_date_long
id: node.opinia.bootstrap_carousel
targetEntityType: node
bundle: opinia
mode: bootstrap_carousel
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings:
      ds:
        ft:
          id: expert
          settings:
            lb: ''
            lbw: false
            lbw-el: ''
            lbw-cl: ''
            lbw-at: ''
            lb-col: false
            ow: true
            ow-el: ''
            ow-cl: 'field--name-body readmore-js'
            ow-at: ''
            ow-def-at: false
            ow-def-cl: false
            fis: true
            fis-el: ''
            fis-cl: to-colaps
            fis-at: ''
            fis-def-at: false
            fi: false
            fi-el: ''
            fi-cl: ''
            fi-at: ''
            fi-def-at: false
            prefix: ''
            suffix: ''
    weight: 0
    region: ds_content
  field_kategoria:
    type: entity_reference_rss_category
    label: inline
    settings: {  }
    third_party_settings:
      ds:
        ft:
          id: expert
          settings:
            lb: 'Szkolenie:'
            lbw: false
            lbw-el: ''
            lbw-cl: ''
            lbw-at: ''
            lb-col: false
            ow: false
            ow-el: ''
            ow-cl: ''
            ow-at: ''
            ow-def-at: false
            ow-def-cl: false
            fis: false
            fis-el: ''
            fis-cl: ''
            fis-at: ''
            fis-def-at: false
            fi: false
            fi-el: ''
            fi-cl: ''
            fi-at: ''
            fi-def-at: false
            prefix: ''
            suffix: ''
    weight: 2
    region: ds_hidden
  field_metatags:
    type: metatag_empty_formatter
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 3
    region: ds_hidden
  field_tagi:
    type: entity_reference_entity_id
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 7
    region: ds_hidden
  field_trener:
    type: entity_reference_entity_id
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 6
    region: ds_hidden
  field_zajawka:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: ds_content
  langcode:
    type: language
    label: above
    settings:
      link_to_entity: false
      native_language: false
    third_party_settings: {  }
    weight: 5
    region: ds_hidden
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 4
    region: ds_hidden
  npx_training_title_extra:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  field_additional_info: true
  field_bonus_email: true
  field_goldenline_link: true
  field_image: true
  field_kat_szkolenie: true
  field_linkedin_link: true
  field_name: true
  field_ocena_wartosci_opinii: true
  field_promotions: true
  field_surname: true
  field_survey: true
