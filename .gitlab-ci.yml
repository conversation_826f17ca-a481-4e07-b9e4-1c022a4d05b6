################
# DrupalCI GitLabCI template
#
# Gitlab-ci.yml to replicate DrupalCI testing for Contrib
#
# With thanks to:
#   * The GitLab Acceleration Initiative participants
#   * DrupalSpoons
################

################
# Guidelines
#
# This template is designed to give any Contrib maintainer everything they need to test, without requiring modification. It is also designed to keep up to date with Core Development automatically through the use of include files that can be centrally maintained.
#
# However, you can modify this template if you have additional needs for your project.
################

################
# Includes
#
# Additional configuration can be provided through includes.
# One advantage of include files is that if they are updated upstream, the changes affect all pipelines using that include.
#
# Includes can be overridden by re-declaring anything provided in an include, here in gitlab-ci.yml
# https://docs.gitlab.com/ee/ci/yaml/includes.html#override-included-configuration-values
################

include:
  ################
  # DrupalCI includes:
  # As long as you include this, any future includes added by the Drupal Association will be accessible to your pipelines automatically.
  # View these include files at https://git.drupalcode.org/project/gitlab_templates/
  ################
  - project: "project/gitlab_orca"
    ref: "drupalci"
    file:
      - '/includes/include.orca.main.yml'
      - '/includes/include.orca.variables.yml'

variables:
  ORCA_SUT_NAME: "drupal/google_tag"
  DRUPAL_PROJECT_PATH: "modules/contrib"
  CORE_PREVIOUS_PHP_MIN: 8.1

phpcs:
  allow_failure: true
