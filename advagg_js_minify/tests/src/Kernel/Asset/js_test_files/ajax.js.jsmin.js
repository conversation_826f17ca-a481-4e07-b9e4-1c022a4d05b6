(function($,window,Drupal,drupalSettings){'use strict';Drupal.behaviors.AJAX={attach:function(context,settings){function loadAjaxBehavior(base){var element_settings=settings.ajax[base];if(typeof element_settings.selector==='undefined'){element_settings.selector='#'+base;}
$(element_settings.selector).once('drupal-ajax').each(function(){element_settings.element=this;element_settings.base=base;Drupal.ajax(element_settings);});}
for(var base in settings.ajax){if(settings.ajax.hasOwnProperty(base)){loadAjaxBehavior(base);}}
$('.use-ajax').once('ajax').each(function(){var element_settings={};element_settings.progress={type:'throbber'};var href=$(this).attr('href');if(href){element_settings.url=href;element_settings.event='click';}
element_settings.dialogType=$(this).data('dialog-type');element_settings.dialog=$(this).data('dialog-options');element_settings.base=$(this).attr('id');element_settings.element=this;Drupal.ajax(element_settings);});$('.use-ajax-submit').once('ajax').each(function(){var element_settings={};element_settings.url=$(this.form).attr('action');element_settings.setClick=true;element_settings.event='click';element_settings.progress={type:'throbber'};element_settings.base=$(this).attr('id');element_settings.element=this;Drupal.ajax(element_settings);});},detach:function(context,settings,trigger){if(trigger==='unload'){Drupal.ajax.expired().forEach(function(instance){Drupal.ajax.instances[instance.instanceIndex]=null;});}}};Drupal.AjaxError=function(xmlhttp,uri,customMessage){var statusCode;var statusText;var pathText;var responseText;var readyStateText;if(xmlhttp.status){statusCode='\n'+Drupal.t('An AJAX HTTP error occurred.')+'\n'+Drupal.t('HTTP Result Code: !status',{'!status':xmlhttp.status});}
else{statusCode='\n'+Drupal.t('An AJAX HTTP request terminated abnormally.');}
statusCode+='\n'+Drupal.t('Debugging information follows.');pathText='\n'+Drupal.t('Path: !uri',{'!uri':uri});statusText='';try{statusText='\n'+Drupal.t('StatusText: !statusText',{'!statusText':$.trim(xmlhttp.statusText)});}
catch(e){}
responseText='';try{responseText='\n'+Drupal.t('ResponseText: !responseText',{'!responseText':$.trim(xmlhttp.responseText)});}
catch(e){}
responseText=responseText.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi,'');responseText=responseText.replace(/[\n]+\s+/g,'\n');readyStateText=xmlhttp.status===0?('\n'+Drupal.t('ReadyState: !readyState',{'!readyState':xmlhttp.readyState})):'';customMessage=customMessage?('\n'+Drupal.t('CustomMessage: !customMessage',{'!customMessage':customMessage})):'';this.message=statusCode+pathText+statusText+customMessage+responseText+readyStateText;this.name='AjaxError';};Drupal.AjaxError.prototype=new Error();Drupal.AjaxError.prototype.constructor=Drupal.AjaxError;Drupal.ajax=function(settings){if(arguments.length!==1){throw new Error('Drupal.ajax() function must be called with one configuration object only');}
var base=settings.base||false;var element=settings.element||false;delete settings.base;delete settings.element;if(!settings.progress&&!element){settings.progress=false;}
var ajax=new Drupal.Ajax(base,element,settings);ajax.instanceIndex=Drupal.ajax.instances.length;Drupal.ajax.instances.push(ajax);return ajax;};Drupal.ajax.instances=[];Drupal.ajax.expired=function(){return Drupal.ajax.instances.filter(function(instance){return instance&&instance.element!==false&&!document.body.contains(instance.element);});};Drupal.Ajax=function(base,element,element_settings){var defaults={event:element?'mousedown':null,keypress:true,selector:base?'#'+base:null,effect:'none',speed:'none',method:'replaceWith',progress:{type:'throbber',message:Drupal.t('Please wait...')},submit:{js:true}};$.extend(this,defaults,element_settings);this.commands=new Drupal.AjaxCommands();this.instanceIndex=false;if(this.wrapper){this.wrapper='#'+this.wrapper;}
this.element=element;this.element_settings=element_settings;if(this.element&&this.element.form){this.$form=$(this.element.form);}
if(!this.url){var $element=$(this.element);if($element.is('a')){this.url=$element.attr('href');}
else if(this.element&&element.form){this.url=this.$form.attr('action');}}
var originalUrl=this.url;this.url=this.url.replace(/\/nojs(\/|$|\?|#)/g,'/ajax$1');if(drupalSettings.ajaxTrustedUrl[originalUrl]){drupalSettings.ajaxTrustedUrl[this.url]=true;}
var ajax=this;ajax.options={url:ajax.url,data:ajax.submit,beforeSerialize:function(element_settings,options){return ajax.beforeSerialize(element_settings,options);},beforeSubmit:function(form_values,element_settings,options){ajax.ajaxing=true;return ajax.beforeSubmit(form_values,element_settings,options);},beforeSend:function(xmlhttprequest,options){ajax.ajaxing=true;return ajax.beforeSend(xmlhttprequest,options);},success:function(response,status,xmlhttprequest){if(typeof response==='string'){response=$.parseJSON(response);}
if(response!==null&&!drupalSettings.ajaxTrustedUrl[ajax.url]){if(xmlhttprequest.getResponseHeader('X-Drupal-Ajax-Token')!=='1'){var customMessage=Drupal.t('The response failed verification so will not be processed.');return ajax.error(xmlhttprequest,ajax.url,customMessage);}}
return ajax.success(response,status);},complete:function(xmlhttprequest,status){ajax.ajaxing=false;if(status==='error'||status==='parsererror'){return ajax.error(xmlhttprequest,ajax.url);}},dataType:'json',type:'POST'};if(element_settings.dialog){ajax.options.data.dialogOptions=element_settings.dialog;}
if(ajax.options.url.indexOf('?')===-1){ajax.options.url+='?';}
else{ajax.options.url+='&';}
ajax.options.url+=Drupal.ajax.WRAPPER_FORMAT+'=drupal_'+(element_settings.dialogType||'ajax');$(ajax.element).on(element_settings.event,function(event){if(!drupalSettings.ajaxTrustedUrl[ajax.url]&&!Drupal.url.isLocal(ajax.url)){throw new Error(Drupal.t('The callback URL is not local and not trusted: !url',{'!url':ajax.url}));}
return ajax.eventResponse(this,event);});if(element_settings.keypress){$(ajax.element).on('keypress',function(event){return ajax.keypressResponse(this,event);});}
if(element_settings.prevent){$(ajax.element).on(element_settings.prevent,false);}};Drupal.ajax.WRAPPER_FORMAT='_wrapper_format';Drupal.Ajax.AJAX_REQUEST_PARAMETER='_drupal_ajax';Drupal.Ajax.prototype.execute=function(){if(this.ajaxing){return;}
try{this.beforeSerialize(this.element,this.options);return $.ajax(this.options);}
catch(e){this.ajaxing=false;window.alert('An error occurred while attempting to process '+this.options.url+': '+e.message);return $.Deferred().reject();}};Drupal.Ajax.prototype.keypressResponse=function(element,event){var ajax=this;if(event.which===13||(event.which===32&&element.type!=='text'&&element.type!=='textarea'&&element.type!=='tel'&&element.type!=='number')){event.preventDefault();event.stopPropagation();$(ajax.element_settings.element).trigger(ajax.element_settings.event);}};Drupal.Ajax.prototype.eventResponse=function(element,event){event.preventDefault();event.stopPropagation();var ajax=this;if(ajax.ajaxing){return;}
try{if(ajax.$form){if(ajax.setClick){element.form.clk=element;}
ajax.$form.ajaxSubmit(ajax.options);}
else{ajax.beforeSerialize(ajax.element,ajax.options);$.ajax(ajax.options);}}
catch(e){ajax.ajaxing=false;window.alert('An error occurred while attempting to process '+ajax.options.url+': '+e.message);}};Drupal.Ajax.prototype.beforeSerialize=function(element,options){if(this.$form){var settings=this.settings||drupalSettings;Drupal.detachBehaviors(this.$form.get(0),settings,'serialize');}
options.data[Drupal.Ajax.AJAX_REQUEST_PARAMETER]=1;var pageState=drupalSettings.ajaxPageState;options.data['ajax_page_state[theme]']=pageState.theme;options.data['ajax_page_state[theme_token]']=pageState.theme_token;options.data['ajax_page_state[libraries]']=pageState.libraries;};Drupal.Ajax.prototype.beforeSubmit=function(form_values,element,options){};Drupal.Ajax.prototype.beforeSend=function(xmlhttprequest,options){if(this.$form){options.extraData=options.extraData||{};options.extraData.ajax_iframe_upload='1';var v=$.fieldValue(this.element);if(v!==null){options.extraData[this.element.name]=v;}}
$(this.element).prop('disabled',true);if(!this.progress||!this.progress.type){return;}
var progressIndicatorMethod='setProgressIndicator'+this.progress.type.slice(0,1).toUpperCase()+this.progress.type.slice(1).toLowerCase();if(progressIndicatorMethod in this&&typeof this[progressIndicatorMethod]==='function'){this[progressIndicatorMethod].call(this);}};Drupal.Ajax.prototype.setProgressIndicatorBar=function(){var progressBar=new Drupal.ProgressBar('ajax-progress-'+this.element.id,$.noop,this.progress.method,$.noop);if(this.progress.message){progressBar.setProgress(-1,this.progress.message);}
if(this.progress.url){progressBar.startMonitoring(this.progress.url,this.progress.interval||1500);}
this.progress.element=$(progressBar.element).addClass('ajax-progress ajax-progress-bar');this.progress.object=progressBar;$(this.element).after(this.progress.element);};Drupal.Ajax.prototype.setProgressIndicatorThrobber=function(){this.progress.element=$('<div class="ajax-progress ajax-progress-throbber"><div class="throbber">&nbsp;</div></div>');if(this.progress.message){this.progress.element.find('.throbber').after('<div class="message">'+this.progress.message+'</div>');}
$(this.element).after(this.progress.element);};Drupal.Ajax.prototype.setProgressIndicatorFullscreen=function(){this.progress.element=$('<div class="ajax-progress ajax-progress-fullscreen">&nbsp;</div>');$('body').after(this.progress.element);};Drupal.Ajax.prototype.success=function(response,status){if(this.progress.element){$(this.progress.element).remove();}
if(this.progress.object){this.progress.object.stopMonitoring();}
$(this.element).prop('disabled',false);var elementParents=$(this.element).parents('[data-drupal-selector]').addBack().toArray();var focusChanged=false;for(var i in response){if(response.hasOwnProperty(i)&&response[i].command&&this.commands[response[i].command]){this.commands[response[i].command](this,response[i],status);if(response[i].command==='invoke'&&response[i].method==='focus'){focusChanged=true;}}}
if(!focusChanged&&this.element&&!$(this.element).data('disable-refocus')){var target=false;for(var n=elementParents.length-1;!target&&n>0;n--){target=document.querySelector('[data-drupal-selector="'+elementParents[n].getAttribute('data-drupal-selector')+'"]');}
if(target){$(target).trigger('focus');}}
if(this.$form){var settings=this.settings||drupalSettings;Drupal.attachBehaviors(this.$form.get(0),settings);}
this.settings=null;};Drupal.Ajax.prototype.getEffect=function(response){var type=response.effect||this.effect;var speed=response.speed||this.speed;var effect={};if(type==='none'){effect.showEffect='show';effect.hideEffect='hide';effect.showSpeed='';}
else if(type==='fade'){effect.showEffect='fadeIn';effect.hideEffect='fadeOut';effect.showSpeed=speed;}
else{effect.showEffect=type+'Toggle';effect.hideEffect=type+'Toggle';effect.showSpeed=speed;}
return effect;};Drupal.Ajax.prototype.error=function(xmlhttprequest,uri,customMessage){if(this.progress.element){$(this.progress.element).remove();}
if(this.progress.object){this.progress.object.stopMonitoring();}
$(this.wrapper).show();$(this.element).prop('disabled',false);if(this.$form){var settings=this.settings||drupalSettings;Drupal.attachBehaviors(this.$form.get(0),settings);}
throw new Drupal.AjaxError(xmlhttprequest,uri,customMessage);};Drupal.AjaxCommands=function(){};Drupal.AjaxCommands.prototype={insert:function(ajax,response,status){var $wrapper=response.selector?$(response.selector):$(ajax.wrapper);var method=response.method||ajax.method;var effect=ajax.getEffect(response);var settings;var $new_content_wrapped=$('<div></div>').html(response.data);var $new_content=$new_content_wrapped.contents();if($new_content.length!==1||$new_content.get(0).nodeType!==1){$new_content=$new_content_wrapped;}
switch(method){case'html':case'replaceWith':case'replaceAll':case'empty':case'remove':settings=response.settings||ajax.settings||drupalSettings;Drupal.detachBehaviors($wrapper.get(0),settings);}
$wrapper[method]($new_content);if(effect.showEffect!=='show'){$new_content.hide();}
if($new_content.find('.ajax-new-content').length>0){$new_content.find('.ajax-new-content').hide();$new_content.show();$new_content.find('.ajax-new-content')[effect.showEffect](effect.showSpeed);}
else if(effect.showEffect!=='show'){$new_content[effect.showEffect](effect.showSpeed);}
if($new_content.parents('html').length>0){settings=response.settings||ajax.settings||drupalSettings;Drupal.attachBehaviors($new_content.get(0),settings);}},remove:function(ajax,response,status){var settings=response.settings||ajax.settings||drupalSettings;$(response.selector).each(function(){Drupal.detachBehaviors(this,settings);}).remove();},changed:function(ajax,response,status){var $element=$(response.selector);if(!$element.hasClass('ajax-changed')){$element.addClass('ajax-changed');if(response.asterisk){$element.find(response.asterisk).append(' <abbr class="ajax-changed" title="'+Drupal.t('Changed')+'">*</abbr> ');}}},alert:function(ajax,response,status){window.alert(response.text,response.title);},redirect:function(ajax,response,status){window.location=response.url;},css:function(ajax,response,status){$(response.selector).css(response.argument);},settings:function(ajax,response,status){var ajaxSettings=drupalSettings.ajax;if(ajaxSettings){Drupal.ajax.expired().forEach(function(instance){if(instance.selector){var selector=instance.selector.replace('#','');if(selector in ajaxSettings){delete ajaxSettings[selector];}}});}
if(response.merge){$.extend(true,drupalSettings,response.settings);}
else{ajax.settings=response.settings;}},data:function(ajax,response,status){$(response.selector).data(response.name,response.value);},invoke:function(ajax,response,status){var $element=$(response.selector);$element[response.method].apply($element,response.args);},restripe:function(ajax,response,status){$(response.selector).find('> tbody > tr:visible, > tr:visible').removeClass('odd even').filter(':even').addClass('odd').end().filter(':odd').addClass('even');},update_build_id:function(ajax,response,status){$('input[name="form_build_id"][value="'+response.old+'"]').val(response.new);},add_css:function(ajax,response,status){$('head').prepend(response.data);var match;var importMatch=/^@import url\("(.*)"\);$/igm;if(document.styleSheets[0].addImport&&importMatch.test(response.data)){importMatch.lastIndex=0;do{match=importMatch.exec(response.data);document.styleSheets[0].addImport(match[1]);}while(match);}}};})(jQuery,window,Drupal,drupalSettings);