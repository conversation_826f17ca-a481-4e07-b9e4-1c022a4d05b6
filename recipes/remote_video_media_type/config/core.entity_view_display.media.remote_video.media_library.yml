langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.media_library
    - field.field.media.remote_video.field_media_oembed_video
    - image.style.medium
    - media.type.remote_video
  module:
    - image
id: media.remote_video.media_library
targetEntityType: media
bundle: remote_video
mode: media_library
content:
  thumbnail:
    type: image
    label: hidden
    settings:
      image_style: medium
      image_link: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  created: true
  field_media_oembed_video: true
  name: true
  uid: true
